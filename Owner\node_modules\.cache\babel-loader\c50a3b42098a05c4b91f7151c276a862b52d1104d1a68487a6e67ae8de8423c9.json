{"ast": null, "code": "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  Postpone: null,\n  abortAndThrowOnSynchronousRequestDataAccess: null,\n  abortOnSynchronousPlatformIOAccess: null,\n  accessedDynamicData: null,\n  annotateDynamicAccess: null,\n  consumeDynamicAccess: null,\n  createDynamicTrackingState: null,\n  createDynamicValidationState: null,\n  createHangingInputAbortSignal: null,\n  createPostponedAbortSignal: null,\n  formatDynamicAPIAccesses: null,\n  getFirstDynamicReason: null,\n  isDynamicPostpone: null,\n  isPrerenderInterruptedError: null,\n  markCurrentScopeAsDynamic: null,\n  postponeWithTracking: null,\n  throwIfDisallowedDynamic: null,\n  throwToInterruptStaticGeneration: null,\n  trackAllowedDynamicAccess: null,\n  trackDynamicDataInDynamicRender: null,\n  trackFallbackParamAccessed: null,\n  trackSynchronousPlatformIOAccessInDev: null,\n  trackSynchronousRequestDataAccessInDev: null,\n  useDynamicRouteParams: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  Postpone: function () {\n    return Postpone;\n  },\n  abortAndThrowOnSynchronousRequestDataAccess: function () {\n    return abortAndThrowOnSynchronousRequestDataAccess;\n  },\n  abortOnSynchronousPlatformIOAccess: function () {\n    return abortOnSynchronousPlatformIOAccess;\n  },\n  accessedDynamicData: function () {\n    return accessedDynamicData;\n  },\n  annotateDynamicAccess: function () {\n    return annotateDynamicAccess;\n  },\n  consumeDynamicAccess: function () {\n    return consumeDynamicAccess;\n  },\n  createDynamicTrackingState: function () {\n    return createDynamicTrackingState;\n  },\n  createDynamicValidationState: function () {\n    return createDynamicValidationState;\n  },\n  createHangingInputAbortSignal: function () {\n    return createHangingInputAbortSignal;\n  },\n  createPostponedAbortSignal: function () {\n    return createPostponedAbortSignal;\n  },\n  formatDynamicAPIAccesses: function () {\n    return formatDynamicAPIAccesses;\n  },\n  getFirstDynamicReason: function () {\n    return getFirstDynamicReason;\n  },\n  isDynamicPostpone: function () {\n    return isDynamicPostpone;\n  },\n  isPrerenderInterruptedError: function () {\n    return isPrerenderInterruptedError;\n  },\n  markCurrentScopeAsDynamic: function () {\n    return markCurrentScopeAsDynamic;\n  },\n  postponeWithTracking: function () {\n    return postponeWithTracking;\n  },\n  throwIfDisallowedDynamic: function () {\n    return throwIfDisallowedDynamic;\n  },\n  throwToInterruptStaticGeneration: function () {\n    return throwToInterruptStaticGeneration;\n  },\n  trackAllowedDynamicAccess: function () {\n    return trackAllowedDynamicAccess;\n  },\n  trackDynamicDataInDynamicRender: function () {\n    return trackDynamicDataInDynamicRender;\n  },\n  trackFallbackParamAccessed: function () {\n    return trackFallbackParamAccessed;\n  },\n  trackSynchronousPlatformIOAccessInDev: function () {\n    return trackSynchronousPlatformIOAccessInDev;\n  },\n  trackSynchronousRequestDataAccessInDev: function () {\n    return trackSynchronousRequestDataAccessInDev;\n  },\n  useDynamicRouteParams: function () {\n    return useDynamicRouteParams;\n  }\n});\nconst _react = /*#__PURE__*/_interop_require_default(require(\"react\"));\nconst _hooksservercontext = require(\"../../client/components/hooks-server-context\");\nconst _staticgenerationbailout = require(\"../../client/components/static-generation-bailout\");\nconst _workunitasyncstorageexternal = require(\"./work-unit-async-storage.external\");\nconst _workasyncstorageexternal = require(\"../app-render/work-async-storage.external\");\nconst _dynamicrenderingutils = require(\"../dynamic-rendering-utils\");\nconst _metadataconstants = require(\"../../lib/metadata/metadata-constants\");\nconst _scheduler = require(\"../../lib/scheduler\");\nfunction _interop_require_default(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nconst hasPostpone = typeof _react.default.unstable_postpone === 'function';\nfunction createDynamicTrackingState(isDebugDynamicAccesses) {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null\n  };\n}\nfunction createDynamicValidationState() {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: []\n  };\n}\nfunction getFirstDynamicReason(trackingState) {\n  var _trackingState_dynamicAccesses_;\n  return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\nfunction markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n  if (workUnitStore) {\n    if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return;\n    }\n  }\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return;\n  if (store.dynamicShouldError) {\n    throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n      value: \"E553\",\n      enumerable: false,\n      configurable: true\n    });\n  }\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0;\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n        value: \"E550\",\n        enumerable: false,\n        configurable: true\n      });\n      store.dynamicUsageDescription = expression;\n      store.dynamicUsageStack = err.stack;\n      throw err;\n    } else if (process.env.NODE_ENV === 'development' && workUnitStore && workUnitStore.type === 'request') {\n      workUnitStore.usedDynamic = true;\n    }\n  }\n}\nfunction trackFallbackParamAccessed(store, expression) {\n  const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return;\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking);\n}\nfunction throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n    value: \"E558\",\n    enumerable: false,\n    configurable: true\n  });\n  prerenderStore.revalidate = 0;\n  store.dynamicUsageDescription = expression;\n  store.dynamicUsageStack = err.stack;\n  throw err;\n}\nfunction trackDynamicDataInDynamicRender(_store, workUnitStore) {\n  if (workUnitStore) {\n    if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return;\n    }\n    if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0;\n    }\n    if (process.env.NODE_ENV === 'development' && workUnitStore.type === 'request') {\n      workUnitStore.usedDynamic = true;\n    }\n  }\n}\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`;\n  const error = createPrerenderInterruptedError(reason);\n  prerenderStore.controller.abort(error);\n  const dynamicTracking = prerenderStore.dynamicTracking;\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n      expression\n    });\n  }\n}\nfunction abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n  const dynamicTracking = prerenderStore.dynamicTracking;\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression;\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n}\nfunction trackSynchronousPlatformIOAccessInDev(requestStore) {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false;\n}\nfunction abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n  const prerenderSignal = prerenderStore.controller.signal;\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression;\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true;\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n  }\n  throw createPrerenderInterruptedError(`Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`);\n}\nconst trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nfunction Postpone({\n  reason,\n  route\n}) {\n  const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n  const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n  postponeWithTracking(route, reason, dynamicTracking);\n}\nfunction postponeWithTracking(route, expression, dynamicTracking) {\n  assertPostpone();\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n      expression\n    });\n  }\n  _react.default.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n  return `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n}\nfunction isDynamicPostpone(err) {\n  if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n    return isDynamicPostponeReason(err.message);\n  }\n  return false;\n}\nfunction isDynamicPostponeReason(reason) {\n  return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n    value: \"E296\",\n    enumerable: false,\n    configurable: true\n  });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n  const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n    value: \"E394\",\n    enumerable: false,\n    configurable: true\n  });\n  error.digest = NEXT_PRERENDER_INTERRUPTED;\n  return error;\n}\nfunction isPrerenderInterruptedError(error) {\n  return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nfunction accessedDynamicData(dynamicAccesses) {\n  return dynamicAccesses.length > 0;\n}\nfunction consumeDynamicAccess(serverDynamic, clientDynamic) {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n  return serverDynamic.dynamicAccesses;\n}\nfunction formatDynamicAPIAccesses(dynamicAccesses) {\n  return dynamicAccesses.filter(access => typeof access.stack === 'string' && access.stack.length > 0).map(({\n    expression,\n    stack\n  }) => {\n    stack = stack.split('\\n') // Remove the \"Error: \" prefix from the first line of the stack trace as\n    // well as the first 4 lines of the stack trace which is the distance\n    // from the user code and the `new Error().stack` call.\n    .slice(4).filter(line => {\n      // Exclude Next.js internals from the stack trace.\n      if (line.includes('node_modules/next/')) {\n        return false;\n      }\n      // Exclude anonymous functions from the stack trace.\n      if (line.includes(' (<anonymous>)')) {\n        return false;\n      }\n      // Exclude Node.js internals from the stack trace.\n      if (line.includes(' (node:')) {\n        return false;\n      }\n      return true;\n    }).join('\\n');\n    return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n  });\n}\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n      value: \"E224\",\n      enumerable: false,\n      configurable: true\n    });\n  }\n}\nfunction createPostponedAbortSignal(reason) {\n  assertPostpone();\n  const controller = new AbortController();\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    _react.default.unstable_postpone(reason);\n  } catch (x) {\n    controller.abort(x);\n  }\n  return controller.signal;\n}\nfunction createHangingInputAbortSignal(workUnitStore) {\n  const controller = new AbortController();\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort();\n    });\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    (0, _scheduler.scheduleOnNextTick)(() => controller.abort());\n  }\n  return controller.signal;\n}\nfunction annotateDynamicAccess(expression, prerenderStore) {\n  const dynamicTracking = prerenderStore.dynamicTracking;\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n      expression\n    });\n  }\n}\nfunction useDynamicRouteParams(expression) {\n  const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n  if (workStore && workStore.isStaticGeneration && workStore.fallbackRouteParams && workStore.fallbackRouteParams.size > 0) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        _react.default.use((0, _dynamicrenderingutils.makeHangingPromise)(workUnitStore.renderSignal, expression));\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore);\n      }\n    }\n  }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasMetadataRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasViewportRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`);\nfunction trackAllowedDynamicAccess(route, componentStack, dynamicValidation, serverDynamic, clientDynamic) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return;\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true;\n    return;\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true;\n    return;\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true;\n    return;\n  } else if (serverDynamic.syncDynamicErrorWithStack || clientDynamic.syncDynamicErrorWithStack) {\n    dynamicValidation.hasSyncDynamicErrors = true;\n    return;\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;\n    const error = createErrorWithComponentStack(message, componentStack);\n    dynamicValidation.dynamicErrors.push(error);\n    return;\n  }\n}\nfunction createErrorWithComponentStack(message, componentStack) {\n  const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n    value: \"E394\",\n    enumerable: false,\n    configurable: true\n  });\n  error.stack = 'Error: ' + message + componentStack;\n  return error;\n}\nfunction throwIfDisallowedDynamic(route, dynamicValidation, serverDynamic, clientDynamic) {\n  let syncError;\n  let syncExpression;\n  let syncLogged;\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack;\n    syncExpression = serverDynamic.syncDynamicExpression;\n    syncLogged = serverDynamic.syncDynamicLogged === true;\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack;\n    syncExpression = clientDynamic.syncDynamicExpression;\n    syncLogged = clientDynamic.syncDynamicLogged === true;\n  } else {\n    syncError = null;\n    syncExpression = undefined;\n    syncLogged = false;\n  }\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError);\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new _staticgenerationbailout.StaticGenBailoutError();\n  }\n  const dynamicErrors = dynamicValidation.dynamicErrors;\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i]);\n    }\n    throw new _staticgenerationbailout.StaticGenBailoutError();\n  }\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError);\n        throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n          value: \"E608\",\n          enumerable: false,\n          configurable: true\n        });\n      }\n      throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n        value: \"E534\",\n        enumerable: false,\n        configurable: true\n      });\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError);\n        throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n          value: \"E573\",\n          enumerable: false,\n          configurable: true\n        });\n      }\n      throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n        value: \"E590\",\n        enumerable: false,\n        configurable: true\n      });\n    }\n  }\n}", "map": {"version": 3, "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "_react", "default", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "_trackingState_dynamicAccesses_", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "Object", "defineProperty", "_staticgenerationbailout", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "_hooksservercontext", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "_workunitasyncstorageexternal", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "_scheduler", "scheduleOnNextTick", "workStore", "_workasyncstorageexternal", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "_dynamicrenderingutils", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "_metadataconstants", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\server\\app-render\\dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;GAoBC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAoVeA,QAAQ,WAAAA,CAAA;WAARA,QAAA;;EA3CAC,2CAA2C,WAAAA,CAAA;WAA3CA,2CAAA;;EAlCAC,kCAAkC,WAAAA,CAAA;WAAlCA,kCAAA;;EAuKAC,mBAAmB,WAAAA,CAAA;WAAnBA,mBAAA;;EA4GAC,qBAAqB,WAAAA,CAAA;WAArBA,qBAAA;;EAtGAC,oBAAoB,WAAAA,CAAA;WAApBA,oBAAA;;EAhXAC,0BAA0B,WAAAA,CAAA;WAA1BA,0BAAA;;EAWAC,4BAA4B,WAAAA,CAAA;WAA5BA,4BAAA;;EAmbAC,6BAA6B,WAAAA,CAAA;WAA7BA,6BAAA;;EAjBAC,0BAA0B,WAAAA,CAAA;WAA1BA,0BAAA;;EAlDAC,wBAAwB,WAAAA,CAAA;WAAxBA,wBAAA;;EAtWAC,qBAAqB,WAAAA,CAAA;WAArBA,qBAAA;;EAgSAC,iBAAiB,WAAAA,CAAA;WAAjBA,iBAAA;;EAwCAC,2BAA2B,WAAAA,CAAA;WAA3BA,2BAAA;;EA3TAC,yBAAyB,WAAAA,CAAA;WAAzBA,yBAAA;;EAuPAC,oBAAoB,WAAAA,CAAA;WAApBA,oBAAA;;EAgSAC,wBAAwB,WAAAA,CAAA;WAAxBA,wBAAA;;EAvcAC,gCAAgC,WAAAA,CAAA;WAAhCA,gCAAA;;EA6ZAC,yBAAyB,WAAAA,CAAA;WAAzBA,yBAAA;;EApYAC,+BAA+B,WAAAA,CAAA;WAA/BA,+BAAA;;EAzCAC,0BAA0B,WAAAA,CAAA;WAA1BA,0BAAA;;EAiHAC,qCAAqC,WAAAA,CAAA;WAArCA,qCAAA;;EAmDHC,sCAAsC,WAAAA,CAAA;WAAtCA,sCAAA;;EA+NGC,qBAAqB,WAAAA,CAAA;WAArBA,qBAAA;;;6DA9hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,WAAA,GAAc,OAAOC,MAAA,CAAAC,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASrB,2BACdsB,sBAA2C;EAE3C,OAAO;IACLA,sBAAA;IACAC,eAAA,EAAiB,EAAE;IACnBC,qBAAA,EAAuBC,SAAA;IACvBC,yBAAA,EAA2B;EAC7B;AACF;AAEO,SAASzB,6BAAA;EACd,OAAO;IACL0B,mBAAA,EAAqB;IACrBC,kBAAA,EAAoB;IACpBC,kBAAA,EAAoB;IACpBC,oBAAA,EAAsB;IACtBC,aAAA,EAAe;EACjB;AACF;AAEO,SAAS1B,sBACd2B,aAAmC;MAE5BC,+BAAA;EAAP,QAAOA,+BAAA,GAAAD,aAAA,CAAcT,eAAe,CAAC,EAAE,qBAAhCU,+BAAA,CAAkCC,UAAU;AACrD;AASO,SAAS1B,0BACd2B,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;EAElB,IAAIE,aAAA,EAAe;IACjB,IACEA,aAAA,CAAcC,IAAI,KAAK,WACvBD,aAAA,CAAcC,IAAI,KAAK,kBACvB;MACA;MACA;MACA;MACA;IACF;EACF;EAEA;EACA;EACA;EACA,IAAIF,KAAA,CAAMG,YAAY,IAAIH,KAAA,CAAMI,WAAW,EAAE;EAE7C,IAAIJ,KAAA,CAAMK,kBAAkB,EAAE;IAC5B,MAAMC,MAAA,CAAAC,cAEL,CAFK,IAAIC,wBAAA,CAAAC,qBAAqB,CAC7B,SAAST,KAAA,CAAMU,KAAK,iFAAiFX,UAAA,8HAAwI,GADzO;aAAA;kBAAA;oBAAA;IAEN;EACF;EAEA,IAAIE,aAAA,EAAe;IACjB,IAAIA,aAAA,CAAcC,IAAI,KAAK,iBAAiB;MAC1C5B,oBAAA,CACE0B,KAAA,CAAMU,KAAK,EACXX,UAAA,EACAE,aAAA,CAAcU,eAAe;IAEjC,OAAO,IAAIV,aAAA,CAAcC,IAAI,KAAK,oBAAoB;MACpDD,aAAA,CAAcW,UAAU,GAAG;MAE3B;MACA,MAAMC,GAAA,GAAMP,MAAA,CAAAC,cAEX,CAFW,IAAIO,mBAAA,CAAAC,kBAAkB,CAChC,SAASf,KAAA,CAAMU,KAAK,oDAAoDX,UAAA,6EAAuF,GADrJ;eAAA;oBAAA;sBAAA;MAEZ;MACAC,KAAA,CAAMgB,uBAAuB,GAAGjB,UAAA;MAChCC,KAAA,CAAMiB,iBAAiB,GAAGJ,GAAA,CAAIK,KAAK;MAEnC,MAAML,GAAA;IACR,OAAO,IACLM,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBpB,aAAA,IACAA,aAAA,CAAcC,IAAI,KAAK,WACvB;MACAD,aAAA,CAAcqB,WAAW,GAAG;IAC9B;EACF;AACF;AAUO,SAAS3C,2BACdqB,KAAgB,EAChBD,UAAkB;EAElB,MAAMwB,cAAA,GAAiBC,6BAAA,CAAAC,oBAAoB,CAACC,QAAQ;EACpD,IAAI,CAACH,cAAA,IAAkBA,cAAA,CAAerB,IAAI,KAAK,iBAAiB;EAEhE5B,oBAAA,CAAqB0B,KAAA,CAAMU,KAAK,EAAEX,UAAA,EAAYwB,cAAA,CAAeZ,eAAe;AAC9E;AAQO,SAASnC,iCACduB,UAAkB,EAClBC,KAAgB,EAChBuB,cAAoC;EAEpC;EACA,MAAMV,GAAA,GAAMP,MAAA,CAAAC,cAEX,CAFW,IAAIO,mBAAA,CAAAC,kBAAkB,CAChC,SAASf,KAAA,CAAMU,KAAK,sDAAsDX,UAAA,+EAAyF,GADzJ;WAAA;gBAAA;kBAAA;EAEZ;EAEAwB,cAAA,CAAeX,UAAU,GAAG;EAE5BZ,KAAA,CAAMgB,uBAAuB,GAAGjB,UAAA;EAChCC,KAAA,CAAMiB,iBAAiB,GAAGJ,GAAA,CAAIK,KAAK;EAEnC,MAAML,GAAA;AACR;AASO,SAASnC,gCACdiD,MAAiB,EACjB1B,aAAmC;EAEnC,IAAIA,aAAA,EAAe;IACjB,IACEA,aAAA,CAAcC,IAAI,KAAK,WACvBD,aAAA,CAAcC,IAAI,KAAK,kBACvB;MACA;MACA;MACA;MACA;IACF;IACA,IACED,aAAA,CAAcC,IAAI,KAAK,eACvBD,aAAA,CAAcC,IAAI,KAAK,oBACvB;MACAD,aAAA,CAAcW,UAAU,GAAG;IAC7B;IACA,IACEO,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBpB,aAAA,CAAcC,IAAI,KAAK,WACvB;MACAD,aAAA,CAAcqB,WAAW,GAAG;IAC9B;EACF;AACF;AAEA;AACA;AACA;AACA,SAASM,oCACPlB,KAAa,EACbX,UAAkB,EAClBwB,cAAoC;EAEpC,MAAMM,MAAA,GAAS,SAASnB,KAAA,oEAAyEX,UAAA,GAAa;EAE9G,MAAM+B,KAAA,GAAQC,+BAAA,CAAgCF,MAAA;EAE9CN,cAAA,CAAeS,UAAU,CAACC,KAAK,CAACH,KAAA;EAEhC,MAAMnB,eAAA,GAAkBY,cAAA,CAAeZ,eAAe;EACtD,IAAIA,eAAA,EAAiB;IACnBA,eAAA,CAAgBvB,eAAe,CAAC8C,IAAI,CAAC;MACnC;MACA;MACAhB,KAAA,EAAOP,eAAA,CAAgBxB,sBAAsB,GACzC,IAAIgD,KAAA,GAAQjB,KAAK,GACjB5B,SAAA;MACJS;IACF;EACF;AACF;AAEO,SAAStC,mCACdiD,KAAa,EACbX,UAAkB,EAClBqC,cAAqB,EACrBb,cAAoC;EAEpC,MAAMZ,eAAA,GAAkBY,cAAA,CAAeZ,eAAe;EACtD,IAAIA,eAAA,EAAiB;IACnB,IAAIA,eAAA,CAAgBpB,yBAAyB,KAAK,MAAM;MACtDoB,eAAA,CAAgBtB,qBAAqB,GAAGU,UAAA;MACxCY,eAAA,CAAgBpB,yBAAyB,GAAG6C,cAAA;IAC9C;EACF;EACAR,mCAAA,CAAoClB,KAAA,EAAOX,UAAA,EAAYwB,cAAA;AACzD;AAEO,SAAS3C,sCACdyD,YAA0B;EAE1B;EACA;EACAA,YAAA,CAAaC,cAAc,GAAG;AAChC;AAYO,SAAS9E,4CACdkD,KAAa,EACbX,UAAkB,EAClBqC,cAAqB,EACrBb,cAAoC;EAEpC,MAAMgB,eAAA,GAAkBhB,cAAA,CAAeS,UAAU,CAACQ,MAAM;EACxD,IAAID,eAAA,CAAgBE,OAAO,KAAK,OAAO;IACrC;IACA;IACA;IACA;IACA;IACA,MAAM9B,eAAA,GAAkBY,cAAA,CAAeZ,eAAe;IACtD,IAAIA,eAAA,EAAiB;MACnB,IAAIA,eAAA,CAAgBpB,yBAAyB,KAAK,MAAM;QACtDoB,eAAA,CAAgBtB,qBAAqB,GAAGU,UAAA;QACxCY,eAAA,CAAgBpB,yBAAyB,GAAG6C,cAAA;QAC5C,IAAIb,cAAA,CAAemB,UAAU,KAAK,MAAM;UACtC;UACA;UACA/B,eAAA,CAAgBgC,iBAAiB,GAAG;QACtC;MACF;IACF;IACAf,mCAAA,CAAoClB,KAAA,EAAOX,UAAA,EAAYwB,cAAA;EACzD;EACA,MAAMQ,+BAAA,CACJ,SAASrB,KAAA,oEAAyEX,UAAA,GAAa;AAEnG;AAGO,MAAMlB,sCAAA,GACXD,qCAAA;AASK,SAASrB,SAAS;EAAEsE,MAAM;EAAEnB;AAAK,CAAiB;EACvD,MAAMa,cAAA,GAAiBC,6BAAA,CAAAC,oBAAoB,CAACC,QAAQ;EACpD,MAAMf,eAAA,GACJY,cAAA,IAAkBA,cAAA,CAAerB,IAAI,KAAK,kBACtCqB,cAAA,CAAeZ,eAAe,GAC9B;EACNrC,oBAAA,CAAqBoC,KAAA,EAAOmB,MAAA,EAAQlB,eAAA;AACtC;AAEO,SAASrC,qBACdoC,KAAa,EACbX,UAAkB,EAClBY,eAA4C;EAE5CiC,cAAA;EACA,IAAIjC,eAAA,EAAiB;IACnBA,eAAA,CAAgBvB,eAAe,CAAC8C,IAAI,CAAC;MACnC;MACA;MACAhB,KAAA,EAAOP,eAAA,CAAgBxB,sBAAsB,GACzC,IAAIgD,KAAA,GAAQjB,KAAK,GACjB5B,SAAA;MACJS;IACF;EACF;EAEAf,MAAA,CAAAC,OAAK,CAACC,iBAAiB,CAAC2D,oBAAA,CAAqBnC,KAAA,EAAOX,UAAA;AACtD;AAEA,SAAS8C,qBAAqBnC,KAAa,EAAEX,UAAkB;EAC7D,OACE,SAASW,KAAA,oEAAyEX,UAAA,IAAc,GAChG,iFAAiF,GACjF,mFAAmF;AAEvF;AAEO,SAAS5B,kBAAkB0C,GAAY;EAC5C,IACE,OAAOA,GAAA,KAAQ,YACfA,GAAA,KAAQ,QACR,OAAOA,GAAC,CAAYiC,OAAO,KAAK,UAChC;IACA,OAAOC,uBAAA,CAAwBlC,GAAC,CAAYiC,OAAO;EACrD;EACA,OAAO;AACT;AAEA,SAASC,wBAAwBlB,MAAc;EAC7C,OACEA,MAAA,CAAOmB,QAAQ,CACb,sEAEFnB,MAAA,CAAOmB,QAAQ,CACb;AAGN;AAEA,IAAID,uBAAA,CAAwBF,oBAAA,CAAqB,OAAO,YAAY,OAAO;EACzE,MAAMvC,MAAA,CAAAC,cAEL,CAFK,IAAI4B,KAAA,CACR,2FADI;WAAA;gBAAA;kBAAA;EAEN;AACF;AAEA,MAAMc,0BAAA,GAA6B;AAEnC,SAASlB,gCAAgCe,OAAe;EACtD,MAAMhB,KAAA,GAAQxB,MAAA,CAAAC,cAAkB,CAAlB,IAAI4B,KAAA,CAAMW,OAAA,GAAV;WAAA;gBAAA;kBAAA;EAAiB;EAC7BhB,KAAA,CAAcoB,MAAM,GAAGD,0BAAA;EACzB,OAAOnB,KAAA;AACT;AAMO,SAAS1D,4BACd0D,KAAc;EAEd,OACE,OAAOA,KAAA,KAAU,YACjBA,KAAA,KAAU,QACVA,KAAC,CAAcoB,MAAM,KAAKD,0BAAA,IAC1B,UAAUnB,KAAA,IACV,aAAaA,KAAA,IACbA,KAAA,YAAiBK,KAAA;AAErB;AAEO,SAASzE,oBACd0B,eAAqC;EAErC,OAAOA,eAAA,CAAgB+D,MAAM,GAAG;AAClC;AAEO,SAASvF,qBACdwF,aAAmC,EACnCC,aAAmC;EAEnC;EACA;EACA;EACAD,aAAA,CAAchE,eAAe,CAAC8C,IAAI,IAAImB,aAAA,CAAcjE,eAAe;EACnE,OAAOgE,aAAA,CAAchE,eAAe;AACtC;AAEO,SAASnB,yBACdmB,eAAqC;EAErC,OAAOA,eAAA,CACJkE,MAAM,CACJC,MAAA,IACC,OAAOA,MAAA,CAAOrC,KAAK,KAAK,YAAYqC,MAAA,CAAOrC,KAAK,CAACiC,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC;IAAEzD,UAAU;IAAEmB;EAAK,CAAE;IACzBA,KAAA,GAAQA,KAAA,CACLuC,KAAK,CAAC,KACP;IACA;IACA;IAAA,CACCC,KAAK,CAAC,GACNJ,MAAM,CAAEK,IAAA;MACP;MACA,IAAIA,IAAA,CAAKX,QAAQ,CAAC,uBAAuB;QACvC,OAAO;MACT;MAEA;MACA,IAAIW,IAAA,CAAKX,QAAQ,CAAC,mBAAmB;QACnC,OAAO;MACT;MAEA;MACA,IAAIW,IAAA,CAAKX,QAAQ,CAAC,YAAY;QAC5B,OAAO;MACT;MAEA,OAAO;IACT,GACCY,IAAI,CAAC;IACR,OAAO,6BAA6B7D,UAAA,MAAgBmB,KAAA,EAAO;EAC7D;AACJ;AAEA,SAAS0B,eAAA;EACP,IAAI,CAAC7D,WAAA,EAAa;IAChB,MAAMuB,MAAA,CAAAC,cAEL,CAFK,IAAI4B,KAAA,CACR,kIAAkI,GAD9H;aAAA;kBAAA;oBAAA;IAEN;EACF;AACF;AAMO,SAASnE,2BAA2B6D,MAAc;EACvDe,cAAA;EACA,MAAMZ,UAAA,GAAa,IAAI6B,eAAA;EACvB;EACA,IAAI;IACF7E,MAAA,CAAAC,OAAK,CAACC,iBAAiB,CAAC2C,MAAA;EAC1B,EAAE,OAAOiC,CAAA,EAAY;IACnB9B,UAAA,CAAWC,KAAK,CAAC6B,CAAA;EACnB;EACA,OAAO9B,UAAA,CAAWQ,MAAM;AAC1B;AAOO,SAASzE,8BACdkC,aAAmC;EAEnC,MAAM+B,UAAA,GAAa,IAAI6B,eAAA;EAEvB,IAAI5D,aAAA,CAAc8D,WAAW,EAAE;IAC7B;IACA;IACA;IACA9D,aAAA,CAAc8D,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;MAC1CjC,UAAA,CAAWC,KAAK;IAClB;EACF,OAAO;IACL;IACA;IACA;IACA;IACA;IACA,IAAAiC,UAAA,CAAAC,kBAAkB,EAAC,MAAMnC,UAAA,CAAWC,KAAK;EAC3C;EAEA,OAAOD,UAAA,CAAWQ,MAAM;AAC1B;AAEO,SAAS7E,sBACdoC,UAAkB,EAClBwB,cAAoC;EAEpC,MAAMZ,eAAA,GAAkBY,cAAA,CAAeZ,eAAe;EACtD,IAAIA,eAAA,EAAiB;IACnBA,eAAA,CAAgBvB,eAAe,CAAC8C,IAAI,CAAC;MACnChB,KAAA,EAAOP,eAAA,CAAgBxB,sBAAsB,GACzC,IAAIgD,KAAA,GAAQjB,KAAK,GACjB5B,SAAA;MACJS;IACF;EACF;AACF;AAEO,SAASjB,sBAAsBiB,UAAkB;EACtD,MAAMqE,SAAA,GAAYC,yBAAA,CAAAC,gBAAgB,CAAC5C,QAAQ;EAE3C,IACE0C,SAAA,IACAA,SAAA,CAAUG,kBAAkB,IAC5BH,SAAA,CAAUI,mBAAmB,IAC7BJ,SAAA,CAAUI,mBAAmB,CAACC,IAAI,GAAG,GACrC;IACA;IACA;IACA,MAAMxE,aAAA,GAAgBuB,6BAAA,CAAAC,oBAAoB,CAACC,QAAQ;IACnD,IAAIzB,aAAA,EAAe;MACjB;MACA,IAAIA,aAAA,CAAcC,IAAI,KAAK,aAAa;QACtC;QACA;QACA;QACAlB,MAAA,CAAAC,OAAK,CAACyF,GAAG,CAAC,IAAAC,sBAAA,CAAAC,kBAAkB,EAAC3E,aAAA,CAAc4E,YAAY,EAAE9E,UAAA;MAC3D,OAAO,IAAIE,aAAA,CAAcC,IAAI,KAAK,iBAAiB;QACjD;QACA5B,oBAAA,CACE8F,SAAA,CAAU1D,KAAK,EACfX,UAAA,EACAE,aAAA,CAAcU,eAAe;MAEjC,OAAO,IAAIV,aAAA,CAAcC,IAAI,KAAK,oBAAoB;QACpD1B,gCAAA,CAAiCuB,UAAA,EAAYqE,SAAA,EAAWnE,aAAA;MAC1D;IACF;EACF;AACF;AAEA,MAAM6E,gBAAA,GAAmB;AACzB,MAAMC,gBAAA,GAAmB,IAAIC,MAAA,CAC3B,aAAaC,kBAAA,CAAAC,sBAAsB,UAAU;AAE/C,MAAMC,gBAAA,GAAmB,IAAIH,MAAA,CAC3B,aAAaC,kBAAA,CAAAG,sBAAsB,UAAU;AAE/C,MAAMC,cAAA,GAAiB,IAAIL,MAAA,CAAO,aAAaC,kBAAA,CAAAK,oBAAoB,UAAU;AAEtE,SAAS7G,0BACdiC,KAAa,EACb6E,cAAsB,EACtBC,iBAAyC,EACzCpC,aAAmC,EACnCC,aAAmC;EAEnC,IAAIgC,cAAA,CAAeI,IAAI,CAACF,cAAA,GAAiB;IACvC;IACA;EACF,OAAO,IAAIR,gBAAA,CAAiBU,IAAI,CAACF,cAAA,GAAiB;IAChDC,iBAAA,CAAkB/F,kBAAkB,GAAG;IACvC;EACF,OAAO,IAAI0F,gBAAA,CAAiBM,IAAI,CAACF,cAAA,GAAiB;IAChDC,iBAAA,CAAkB9F,kBAAkB,GAAG;IACvC;EACF,OAAO,IAAIoF,gBAAA,CAAiBW,IAAI,CAACF,cAAA,GAAiB;IAChDC,iBAAA,CAAkBhG,mBAAmB,GAAG;IACxC;EACF,OAAO,IACL4D,aAAA,CAAc7D,yBAAyB,IACvC8D,aAAA,CAAc9D,yBAAyB,EACvC;IACAiG,iBAAA,CAAkB7F,oBAAoB,GAAG;IACzC;EACF,OAAO;IACL,MAAMmD,OAAA,GAAU,UAAUpC,KAAA,iVAAsV;IAChX,MAAMoB,KAAA,GAAQ4D,6BAAA,CAA8B5C,OAAA,EAASyC,cAAA;IACrDC,iBAAA,CAAkB5F,aAAa,CAACsC,IAAI,CAACJ,KAAA;IACrC;EACF;AACF;AAEA,SAAS4D,8BACP5C,OAAe,EACfyC,cAAsB;EAEtB,MAAMzD,KAAA,GAAQxB,MAAA,CAAAC,cAAkB,CAAlB,IAAI4B,KAAA,CAAMW,OAAA,GAAV;WAAA;gBAAA;kBAAA;EAAiB;EAC/BhB,KAAA,CAAMZ,KAAK,GAAG,YAAY4B,OAAA,GAAUyC,cAAA;EACpC,OAAOzD,KAAA;AACT;AAEO,SAASvD,yBACdmC,KAAa,EACb8E,iBAAyC,EACzCpC,aAAmC,EACnCC,aAAmC;EAEnC,IAAIsC,SAAA;EACJ,IAAIC,cAAA;EACJ,IAAIC,UAAA;EACJ,IAAIzC,aAAA,CAAc7D,yBAAyB,EAAE;IAC3CoG,SAAA,GAAYvC,aAAA,CAAc7D,yBAAyB;IACnDqG,cAAA,GAAiBxC,aAAA,CAAc/D,qBAAqB;IACpDwG,UAAA,GAAazC,aAAA,CAAcT,iBAAiB,KAAK;EACnD,OAAO,IAAIU,aAAA,CAAc9D,yBAAyB,EAAE;IAClDoG,SAAA,GAAYtC,aAAA,CAAc9D,yBAAyB;IACnDqG,cAAA,GAAiBvC,aAAA,CAAchE,qBAAqB;IACpDwG,UAAA,GAAaxC,aAAA,CAAcV,iBAAiB,KAAK;EACnD,OAAO;IACLgD,SAAA,GAAY;IACZC,cAAA,GAAiBtG,SAAA;IACjBuG,UAAA,GAAa;EACf;EAEA,IAAIL,iBAAA,CAAkB7F,oBAAoB,IAAIgG,SAAA,EAAW;IACvD,IAAI,CAACE,UAAA,EAAY;MACf;MACA;MACAC,OAAA,CAAQhE,KAAK,CAAC6D,SAAA;IAChB;IACA;IACA,MAAM,IAAInF,wBAAA,CAAAC,qBAAqB;EACjC;EAEA,MAAMb,aAAA,GAAgB4F,iBAAA,CAAkB5F,aAAa;EACrD,IAAIA,aAAA,CAAcuD,MAAM,EAAE;IACxB,KAAK,IAAI4C,CAAA,GAAI,GAAGA,CAAA,GAAInG,aAAA,CAAcuD,MAAM,EAAE4C,CAAA,IAAK;MAC7CD,OAAA,CAAQhE,KAAK,CAAClC,aAAa,CAACmG,CAAA,CAAE;IAChC;IAEA,MAAM,IAAIvF,wBAAA,CAAAC,qBAAqB;EACjC;EAEA,IAAI,CAAC+E,iBAAA,CAAkBhG,mBAAmB,EAAE;IAC1C,IAAIgG,iBAAA,CAAkB/F,kBAAkB,EAAE;MACxC,IAAIkG,SAAA,EAAW;QACbG,OAAA,CAAQhE,KAAK,CAAC6D,SAAA;QACd,MAAMrF,MAAA,CAAAC,cAEL,CAFK,IAAIC,wBAAA,CAAAC,qBAAqB,CAC7B,UAAUC,KAAA,uEAA4EkF,cAAA,iFAA+F,GADjL;iBAAA;sBAAA;wBAAA;QAEN;MACF;MACA,MAAMtF,MAAA,CAAAC,cAEL,CAFK,IAAIC,wBAAA,CAAAC,qBAAqB,CAC7B,UAAUC,KAAA,gdAAqd,GAD3d;eAAA;oBAAA;sBAAA;MAEN;IACF,OAAO,IAAI8E,iBAAA,CAAkB9F,kBAAkB,EAAE;MAC/C,IAAIiG,SAAA,EAAW;QACbG,OAAA,CAAQhE,KAAK,CAAC6D,SAAA;QACd,MAAMrF,MAAA,CAAAC,cAEL,CAFK,IAAIC,wBAAA,CAAAC,qBAAqB,CAC7B,UAAUC,KAAA,uEAA4EkF,cAAA,iFAA+F,GADjL;iBAAA;sBAAA;wBAAA;QAEN;MACF;MACA,MAAMtF,MAAA,CAAAC,cAEL,CAFK,IAAIC,wBAAA,CAAAC,qBAAqB,CAC7B,UAAUC,KAAA,gdAAqd,GAD3d;eAAA;oBAAA;sBAAA;MAEN;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}