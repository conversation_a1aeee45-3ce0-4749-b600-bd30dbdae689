{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  login: data => {\n    return api.post(ApiConstants.LOGIN_OWNER, data);\n  },\n  register_owner: data => {\n    return api.post(ApiConstants.REGISTER_OWNER, data);\n  },\n  update_profile: data => {\n    return api.post(ApiConstants.UPDATE_PROFILE, data);\n  },\n  change_password: data => {\n    return api.post(ApiConstants.CHANGE_PASSWORD, data);\n  },\n  verify_email: data => {\n    return api.post(ApiConstants.VERIFY_EMAIL, data);\n  },\n  resend_verification: data => {\n    return api.post(ApiConstants.RESEND_VERIFICATION, data);\n  },\n  update_avatar: formData => {\n    return api.put(ApiConstants.UPDATE_AVATAR, formData);\n  },\n  forgetPassword: data => {\n    return api.post(ApiConstants.FORGOT_PASSWORD, data);\n  },\n  reset_password: data => {\n    return api.post(ApiConstants.RESET_PASSWORD, data);\n  },\n  verify_forgot_password: data => {\n    return api.post(ApiConstants.VERIFY_FORGOT_PASSWORD, data);\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "login", "data", "post", "LOGIN_OWNER", "register_owner", "REGISTER_OWNER", "update_profile", "UPDATE_PROFILE", "change_password", "CHANGE_PASSWORD", "verify_email", "VERIFY_EMAIL", "resend_verification", "RESEND_VERIFICATION", "update_avatar", "formData", "put", "UPDATE_AVATAR", "forgetPassword", "FORGOT_PASSWORD", "reset_password", "RESET_PASSWORD", "verify_forgot_password", "VERIFY_FORGOT_PASSWORD"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/auth/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  login: (data) => {\r\n    return api.post(ApiConstants.LOGIN_OWNER, data);\r\n  },\r\n  register_owner: (data) => {\r\n    return api.post(ApiConstants.REGISTER_OWNER, data);\r\n  },\r\n  update_profile: (data) => {\r\n    return api.post(ApiConstants.UPDATE_PROFILE, data);\r\n  },\r\n  change_password: (data) => {\r\n    return api.post(ApiConstants.CHANGE_PASSWORD, data);\r\n  },\r\n  verify_email: (data) => {\r\n    return api.post(ApiConstants.VERIFY_EMAIL, data);\r\n  },\r\n  resend_verification: (data) => {\r\n    return api.post(ApiConstants.RESEND_VERIFICATION, data);\r\n  },\r\n  update_avatar: (formData) => {\r\n    return api.put(ApiConstants.UPDATE_AVATAR, formData);\r\n  },\r\n  forgetPassword: (data) => {\r\n    return api.post(ApiConstants.FORGOT_PASSWORD, data);\r\n  },\r\n\r\n  reset_password: (data) => {\r\n    return api.post(ApiConstants.RESET_PASSWORD, data);\r\n  },\r\n  verify_forgot_password: (data) => {\r\n    return api.post(ApiConstants.VERIFY_FORGOT_PASSWORD, data);\r\n  },\r\n};\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAGC,IAAI,IAAK;IACf,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACM,WAAW,EAAEF,IAAI,CAAC;EACjD,CAAC;EACDG,cAAc,EAAGH,IAAI,IAAK;IACxB,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACQ,cAAc,EAAEJ,IAAI,CAAC;EACpD,CAAC;EACDK,cAAc,EAAGL,IAAI,IAAK;IACxB,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACU,cAAc,EAAEN,IAAI,CAAC;EACpD,CAAC;EACDO,eAAe,EAAGP,IAAI,IAAK;IACzB,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACY,eAAe,EAAER,IAAI,CAAC;EACrD,CAAC;EACDS,YAAY,EAAGT,IAAI,IAAK;IACtB,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACc,YAAY,EAAEV,IAAI,CAAC;EAClD,CAAC;EACDW,mBAAmB,EAAGX,IAAI,IAAK;IAC7B,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACgB,mBAAmB,EAAEZ,IAAI,CAAC;EACzD,CAAC;EACDa,aAAa,EAAGC,QAAQ,IAAK;IAC3B,OAAOjB,GAAG,CAACkB,GAAG,CAACnB,YAAY,CAACoB,aAAa,EAAEF,QAAQ,CAAC;EACtD,CAAC;EACDG,cAAc,EAAGjB,IAAI,IAAK;IACxB,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACsB,eAAe,EAAElB,IAAI,CAAC;EACrD,CAAC;EAEDmB,cAAc,EAAGnB,IAAI,IAAK;IACxB,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAACwB,cAAc,EAAEpB,IAAI,CAAC;EACpD,CAAC;EACDqB,sBAAsB,EAAGrB,IAAI,IAAK;IAChC,OAAOH,GAAG,CAACI,IAAI,CAACL,YAAY,CAAC0B,sBAAsB,EAAEtB,IAAI,CAAC;EAC5D;AACF,CAAC;AACD,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}