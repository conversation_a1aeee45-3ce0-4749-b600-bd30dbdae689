{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Admin\\\\src\\\\pages\\\\dashboard\\\\DashboardPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardPage = ({\n  setActiveTab\n}) => {\n  _s();\n  var _dashboardData$revenu, _dashboardData$revenu2, _dashboardData$hotelD, _dashboardData$hotelD2, _dashboardData$hotelC, _dashboardData$hotelC2;\n  const dispatch = useDispatch();\n  const {\n    data: dashboardData,\n    loading,\n    error\n  } = useSelector(state => state.AdminDashboard);\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n\n  // Fetch dashboard data on component mount and when period changes\n  useEffect(() => {\n    dispatch({\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\n      payload: {\n        params: {\n          period: selectedPeriod\n        },\n        onSuccess: data => {\n          console.log('Dashboard data loaded successfully:', data);\n        },\n        onFailed: error => {\n          console.error('Failed to load dashboard data:', error);\n        }\n      }\n    });\n  }, [dispatch, selectedPeriod]);\n\n  // Handle period change\n  const handlePeriodChange = period => {\n    setSelectedPeriod(period);\n  };\n\n  // Chart empty state component\n  const ChartEmptyState = ({\n    icon,\n    message\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex align-items-center justify-content-center h-100 text-muted\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `bi ${icon} fs-1 d-block mb-2`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n\n  // Format revenue for display\n  const formatRevenue = revenue => {\n    if (revenue >= 1000000) {\n      return (revenue / 1000000).toFixed(1) + 'M';\n    } else if (revenue >= 1000) {\n      return (revenue / 1000).toFixed(1) + 'K';\n    }\n    return (revenue === null || revenue === void 0 ? void 0 : revenue.toLocaleString()) || '0';\n  };\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          height: '400px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"alert-heading\",\n          children: \"L\\u1ED7i!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-danger\",\n          onClick: () => handlePeriodChange(selectedPeriod),\n          children: \"Th\\u1EED l\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Lấy màu cho trạng thái\n  const getStatusColor = status => {\n    switch (status) {\n      case \"Đã thanh toán\":\n      case \"Hoạt động\":\n        return \"success\";\n      case \"Đang xử lý\":\n      case \"Đang xem xét\":\n      case \"Đang chờ\":\n        return \"warning\";\n      case \"Tạm khóa\":\n      case \"Chưa xử lý\":\n        return \"danger\";\n      default:\n        return \"secondary\";\n    }\n  };\n\n  // Lấy màu cho mức độ nghiêm trọng\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case \"Cao\":\n        return \"danger\";\n      case \"Trung bình\":\n        return \"warning\";\n      case \"Thấp\":\n        return \"info\";\n      default:\n        return \"secondary\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"T\\u1ED5ng quan h\\u1EC7 th\\u1ED1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-filter\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select\",\n            value: selectedPeriod,\n            onChange: e => handlePeriodChange(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"day\",\n              children: \"H\\xF4m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"week\",\n              children: \"Tu\\u1EA7n n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"month\",\n              children: \"Th\\xE1ng n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"year\",\n              children: \"N\\u0103m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-secondary me-2\",\n          onClick: () => handlePeriodChange(selectedPeriod),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-arrow-clockwise\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), \" L\\xE0m m\\u1EDBi\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), \" Xu\\u1EA5t b\\xE1o c\\xE1o\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon hotels\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-building\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.activeHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xE1ch s\\u1EA1n ho\\u1EA1t \\u0111\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon active\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-check-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.pendingApprovals || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon pending\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-hourglass-split\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalCustomers || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon customers\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-people\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Doanh thu h\\u1EC7 th\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"btn-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Ng\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Tu\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-primary\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"N\\u0103m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-body\",\n        children: ((_dashboardData$revenu = dashboardData.revenueData) === null || _dashboardData$revenu === void 0 ? void 0 : (_dashboardData$revenu2 = _dashboardData$revenu.labels) === null || _dashboardData$revenu2 === void 0 ? void 0 : _dashboardData$revenu2.length) > 0 ? /*#__PURE__*/_jsxDEV(Line, {\n          data: dashboardData.revenueData,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                position: \"top\"\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: false,\n                grid: {\n                  drawBorder: false\n                },\n                ticks: {\n                  callback: value => formatRevenue(value)\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n          icon: \"bi-graph-up\",\n          message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u doanh thu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n b\\u1ED1 kh\\xE1ch s\\u1EA1n theo khu v\\u1EF1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelD = dashboardData.hotelDistributionData) === null || _dashboardData$hotelD === void 0 ? void 0 : (_dashboardData$hotelD2 = _dashboardData$hotelD.labels) === null || _dashboardData$hotelD2 === void 0 ? void 0 : _dashboardData$hotelD2.length) > 0 ? /*#__PURE__*/_jsxDEV(Doughnut, {\n            data: dashboardData.hotelDistributionData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\"\n                }\n              },\n              cutout: \"70%\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n b\\u1ED1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelC = dashboardData.hotelCategoryData) === null || _dashboardData$hotelC === void 0 ? void 0 : (_dashboardData$hotelC2 = _dashboardData$hotelC.labels) === null || _dashboardData$hotelC2 === void 0 ? void 0 : _dashboardData$hotelC2.length) > 0 ? /*#__PURE__*/_jsxDEV(Pie, {\n            data: dashboardData.hotelCategoryData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\"\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart-fill\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n lo\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detailed-analysis\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-geo-alt me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch chi ti\\u1EBFt theo khu v\\u1EF1c\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Khu v\\u1EF1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.locationBreakdown || []).length > 0 ? (dashboardData.locationBreakdown || []).map((location, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: location.region\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: location.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: location.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: location.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${location.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 328,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 327,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [location.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 333,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: location.activePercentage >= 80 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 29\n                    }, this) : location.activePercentage >= 60 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-geo fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch khu v\\u1EF1c\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-star me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch theo ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ph\\xE2n lo\\u1EA1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\xE1nh gi\\xE1 TB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EA5t l\\u01B0\\u1EE3ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.categoryBreakdown || []).length > 0 ? (dashboardData.categoryBreakdown || []).map((category, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: category.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: category.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 391,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: category.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: category.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: `bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`,\n                        style: {\n                          fontSize: '12px'\n                        }\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 402,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"ms-1\",\n                        children: [\"(\", category.avgRating, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${category.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 414,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 413,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [category.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 419,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: category.avgRating >= 4.5 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"Xu\\u1EA5t s\\u1EAFc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 4.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-info\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 3.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"7\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-star fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch ph\\xE2n lo\\u1EA1i\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-clock-history me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this), \"Kh\\xE1ch s\\u1EA1n ch\\u1EDD ph\\xEA duy\\u1EC7t\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge bg-warning fs-6\",\n              children: [(dashboardData.pendingHotels || []).length, \" kh\\xE1ch s\\u1EA1n\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\xEAn kh\\xE1ch s\\u1EA1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EE7 s\\u1EDF h\\u1EEFu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\u1ECBa \\u0111i\\u1EC3m\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\xE1nh gi\\xE1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ng\\xE0y g\\u1EEDi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"H\\xECnh \\u1EA3nh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Thao t\\xE1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.pendingHotels || []).length > 0 ? (dashboardData.pendingHotels || []).map(hotel => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: hotel.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: hotel.owner\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: hotel.ownerEmail\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: hotel.location\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: `bi ${i < Math.floor(hotel.rating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`,\n                        style: {\n                          fontSize: '12px'\n                        }\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 493,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"ms-1\",\n                        children: [\"(\", hotel.rating || 0, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 499,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: hotel.requestDate\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: hotel.hasImages ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"C\\xF3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"Ch\\u01B0a c\\xF3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-primary me-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 513,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-success me-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-check-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 516,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 515,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-danger\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-x-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 519,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 518,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this)]\n                }, hotel.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"7\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-inbox fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 25\n                    }, this), \"Kh\\xF4ng c\\xF3 b\\xE1o c\\xE1o vi ph\\u1EA1m n\\xE0o\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"pBlSUJ1rKJpORKZOoh/qh4nmKVQ=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "Line", "Bar", "Pie", "Doughnut", "useDispatch", "useSelector", "AdminDashboardActions", "jsxDEV", "_jsxDEV", "DashboardPage", "setActiveTab", "_s", "_dashboardData$revenu", "_dashboardData$revenu2", "_dashboardData$hotelD", "_dashboardData$hotelD2", "_dashboardData$hotelC", "_dashboardData$hotelC2", "dispatch", "data", "dashboardData", "loading", "error", "state", "AdminDashboard", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "type", "FETCH_ADMIN_DASHBOARD_METRICS", "payload", "params", "period", "onSuccess", "console", "log", "onFailed", "handlePeriodChange", "ChartEmptyState", "icon", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatRevenue", "revenue", "toFixed", "toLocaleString", "style", "height", "role", "onClick", "getStatusColor", "status", "getSeverityColor", "severity", "value", "onChange", "e", "target", "disabled", "totalHotels", "activeHotels", "pendingApprovals", "totalCustomers", "revenueData", "labels", "length", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "scales", "y", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "x", "display", "hotelDistributionData", "cutout", "hotelCategoryData", "locationBreakdown", "map", "location", "index", "region", "total", "active", "pending", "width", "activePercentage", "colSpan", "categoryBreakdown", "category", "Array", "_", "i", "Math", "floor", "avgRating", "fontSize", "pendingHotels", "hotel", "name", "owner", "ownerEmail", "rating", "requestDate", "hasImages", "id", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/pages/dashboard/DashboardPage.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\r\n\r\nconst DashboardPage = ({setActiveTab}) => {\r\n  const dispatch = useDispatch();\r\n  const { data: dashboardData, loading, error } = useSelector(state => state.AdminDashboard);\r\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\r\n\r\n  // Fetch dashboard data on component mount and when period changes\r\n  useEffect(() => {\r\n    dispatch({\r\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\r\n      payload: {\r\n        params: { period: selectedPeriod },\r\n        onSuccess: (data) => {\r\n          console.log('Dashboard data loaded successfully:', data);\r\n        },\r\n        onFailed: (error) => {\r\n          console.error('Failed to load dashboard data:', error);\r\n        }\r\n      }\r\n    });\r\n  }, [dispatch, selectedPeriod]);\r\n\r\n  // Handle period change\r\n  const handlePeriodChange = (period) => {\r\n    setSelectedPeriod(period);\r\n  };\r\n\r\n  // Chart empty state component\r\n  const ChartEmptyState = ({ icon, message }) => (\r\n    <div className=\"d-flex align-items-center justify-content-center h-100 text-muted\">\r\n      <div className=\"text-center\">\r\n        <i className={`bi ${icon} fs-1 d-block mb-2`}></i>\r\n        <p>{message}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Format revenue for display\r\n  const formatRevenue = (revenue) => {\r\n    if (revenue >= 1000000) {\r\n      return (revenue / 1000000).toFixed(1) + 'M';\r\n    } else if (revenue >= 1000) {\r\n      return (revenue / 1000).toFixed(1) + 'K';\r\n    }\r\n    return revenue?.toLocaleString() || '0';\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n          <div className=\"spinner-border text-primary\" role=\"status\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"alert alert-danger\" role=\"alert\">\r\n          <h4 className=\"alert-heading\">Lỗi!</h4>\r\n          <p>{error}</p>\r\n          <button\r\n            className=\"btn btn-outline-danger\"\r\n            onClick={() => handlePeriodChange(selectedPeriod)}\r\n          >\r\n            Thử lại\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n\r\n  // Lấy màu cho trạng thái\r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case \"Đã thanh toán\":\r\n      case \"Hoạt động\":\r\n        return \"success\";\r\n      case \"Đang xử lý\":\r\n      case \"Đang xem xét\":\r\n      case \"Đang chờ\":\r\n        return \"warning\";\r\n      case \"Tạm khóa\":\r\n      case \"Chưa xử lý\":\r\n        return \"danger\";\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n\r\n  // Lấy màu cho mức độ nghiêm trọng\r\n  const getSeverityColor = (severity) => {\r\n    switch (severity) {\r\n      case \"Cao\":\r\n        return \"danger\";\r\n      case \"Trung bình\":\r\n        return \"warning\";\r\n      case \"Thấp\":\r\n        return \"info\";\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n  return (\r\n    <div className=\"dashboard-content\">\r\n      <div className=\"page-header\">\r\n        <h1>Tổng quan hệ thống</h1>\r\n        <div className=\"page-actions\">\r\n          <div className=\"date-filter\">\r\n            <select\r\n              className=\"form-select\"\r\n              value={selectedPeriod}\r\n              onChange={(e) => handlePeriodChange(e.target.value)}\r\n            >\r\n              <option value=\"day\">Hôm nay</option>\r\n              <option value=\"week\">Tuần này</option>\r\n              <option value=\"month\">Tháng này</option>\r\n              <option value=\"year\">Năm nay</option>\r\n            </select>\r\n          </div>\r\n          <button\r\n            className=\"btn btn-outline-secondary me-2\"\r\n            onClick={() => handlePeriodChange(selectedPeriod)}\r\n            disabled={loading}\r\n          >\r\n            <i className=\"bi bi-arrow-clockwise\"></i> Làm mới\r\n          </button>\r\n          <button className=\"btn btn-primary\">\r\n            <i className=\"bi bi-download\"></i> Xuất báo cáo\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"stats-cards\">\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalHotels || 0}</h3>\r\n            <p>Tổng số khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon hotels\">\r\n            <i className=\"bi bi-building\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.activeHotels || 0}</h3>\r\n            <p>Khách sạn hoạt động</p>\r\n          </div>\r\n          <div className=\"stat-card-icon active\">\r\n            <i className=\"bi bi-check-circle\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.pendingApprovals || 0}</h3>\r\n            <p>Chờ phê duyệt</p>\r\n          </div>\r\n          <div className=\"stat-card-icon pending\">\r\n            <i className=\"bi bi-hourglass-split\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalCustomers || 0}</h3>\r\n            <p>Tổng số khách hàng</p>\r\n          </div>\r\n          <div className=\"stat-card-icon customers\">\r\n            <i className=\"bi bi-people\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Revenue Chart */}\r\n      <div className=\"chart-container\">\r\n        <div className=\"chart-header\">\r\n          <h2>Doanh thu hệ thống</h2>\r\n          <div className=\"chart-actions\">\r\n            <div className=\"btn-group\">\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Ngày</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Tuần</button>\r\n              <button className=\"btn btn-sm btn-primary\">Tháng</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Năm</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-body\">\r\n          {dashboardData.revenueData?.labels?.length > 0 ? (\r\n            <Line\r\n              data={dashboardData.revenueData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"top\",\r\n                  },\r\n                },\r\n                scales: {\r\n                  y: {\r\n                    beginAtZero: false,\r\n                    grid: {\r\n                      drawBorder: false,\r\n                    },\r\n                    ticks: {\r\n                      callback: (value) => formatRevenue(value),\r\n                    },\r\n                  },\r\n                  x: {\r\n                    grid: {\r\n                      display: false,\r\n                    },\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n          ) : (\r\n            <ChartEmptyState icon=\"bi-graph-up\" message=\"Chưa có dữ liệu doanh thu\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Distribution Charts */}\r\n      <div className=\"charts-row\">\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân bố khách sạn theo khu vực</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelDistributionData?.labels?.length > 0 ? (\r\n              <Doughnut\r\n                data={dashboardData.hotelDistributionData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                    },\r\n                  },\r\n                  cutout: \"70%\",\r\n                }}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart\" message=\"Chưa có dữ liệu phân bố\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân loại khách sạn</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelCategoryData?.labels?.length > 0 ? (\r\n              <Pie\r\n                data={dashboardData.hotelCategoryData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                    },\r\n                  },\r\n                }}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart-fill\" message=\"Chưa có dữ liệu phân loại\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detailed Analysis */}\r\n      <div className=\"detailed-analysis\">\r\n        {/* Location Breakdown */}\r\n        <div className=\"analysis-container\">\r\n          <div className=\"analysis-header\">\r\n            <h2>\r\n              <i className=\"bi bi-geo-alt me-2\"></i>\r\n              Phân tích chi tiết theo khu vực\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Khu vực</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Trạng thái</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.locationBreakdown || []).length > 0 ? (\r\n                    (dashboardData.locationBreakdown || []).map((location, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{location.region}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{location.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{location.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{location.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${location.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{location.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {location.activePercentage >= 80 ? (\r\n                            <span className=\"badge bg-success\">Tốt</span>\r\n                          ) : location.activePercentage >= 60 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"6\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-geo fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích khu vực\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category Breakdown */}\r\n        <div className=\"analysis-container\">\r\n          <div className=\"analysis-header\">\r\n            <h2>\r\n              <i className=\"bi bi-star me-2\"></i>\r\n              Phân tích theo phân loại khách sạn\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Phân loại</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Đánh giá TB</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Chất lượng</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.categoryBreakdown || []).length > 0 ? (\r\n                    (dashboardData.categoryBreakdown || []).map((category, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{category.category}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{category.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{category.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{category.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            {[...Array(5)].map((_, i) => (\r\n                              <i\r\n                                key={i}\r\n                                className={`bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`}\r\n                                style={{ fontSize: '12px' }}\r\n                              ></i>\r\n                            ))}\r\n                            <small className=\"ms-1\">({category.avgRating})</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${category.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{category.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {category.avgRating >= 4.5 ? (\r\n                            <span className=\"badge bg-success\">Xuất sắc</span>\r\n                          ) : category.avgRating >= 4.0 ? (\r\n                            <span className=\"badge bg-info\">Tốt</span>\r\n                          ) : category.avgRating >= 3.0 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"7\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-star fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích phân loại\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Pending Hotels Management */}\r\n        <div className=\"analysis-container\">\r\n          <div className=\"analysis-header\">\r\n            <h2>\r\n              <i className=\"bi bi-clock-history me-2\"></i>\r\n              Khách sạn chờ phê duyệt\r\n            </h2>\r\n            <div className=\"header-actions\">\r\n              <span className=\"badge bg-warning fs-6\">\r\n                {(dashboardData.pendingHotels || []).length} khách sạn\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div className=\"analysis-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Tên khách sạn</th>\r\n                    <th>Chủ sở hữu</th>\r\n                    <th>Địa điểm</th>\r\n                    <th>Đánh giá</th>\r\n                    <th>Ngày gửi</th>\r\n                    <th>Hình ảnh</th>\r\n                    <th>Thao tác</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.pendingHotels || []).length > 0 ? (\r\n                    (dashboardData.pendingHotels || []).map((hotel) => (\r\n                      <tr key={hotel.id}>\r\n                        <td>\r\n                          <strong>{hotel.name}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <div>\r\n                            <div>{hotel.owner}</div>\r\n                            <small className=\"text-muted\">{hotel.ownerEmail}</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>{hotel.location}</td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            {[...Array(5)].map((_, i) => (\r\n                              <i\r\n                                key={i}\r\n                                className={`bi ${i < Math.floor(hotel.rating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`}\r\n                                style={{ fontSize: '12px' }}\r\n                              ></i>\r\n                            ))}\r\n                            <small className=\"ms-1\">({hotel.rating || 0})</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>{hotel.requestDate}</td>\r\n                        <td>\r\n                          {hotel.hasImages ? (\r\n                            <span className=\"badge bg-success\">Có</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Chưa có</span>\r\n                          )}\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"action-buttons\">\r\n                            <button className=\"btn btn-sm btn-primary me-1\">\r\n                              <i className=\"bi bi-eye\"></i>\r\n                            </button>\r\n                            <button className=\"btn btn-sm btn-success me-1\">\r\n                              <i className=\"bi bi-check-lg\"></i>\r\n                            </button>\r\n                            <button className=\"btn btn-sm btn-danger\">\r\n                              <i className=\"bi bi-x-lg\"></i>\r\n                            </button>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"7\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-inbox fs-1 d-block mb-2\"></i>\r\n                        Không có báo cáo vi phạm nào\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,aAAa,GAAGA,CAAC;EAACC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACxC,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe,IAAI,EAAEC,aAAa;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGjB,WAAW,CAACkB,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;EAC1F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,OAAO,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACdmB,QAAQ,CAAC;MACPS,IAAI,EAAErB,qBAAqB,CAACsB,6BAA6B;MACzDC,OAAO,EAAE;QACPC,MAAM,EAAE;UAAEC,MAAM,EAAEN;QAAe,CAAC;QAClCO,SAAS,EAAGb,IAAI,IAAK;UACnBc,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEf,IAAI,CAAC;QAC1D,CAAC;QACDgB,QAAQ,EAAGb,KAAK,IAAK;UACnBW,OAAO,CAACX,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,QAAQ,EAAEO,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMW,kBAAkB,GAAIL,MAAM,IAAK;IACrCL,iBAAiB,CAACK,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMM,eAAe,GAAGA,CAAC;IAAEC,IAAI;IAAEC;EAAQ,CAAC,kBACxC/B,OAAA;IAAKgC,SAAS,EAAC,mEAAmE;IAAAC,QAAA,eAChFjC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjC,OAAA;QAAGgC,SAAS,EAAE,MAAMF,IAAI;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDrC,OAAA;QAAAiC,QAAA,EAAIF;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMC,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAIA,OAAO,IAAI,OAAO,EAAE;MACtB,OAAO,CAACA,OAAO,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC7C,CAAC,MAAM,IAAID,OAAO,IAAI,IAAI,EAAE;MAC1B,OAAO,CAACA,OAAO,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC1C;IACA,OAAO,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,cAAc,CAAC,CAAC,KAAI,GAAG;EACzC,CAAC;;EAED;EACA,IAAI5B,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKgC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCjC,OAAA;QAAKgC,SAAS,EAAC,kDAAkD;QAACU,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAV,QAAA,eAC3FjC,OAAA;UAAKgC,SAAS,EAAC,6BAA6B;UAACY,IAAI,EAAC,QAAQ;UAAAX,QAAA,eACxDjC,OAAA;YAAMgC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIvB,KAAK,EAAE;IACT,oBACEd,OAAA;MAAKgC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCjC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAACY,IAAI,EAAC,OAAO;QAAAX,QAAA,gBAC9CjC,OAAA;UAAIgC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCrC,OAAA;UAAAiC,QAAA,EAAInB;QAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdrC,OAAA;UACEgC,SAAS,EAAC,wBAAwB;UAClCa,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAACX,cAAc,CAAE;UAAAgB,QAAA,EACnD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAIA;EACA,MAAMS,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,eAAe;MACpB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,YAAY;MACjB,KAAK,cAAc;MACnB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;MACf,KAAK,YAAY;QACf,OAAO,QAAQ;MACjB;QACE,OAAO,WAAW;IACtB;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,MAAM;MACf;QACE,OAAO,WAAW;IACtB;EACF,CAAC;EACD,oBACEjD,OAAA;IAAKgC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCjC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjC,OAAA;QAAAiC,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BrC,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjC,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BjC,OAAA;YACEgC,SAAS,EAAC,aAAa;YACvBkB,KAAK,EAAEjC,cAAe;YACtBkC,QAAQ,EAAGC,CAAC,IAAKxB,kBAAkB,CAACwB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAjB,QAAA,gBAEpDjC,OAAA;cAAQkD,KAAK,EAAC,KAAK;cAAAjB,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCrC,OAAA;cAAQkD,KAAK,EAAC,MAAM;cAAAjB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCrC,OAAA;cAAQkD,KAAK,EAAC,OAAO;cAAAjB,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCrC,OAAA;cAAQkD,KAAK,EAAC,MAAM;cAAAjB,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrC,OAAA;UACEgC,SAAS,EAAC,gCAAgC;UAC1Ca,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAACX,cAAc,CAAE;UAClDqC,QAAQ,EAAEzC,OAAQ;UAAAoB,QAAA,gBAElBjC,OAAA;YAAGgC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAC3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA;UAAQgC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBACjCjC,OAAA;YAAGgC,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,4BACpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAAC2C,WAAW,IAAI;UAAC;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCrC,OAAA;YAAAiC,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCjC,OAAA;YAAGgC,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAAC4C,YAAY,IAAI;UAAC;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CrC,OAAA;YAAAiC,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCjC,OAAA;YAAGgC,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAAC6C,gBAAgB,IAAI;UAAC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CrC,OAAA;YAAAiC,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCjC,OAAA;YAAGgC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAAC8C,cAAc,IAAI;UAAC;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5CrC,OAAA;YAAAiC,QAAA,EAAG;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCjC,OAAA;YAAGgC,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BjC,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjC,OAAA;UAAAiC,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAQgC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClErC,OAAA;cAAQgC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClErC,OAAA;cAAQgC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzDrC,OAAA;cAAQgC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxB,EAAA7B,qBAAA,GAAAQ,aAAa,CAAC+C,WAAW,cAAAvD,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BwD,MAAM,cAAAvD,sBAAA,uBAAjCA,sBAAA,CAAmCwD,MAAM,IAAG,CAAC,gBAC5C7D,OAAA,CAACR,IAAI;UACHmB,IAAI,EAAEC,aAAa,CAAC+C,WAAY;UAChCG,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACPC,MAAM,EAAE;gBACNC,QAAQ,EAAE;cACZ;YACF,CAAC;YACDC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,KAAK;gBAClBC,IAAI,EAAE;kBACJC,UAAU,EAAE;gBACd,CAAC;gBACDC,KAAK,EAAE;kBACLC,QAAQ,EAAGxB,KAAK,IAAKZ,aAAa,CAACY,KAAK;gBAC1C;cACF,CAAC;cACDyB,CAAC,EAAE;gBACDJ,IAAI,EAAE;kBACJK,OAAO,EAAE;gBACX;cACF;YACF;UACF;QAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFrC,OAAA,CAAC6B,eAAe;UAACC,IAAI,EAAC,aAAa;UAACC,OAAO,EAAC;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC1E;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBjC,OAAA;QAAKgC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCjC,OAAA;UAAKgC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BjC,OAAA;YAAAiC,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAA3B,qBAAA,GAAAM,aAAa,CAACiE,qBAAqB,cAAAvE,qBAAA,wBAAAC,sBAAA,GAAnCD,qBAAA,CAAqCsD,MAAM,cAAArD,sBAAA,uBAA3CA,sBAAA,CAA6CsD,MAAM,IAAG,CAAC,gBACtD7D,OAAA,CAACL,QAAQ;YACPgB,IAAI,EAAEC,aAAa,CAACiE,qBAAsB;YAC1Cf,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE;gBACZ;cACF,CAAC;cACDW,MAAM,EAAE;YACV;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFrC,OAAA,CAAC6B,eAAe;YAACC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC;UAAyB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACzE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCjC,OAAA;UAAKgC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BjC,OAAA;YAAAiC,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAAzB,qBAAA,GAAAI,aAAa,CAACmE,iBAAiB,cAAAvE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCoD,MAAM,cAAAnD,sBAAA,uBAAvCA,sBAAA,CAAyCoD,MAAM,IAAG,CAAC,gBAClD7D,OAAA,CAACN,GAAG;YACFiB,IAAI,EAAEC,aAAa,CAACmE,iBAAkB;YACtCjB,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE;gBACZ;cACF;YACF;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFrC,OAAA,CAAC6B,eAAe;YAACC,IAAI,EAAC,mBAAmB;YAACC,OAAO,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAChF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCjC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCjC,OAAA;UAAKgC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjC,OAAA;YAAAiC,QAAA,gBACEjC,OAAA;cAAGgC,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mDAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjC,OAAA;YAAKgC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BjC,OAAA;cAAOgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCjC,OAAA;gBAAAiC,QAAA,eACEjC,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrC,OAAA;gBAAAiC,QAAA,EACG,CAACrB,aAAa,CAACoE,iBAAiB,IAAI,EAAE,EAAEnB,MAAM,GAAG,CAAC,GACjD,CAACjD,aAAa,CAACoE,iBAAiB,IAAI,EAAE,EAAEC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC1DnF,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAAiC,QAAA,EAASiD,QAAQ,CAACE;oBAAM;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEiD,QAAQ,CAACG;oBAAK;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEiD,QAAQ,CAACI;oBAAM;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEiD,QAAQ,CAACK;oBAAO;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCjC,OAAA;wBAAKgC,SAAS,EAAC,eAAe;wBAACU,KAAK,EAAE;0BAAE8C,KAAK,EAAE,MAAM;0BAAE7C,MAAM,EAAE;wBAAM,CAAE;wBAAAV,QAAA,eACrEjC,OAAA;0BACEgC,SAAS,EAAC,yBAAyB;0BACnCU,KAAK,EAAE;4BAAE8C,KAAK,EAAE,GAAGN,QAAQ,CAACO,gBAAgB;0BAAI;wBAAE;0BAAAvD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNrC,OAAA;wBAAAiC,QAAA,GAAQiD,QAAQ,CAACO,gBAAgB,EAAC,GAAC;sBAAA;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,EACGiD,QAAQ,CAACO,gBAAgB,IAAI,EAAE,gBAC9BzF,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAC3C6C,QAAQ,CAACO,gBAAgB,IAAI,EAAE,gBACjCzF,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpDrC,OAAA;sBAAMgC,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAhCE8C,KAAK;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACL,CAAC,gBAEFrC,OAAA;kBAAAiC,QAAA,eACEjC,OAAA;oBAAI0F,OAAO,EAAC,GAAG;oBAAC1D,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrDjC,OAAA;sBAAGgC,SAAS,EAAC;oBAA6B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,kEAEjD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCjC,OAAA;UAAKgC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjC,OAAA;YAAAiC,QAAA,gBACEjC,OAAA;cAAGgC,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,4DAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjC,OAAA;YAAKgC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BjC,OAAA;cAAOgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCjC,OAAA;gBAAAiC,QAAA,eACEjC,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrC,OAAA;gBAAAiC,QAAA,EACG,CAACrB,aAAa,CAAC+E,iBAAiB,IAAI,EAAE,EAAE9B,MAAM,GAAG,CAAC,GACjD,CAACjD,aAAa,CAAC+E,iBAAiB,IAAI,EAAE,EAAEV,GAAG,CAAC,CAACW,QAAQ,EAAET,KAAK,kBAC1DnF,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAAiC,QAAA,EAAS2D,QAAQ,CAACA;oBAAQ;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAE2D,QAAQ,CAACP;oBAAK;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAE2D,QAAQ,CAACN;oBAAM;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAE2D,QAAQ,CAACL;oBAAO;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACvC,CAAC,GAAG4D,KAAK,CAAC,CAAC,CAAC,CAAC,CAACZ,GAAG,CAAC,CAACa,CAAC,EAAEC,CAAC,kBACtB/F,OAAA;wBAEEgC,SAAS,EAAE,MAAM+D,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACM,SAAS,IAAI,CAAC,CAAC,GAAG,cAAc,GAAG,SAAS,oBAAqB;wBAC1GxD,KAAK,EAAE;0BAAEyD,QAAQ,EAAE;wBAAO;sBAAE,GAFvBJ,CAAC;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGJ,CACL,CAAC,eACFrC,OAAA;wBAAOgC,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,GAAC,EAAC2D,QAAQ,CAACM,SAAS,EAAC,GAAC;sBAAA;wBAAAhE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCjC,OAAA;wBAAKgC,SAAS,EAAC,eAAe;wBAACU,KAAK,EAAE;0BAAE8C,KAAK,EAAE,MAAM;0BAAE7C,MAAM,EAAE;wBAAM,CAAE;wBAAAV,QAAA,eACrEjC,OAAA;0BACEgC,SAAS,EAAC,yBAAyB;0BACnCU,KAAK,EAAE;4BAAE8C,KAAK,EAAE,GAAGI,QAAQ,CAACH,gBAAgB;0BAAI;wBAAE;0BAAAvD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNrC,OAAA;wBAAAiC,QAAA,GAAQ2D,QAAQ,CAACH,gBAAgB,EAAC,GAAC;sBAAA;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,EACG2D,QAAQ,CAACM,SAAS,IAAI,GAAG,gBACxBlG,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAChDuD,QAAQ,CAACM,SAAS,IAAI,GAAG,gBAC3BlG,OAAA;sBAAMgC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GACxCuD,QAAQ,CAACM,SAAS,IAAI,GAAG,gBAC3BlG,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpDrC,OAAA;sBAAMgC,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA9CE8C,KAAK;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+CV,CACL,CAAC,gBAEFrC,OAAA;kBAAAiC,QAAA,eACEjC,OAAA;oBAAI0F,OAAO,EAAC,GAAG;oBAAC1D,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrDjC,OAAA;sBAAGgC,SAAS,EAAC;oBAA8B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uEAElD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCjC,OAAA;UAAKgC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BjC,OAAA;YAAAiC,QAAA,gBACEjC,OAAA;cAAGgC,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,gDAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAKgC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BjC,OAAA;cAAMgC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GACpC,CAACrB,aAAa,CAACwF,aAAa,IAAI,EAAE,EAAEvC,MAAM,EAAC,oBAC9C;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjC,OAAA;YAAKgC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BjC,OAAA;cAAOgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCjC,OAAA;gBAAAiC,QAAA,eACEjC,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrC,OAAA;gBAAAiC,QAAA,EACG,CAACrB,aAAa,CAACwF,aAAa,IAAI,EAAE,EAAEvC,MAAM,GAAG,CAAC,GAC7C,CAACjD,aAAa,CAACwF,aAAa,IAAI,EAAE,EAAEnB,GAAG,CAAEoB,KAAK,iBAC5CrG,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAAiC,QAAA,EAASoE,KAAK,CAACC;oBAAI;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAAiC,QAAA,gBACEjC,OAAA;wBAAAiC,QAAA,EAAMoE,KAAK,CAACE;sBAAK;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxBrC,OAAA;wBAAOgC,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAEoE,KAAK,CAACG;sBAAU;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,EAAKoE,KAAK,CAACnB;kBAAQ;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACvC,CAAC,GAAG4D,KAAK,CAAC,CAAC,CAAC,CAAC,CAACZ,GAAG,CAAC,CAACa,CAAC,EAAEC,CAAC,kBACtB/F,OAAA;wBAEEgC,SAAS,EAAE,MAAM+D,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACI,KAAK,CAACI,MAAM,IAAI,CAAC,CAAC,GAAG,cAAc,GAAG,SAAS,oBAAqB;wBACpG/D,KAAK,EAAE;0BAAEyD,QAAQ,EAAE;wBAAO;sBAAE,GAFvBJ,CAAC;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGJ,CACL,CAAC,eACFrC,OAAA;wBAAOgC,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,GAAC,EAACoE,KAAK,CAACI,MAAM,IAAI,CAAC,EAAC,GAAC;sBAAA;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,EAAKoE,KAAK,CAACK;kBAAW;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5BrC,OAAA;oBAAAiC,QAAA,EACGoE,KAAK,CAACM,SAAS,gBACd3G,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAE5CrC,OAAA;sBAAMgC,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAChD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BjC,OAAA;wBAAQgC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,eAC7CjC,OAAA;0BAAGgC,SAAS,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eACTrC,OAAA;wBAAQgC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,eAC7CjC,OAAA;0BAAGgC,SAAS,EAAC;wBAAgB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACTrC,OAAA;wBAAQgC,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,eACvCjC,OAAA;0BAAGgC,SAAS,EAAC;wBAAY;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GA3CEgE,KAAK,CAACO,EAAE;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4Cb,CACL,CAAC,gBAEFrC,OAAA;kBAAAiC,QAAA,eACEjC,OAAA;oBAAI0F,OAAO,EAAC,GAAG;oBAAC1D,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrDjC,OAAA;sBAAGgC,SAAS,EAAC;oBAA+B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,oDAEnD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CAvhBIF,aAAa;EAAA,QACAL,WAAW,EACoBC,WAAW;AAAA;AAAAgH,EAAA,GAFvD5G,aAAa;AAyhBnB,eAAeA,aAAa;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}