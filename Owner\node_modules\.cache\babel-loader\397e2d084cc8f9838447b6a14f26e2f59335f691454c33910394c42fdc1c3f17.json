{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\login_register\\\\VerifyCodeHotelPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { Container, Form, Button, Card, Spinner } from \"react-bootstrap\";\nimport * as Routers from \"../../../utils/Routes\";\nimport Banner from \"../../../images/banner.jpg\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport AuthActions from \"../../../redux/auth/actions\";\nimport Factories from \"../../../redux/auth/factories\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VerifyCodeHotelPage = () => {\n  _s();\n  var _location$state, _location$state2;\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [verificationCode, setVerificationCode] = useState([\"\", \"\", \"\", \"\", \"\", \"\"]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isResending, setIsResending] = useState(false);\n  const inputRefs = useRef([]);\n  const location = useLocation();\n  const [email, setEmail] = useState(((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.email) || \"\");\n  const status = ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.status) || \"\";\n  useEffect(() => {\n    var _location$state3;\n    inputRefs.current = inputRefs.current.slice(0, 6);\n    if ((_location$state3 = location.state) !== null && _location$state3 !== void 0 && _location$state3.message) {\n      showToast.warning(location.state.message);\n    }\n  }, [location]);\n  const handleChange = (index, value) => {\n    // Only allow numbers\n    if (value && !/^\\d*$/.test(value)) return;\n\n    // Update the code array\n    const newCode = [...verificationCode];\n    newCode[index] = value.slice(0, 1); // Only take the first character\n    setVerificationCode(newCode);\n\n    // Auto-focus next input if current input is filled\n    if (value && index < 5) {\n      var _inputRefs$current;\n      (_inputRefs$current = inputRefs.current[index + 1]) === null || _inputRefs$current === void 0 ? void 0 : _inputRefs$current.focus();\n    }\n  };\n  const handleKeyDown = (index, e) => {\n    // Handle backspace\n    if (e.key === \"Backspace\") {\n      if (!verificationCode[index] && index > 0) {\n        var _inputRefs$current2;\n        // If current input is empty and backspace is pressed, focus previous input\n        (_inputRefs$current2 = inputRefs.current[index - 1]) === null || _inputRefs$current2 === void 0 ? void 0 : _inputRefs$current2.focus();\n      }\n    }\n  };\n  const handlePaste = e => {\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData(\"text\").trim();\n\n    // Check if pasted content is a number and has appropriate length\n    if (/^\\d+$/.test(pastedData)) {\n      const digits = pastedData.split(\"\").slice(0, 6);\n      const newCode = [...verificationCode];\n      digits.forEach((digit, index) => {\n        if (index < 6) newCode[index] = digit;\n      });\n      setVerificationCode(newCode);\n\n      // Focus the next empty input or the last input if all are filled\n      const nextEmptyIndex = newCode.findIndex(val => !val);\n      if (nextEmptyIndex !== -1) {\n        var _inputRefs$current$ne;\n        (_inputRefs$current$ne = inputRefs.current[nextEmptyIndex]) === null || _inputRefs$current$ne === void 0 ? void 0 : _inputRefs$current$ne.focus();\n      } else if (newCode[5]) {\n        var _inputRefs$current$;\n        (_inputRefs$current$ = inputRefs.current[5]) === null || _inputRefs$current$ === void 0 ? void 0 : _inputRefs$current$.focus();\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const code = verificationCode.join(\"\");\n    if (code.length !== 6) {\n      showToast.error(\"Vui lòng nhập đầy đủ 6 chữ số của mã xác thực\");\n      return;\n    }\n    setIsLoading(true);\n    if (status === \"FORGET_PASSWORD\") {\n      try {\n        const response = await Factories.verify_forgot_password({\n          code: code\n        });\n        setIsLoading(false);\n        if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n          navigate(Routers.ResetPasswordHotelPage, {\n            state: {\n              code: code,\n              email: email,\n              verified: true,\n              message: \"Xác thực thành công! Nhập mật khẩu mới.\"\n            }\n          });\n        }\n      } catch (error) {\n        var _error$response, _error$response$data, _error$response2, _error$response2$data;\n        setIsLoading(false);\n        showToast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.MsgNo) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Lỗi xác thực mã. Vui lòng thử lại.\");\n      }\n    } else {\n      // For registration verification\n      dispatch({\n        type: AuthActions.VERIFY_EMAIL,\n        payload: {\n          data: {\n            code\n          },\n          onSuccess: data => {\n            setIsLoading(false);\n            navigate(Routers.LoginHotelPage, {\n              state: {\n                message: \"Tài khoản của bạn đã được xác thực. Bạn có thể đăng nhập ngay bây giờ.\"\n              }\n            });\n          },\n          onFailed: msg => {\n            setIsLoading(false);\n            showToast.error(msg);\n          },\n          onError: error => {\n            setIsLoading(false);\n            showToast.error(\"Lỗi xác thực email\");\n          }\n        }\n      });\n    }\n  };\n  const handleResendCode = () => {\n    if (!email) {\n      showToast.error(\"Email bị thiếu. Vui lòng quay lại trang đăng ký.\");\n      return;\n    }\n    setIsResending(true);\n    dispatch({\n      type: AuthActions.RESEND_VERIFICATION,\n      payload: {\n        data: {\n          email: email\n        },\n        onSuccess: data => {\n          setIsResending(false);\n          showToast.success(\"Mã xác thực mới đã được gửi đến email của bạn\");\n        },\n        onFailed: msg => {\n          setIsResending(false);\n          showToast.error(msg);\n        },\n        onError: error => {\n          setIsResending(false);\n          showToast.error(\"Gửi lại mã xác thực thất bại\");\n        }\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 d-flex align-items-center justify-content-center py-5\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      className: \"position-relative\",\n      children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mx-auto shadow\",\n        style: {\n          maxWidth: \"800px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          className: \"p-4 p-md-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-center mb-2\",\n            children: \"X\\xE1c Th\\u1EF1c M\\xE3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted\",\n              children: \"M\\xE3 \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c g\\u1EEDi \\u0111\\u1EBFn email c\\u1EE7a b\\u1EA1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted\",\n              children: \"Vui l\\xF2ng ki\\u1EC3m tra email \\u0111\\u1EC3 nh\\u1EADn m\\xE3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                style: {\n                  fontWeight: 500\n                },\n                children: \"M\\xE3 X\\xE1c Th\\u1EF1c\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between gap-2 mt-3\",\n                children: verificationCode.map((digit, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"position-relative\",\n                  style: {\n                    flex: \"1\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Form.Control, {\n                    ref: el => inputRefs.current[index] = el,\n                    type: \"text\",\n                    value: digit,\n                    onChange: e => handleChange(index, e.target.value),\n                    onKeyDown: e => handleKeyDown(index, e),\n                    onPaste: index === 0 ? handlePaste : undefined,\n                    className: \"text-center py-2\",\n                    style: {\n                      width: \"100px\",\n                      height: \"48px\",\n                      borderColor: index === 0 && !digit ? \"#0d6efd\" : \"#dee2e6\",\n                      borderWidth: index === 0 && !digit ? \"2px\" : \"1px\"\n                    },\n                    maxLength: 1,\n                    autoFocus: index === 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-muted\",\n                children: \"Kh\\xF4ng nh\\u1EADn \\u0111\\u01B0\\u1EE3c m\\xE3? \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"link\",\n                className: \"text-decoration-none p-0\",\n                style: {\n                  cursor: \"pointer\"\n                },\n                onClick: handleResendCode,\n                disabled: isResending,\n                children: isResending ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this), \"\\u0110ang g\\u1EEDi...\"]\n                }, void 0, true) : \"Gửi lại mã\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              type: \"submit\",\n              className: \"w-100 py-2 mt-2\",\n              disabled: verificationCode.some(digit => !digit) || isLoading,\n              children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                  as: \"span\",\n                  animation: \"border\",\n                  size: \"sm\",\n                  role: \"status\",\n                  \"aria-hidden\": \"true\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), \"\\u0110ang x\\xE1c th\\u1EF1c...\"]\n              }, void 0, true) : status === \"FORGET_PASSWORD\" ? \"Đặt Lại Mật Khẩu\" : \"Xác Thực Mã\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_s(VerifyCodeHotelPage, \"zqJfF+71qvZ3m7S6OYbZ4opWPfY=\", false, function () {\n  return [useNavigate, useDispatch, useLocation];\n});\n_c = VerifyCodeHotelPage;\nexport default VerifyCodeHotelPage;\nvar _c;\n$RefreshReg$(_c, \"VerifyCodeHotelPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Container", "Form", "<PERSON><PERSON>", "Card", "Spinner", "Routers", "Banner", "showToast", "ToastProvider", "useLocation", "useNavigate", "useDispatch", "AuthActions", "Factories", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VerifyCodeHotelPage", "_s", "_location$state", "_location$state2", "navigate", "dispatch", "verificationCode", "setVerificationCode", "isLoading", "setIsLoading", "isResending", "setIsResending", "inputRefs", "location", "email", "setEmail", "state", "status", "_location$state3", "current", "slice", "message", "warning", "handleChange", "index", "value", "test", "newCode", "_inputRefs$current", "focus", "handleKeyDown", "e", "key", "_inputRefs$current2", "handlePaste", "preventDefault", "pastedData", "clipboardData", "getData", "trim", "digits", "split", "for<PERSON>ach", "digit", "nextEmptyIndex", "findIndex", "val", "_inputRefs$current$ne", "_inputRefs$current$", "handleSubmit", "code", "join", "length", "error", "response", "verify_forgot_password", "ResetPasswordHotelPage", "verified", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "data", "MsgNo", "type", "VERIFY_EMAIL", "payload", "onSuccess", "LoginHotelPage", "onFailed", "msg", "onError", "handleResendCode", "RESEND_VERIFICATION", "success", "className", "style", "backgroundImage", "backgroundSize", "backgroundPosition", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "Body", "onSubmit", "Group", "Label", "fontWeight", "map", "flex", "Control", "ref", "el", "onChange", "target", "onKeyDown", "onPaste", "undefined", "width", "height", "borderColor", "borderWidth", "max<PERSON><PERSON><PERSON>", "autoFocus", "variant", "cursor", "onClick", "disabled", "as", "animation", "size", "role", "some", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/login_register/VerifyCodeHotelPage.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { Con<PERSON>er, Form, But<PERSON>, Card, Spinner } from \"react-bootstrap\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport { useLocation, useNavigate } from \"react-router-dom\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport AuthActions from \"../../../redux/auth/actions\";\r\nimport Factories from \"../../../redux/auth/factories\";\r\n\r\nconst VerifyCodeHotelPage = () => {\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const [verificationCode, setVerificationCode] = useState([\r\n    \"\",\r\n    \"\",\r\n    \"\",\r\n    \"\",\r\n    \"\",\r\n    \"\",\r\n  ]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isResending, setIsResending] = useState(false);\r\n  const inputRefs = useRef([]);\r\n  const location = useLocation();\r\n  const [email, setEmail] = useState(location.state?.email || \"\");\r\n  const status = location.state?.status || \"\";\r\n\r\n  useEffect(() => {\r\n    inputRefs.current = inputRefs.current.slice(0, 6);\r\n    if (location.state?.message) {\r\n      showToast.warning(location.state.message);\r\n    }\r\n  }, [location]);\r\n\r\n  const handleChange = (index, value) => {\r\n    // Only allow numbers\r\n    if (value && !/^\\d*$/.test(value)) return;\r\n\r\n    // Update the code array\r\n    const newCode = [...verificationCode];\r\n    newCode[index] = value.slice(0, 1); // Only take the first character\r\n    setVerificationCode(newCode);\r\n\r\n    // Auto-focus next input if current input is filled\r\n    if (value && index < 5) {\r\n      inputRefs.current[index + 1]?.focus();\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (index, e) => {\r\n    // Handle backspace\r\n    if (e.key === \"Backspace\") {\r\n      if (!verificationCode[index] && index > 0) {\r\n        // If current input is empty and backspace is pressed, focus previous input\r\n        inputRefs.current[index - 1]?.focus();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePaste = (e) => {\r\n    e.preventDefault();\r\n    const pastedData = e.clipboardData.getData(\"text\").trim();\r\n\r\n    // Check if pasted content is a number and has appropriate length\r\n    if (/^\\d+$/.test(pastedData)) {\r\n      const digits = pastedData.split(\"\").slice(0, 6);\r\n      const newCode = [...verificationCode];\r\n\r\n      digits.forEach((digit, index) => {\r\n        if (index < 6) newCode[index] = digit;\r\n      });\r\n\r\n      setVerificationCode(newCode);\r\n\r\n      // Focus the next empty input or the last input if all are filled\r\n      const nextEmptyIndex = newCode.findIndex((val) => !val);\r\n      if (nextEmptyIndex !== -1) {\r\n        inputRefs.current[nextEmptyIndex]?.focus();\r\n      } else if (newCode[5]) {\r\n        inputRefs.current[5]?.focus();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    const code = verificationCode.join(\"\");\r\n    if (code.length !== 6) {\r\n      showToast.error(\"Vui lòng nhập đầy đủ 6 chữ số của mã xác thực\");\r\n      return;\r\n    }\r\n    setIsLoading(true);\r\n\r\n    if (status === \"FORGET_PASSWORD\") {\r\n      try {\r\n        const response = await Factories.verify_forgot_password({\r\n          code: code\r\n        });\r\n        setIsLoading(false);\r\n        if (response?.status === 200) {\r\n          navigate(Routers.ResetPasswordHotelPage, {\r\n            state: {\r\n              code: code,\r\n              email: email,\r\n              verified: true,\r\n              message: \"Xác thực thành công! Nhập mật khẩu mới.\"\r\n            }\r\n          });\r\n        }\r\n      } catch (error) {\r\n        setIsLoading(false);\r\n        showToast.error(\r\n          error.response?.data?.MsgNo ||\r\n          error.response?.data?.message ||\r\n          \"Lỗi xác thực mã. Vui lòng thử lại.\"\r\n        );\r\n      }\r\n    } else {\r\n      // For registration verification\r\n      dispatch({\r\n        type: AuthActions.VERIFY_EMAIL,\r\n        payload: {\r\n          data: { code },\r\n          onSuccess: (data) => {\r\n            setIsLoading(false);\r\n            navigate(Routers.LoginHotelPage, { \r\n              state: { message: \"Tài khoản của bạn đã được xác thực. Bạn có thể đăng nhập ngay bây giờ.\" }\r\n            });\r\n          },\r\n          onFailed: (msg) => {\r\n            setIsLoading(false);\r\n            showToast.error(msg);\r\n          },\r\n          onError: (error) => {\r\n            setIsLoading(false);\r\n            showToast.error(\"Lỗi xác thực email\");\r\n          },\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleResendCode = () => {\r\n    if (!email) {\r\n      showToast.error(\"Email bị thiếu. Vui lòng quay lại trang đăng ký.\");\r\n      return;\r\n    }\r\n\r\n    setIsResending(true);\r\n\r\n    dispatch({\r\n      type: AuthActions.RESEND_VERIFICATION,\r\n      payload: {\r\n        data: { email: email },\r\n        onSuccess: (data) => {\r\n          setIsResending(false);\r\n          showToast.success(\"Mã xác thực mới đã được gửi đến email của bạn\");\r\n        },\r\n        onFailed: (msg) => {\r\n          setIsResending(false);\r\n          showToast.error(msg);\r\n        },\r\n        onError: (error) => {\r\n          setIsResending(false);\r\n          showToast.error(\"Gửi lại mã xác thực thất bại\");\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"min-vh-100 d-flex align-items-center justify-content-center py-5\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Container className=\"position-relative\">\r\n        <ToastProvider />\r\n\r\n        <Card className=\"mx-auto shadow\" style={{ maxWidth: \"800px\" }}>\r\n          <Card.Body className=\"p-4 p-md-5\">\r\n            <h2 className=\"text-center mb-2\">Xác Thực Mã</h2>\r\n            <div className=\"text-center mb-4\">\r\n              <span className=\"text-muted\">Mã đã được gửi đến email của bạn</span>\r\n              <br />\r\n              <span className=\"text-muted\">\r\n                Vui lòng kiểm tra email để nhận mã\r\n              </span>\r\n            </div>\r\n\r\n            <Form onSubmit={handleSubmit}>\r\n              <Form.Group className=\"mb-4\">\r\n                <Form.Label style={{ fontWeight: 500 }}>\r\n                  Mã Xác Thực\r\n                </Form.Label>\r\n                <div className=\"d-flex justify-content-between gap-2 mt-3\">\r\n                  {verificationCode.map((digit, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"position-relative\"\r\n                      style={{ flex: \"1\" }}\r\n                    >\r\n                      <Form.Control\r\n                        ref={(el) => (inputRefs.current[index] = el)}\r\n                        type=\"text\"\r\n                        value={digit}\r\n                        onChange={(e) => handleChange(index, e.target.value)}\r\n                        onKeyDown={(e) => handleKeyDown(index, e)}\r\n                        onPaste={index === 0 ? handlePaste : undefined}\r\n                        className=\"text-center py-2\"\r\n                        style={{\r\n                          width: \"100px\",\r\n                          height: \"48px\",\r\n                          borderColor:\r\n                            index === 0 && !digit ? \"#0d6efd\" : \"#dee2e6\",\r\n                          borderWidth: index === 0 && !digit ? \"2px\" : \"1px\",\r\n                        }}\r\n                        maxLength={1}\r\n                        autoFocus={index === 0}\r\n                      />\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </Form.Group>\r\n\r\n              <div className=\"text-center mb-3\">\r\n                <span className=\"text-muted\">Không nhận được mã? </span>\r\n                <Button\r\n                  variant=\"link\"\r\n                  className=\"text-decoration-none p-0\"\r\n                  style={{ cursor: \"pointer\" }}\r\n                  onClick={handleResendCode}\r\n                  disabled={isResending}\r\n                >\r\n                  {isResending ? (\r\n                    <>\r\n                      <Spinner\r\n                        as=\"span\"\r\n                        animation=\"border\"\r\n                        size=\"sm\"\r\n                        role=\"status\"\r\n                        aria-hidden=\"true\"\r\n                        className=\"me-1\"\r\n                      />\r\n                      Đang gửi...\r\n                    </>\r\n                  ) : (\r\n                    \"Gửi lại mã\"\r\n                  )}\r\n                </Button>\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"primary\"\r\n                type=\"submit\"\r\n                className=\"w-100 py-2 mt-2\"\r\n                disabled={verificationCode.some((digit) => !digit) || isLoading}\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <Spinner\r\n                      as=\"span\"\r\n                      animation=\"border\"\r\n                      size=\"sm\"\r\n                      role=\"status\"\r\n                      aria-hidden=\"true\"\r\n                      className=\"me-2\"\r\n                    />\r\n                    Đang xác thực...\r\n                  </>\r\n                ) : status === \"FORGET_PASSWORD\" ? (\r\n                  \"Đặt Lại Mật Khẩu\"\r\n                ) : (\r\n                  \"Xác Thực Mã\"\r\n                )}\r\n              </Button>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </Container>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VerifyCodeHotelPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;AACxE,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,SAAS,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAChC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,CACvD,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,CACH,CAAC;EACF,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMiC,SAAS,GAAGhC,MAAM,CAAC,EAAE,CAAC;EAC5B,MAAMiC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,EAAAuB,eAAA,GAAAW,QAAQ,CAACG,KAAK,cAAAd,eAAA,uBAAdA,eAAA,CAAgBY,KAAK,KAAI,EAAE,CAAC;EAC/D,MAAMG,MAAM,GAAG,EAAAd,gBAAA,GAAAU,QAAQ,CAACG,KAAK,cAAAb,gBAAA,uBAAdA,gBAAA,CAAgBc,MAAM,KAAI,EAAE;EAE3CpC,SAAS,CAAC,MAAM;IAAA,IAAAqC,gBAAA;IACdN,SAAS,CAACO,OAAO,GAAGP,SAAS,CAACO,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjD,KAAAF,gBAAA,GAAIL,QAAQ,CAACG,KAAK,cAAAE,gBAAA,eAAdA,gBAAA,CAAgBG,OAAO,EAAE;MAC3BhC,SAAS,CAACiC,OAAO,CAACT,QAAQ,CAACG,KAAK,CAACK,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;EAEd,MAAMU,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrC;IACA,IAAIA,KAAK,IAAI,CAAC,OAAO,CAACC,IAAI,CAACD,KAAK,CAAC,EAAE;;IAEnC;IACA,MAAME,OAAO,GAAG,CAAC,GAAGrB,gBAAgB,CAAC;IACrCqB,OAAO,CAACH,KAAK,CAAC,GAAGC,KAAK,CAACL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpCb,mBAAmB,CAACoB,OAAO,CAAC;;IAE5B;IACA,IAAIF,KAAK,IAAID,KAAK,GAAG,CAAC,EAAE;MAAA,IAAAI,kBAAA;MACtB,CAAAA,kBAAA,GAAAhB,SAAS,CAACO,OAAO,CAACK,KAAK,GAAG,CAAC,CAAC,cAAAI,kBAAA,uBAA5BA,kBAAA,CAA8BC,KAAK,CAAC,CAAC;IACvC;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACN,KAAK,EAAEO,CAAC,KAAK;IAClC;IACA,IAAIA,CAAC,CAACC,GAAG,KAAK,WAAW,EAAE;MACzB,IAAI,CAAC1B,gBAAgB,CAACkB,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAAA,IAAAS,mBAAA;QACzC;QACA,CAAAA,mBAAA,GAAArB,SAAS,CAACO,OAAO,CAACK,KAAK,GAAG,CAAC,CAAC,cAAAS,mBAAA,uBAA5BA,mBAAA,CAA8BJ,KAAK,CAAC,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMK,WAAW,GAAIH,CAAC,IAAK;IACzBA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMC,UAAU,GAAGL,CAAC,CAACM,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC;;IAEzD;IACA,IAAI,OAAO,CAACb,IAAI,CAACU,UAAU,CAAC,EAAE;MAC5B,MAAMI,MAAM,GAAGJ,UAAU,CAACK,KAAK,CAAC,EAAE,CAAC,CAACrB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/C,MAAMO,OAAO,GAAG,CAAC,GAAGrB,gBAAgB,CAAC;MAErCkC,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,EAAEnB,KAAK,KAAK;QAC/B,IAAIA,KAAK,GAAG,CAAC,EAAEG,OAAO,CAACH,KAAK,CAAC,GAAGmB,KAAK;MACvC,CAAC,CAAC;MAEFpC,mBAAmB,CAACoB,OAAO,CAAC;;MAE5B;MACA,MAAMiB,cAAc,GAAGjB,OAAO,CAACkB,SAAS,CAAEC,GAAG,IAAK,CAACA,GAAG,CAAC;MACvD,IAAIF,cAAc,KAAK,CAAC,CAAC,EAAE;QAAA,IAAAG,qBAAA;QACzB,CAAAA,qBAAA,GAAAnC,SAAS,CAACO,OAAO,CAACyB,cAAc,CAAC,cAAAG,qBAAA,uBAAjCA,qBAAA,CAAmClB,KAAK,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAIF,OAAO,CAAC,CAAC,CAAC,EAAE;QAAA,IAAAqB,mBAAA;QACrB,CAAAA,mBAAA,GAAApC,SAAS,CAACO,OAAO,CAAC,CAAC,CAAC,cAAA6B,mBAAA,uBAApBA,mBAAA,CAAsBnB,KAAK,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAOlB,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMe,IAAI,GAAG5C,gBAAgB,CAAC6C,IAAI,CAAC,EAAE,CAAC;IACtC,IAAID,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;MACrB/D,SAAS,CAACgE,KAAK,CAAC,+CAA+C,CAAC;MAChE;IACF;IACA5C,YAAY,CAAC,IAAI,CAAC;IAElB,IAAIQ,MAAM,KAAK,iBAAiB,EAAE;MAChC,IAAI;QACF,MAAMqC,QAAQ,GAAG,MAAM3D,SAAS,CAAC4D,sBAAsB,CAAC;UACtDL,IAAI,EAAEA;QACR,CAAC,CAAC;QACFzC,YAAY,CAAC,KAAK,CAAC;QACnB,IAAI,CAAA6C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErC,MAAM,MAAK,GAAG,EAAE;UAC5Bb,QAAQ,CAACjB,OAAO,CAACqE,sBAAsB,EAAE;YACvCxC,KAAK,EAAE;cACLkC,IAAI,EAAEA,IAAI;cACVpC,KAAK,EAAEA,KAAK;cACZ2C,QAAQ,EAAE,IAAI;cACdpC,OAAO,EAAE;YACX;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;QAAA,IAAAK,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACdpD,YAAY,CAAC,KAAK,CAAC;QACnBpB,SAAS,CAACgE,KAAK,CACb,EAAAK,eAAA,GAAAL,KAAK,CAACC,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,KAAK,OAAAH,gBAAA,GAC3BP,KAAK,CAACC,QAAQ,cAAAM,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBE,IAAI,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBxC,OAAO,KAC7B,oCACF,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACAhB,QAAQ,CAAC;QACP2D,IAAI,EAAEtE,WAAW,CAACuE,YAAY;QAC9BC,OAAO,EAAE;UACPJ,IAAI,EAAE;YAAEZ;UAAK,CAAC;UACdiB,SAAS,EAAGL,IAAI,IAAK;YACnBrD,YAAY,CAAC,KAAK,CAAC;YACnBL,QAAQ,CAACjB,OAAO,CAACiF,cAAc,EAAE;cAC/BpD,KAAK,EAAE;gBAAEK,OAAO,EAAE;cAAyE;YAC7F,CAAC,CAAC;UACJ,CAAC;UACDgD,QAAQ,EAAGC,GAAG,IAAK;YACjB7D,YAAY,CAAC,KAAK,CAAC;YACnBpB,SAAS,CAACgE,KAAK,CAACiB,GAAG,CAAC;UACtB,CAAC;UACDC,OAAO,EAAGlB,KAAK,IAAK;YAClB5C,YAAY,CAAC,KAAK,CAAC;YACnBpB,SAAS,CAACgE,KAAK,CAAC,oBAAoB,CAAC;UACvC;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC1D,KAAK,EAAE;MACVzB,SAAS,CAACgE,KAAK,CAAC,kDAAkD,CAAC;MACnE;IACF;IAEA1C,cAAc,CAAC,IAAI,CAAC;IAEpBN,QAAQ,CAAC;MACP2D,IAAI,EAAEtE,WAAW,CAAC+E,mBAAmB;MACrCP,OAAO,EAAE;QACPJ,IAAI,EAAE;UAAEhD,KAAK,EAAEA;QAAM,CAAC;QACtBqD,SAAS,EAAGL,IAAI,IAAK;UACnBnD,cAAc,CAAC,KAAK,CAAC;UACrBtB,SAAS,CAACqF,OAAO,CAAC,+CAA+C,CAAC;QACpE,CAAC;QACDL,QAAQ,EAAGC,GAAG,IAAK;UACjB3D,cAAc,CAAC,KAAK,CAAC;UACrBtB,SAAS,CAACgE,KAAK,CAACiB,GAAG,CAAC;QACtB,CAAC;QACDC,OAAO,EAAGlB,KAAK,IAAK;UAClB1C,cAAc,CAAC,KAAK,CAAC;UACrBtB,SAAS,CAACgE,KAAK,CAAC,8BAA8B,CAAC;QACjD;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACExD,OAAA;IACE8E,SAAS,EAAC,kEAAkE;IAC5EC,KAAK,EAAE;MACLC,eAAe,EAAE,OAAOzF,MAAM,GAAG;MACjC0F,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAC,QAAA,eAEFnF,OAAA,CAACf,SAAS;MAAC6F,SAAS,EAAC,mBAAmB;MAAAK,QAAA,gBACtCnF,OAAA,CAACP,aAAa;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEjBvF,OAAA,CAACZ,IAAI;QAAC0F,SAAS,EAAC,gBAAgB;QAACC,KAAK,EAAE;UAAES,QAAQ,EAAE;QAAQ,CAAE;QAAAL,QAAA,eAC5DnF,OAAA,CAACZ,IAAI,CAACqG,IAAI;UAACX,SAAS,EAAC,YAAY;UAAAK,QAAA,gBAC/BnF,OAAA;YAAI8E,SAAS,EAAC,kBAAkB;YAAAK,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDvF,OAAA;YAAK8E,SAAS,EAAC,kBAAkB;YAAAK,QAAA,gBAC/BnF,OAAA;cAAM8E,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEvF,OAAA;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNvF,OAAA;cAAM8E,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENvF,OAAA,CAACd,IAAI;YAACwG,QAAQ,EAAEtC,YAAa;YAAA+B,QAAA,gBAC3BnF,OAAA,CAACd,IAAI,CAACyG,KAAK;cAACb,SAAS,EAAC,MAAM;cAAAK,QAAA,gBAC1BnF,OAAA,CAACd,IAAI,CAAC0G,KAAK;gBAACb,KAAK,EAAE;kBAAEc,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAExC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvF,OAAA;gBAAK8E,SAAS,EAAC,2CAA2C;gBAAAK,QAAA,EACvD1E,gBAAgB,CAACqF,GAAG,CAAC,CAAChD,KAAK,EAAEnB,KAAK,kBACjC3B,OAAA;kBAEE8E,SAAS,EAAC,mBAAmB;kBAC7BC,KAAK,EAAE;oBAAEgB,IAAI,EAAE;kBAAI,CAAE;kBAAAZ,QAAA,eAErBnF,OAAA,CAACd,IAAI,CAAC8G,OAAO;oBACXC,GAAG,EAAGC,EAAE,IAAMnF,SAAS,CAACO,OAAO,CAACK,KAAK,CAAC,GAAGuE,EAAI;oBAC7C/B,IAAI,EAAC,MAAM;oBACXvC,KAAK,EAAEkB,KAAM;oBACbqD,QAAQ,EAAGjE,CAAC,IAAKR,YAAY,CAACC,KAAK,EAAEO,CAAC,CAACkE,MAAM,CAACxE,KAAK,CAAE;oBACrDyE,SAAS,EAAGnE,CAAC,IAAKD,aAAa,CAACN,KAAK,EAAEO,CAAC,CAAE;oBAC1CoE,OAAO,EAAE3E,KAAK,KAAK,CAAC,GAAGU,WAAW,GAAGkE,SAAU;oBAC/CzB,SAAS,EAAC,kBAAkB;oBAC5BC,KAAK,EAAE;sBACLyB,KAAK,EAAE,OAAO;sBACdC,MAAM,EAAE,MAAM;sBACdC,WAAW,EACT/E,KAAK,KAAK,CAAC,IAAI,CAACmB,KAAK,GAAG,SAAS,GAAG,SAAS;sBAC/C6D,WAAW,EAAEhF,KAAK,KAAK,CAAC,IAAI,CAACmB,KAAK,GAAG,KAAK,GAAG;oBAC/C,CAAE;oBACF8D,SAAS,EAAE,CAAE;oBACbC,SAAS,EAAElF,KAAK,KAAK;kBAAE;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC,GArBG5D,KAAK;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsBP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAEbvF,OAAA;cAAK8E,SAAS,EAAC,kBAAkB;cAAAK,QAAA,gBAC/BnF,OAAA;gBAAM8E,SAAS,EAAC,YAAY;gBAAAK,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDvF,OAAA,CAACb,MAAM;gBACL2H,OAAO,EAAC,MAAM;gBACdhC,SAAS,EAAC,0BAA0B;gBACpCC,KAAK,EAAE;kBAAEgC,MAAM,EAAE;gBAAU,CAAE;gBAC7BC,OAAO,EAAErC,gBAAiB;gBAC1BsC,QAAQ,EAAEpG,WAAY;gBAAAsE,QAAA,EAErBtE,WAAW,gBACVb,OAAA,CAAAE,SAAA;kBAAAiF,QAAA,gBACEnF,OAAA,CAACX,OAAO;oBACN6H,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClBvC,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,yBAEJ;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvF,OAAA,CAACb,MAAM;cACL2H,OAAO,EAAC,SAAS;cACjB3C,IAAI,EAAC,QAAQ;cACbW,SAAS,EAAC,iBAAiB;cAC3BmC,QAAQ,EAAExG,gBAAgB,CAAC6G,IAAI,CAAExE,KAAK,IAAK,CAACA,KAAK,CAAC,IAAInC,SAAU;cAAAwE,QAAA,EAE/DxE,SAAS,gBACRX,OAAA,CAAAE,SAAA;gBAAAiF,QAAA,gBACEnF,OAAA,CAACX,OAAO;kBACN6H,EAAE,EAAC,MAAM;kBACTC,SAAS,EAAC,QAAQ;kBAClBC,IAAI,EAAC,IAAI;kBACTC,IAAI,EAAC,QAAQ;kBACb,eAAY,MAAM;kBAClBvC,SAAS,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,iCAEJ;cAAA,eAAE,CAAC,GACDnE,MAAM,KAAK,iBAAiB,GAC9B,kBAAkB,GAElB;YACD;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACnF,EAAA,CApRID,mBAAmB;EAAA,QACNR,WAAW,EACXC,WAAW,EAYXF,WAAW;AAAA;AAAA6H,EAAA,GAdxBpH,mBAAmB;AAsRzB,eAAeA,mBAAmB;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}