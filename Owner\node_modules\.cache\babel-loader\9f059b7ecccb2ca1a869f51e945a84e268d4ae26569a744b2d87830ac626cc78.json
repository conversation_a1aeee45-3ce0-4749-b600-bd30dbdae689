{"ast": null, "code": "import { all, call, fork, put, takeEvery } from \"@redux-saga/core/effects\";\nimport RoomActions from \"./actions\";\nimport Factories from \"./factories\";\nexport default function* rootSaga() {\n  function* getRoomsByHotel() {\n    yield takeEvery(RoomActions.FETCH_ROOM, function* (action) {\n      const {\n        hotelId,\n        query,\n        onSuccess,\n        onFailed,\n        onError\n      } = action.payload;\n      try {\n        const response = yield call(Factories.fetch_room, hotelId, query);\n        if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n          yield put({\n            type: RoomActions.FETCH_ROOM_SUCCESS,\n            payload: response.data.rooms\n          });\n          onSuccess && onSuccess(response.data.rooms);\n        } else {\n          throw new Error('Failed to fetch rooms');\n        }\n      } catch (error) {\n        console.error(\"Error fetching rooms: \", error);\n      }\n    });\n  }\n  yield all([fork(getRoomsByHotel)]);\n}", "map": {"version": 3, "names": ["all", "call", "fork", "put", "takeEvery", "RoomActions", "Factories", "rootSaga", "getRoomsByHotel", "FETCH_ROOM", "action", "hotelId", "query", "onSuccess", "onFailed", "onError", "payload", "response", "fetch_room", "status", "type", "FETCH_ROOM_SUCCESS", "data", "rooms", "Error", "error", "console"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/room/saga.js"], "sourcesContent": ["import { all, call, fork, put, takeEvery } from \"@redux-saga/core/effects\";\r\nimport RoomActions from \"./actions\";\r\nimport Factories from \"./factories\";\r\n\r\n\r\n\r\nexport default function* rootSaga() {\r\n  function* getRoomsByHotel() {\r\n  yield takeEvery(RoomActions.FETCH_ROOM, function* (action) {\r\n    const { hotelId, query, onSuccess, onFailed, onError } = action.payload;\r\n\r\n    try {\r\n      const response = yield call(Factories.fetch_room, hotelId, query); \r\n      if (response?.status === 200) {\r\n        yield put({\r\n          type: RoomActions.FETCH_ROOM_SUCCESS,\r\n          payload: response.data.rooms, \r\n        });\r\n\r\n        onSuccess && onSuccess(response.data.rooms); \r\n      } else {\r\n        throw new Error('Failed to fetch rooms');\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching rooms: \", error)\r\n    }\r\n  });\r\n}\r\n  yield all([\r\n    fork(getRoomsByHotel),\r\n  ]);\r\n}\r\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,0BAA0B;AAC1E,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,SAAS,MAAM,aAAa;AAInC,eAAe,UAAUC,QAAQA,CAAA,EAAG;EAClC,UAAUC,eAAeA,CAAA,EAAG;IAC5B,MAAMJ,SAAS,CAACC,WAAW,CAACI,UAAU,EAAE,WAAWC,MAAM,EAAE;MACzD,MAAM;QAAEC,OAAO;QAAEC,KAAK;QAAEC,SAAS;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,GAAGL,MAAM,CAACM,OAAO;MAEvE,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMhB,IAAI,CAACK,SAAS,CAACY,UAAU,EAAEP,OAAO,EAAEC,KAAK,CAAC;QACjE,IAAI,CAAAK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAC5B,MAAMhB,GAAG,CAAC;YACRiB,IAAI,EAAEf,WAAW,CAACgB,kBAAkB;YACpCL,OAAO,EAAEC,QAAQ,CAACK,IAAI,CAACC;UACzB,CAAC,CAAC;UAEFV,SAAS,IAAIA,SAAS,CAACI,QAAQ,CAACK,IAAI,CAACC,KAAK,CAAC;QAC7C,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF,CAAC,CAAC;EACJ;EACE,MAAMzB,GAAG,CAAC,CACRE,IAAI,CAACM,eAAe,CAAC,CACtB,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}