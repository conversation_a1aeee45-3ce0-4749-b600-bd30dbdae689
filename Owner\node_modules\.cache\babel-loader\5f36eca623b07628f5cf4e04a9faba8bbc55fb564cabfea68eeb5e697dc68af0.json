{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  DynamicServerError: null,\n  isDynamicServerError: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  DynamicServerError: function () {\n    return DynamicServerError;\n  },\n  isDynamicServerError: function () {\n    return isDynamicServerError;\n  }\n});\nconst DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE';\nclass DynamicServerError extends Error {\n  constructor(description) {\n    super(\"Dynamic server usage: \" + description), this.description = description, this.digest = DYNAMIC_ERROR_CODE;\n  }\n}\nfunction isDynamicServerError(err) {\n  if (typeof err !== 'object' || err === null || !('digest' in err) || typeof err.digest !== 'string') {\n    return false;\n  }\n  return err.digest === DYNAMIC_ERROR_CODE;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}", "map": {"version": 3, "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\client\\components\\hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "mappings": ";;;;;;;;;;;;;;;;EAEaA,kBAAkB,WAAAA,CAAA;WAAlBA,kBAAA;;EAQGC,oBAAoB,WAAAA,CAAA;WAApBA,oBAAA;;;AAVhB,MAAMC,kBAAA,GAAqB;AAEpB,MAAMF,kBAAA,SAA2BG,KAAA;EAGtCC,YAAYC,WAAmC,EAAE;IAC/C,KAAK,CAAC,wBAAC,GAAwBA,WAAA,QADLA,WAAA,GAAAA,WAAA,OAF5BC,MAAA,GAAoCJ,kBAAA;EAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;EAC/C,IACE,OAAOA,GAAA,KAAQ,YACfA,GAAA,KAAQ,QACR,EAAE,YAAYA,GAAE,KAChB,OAAOA,GAAA,CAAID,MAAM,KAAK,UACtB;IACA,OAAO;EACT;EAEA,OAAOC,GAAA,CAAID,MAAM,KAAKJ,kBAAA;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}