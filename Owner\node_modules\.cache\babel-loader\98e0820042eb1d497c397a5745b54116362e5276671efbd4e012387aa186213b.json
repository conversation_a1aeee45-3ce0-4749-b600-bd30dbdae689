{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  atLeastOneTask: null,\n  scheduleImmediate: null,\n  scheduleOnNextTick: null,\n  waitAtLeastOneReactRenderTask: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  atLeastOneTask: function () {\n    return atLeastOneTask;\n  },\n  scheduleImmediate: function () {\n    return scheduleImmediate;\n  },\n  scheduleOnNextTick: function () {\n    return scheduleOnNextTick;\n  },\n  waitAtLeastOneReactRenderTask: function () {\n    return waitAtLeastOneReactRenderTask;\n  }\n});\nconst scheduleOnNextTick = cb => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0);\n    } else {\n      process.nextTick(cb);\n    }\n  });\n};\nconst scheduleImmediate = cb => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0);\n  } else {\n    setImmediate(cb);\n  }\n};\nfunction atLeastOneTask() {\n  return new Promise(resolve => scheduleImmediate(resolve));\n}\nfunction waitAtLeastOneReactRenderTask() {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise(r => setTimeout(r, 0));\n  } else {\n    return new Promise(r => setImmediate(r));\n  }\n}", "map": {"version": 3, "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\lib\\scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;EA4CgBA,cAAc,WAAAA,CAAA;WAAdA,cAAA;;EAbHC,iBAAiB,WAAAA,CAAA;WAAjBA,iBAAA;;EAtBAC,kBAAkB,WAAAA,CAAA;WAAlBA,kBAAA;;EAgDGC,6BAA6B,WAAAA,CAAA;WAA7BA,6BAAA;;;AAhDT,MAAMD,kBAAA,GAAgCE,EAAA;EAC3C;EACA;EACA;EACA;EACA;EACA;EACAC,OAAA,CAAQC,OAAO,GAAGC,IAAI,CAAC;IACrB,IAAIC,OAAA,CAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;MACvCC,UAAA,CAAWP,EAAA,EAAI;IACjB,OAAO;MACLI,OAAA,CAAQI,QAAQ,CAACR,EAAA;IACnB;EACF;AACF;AAQO,MAAMH,iBAAA,GAA+BG,EAAA;EAC1C,IAAII,OAAA,CAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCC,UAAA,CAAWP,EAAA,EAAI;EACjB,OAAO;IACLS,YAAA,CAAaT,EAAA;EACf;AACF;AAOO,SAASJ,eAAA;EACd,OAAO,IAAIK,OAAA,CAAeC,OAAA,IAAYL,iBAAA,CAAkBK,OAAA;AAC1D;AAWO,SAASH,8BAAA;EACd,IAAIK,OAAA,CAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvC,OAAO,IAAIL,OAAA,CAASS,CAAA,IAAMH,UAAA,CAAWG,CAAA,EAAG;EAC1C,OAAO;IACL,OAAO,IAAIT,OAAA,CAASS,CAAA,IAAMD,YAAA,CAAaC,CAAA;EACzC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}