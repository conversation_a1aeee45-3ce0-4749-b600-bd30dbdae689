{"ast": null, "code": "import { all } from 'redux-saga/effects';\nimport AuthSaga from './auth/saga';\nimport FeedbackSaga from './feedback/saga';\nimport ReportFeedbackSaga from \"./reportedFeedback/saga\";\nimport MessageSaga from './message/saga';\nimport PromotionSaga from './promotion/saga';\nimport AdminDashboardSaga from './adminDashboard/saga';\nexport default function* rootSaga() {\n  yield all([AuthSaga(), FeedbackSaga(), ReportFeedbackSaga(), MessageSaga(), PromotionSaga(), AdminDashboardSaga()]);\n}", "map": {"version": 3, "names": ["all", "Auth<PERSON>aga", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReportFeedbackSaga", "MessageSaga", "PromotionSaga", "AdminDashboardSaga", "rootSaga"], "sources": ["E:/WDP301_UROOM/Admin/src/redux/root-saga.js"], "sourcesContent": ["import 'regenerator-runtime/runtime';\r\nimport {all} from 'redux-saga/effects';\r\nimport AuthSaga from './auth/saga';\r\nimport FeedbackSaga from './feedback/saga';\r\nimport ReportFeedbackSaga from \"./reportedFeedback/saga\";\r\nimport MessageSaga from './message/saga';\r\nimport <PERSON>Saga from './promotion/saga';\r\nimport AdminDashboardSaga from './adminDashboard/saga';\r\n\r\nexport default function* rootSaga() {\r\n  yield all([\r\n    AuthSaga(),\r\n    FeedbackSaga(),\r\n    ReportFeedbackSaga(),\r\n    MessageSaga(),\r\n    PromotionSaga(),\r\n    AdminDashboardSaga(),\r\n  ]);\r\n}\r\n"], "mappings": "AACA,SAAQA,GAAG,QAAO,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,kBAAkB,MAAM,uBAAuB;AAEtD,eAAe,UAAUC,QAAQA,CAAA,EAAG;EAClC,MAAMP,GAAG,CAAC,CACRC,QAAQ,CAAC,CAAC,EACVC,YAAY,CAAC,CAAC,EACdC,kBAAkB,CAAC,CAAC,EACpBC,WAAW,CAAC,CAAC,EACbC,aAAa,CAAC,CAAC,EACfC,kBAAkB,CAAC,CAAC,CACrB,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}