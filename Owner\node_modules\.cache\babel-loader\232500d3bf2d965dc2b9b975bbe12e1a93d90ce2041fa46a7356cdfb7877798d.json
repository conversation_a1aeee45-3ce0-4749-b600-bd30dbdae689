{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\create_hotel\\\\PricingSetupForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { Container, Row, Col, Form, Button, Card, InputGroup, Navbar, ProgressBar } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport RoomActions from \"@redux/room/actions\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport Utils from \"@utils/Utils\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PricingSetupForm() {\n  _s();\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [price, setPrice] = useState(\"0\");\n  const createRoom = useSelector(state => state.Room.createRoom);\n  const handlePriceChange = e => {\n    // Remove non-digits and format\n    const value = e.target.value.replace(/\\D/g, \"\");\n    setPrice(value);\n  };\n  const handleContinue = () => {\n    // Save pricing and complete room creation\n    const finalPrice = Number(price);\n    dispatch({\n      type: RoomActions.SAVE_ROOM_PRICING_CREATE,\n      payload: {\n        price: finalPrice\n      }\n    });\n\n    // Add completed room to create list\n    const completeRoom = {\n      ...createRoom,\n      price: finalPrice\n    };\n    dispatch({\n      type: RoomActions.SAVE_ROOM_TO_CREATE_LIST,\n      payload: completeRoom\n    });\n\n    // Clear current room creation data\n    dispatch({\n      type: RoomActions.CLEAR_ROOM_CREATE\n    });\n    showToast.success(\"Thêm phòng thành công!\");\n    navigate(Routers.BookingPropertyChecklist);\n  };\n  const styles = {\n    bookingApp: {\n      minHeight: \"100vh\",\n      backgroundColor: \"#f8f9fa\"\n    },\n    container: {\n      maxWidth: \"1000px\",\n      margin: \"20px auto\",\n      padding: \"20px\"\n    },\n    title: {\n      fontSize: \"28px\",\n      fontWeight: \"bold\",\n      marginBottom: \"30px\"\n    },\n    card: {\n      border: \"1px solid #e7e7e7\",\n      borderRadius: \"4px\",\n      padding: \"20px\",\n      marginBottom: \"20px\",\n      backgroundColor: \"#fff\"\n    },\n    sliderContainer: {\n      marginTop: \"20px\",\n      marginBottom: \"20px\"\n    },\n    sliderLabel: {\n      textAlign: \"center\",\n      backgroundColor: \"#0071c2\",\n      color: \"white\",\n      padding: \"5px 10px\",\n      borderRadius: \"4px\",\n      position: \"relative\",\n      marginBottom: \"10px\"\n    },\n    sliderLabelArrow: {\n      width: \"0\",\n      height: \"0\",\n      borderLeft: \"8px solid transparent\",\n      borderRight: \"8px solid transparent\",\n      borderTop: \"8px solid #0071c2\",\n      position: \"absolute\",\n      bottom: \"-8px\",\n      left: \"50%\",\n      transform: \"translateX(-50%)\"\n    },\n    slider: {\n      width: \"100%\"\n    },\n    priceRange: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      marginTop: \"10px\"\n    },\n    feedbackButtons: {\n      display: \"flex\",\n      alignItems: \"center\",\n      marginTop: \"15px\"\n    },\n    feedbackButton: {\n      background: \"none\",\n      border: \"none\",\n      fontSize: \"24px\",\n      marginLeft: \"10px\",\n      cursor: \"pointer\"\n    },\n    infoCard: {\n      border: \"1px solid #e7e7e7\",\n      borderRadius: \"4px\",\n      padding: \"20px\",\n      backgroundColor: \"#fff\"\n    },\n    infoHeader: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      marginBottom: \"15px\"\n    },\n    infoTitle: {\n      display: \"flex\",\n      alignItems: \"center\",\n      fontWeight: \"bold\",\n      fontSize: \"16px\"\n    },\n    lightBulbIcon: {\n      marginRight: \"10px\",\n      color: \"#febb02\"\n    },\n    closeButton: {\n      background: \"none\",\n      border: \"none\",\n      fontSize: \"20px\",\n      cursor: \"pointer\"\n    },\n    highlightText: {\n      color: \"#0071c2\",\n      cursor: \"pointer\"\n    },\n    priceInput: {\n      marginTop: \"10px\",\n      marginBottom: \"5px\"\n    },\n    smallText: {\n      fontSize: \"12px\",\n      color: \"#6b6b6b\"\n    },\n    commissionRow: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      marginTop: \"20px\",\n      marginBottom: \"10px\"\n    },\n    benefitItem: {\n      display: \"flex\",\n      alignItems: \"flex-start\",\n      marginBottom: \"10px\"\n    },\n    checkIcon: {\n      color: \"#008009\",\n      marginRight: \"10px\",\n      fontSize: \"18px\"\n    },\n    revenueRow: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      marginTop: \"20px\",\n      paddingTop: \"15px\",\n      borderTop: \"1px solid #e7e7e7\"\n    },\n    buttonContainer: {\n      display: \"flex\",\n      marginTop: \"30px\"\n    },\n    backButton: {\n      width: \"50px\",\n      marginRight: \"10px\",\n      backgroundColor: \"white\",\n      color: \"#0071c2\",\n      border: \"1px solid #0071c2\"\n    },\n    continueButton: {\n      flex: 1,\n      backgroundColor: \"#0071c2\",\n      border: \"none\"\n    },\n    // Navbar styles\n    navbarCustom: {\n      backgroundColor: \"#003580\",\n      padding: \"10px 0\"\n    },\n    navbarBrand: {\n      color: \"#fff\",\n      fontWeight: \"bold\"\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.bookingApp,\n    children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navbar, {\n      style: styles.navbarCustom,\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n          href: \"#home\",\n          className: \"text-white fw-bold\",\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            style: {\n              fontSize: 30\n            },\n            children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#f8e71c\"\n              },\n              children: \"OO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), \"M\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-label mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Th\\xF4ng tin c\\u01A1 b\\u1EA3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n          style: {\n            height: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 1, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 2, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 3, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 4, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      style: styles.container,\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: styles.title,\n        children: \"Thi\\u1EBFt l\\u1EADp gi\\xE1 m\\u1ED7i \\u0111\\xEAm cho ph\\xF2ng n\\xE0y\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 7,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.card,\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Qu\\xFD v\\u1ECB mu\\u1ED1n thu bao nhi\\xEAu ti\\u1EC1n m\\u1ED7i \\u0111\\xEAm?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"S\\u1ED1 ti\\u1EC1n kh\\xE1ch tr\\u1EA3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                style: styles.priceInput,\n                children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                  children: \"$\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  value: Utils.formatCurrency(price),\n                  onChange: handlePriceChange,\n                  placeholder: \"Nh\\u1EADp gi\\xE1 ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                style: styles.smallText,\n                children: \"Bao g\\u1ED3m c\\xE1c lo\\u1EA1i thu\\u1EBF, ph\\xED v\\xE0 hoa h\\u1ED3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.commissionRow,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"12,00% Hoa h\\u1ED3ng cho Booking.com\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.benefitItem,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: styles.checkIcon,\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Tr\\u1EE3 gi\\xFAp 24/7 b\\u1EB1ng ng\\xF4n ng\\u1EEF c\\u1EE7a Qu\\xFD v\\u1ECB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.benefitItem,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: styles.checkIcon,\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Ti\\u1EBFt ki\\u1EC7m th\\u1EDDi gian v\\u1EDBi \\u0111\\u1EB7t ph\\xF2ng \\u0111\\u01B0\\u1EE3c x\\xE1c nh\\u1EADn t\\u1EF1 \\u0111\\u1ED9ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.benefitItem,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: styles.checkIcon,\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Ch\\xFAng t\\xF4i s\\u1EBD qu\\u1EA3ng b\\xE1 ch\\u1ED7 ngh\\u1EC9 c\\u1EE7a Qu\\xFD v\\u1ECB tr\\xEAn Google\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.revenueRow,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Utils.formatCurrency(price * 88 / 100)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Doanh thu c\\u1EE7a Qu\\xFD v\\u1ECB (bao g\\u1ED3m thu\\u1EBF)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.infoCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.infoHeader,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.infoTitle,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: styles.lightBulbIcon,\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fa fa-lightbulb-o\",\n                    \"aria-hidden\": \"true\",\n                    children: \"\\uD83D\\uDCA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this), \"N\\u1EBFu t\\xF4i c\\u1EA3m th\\u1EA5y ch\\u01B0a ch\\u1EAFc ch\\u1EAFn v\\u1EC1 gi\\xE1 th\\xEC sao?\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.closeButton,\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0110\\u1EEBng lo l\\u1EAFng, Qu\\xFD v\\u1ECB c\\xF3 th\\u1EC3 \\u0111\\u1ED5i l\\u1EA1i b\\u1EA5t c\\u1EE9 l\\xFAc n\\xE0o. Th\\u1EADm ch\\xED Qu\\xFD v\\u1ECB c\\xF3 th\\u1EC3 thi\\u1EBFt l\\u1EADp gi\\xE1 cu\\u1ED1i tu\\u1EA7n, gi\\u1EEFa tu\\u1EA7n v\\xE0 theo m\\xF9a, nh\\u1EDD \\u0111\\xF3 gi\\xFAp Qu\\xFD v\\u1ECB ki\\u1EC3m so\\xE1t doanh thu t\\u1ED1t h\\u01A1n.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.buttonContainer,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: styles.backButton,\n          onClick: () => {\n            navigate(\"/RoomImageForm\");\n          },\n          children: \"\\u2190\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          style: styles.continueButton,\n          onClick: () => setShowAcceptModal(true),\n          children: \"Ho\\xE0n th\\xE0nh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      show: showAcceptModal,\n      onHide: () => setShowAcceptModal(false),\n      onConfirm: handleContinue,\n      title: \"Confirm Acceptance\",\n      message: \"Are you sure you want to create room setup?\",\n      confirmButtonText: \"Accept\",\n      type: \"accept\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this);\n}\n_s(PricingSetupForm, \"i7fmMsrgmOapbTVRLbpXWNKv5rs=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = PricingSetupForm;\nexport default PricingSetupForm;\nvar _c;\n$RefreshReg$(_c, \"PricingSetupForm\");", "map": {"version": 3, "names": ["React", "useState", "Routers", "Container", "Row", "Col", "Form", "<PERSON><PERSON>", "Card", "InputGroup", "<PERSON><PERSON><PERSON>", "ProgressBar", "useNavigate", "useDispatch", "useSelector", "RoomActions", "showToast", "ToastProvider", "Utils", "ConfirmationModal", "jsxDEV", "_jsxDEV", "PricingSetupForm", "_s", "showAcceptModal", "setShowAcceptModal", "navigate", "dispatch", "price", "setPrice", "createRoom", "state", "Room", "handlePriceChange", "e", "value", "target", "replace", "handleContinue", "finalPrice", "Number", "type", "SAVE_ROOM_PRICING_CREATE", "payload", "completeRoom", "SAVE_ROOM_TO_CREATE_LIST", "CLEAR_ROOM_CREATE", "success", "BookingPropertyChecklist", "styles", "bookingApp", "minHeight", "backgroundColor", "container", "max<PERSON><PERSON><PERSON>", "margin", "padding", "title", "fontSize", "fontWeight", "marginBottom", "card", "border", "borderRadius", "slide<PERSON><PERSON><PERSON><PERSON>", "marginTop", "slider<PERSON><PERSON><PERSON>", "textAlign", "color", "position", "slider<PERSON><PERSON><PERSON><PERSON><PERSON>", "width", "height", "borderLeft", "borderRight", "borderTop", "bottom", "left", "transform", "slider", "priceRange", "display", "justifyContent", "feedbackButtons", "alignItems", "feedbackButton", "background", "marginLeft", "cursor", "infoCard", "infoHeader", "infoTitle", "lightBulbIcon", "marginRight", "closeButton", "highlightText", "priceInput", "smallText", "commissionRow", "benefitItem", "checkIcon", "revenueRow", "paddingTop", "buttonContainer", "backButton", "continueButton", "flex", "navbarCustom", "navbar<PERSON><PERSON>", "style", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Brand", "href", "className", "variant", "now", "md", "Group", "Label", "Text", "Control", "formatCurrency", "onChange", "placeholder", "onClick", "show", "onHide", "onConfirm", "message", "confirmButtonText", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/create_hotel/PricingSetupForm.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Form,\r\n  Button,\r\n  Card,\r\n  InputGroup,\r\n  Navbar,\r\n  ProgressBar,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport RoomActions from \"@redux/room/actions\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport Utils from \"@utils/Utils\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\n\r\nfunction PricingSetupForm() {\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const [price, setPrice] = useState(\"0\");\r\n  const createRoom = useSelector(state => state.Room.createRoom);\r\n  \r\n\r\n  const handlePriceChange = (e) => {\r\n    // Remove non-digits and format\r\n    const value = e.target.value.replace(/\\D/g, \"\");\r\n    setPrice(value);\r\n  };\r\n\r\n  const handleContinue = () => {\r\n    // Save pricing and complete room creation\r\n    const finalPrice = Number(price);\r\n    \r\n    dispatch({\r\n      type: RoomActions.SAVE_ROOM_PRICING_CREATE,\r\n      payload: { price: finalPrice }\r\n    });\r\n\r\n    // Add completed room to create list\r\n    const completeRoom = {\r\n      ...createRoom,\r\n      price: finalPrice\r\n    };\r\n\r\n    dispatch({\r\n      type: RoomActions.SAVE_ROOM_TO_CREATE_LIST,\r\n      payload: completeRoom\r\n    });\r\n\r\n    // Clear current room creation data\r\n    dispatch({\r\n      type: RoomActions.CLEAR_ROOM_CREATE\r\n    });\r\n\r\n    showToast.success(\"Thêm phòng thành công!\");\r\n    navigate(Routers.BookingPropertyChecklist);\r\n  };\r\n  \r\n  const styles = {\r\n    bookingApp: {\r\n      minHeight: \"100vh\",\r\n      backgroundColor: \"#f8f9fa\",\r\n    },\r\n    container: {\r\n      maxWidth: \"1000px\",\r\n      margin: \"20px auto\",\r\n      padding: \"20px\",\r\n    },\r\n    title: {\r\n      fontSize: \"28px\",\r\n      fontWeight: \"bold\",\r\n      marginBottom: \"30px\",\r\n    },\r\n    card: {\r\n      border: \"1px solid #e7e7e7\",\r\n      borderRadius: \"4px\",\r\n      padding: \"20px\",\r\n      marginBottom: \"20px\",\r\n      backgroundColor: \"#fff\",\r\n    },\r\n    sliderContainer: {\r\n      marginTop: \"20px\",\r\n      marginBottom: \"20px\",\r\n    },\r\n    sliderLabel: {\r\n      textAlign: \"center\",\r\n      backgroundColor: \"#0071c2\",\r\n      color: \"white\",\r\n      padding: \"5px 10px\",\r\n      borderRadius: \"4px\",\r\n      position: \"relative\",\r\n      marginBottom: \"10px\",\r\n    },\r\n    sliderLabelArrow: {\r\n      width: \"0\",\r\n      height: \"0\",\r\n      borderLeft: \"8px solid transparent\",\r\n      borderRight: \"8px solid transparent\",\r\n      borderTop: \"8px solid #0071c2\",\r\n      position: \"absolute\",\r\n      bottom: \"-8px\",\r\n      left: \"50%\",\r\n      transform: \"translateX(-50%)\",\r\n    },\r\n    slider: {\r\n      width: \"100%\",\r\n    },\r\n    priceRange: {\r\n      display: \"flex\",\r\n      justifyContent: \"space-between\",\r\n      marginTop: \"10px\",\r\n    },\r\n    feedbackButtons: {\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      marginTop: \"15px\",\r\n    },\r\n    feedbackButton: {\r\n      background: \"none\",\r\n      border: \"none\",\r\n      fontSize: \"24px\",\r\n      marginLeft: \"10px\",\r\n      cursor: \"pointer\",\r\n    },\r\n    infoCard: {\r\n      border: \"1px solid #e7e7e7\",\r\n      borderRadius: \"4px\",\r\n      padding: \"20px\",\r\n      backgroundColor: \"#fff\",\r\n    },\r\n    infoHeader: {\r\n      display: \"flex\",\r\n      justifyContent: \"space-between\",\r\n      alignItems: \"center\",\r\n      marginBottom: \"15px\",\r\n    },\r\n    infoTitle: {\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      fontWeight: \"bold\",\r\n      fontSize: \"16px\",\r\n    },\r\n    lightBulbIcon: {\r\n      marginRight: \"10px\",\r\n      color: \"#febb02\",\r\n    },\r\n    closeButton: {\r\n      background: \"none\",\r\n      border: \"none\",\r\n      fontSize: \"20px\",\r\n      cursor: \"pointer\",\r\n    },\r\n    highlightText: {\r\n      color: \"#0071c2\",\r\n      cursor: \"pointer\",\r\n    },\r\n    priceInput: {\r\n      marginTop: \"10px\",\r\n      marginBottom: \"5px\",\r\n    },\r\n    smallText: {\r\n      fontSize: \"12px\",\r\n      color: \"#6b6b6b\",\r\n    },\r\n    commissionRow: {\r\n      display: \"flex\",\r\n      justifyContent: \"space-between\",\r\n      marginTop: \"20px\",\r\n      marginBottom: \"10px\",\r\n    },\r\n    benefitItem: {\r\n      display: \"flex\",\r\n      alignItems: \"flex-start\",\r\n      marginBottom: \"10px\",\r\n    },\r\n    checkIcon: {\r\n      color: \"#008009\",\r\n      marginRight: \"10px\",\r\n      fontSize: \"18px\",\r\n    },\r\n    revenueRow: {\r\n      display: \"flex\",\r\n      justifyContent: \"space-between\",\r\n      marginTop: \"20px\",\r\n      paddingTop: \"15px\",\r\n      borderTop: \"1px solid #e7e7e7\",\r\n    },\r\n    buttonContainer: {\r\n      display: \"flex\",\r\n      marginTop: \"30px\",\r\n    },\r\n    backButton: {\r\n      width: \"50px\",\r\n      marginRight: \"10px\",\r\n      backgroundColor: \"white\",\r\n      color: \"#0071c2\",\r\n      border: \"1px solid #0071c2\",\r\n    },\r\n    continueButton: {\r\n      flex: 1,\r\n      backgroundColor: \"#0071c2\",\r\n      border: \"none\",\r\n    },\r\n    // Navbar styles\r\n    navbarCustom: {\r\n        backgroundColor: \"#003580\",\r\n        padding: \"10px 0\",\r\n    },\r\n    navbarBrand: {\r\n        color: \"#fff\",\r\n        fontWeight: \"bold\",\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div style={styles.bookingApp}>\r\n      <ToastProvider />\r\n      {/* Navigation Bar */}\r\n      <Navbar style={styles.navbarCustom}>\r\n        <Container>\r\n          <Navbar.Brand href=\"#home\" className=\"text-white fw-bold\">\r\n            <b style={{ fontSize: 30 }}>\r\n              UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n            </b>\r\n          </Navbar.Brand>\r\n        </Container>\r\n      </Navbar>\r\n\r\n      {/* Progress Bar */}\r\n      <Container className=\"mt-4 mb-4\">\r\n        <div className=\"progress-section\">\r\n          <div className=\"progress-label mb-2\">\r\n            <h5>Thông tin cơ bản</h5>\r\n          </div>\r\n          <ProgressBar style={{ height: \"20px\" }}>\r\n            <ProgressBar variant=\"primary\" now={25} key={1} />\r\n            <ProgressBar variant=\"primary\" now={25} key={2} />\r\n            <ProgressBar variant=\"primary\" now={25} key={3} />\r\n            <ProgressBar variant=\"primary\" now={25} key={4} />\r\n          </ProgressBar>\r\n        </div>\r\n      </Container>\r\n\r\n      <Container style={styles.container}>\r\n        <h1 style={styles.title}>Thiết lập giá mỗi đêm cho phòng này</h1>\r\n        <Row>\r\n          <Col md={7}>\r\n            <div style={styles.card}>\r\n              <h5>Quý vị muốn thu bao nhiêu tiền mỗi đêm?</h5>\r\n              <Form.Group>\r\n                <Form.Label>Số tiền khách trả</Form.Label>\r\n                <InputGroup style={styles.priceInput}>\r\n                  <InputGroup.Text>$</InputGroup.Text>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    value={Utils.formatCurrency(price)}\r\n                    onChange={handlePriceChange}\r\n                    placeholder=\"Nhập giá phòng\"\r\n                  />\r\n                </InputGroup>\r\n                <Form.Text style={styles.smallText}>\r\n                  Bao gồm các loại thuế, phí và hoa hồng\r\n                </Form.Text>\r\n              </Form.Group>\r\n\r\n              <div style={styles.commissionRow}>\r\n                <span>12,00% Hoa hồng cho Booking.com</span>\r\n              </div>\r\n\r\n              <div>\r\n                <div style={styles.benefitItem}>\r\n                  <span style={styles.checkIcon}>✓</span>\r\n                  <span>Trợ giúp 24/7 bằng ngôn ngữ của Quý vị</span>\r\n                </div>\r\n                <div style={styles.benefitItem}>\r\n                  <span style={styles.checkIcon}>✓</span>\r\n                  <span>\r\n                    Tiết kiệm thời gian với đặt phòng được xác nhận tự động\r\n                  </span>\r\n                </div>\r\n                <div style={styles.benefitItem}>\r\n                  <span style={styles.checkIcon}>✓</span>\r\n                  <span>\r\n                    Chúng tôi sẽ quảng bá chỗ nghỉ của Quý vị trên Google\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <div style={styles.revenueRow}>\r\n                <strong>{Utils.formatCurrency(price * 88 / 100)}</strong>\r\n                <span>Doanh thu của Quý vị (bao gồm thuế)</span>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n\r\n          <Col md={5}>\r\n            <div style={styles.infoCard}>\r\n              <div style={styles.infoHeader}>\r\n                <div style={styles.infoTitle}>\r\n                  <span style={styles.lightBulbIcon}>\r\n                    <i className=\"fa fa-lightbulb-o\" aria-hidden=\"true\">\r\n                      💡\r\n                    </i>\r\n                  </span>\r\n                  Nếu tôi cảm thấy chưa chắc chắn về giá thì sao?\r\n                </div>\r\n                <button style={styles.closeButton}>×</button>\r\n              </div>\r\n\r\n              <p>\r\n                Đừng lo lắng, Quý vị có thể đổi lại bất cứ lúc nào. Thậm chí Quý\r\n                vị có thể thiết lập giá cuối tuần, giữa tuần và theo mùa, nhờ đó\r\n                giúp Quý vị kiểm soát doanh thu tốt hơn.\r\n              </p>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <div style={styles.buttonContainer}>\r\n          <Button \r\n            style={styles.backButton}\r\n            onClick={() => {\r\n              navigate(\"/RoomImageForm\");\r\n            }}\r\n          >←</Button>\r\n          <Button \r\n            style={styles.continueButton}\r\n            onClick={() => setShowAcceptModal(true)}\r\n          >\r\n            Hoàn thành\r\n          </Button>\r\n        </div>\r\n      </Container>\r\n      <ConfirmationModal\r\n        show={showAcceptModal}\r\n        onHide={() => setShowAcceptModal(false)}\r\n        onConfirm={handleContinue}\r\n        title=\"Confirm Acceptance\"\r\n        message=\"Are you sure you want to create room setup?\"\r\n        confirmButtonText=\"Accept\"\r\n        type=\"accept\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default PricingSetupForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,QACN,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,iBAAiB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMyB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,GAAG,CAAC;EACvC,MAAM6B,UAAU,GAAGhB,WAAW,CAACiB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACF,UAAU,CAAC;EAG9D,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B;IACA,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC/CR,QAAQ,CAACM,KAAK,CAAC;EACjB,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,UAAU,GAAGC,MAAM,CAACZ,KAAK,CAAC;IAEhCD,QAAQ,CAAC;MACPc,IAAI,EAAE1B,WAAW,CAAC2B,wBAAwB;MAC1CC,OAAO,EAAE;QAAEf,KAAK,EAAEW;MAAW;IAC/B,CAAC,CAAC;;IAEF;IACA,MAAMK,YAAY,GAAG;MACnB,GAAGd,UAAU;MACbF,KAAK,EAAEW;IACT,CAAC;IAEDZ,QAAQ,CAAC;MACPc,IAAI,EAAE1B,WAAW,CAAC8B,wBAAwB;MAC1CF,OAAO,EAAEC;IACX,CAAC,CAAC;;IAEF;IACAjB,QAAQ,CAAC;MACPc,IAAI,EAAE1B,WAAW,CAAC+B;IACpB,CAAC,CAAC;IAEF9B,SAAS,CAAC+B,OAAO,CAAC,wBAAwB,CAAC;IAC3CrB,QAAQ,CAACxB,OAAO,CAAC8C,wBAAwB,CAAC;EAC5C,CAAC;EAED,MAAMC,MAAM,GAAG;IACbC,UAAU,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,eAAe,EAAE;IACnB,CAAC;IACDC,SAAS,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,WAAW;MACnBC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,YAAY,EAAE;IAChB,CAAC;IACDC,IAAI,EAAE;MACJC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,KAAK;MACnBP,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,MAAM;MACpBR,eAAe,EAAE;IACnB,CAAC;IACDY,eAAe,EAAE;MACfC,SAAS,EAAE,MAAM;MACjBL,YAAY,EAAE;IAChB,CAAC;IACDM,WAAW,EAAE;MACXC,SAAS,EAAE,QAAQ;MACnBf,eAAe,EAAE,SAAS;MAC1BgB,KAAK,EAAE,OAAO;MACdZ,OAAO,EAAE,UAAU;MACnBO,YAAY,EAAE,KAAK;MACnBM,QAAQ,EAAE,UAAU;MACpBT,YAAY,EAAE;IAChB,CAAC;IACDU,gBAAgB,EAAE;MAChBC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,UAAU,EAAE,uBAAuB;MACnCC,WAAW,EAAE,uBAAuB;MACpCC,SAAS,EAAE,mBAAmB;MAC9BN,QAAQ,EAAE,UAAU;MACpBO,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNR,KAAK,EAAE;IACT,CAAC;IACDS,UAAU,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BjB,SAAS,EAAE;IACb,CAAC;IACDkB,eAAe,EAAE;MACfF,OAAO,EAAE,MAAM;MACfG,UAAU,EAAE,QAAQ;MACpBnB,SAAS,EAAE;IACb,CAAC;IACDoB,cAAc,EAAE;MACdC,UAAU,EAAE,MAAM;MAClBxB,MAAM,EAAE,MAAM;MACdJ,QAAQ,EAAE,MAAM;MAChB6B,UAAU,EAAE,MAAM;MAClBC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACR3B,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,KAAK;MACnBP,OAAO,EAAE,MAAM;MACfJ,eAAe,EAAE;IACnB,CAAC;IACDsC,UAAU,EAAE;MACVT,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BE,UAAU,EAAE,QAAQ;MACpBxB,YAAY,EAAE;IAChB,CAAC;IACD+B,SAAS,EAAE;MACTV,OAAO,EAAE,MAAM;MACfG,UAAU,EAAE,QAAQ;MACpBzB,UAAU,EAAE,MAAM;MAClBD,QAAQ,EAAE;IACZ,CAAC;IACDkC,aAAa,EAAE;MACbC,WAAW,EAAE,MAAM;MACnBzB,KAAK,EAAE;IACT,CAAC;IACD0B,WAAW,EAAE;MACXR,UAAU,EAAE,MAAM;MAClBxB,MAAM,EAAE,MAAM;MACdJ,QAAQ,EAAE,MAAM;MAChB8B,MAAM,EAAE;IACV,CAAC;IACDO,aAAa,EAAE;MACb3B,KAAK,EAAE,SAAS;MAChBoB,MAAM,EAAE;IACV,CAAC;IACDQ,UAAU,EAAE;MACV/B,SAAS,EAAE,MAAM;MACjBL,YAAY,EAAE;IAChB,CAAC;IACDqC,SAAS,EAAE;MACTvC,QAAQ,EAAE,MAAM;MAChBU,KAAK,EAAE;IACT,CAAC;IACD8B,aAAa,EAAE;MACbjB,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BjB,SAAS,EAAE,MAAM;MACjBL,YAAY,EAAE;IAChB,CAAC;IACDuC,WAAW,EAAE;MACXlB,OAAO,EAAE,MAAM;MACfG,UAAU,EAAE,YAAY;MACxBxB,YAAY,EAAE;IAChB,CAAC;IACDwC,SAAS,EAAE;MACThC,KAAK,EAAE,SAAS;MAChByB,WAAW,EAAE,MAAM;MACnBnC,QAAQ,EAAE;IACZ,CAAC;IACD2C,UAAU,EAAE;MACVpB,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BjB,SAAS,EAAE,MAAM;MACjBqC,UAAU,EAAE,MAAM;MAClB3B,SAAS,EAAE;IACb,CAAC;IACD4B,eAAe,EAAE;MACftB,OAAO,EAAE,MAAM;MACfhB,SAAS,EAAE;IACb,CAAC;IACDuC,UAAU,EAAE;MACVjC,KAAK,EAAE,MAAM;MACbsB,WAAW,EAAE,MAAM;MACnBzC,eAAe,EAAE,OAAO;MACxBgB,KAAK,EAAE,SAAS;MAChBN,MAAM,EAAE;IACV,CAAC;IACD2C,cAAc,EAAE;MACdC,IAAI,EAAE,CAAC;MACPtD,eAAe,EAAE,SAAS;MAC1BU,MAAM,EAAE;IACV,CAAC;IACD;IACA6C,YAAY,EAAE;MACVvD,eAAe,EAAE,SAAS;MAC1BI,OAAO,EAAE;IACb,CAAC;IACDoD,WAAW,EAAE;MACTxC,KAAK,EAAE,MAAM;MACbT,UAAU,EAAE;IAChB;EACF,CAAC;EAED,oBACEtC,OAAA;IAAKwF,KAAK,EAAE5D,MAAM,CAACC,UAAW;IAAA4D,QAAA,gBAC5BzF,OAAA,CAACJ,aAAa;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjB7F,OAAA,CAACX,MAAM;MAACmG,KAAK,EAAE5D,MAAM,CAAC0D,YAAa;MAAAG,QAAA,eACjCzF,OAAA,CAAClB,SAAS;QAAA2G,QAAA,eACRzF,OAAA,CAACX,MAAM,CAACyG,KAAK;UAACC,IAAI,EAAC,OAAO;UAACC,SAAS,EAAC,oBAAoB;UAAAP,QAAA,eACvDzF,OAAA;YAAGwF,KAAK,EAAE;cAAEnD,QAAQ,EAAE;YAAG,CAAE;YAAAoD,QAAA,GAAC,IACxB,eAAAzF,OAAA;cAAMwF,KAAK,EAAE;gBAAEzC,KAAK,EAAE;cAAU,CAAE;cAAA0C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGT7F,OAAA,CAAClB,SAAS;MAACkH,SAAS,EAAC,WAAW;MAAAP,QAAA,eAC9BzF,OAAA;QAAKgG,SAAS,EAAC,kBAAkB;QAAAP,QAAA,gBAC/BzF,OAAA;UAAKgG,SAAS,EAAC,qBAAqB;UAAAP,QAAA,eAClCzF,OAAA;YAAAyF,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACN7F,OAAA,CAACV,WAAW;UAACkG,KAAK,EAAE;YAAErC,MAAM,EAAE;UAAO,CAAE;UAAAsC,QAAA,gBACrCzF,OAAA,CAACV,WAAW;YAAC2G,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClD7F,OAAA,CAACV,WAAW;YAAC2G,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClD7F,OAAA,CAACV,WAAW;YAAC2G,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClD7F,OAAA,CAACV,WAAW;YAAC2G,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEZ7F,OAAA,CAAClB,SAAS;MAAC0G,KAAK,EAAE5D,MAAM,CAACI,SAAU;MAAAyD,QAAA,gBACjCzF,OAAA;QAAIwF,KAAK,EAAE5D,MAAM,CAACQ,KAAM;QAAAqD,QAAA,EAAC;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjE7F,OAAA,CAACjB,GAAG;QAAA0G,QAAA,gBACFzF,OAAA,CAAChB,GAAG;UAACmH,EAAE,EAAE,CAAE;UAAAV,QAAA,eACTzF,OAAA;YAAKwF,KAAK,EAAE5D,MAAM,CAACY,IAAK;YAAAiD,QAAA,gBACtBzF,OAAA;cAAAyF,QAAA,EAAI;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD7F,OAAA,CAACf,IAAI,CAACmH,KAAK;cAAAX,QAAA,gBACTzF,OAAA,CAACf,IAAI,CAACoH,KAAK;gBAAAZ,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C7F,OAAA,CAACZ,UAAU;gBAACoG,KAAK,EAAE5D,MAAM,CAAC+C,UAAW;gBAAAc,QAAA,gBACnCzF,OAAA,CAACZ,UAAU,CAACkH,IAAI;kBAAAb,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC,eACpC7F,OAAA,CAACf,IAAI,CAACsH,OAAO;kBACXnF,IAAI,EAAC,MAAM;kBACXN,KAAK,EAAEjB,KAAK,CAAC2G,cAAc,CAACjG,KAAK,CAAE;kBACnCkG,QAAQ,EAAE7F,iBAAkB;kBAC5B8F,WAAW,EAAC;gBAAgB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACb7F,OAAA,CAACf,IAAI,CAACqH,IAAI;gBAACd,KAAK,EAAE5D,MAAM,CAACgD,SAAU;gBAAAa,QAAA,EAAC;cAEpC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEb7F,OAAA;cAAKwF,KAAK,EAAE5D,MAAM,CAACiD,aAAc;cAAAY,QAAA,eAC/BzF,OAAA;gBAAAyF,QAAA,EAAM;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAEN7F,OAAA;cAAAyF,QAAA,gBACEzF,OAAA;gBAAKwF,KAAK,EAAE5D,MAAM,CAACkD,WAAY;gBAAAW,QAAA,gBAC7BzF,OAAA;kBAAMwF,KAAK,EAAE5D,MAAM,CAACmD,SAAU;kBAAAU,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvC7F,OAAA;kBAAAyF,QAAA,EAAM;gBAAsC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN7F,OAAA;gBAAKwF,KAAK,EAAE5D,MAAM,CAACkD,WAAY;gBAAAW,QAAA,gBAC7BzF,OAAA;kBAAMwF,KAAK,EAAE5D,MAAM,CAACmD,SAAU;kBAAAU,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvC7F,OAAA;kBAAAyF,QAAA,EAAM;gBAEN;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN7F,OAAA;gBAAKwF,KAAK,EAAE5D,MAAM,CAACkD,WAAY;gBAAAW,QAAA,gBAC7BzF,OAAA;kBAAMwF,KAAK,EAAE5D,MAAM,CAACmD,SAAU;kBAAAU,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvC7F,OAAA;kBAAAyF,QAAA,EAAM;gBAEN;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7F,OAAA;cAAKwF,KAAK,EAAE5D,MAAM,CAACoD,UAAW;cAAAS,QAAA,gBAC5BzF,OAAA;gBAAAyF,QAAA,EAAS5F,KAAK,CAAC2G,cAAc,CAACjG,KAAK,GAAG,EAAE,GAAG,GAAG;cAAC;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACzD7F,OAAA;gBAAAyF,QAAA,EAAM;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7F,OAAA,CAAChB,GAAG;UAACmH,EAAE,EAAE,CAAE;UAAAV,QAAA,eACTzF,OAAA;YAAKwF,KAAK,EAAE5D,MAAM,CAACwC,QAAS;YAAAqB,QAAA,gBAC1BzF,OAAA;cAAKwF,KAAK,EAAE5D,MAAM,CAACyC,UAAW;cAAAoB,QAAA,gBAC5BzF,OAAA;gBAAKwF,KAAK,EAAE5D,MAAM,CAAC0C,SAAU;gBAAAmB,QAAA,gBAC3BzF,OAAA;kBAAMwF,KAAK,EAAE5D,MAAM,CAAC2C,aAAc;kBAAAkB,QAAA,eAChCzF,OAAA;oBAAGgG,SAAS,EAAC,mBAAmB;oBAAC,eAAY,MAAM;oBAAAP,QAAA,EAAC;kBAEpD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,+FAET;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN7F,OAAA;gBAAQwF,KAAK,EAAE5D,MAAM,CAAC6C,WAAY;gBAAAgB,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eAEN7F,OAAA;cAAAyF,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7F,OAAA;QAAKwF,KAAK,EAAE5D,MAAM,CAACsD,eAAgB;QAAAO,QAAA,gBACjCzF,OAAA,CAACd,MAAM;UACLsG,KAAK,EAAE5D,MAAM,CAACuD,UAAW;UACzBwB,OAAO,EAAEA,CAAA,KAAM;YACbtG,QAAQ,CAAC,gBAAgB,CAAC;UAC5B,CAAE;UAAAoF,QAAA,EACH;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACX7F,OAAA,CAACd,MAAM;UACLsG,KAAK,EAAE5D,MAAM,CAACwD,cAAe;UAC7BuB,OAAO,EAAEA,CAAA,KAAMvG,kBAAkB,CAAC,IAAI,CAAE;UAAAqF,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eACZ7F,OAAA,CAACF,iBAAiB;MAChB8G,IAAI,EAAEzG,eAAgB;MACtB0G,MAAM,EAAEA,CAAA,KAAMzG,kBAAkB,CAAC,KAAK,CAAE;MACxC0G,SAAS,EAAE7F,cAAe;MAC1BmB,KAAK,EAAC,oBAAoB;MAC1B2E,OAAO,EAAC,6CAA6C;MACrDC,iBAAiB,EAAC,QAAQ;MAC1B5F,IAAI,EAAC;IAAQ;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC3F,EAAA,CAzUQD,gBAAgB;EAAA,QAENV,WAAW,EACXC,WAAW,EAETC,WAAW;AAAA;AAAAwH,EAAA,GALvBhH,gBAAgB;AA2UzB,eAAeA,gBAAgB;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}