{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\create_hotel\\\\BookingPropertyChecklist.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Navbar, Container, Button, Row, Col, Card, Badge, Modal, Form } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport RoomActions from \"@redux/room/actions\";\nimport { useAppSelector } from \"@redux/store\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport Utils from \"@utils/Utils\";\nimport Room from \"@pages/room/Room\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction BookingPropertyChecklist() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [showModal, setShowModal] = React.useState(false);\n  const [showServiceModal, setShowServiceModal] = React.useState(false);\n  const [showRoomModal, setShowRoomModal] = React.useState(false);\n  const [editingService, setEditingService] = React.useState(null);\n  const [editingRoom, setEditingRoom] = React.useState(null);\n  const [currentService, setCurrentService] = React.useState({\n    name: \"\",\n    description: \"\",\n    price: \"\",\n    type: \"person\"\n  });\n  const [currentRoom, setCurrentRoom] = React.useState({\n    name: \"\",\n    type: \"Single Room\",\n    capacity: 1,\n    quantity: 1,\n    description: \"\",\n    price: \"\",\n    bed: [],\n    facilities: []\n  });\n  const createHotel = useAppSelector(state => state.Hotel.createHotel);\n  const createService = useAppSelector(state => state.Hotel.createService);\n  const createRoomList = useAppSelector(state => state.Room.createRoomList);\n  const createRoom = useAppSelector(state => state.Room.createRoom);\n  console.log(\"createRoom\", createRoom);\n  console.log(\"createHotel\", createHotel);\n  console.log(\"createService\", createService);\n  console.log(\"createRoomList\", createRoomList);\n  const serviceTypes = [{\n    value: \"person\",\n    label: \"Theo người\"\n  }, {\n    value: \"service\",\n    label: \"Theo dịch vụ\"\n  }, {\n    value: \"room\",\n    label: \"Theo phòng\"\n  }, {\n    value: \"day\",\n    label: \"Theo ngày\"\n  }, {\n    value: \"night\",\n    label: \"Theo đêm\"\n  }, {\n    value: \"month\",\n    label: \"Theo tháng\"\n  }, {\n    value: \"year\",\n    label: \"Theo năm\"\n  }];\n  const roomTypes = [\"Single Room\", \"Double Room\", \"Family Room\", \"Suite\", \"VIP Room\", \"Deluxe Room\"];\n  const handleEditService = (service, index) => {\n    setEditingService(index);\n    setCurrentService({\n      ...service,\n      price: service.price.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\")\n    });\n    setShowServiceModal(true);\n  };\n  const handleCreateService = () => {\n    setEditingService(null);\n    navigate(Routers.CreateService);\n  };\n\n  // Add missing service management functions\n  const handleDeleteService = index => {\n    dispatch({\n      type: HotelActions.DELETE_SERVICE_CREATE,\n      payload: {\n        index\n      }\n    });\n  };\n  const handleInputChange = (field, value) => {\n    setCurrentService(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const formatPrice = price => {\n    if (!price) return \"\";\n    // Remove all non-digit characters\n    const numericValue = price.toString().replace(/\\D/g, \"\");\n    // Format with dots as thousand separators\n    return numericValue.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\");\n  };\n  const handlePriceChange = e => {\n    const value = e.target.value;\n    // Remove all non-digit characters for internal storage\n    const numericValue = value.replace(/\\D/g, \"\");\n    setCurrentService(prev => ({\n      ...prev,\n      price: numericValue\n    }));\n  };\n  const handleSaveService = () => {\n    // Validate required fields\n    if (!currentService.name || !currentService.description || !currentService.price) {\n      return;\n    }\n    const serviceData = {\n      ...currentService,\n      price: parseInt(currentService.price.replace(/\\D/g, \"\")) || 0\n    };\n    if (editingService !== null) {\n      // Update existing service\n      dispatch({\n        type: HotelActions.EDIT_SERVICE_CREATE,\n        payload: {\n          index: editingService,\n          serviceData\n        }\n      });\n    } else {\n      // Add new service\n      dispatch({\n        type: HotelActions.SAVE_SERVICE_TO_CREATE_LIST,\n        payload: serviceData\n      });\n    }\n    setShowServiceModal(false);\n    setEditingService(null);\n    setCurrentService({\n      name: \"\",\n      description: \"\",\n      price: \"\",\n      type: \"person\"\n    });\n  };\n\n  // Room management handlers\n  const handleEditRoom = (room, index) => {\n    setEditingRoom(room);\n    setShowRoomModal(true);\n  };\n  const handleCreateRoom = () => {\n    navigate(Routers.CreateRoom);\n  };\n  const handleSaveRoom = roomData => {\n    if (editingRoom) {\n      // Update existing room\n      const roomIndex = createRoomList.findIndex(room => room === editingRoom);\n      dispatch({\n        type: RoomActions.EDIT_ROOM_IN_CREATE_LIST,\n        payload: {\n          index: roomIndex,\n          roomData\n        }\n      });\n    } else {\n      // Add new room\n      dispatch({\n        type: RoomActions.SAVE_ROOM_TO_CREATE_LIST,\n        payload: roomData\n      });\n    }\n    setShowRoomModal(false);\n  };\n  const handleDeleteRoom = index => {\n    dispatch({\n      type: RoomActions.DELETE_ROOM_FROM_CREATE_LIST,\n      payload: {\n        index\n      }\n    });\n    showToast.success(\"Bạn đã xóa phòng thành công!\");\n  };\n  const handleComfirm = () => {\n    dispatch({\n      type: HotelActions.CREATE_HOTEL,\n      payload: {\n        createHotel: createHotel,\n        createRoomList: createRoomList,\n        createService: createService,\n        onSuccess: () => {\n          setShowModal(false);\n        }\n      }\n    });\n    dispatch({\n      type: HotelActions.CLEAR_HOTEL_CREATE\n    });\n    dispatch({\n      type: HotelActions.CLEAR_SERVICE_CREATE\n    });\n    dispatch({\n      type: RoomActions.CLEAR_ROOM_CREATE_LIST\n    });\n    navigate(Routers.WaitPendingPage);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.bookingApp,\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      style: styles.navbarCustom,\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n          href: \"#home\",\n          className: \"text-white fw-bold\",\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            style: {\n              fontSize: 30\n            },\n            children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#f8e71c\"\n              },\n              children: \"OO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), \"M\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      style: styles.mainContent,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.checklistItem,\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: \"auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepIcon,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.completedIcon,\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepNumber,\n              children: \"B\\u01B0\\u1EDBc 1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepTitle,\n              children: \"Th\\xF4ng tin ch\\u1ED7 ngh\\u1EC9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepDescription,\n              children: \"C\\xE1c th\\xF4ng tin c\\u01A1 b\\u1EA3n: Nh\\u1EADp t\\xEAn ch\\u1ED7 ngh\\u1EC9, \\u0111\\u1ECBa ch\\u1EC9, ti\\u1EC7n nghi v\\xE0 nhi\\u1EC1u h\\u01A1n n\\u1EEFa.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: \"auto\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => {\n                dispatch({\n                  type: HotelActions.EDIT_HOTEL_DESCRIPTION_CREATE,\n                  payload: {\n                    checkCreateHotel: false\n                  }\n                });\n                navigate(Routers.BookingRegistration);\n              },\n              style: styles.editLink,\n              children: \"Ch\\u1EC9nh s\\u1EEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.checklistItem,\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: \"auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepIcon,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"room\",\n                style: {\n                  fontSize: \"24px\"\n                },\n                children: \"\\uD83D\\uDECF\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepNumber,\n              children: \"B\\u01B0\\u1EDBc 2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepTitle,\n              children: \"Ph\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepDescription,\n              children: [\"H\\xE3y cho ch\\xFAng t\\xF4i bi\\u1EBFt v\\u1EC1 ph\\xF2ng \\u0111\\u1EA7u ti\\xEAn c\\u1EE7a Qu\\xFD v\\u1ECB. Sau khi \\u0111\\xE3 thi\\u1EBFt l\\u1EADp xong m\\u1ED9t c\\u0103n, Qu\\xFD v\\u1ECB c\\xF3 th\\u1EC3 th\\xEAm nhi\\u1EC1u c\\u0103n n\\u1EEFa.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontStyle: \"italic\",\n                  color: \"#888\"\n                },\n                children: \"* C\\xF3 th\\u1EC3 th\\xEAm sau \\u0111\\xF3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), Array.isArray(createRoomList) && createRoomList.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: \"15px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Danh s\\xE1ch ph\\xF2ng \\u0111\\xE3 t\\u1EA1o:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: createRoomList.map((room, index) => /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  className: \"mb-2\",\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    style: styles.serviceCard,\n                    children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                      style: {\n                        padding: \"10px\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: styles.serviceHeader,\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          style: {\n                            margin: 0,\n                            fontWeight: \"bold\"\n                          },\n                          children: [room.name, \" - \", room.type]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 344,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          children: [Utils.formatCurrency(room.price), \"/\\u0111\\xEAm\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Row, {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(Col, {\n                          md: 6,\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                              children: \"S\\u1EE9c ch\\u1EE9a:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 354,\n                              columnNumber: 35\n                            }, this), \" \", room.capacity, \" ng\\u01B0\\u1EDDi\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 353,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 352,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Col, {\n                          md: 6,\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 359,\n                              columnNumber: 35\n                            }, this), \" \", room.quantity, \" ph\\xF2ng\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 358,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 357,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 351,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                          children: \"M\\xF4 t\\u1EA3: \"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 364,\n                          columnNumber: 31\n                        }, this), room.description.length > 100 ? `${room.description.substring(0, 100)}...` : room.description]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 29\n                      }, this), room.facilities && room.facilities.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                            children: \"Ti\\u1EC7n nghi:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 372,\n                            columnNumber: 35\n                          }, this), \" \", room.facilities.slice(0, 3).join(\", \"), room.facilities.length > 3 && ` và ${room.facilities.length - 3} tiện nghi khác`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 371,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 370,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: styles.serviceActions,\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          size: \"sm\",\n                          variant: \"outline-primary\",\n                          onClick: () => handleEditRoom(room, index),\n                          style: {\n                            marginRight: \"5px\"\n                          },\n                          children: \"S\\u1EEDa\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 382,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          size: \"sm\",\n                          variant: \"outline-danger\",\n                          onClick: () => handleDeleteRoom(index),\n                          children: \"X\\xF3a\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 390,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: \"auto\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              style: styles.actionButton,\n              onClick: handleCreateRoom,\n              children: \"+ Th\\xEAm ph\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.checklistItem,\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: \"auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepIcon,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"service\",\n                style: {\n                  fontSize: \"24px\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  class: \"bi bi-people\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepNumber,\n              children: \"B\\u01B0\\u1EDBc 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepTitle,\n              children: \"D\\u1ECBch v\\u1EE5 \\u0111i k\\xE8m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.stepDescription,\n              children: [\"Nh\\u1EEFng d\\u1ECBch v\\u1EE5 \\u0111i k\\xE8m v\\u1EDBi ph\\xF2ng c\\u1EE7a qu\\xFD v\\u1ECB. Sau khi \\u0111\\xE3 thi\\u1EBFt l\\u1EADp xong t\\u1ED1i thi\\u1EC3u 1 ph\\xF2ng.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontStyle: \"italic\",\n                  color: \"#888\"\n                },\n                children: \"* C\\xF3 th\\u1EC3 th\\xEAm sau \\u0111\\xF3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), Array.isArray(createService) && createService.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: \"15px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Danh s\\xE1ch d\\u1ECBch v\\u1EE5 \\u0111\\xE3 t\\u1EA1o:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: createService.map((service, index) => /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  className: \"mb-2\",\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    style: styles.serviceCard,\n                    children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                      style: {\n                        padding: \"10px\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: styles.serviceHeader,\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          style: {\n                            margin: 0,\n                            fontWeight: \"bold\"\n                          },\n                          children: [\"T\\xEAn d\\u1ECBch v\\u1EE5: \", service.name]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 450,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"primary\",\n                          children: [Utils.formatCurrency(service.price), \"/\", service.type]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 453,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 449,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                          children: \"M\\xF4 t\\u1EA3: \"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 31\n                        }, this), service.description]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: styles.serviceActions,\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          size: \"sm\",\n                          variant: \"outline-primary\",\n                          onClick: () => handleEditService(service, index),\n                          style: {\n                            marginRight: \"5px\"\n                          },\n                          children: \"S\\u1EEDa\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 463,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          size: \"sm\",\n                          variant: \"outline-danger\",\n                          onClick: () => handleDeleteService(index),\n                          children: \"X\\xF3a\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 473,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 462,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: \"auto\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              style: styles.actionButton,\n              onClick: handleCreateService,\n              children: \"+ Th\\xEAm d\\u1ECBch v\\u1EE5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"right\",\n          marginTop: 20\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          style: styles.confirmButton,\n          onClick: () => {\n            setShowModal(true);\n          },\n          children: \"X\\xE1c nh\\u1EADn ho\\xE0n t\\u1EA5t\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        show: showModal,\n        onHide: () => setShowModal(false),\n        onConfirm: handleComfirm,\n        title: \"X\\xE1c nh\\u1EADn t\\u1EA1o ch\\u1ED7 ngh\\u1EC9\",\n        message: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n t\\u1EA1o ch\\u1ED7 ngh\\u1EC9 n\\xE0y kh\\xF4ng? H\\xE0nh \\u0111\\u1ED9ng n\\xE0y s\\u1EBD kh\\xF4ng th\\u1EC3 ho\\xE0n t\\xE1c.\",\n        confirmButtonText: \"T\\u1EA1o ch\\u1ED7 ngh\\u1EC9\",\n        type: \"warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showServiceModal,\n        onHide: () => setShowServiceModal(false),\n        size: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: editingService !== null ? \"Chỉnh sửa dịch vụ\" : \"Thêm dịch vụ mới\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"T\\xEAn d\\u1ECBch v\\u1EE5 *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"text\",\n                placeholder: \"V\\xED d\\u1EE5: B\\u1EEFa s\\xE1ng, Buffet t\\u1ED1i, Spa...\",\n                value: currentService.name,\n                onChange: e => handleInputChange(\"name\", e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"M\\xF4 t\\u1EA3 d\\u1ECBch v\\u1EE5 *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 3,\n                placeholder: \"Nh\\u1EADp m\\xF4 t\\u1EA3 chi ti\\u1EBFt v\\u1EC1 d\\u1ECBch v\\u1EE5...\",\n                value: currentService.description,\n                onChange: e => handleInputChange(\"description\", e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Gi\\xE1 d\\u1ECBch v\\u1EE5 ($) *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    placeholder: \"Nh\\u1EADp gi\\xE1 d\\u1ECBch v\\u1EE5\",\n                    value: formatPrice(currentService.price),\n                    onChange: handlePriceChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Lo\\u1EA1i t\\xEDnh ph\\xED *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: currentService.type,\n                    onChange: e => handleInputChange(\"type\", e.target.value),\n                    children: serviceTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: type.value,\n                      children: type.label\n                    }, type.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowServiceModal(false),\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleSaveService,\n            disabled: !currentService.name || !currentService.description || !currentService.price,\n            children: editingService !== null ? \"Lưu thay đổi\" : \"Thêm dịch vụ\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Room, {\n        show: showRoomModal,\n        handleClose: () => setShowRoomModal(false),\n        onSave: handleSaveRoom,\n        editingRoom: editingRoom\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 5\n  }, this);\n}\n\n// Define styles as a constant object\n_s(BookingPropertyChecklist, \"smPf3MJ/2j+GQ4s9f/aVBfdbpFE=\", false, function () {\n  return [useNavigate, useDispatch, useAppSelector, useAppSelector, useAppSelector, useAppSelector];\n});\n_c = BookingPropertyChecklist;\nconst styles = {\n  // Main container styles\n  bookingApp: {\n    minHeight: \"100vh\"\n  },\n  // Navbar styles\n  navbarCustom: {\n    backgroundColor: \"#003580\",\n    padding: \"10px 0\"\n  },\n  navbarBrand: {\n    color: \"#fff\",\n    fontWeight: \"bold\"\n  },\n  userInfo: {\n    color: \"#fff\",\n    textAlign: \"right\",\n    marginRight: \"15px\"\n  },\n  userName: {\n    fontWeight: \"bold\",\n    fontSize: \"16px\"\n  },\n  userAddress: {\n    fontSize: \"12px\"\n  },\n  languageSelector: {\n    marginRight: \"15px\"\n  },\n  helpButton: {\n    color: \"#fff\",\n    textDecoration: \"none\"\n  },\n  helpIcon: {\n    display: \"inline-flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"24px\",\n    height: \"24px\",\n    backgroundColor: \"#fff\",\n    color: \"#003580\",\n    borderRadius: \"50%\",\n    fontWeight: \"bold\"\n  },\n  userIconCircle: {\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"32px\",\n    height: \"32px\",\n    backgroundColor: \"#fff\",\n    borderRadius: \"50%\",\n    marginLeft: \"10px\"\n  },\n  // Main content styles\n  mainContent: {\n    maxWidth: \"800px\",\n    margin: \"30px auto\",\n    backgroundColor: \"#fff\",\n    borderRadius: \"4px\",\n    boxShadow: \"0 1px 4px rgba(0, 0, 0, 0.05)\"\n  },\n  // Checklist item styles\n  checklistItem: {\n    padding: \"20px\",\n    borderBottom: \"1px solid #e7e7e7\"\n  },\n  checklistItemLast: {\n    padding: \"20px\",\n    borderBottom: \"none\"\n  },\n  stepNumber: {\n    fontSize: \"14px\",\n    color: \"#6b6b6b\",\n    marginBottom: \"5px\"\n  },\n  stepTitle: {\n    fontSize: \"18px\",\n    fontWeight: \"bold\",\n    marginBottom: \"5px\"\n  },\n  stepDescription: {\n    fontSize: \"14px\",\n    color: \"#6b6b6b\"\n  },\n  stepIcon: {\n    width: \"40px\",\n    height: \"40px\",\n    marginRight: \"15px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  completedIcon: {\n    color: \"#fff\",\n    backgroundColor: \"#008009\",\n    borderRadius: \"50%\",\n    width: \"30px\",\n    height: \"30px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  editLink: {\n    color: \"#0071c2\",\n    textDecoration: \"none\",\n    fontSize: \"14px\",\n    cursor: \"pointer\"\n  },\n  actionButton: {\n    backgroundColor: \"#0071c2\",\n    border: \"none\",\n    padding: \"8px 15px\",\n    fontWeight: \"bold\"\n  },\n  secondaryButton: {\n    backgroundColor: \"#f5f5f5\",\n    border: \"1px solid #e7e7e7\",\n    color: \"#333\",\n    padding: \"8px 15px\"\n  },\n  serviceCard: {\n    border: \"1px solid #e7e7e7\",\n    borderRadius: \"8px\",\n    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n    transition: \"all 0.3s ease\"\n  },\n  serviceHeader: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    marginBottom: \"5px\"\n  },\n  serviceActions: {\n    display: \"flex\",\n    justifyContent: \"flex-end\",\n    marginTop: \"10px\"\n  },\n  confirmButton: {\n    backgroundColor: \"#0071c2\",\n    border: \"none\",\n    padding: \"10px 20px\",\n    fontWeight: \"bold\",\n    color: \"white\"\n  }\n};\nexport default BookingPropertyChecklist;\nvar _c;\n$RefreshReg$(_c, \"BookingPropertyChecklist\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Container", "<PERSON><PERSON>", "Row", "Col", "Card", "Badge", "Modal", "Form", "Routers", "useNavigate", "useDispatch", "HotelActions", "RoomActions", "useAppSelector", "ConfirmationModal", "Utils", "Room", "showToast", "ToastProvider", "jsxDEV", "_jsxDEV", "BookingPropertyChecklist", "_s", "navigate", "dispatch", "showModal", "setShowModal", "useState", "showServiceModal", "setShowServiceModal", "showRoomModal", "setShowRoomModal", "editingService", "setEditingService", "editingRoom", "setEditingRoom", "currentService", "setCurrentService", "name", "description", "price", "type", "currentRoom", "setCurrentRoom", "capacity", "quantity", "bed", "facilities", "createHotel", "state", "Hotel", "createService", "createRoomList", "createRoom", "console", "log", "serviceTypes", "value", "label", "roomTypes", "handleEditService", "service", "index", "toString", "replace", "handleCreateService", "CreateService", "handleDeleteService", "DELETE_SERVICE_CREATE", "payload", "handleInputChange", "field", "prev", "formatPrice", "numericValue", "handlePriceChange", "e", "target", "handleSaveService", "serviceData", "parseInt", "EDIT_SERVICE_CREATE", "SAVE_SERVICE_TO_CREATE_LIST", "handleEditRoom", "room", "handleCreateRoom", "CreateRoom", "handleSaveRoom", "roomData", "roomIndex", "findIndex", "EDIT_ROOM_IN_CREATE_LIST", "SAVE_ROOM_TO_CREATE_LIST", "handleDeleteRoom", "DELETE_ROOM_FROM_CREATE_LIST", "success", "handleComfirm", "CREATE_HOTEL", "onSuccess", "CLEAR_HOTEL_CREATE", "CLEAR_SERVICE_CREATE", "CLEAR_ROOM_CREATE_LIST", "WaitPendingPage", "style", "styles", "bookingApp", "children", "navbarCustom", "Brand", "href", "className", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mainContent", "checklistItem", "xs", "stepIcon", "completedIcon", "<PERSON><PERSON><PERSON><PERSON>", "step<PERSON>itle", "stepDescription", "onClick", "EDIT_HOTEL_DESCRIPTION_CREATE", "checkCreateHotel", "BookingRegistration", "editLink", "role", "fontStyle", "Array", "isArray", "length", "marginTop", "map", "md", "serviceCard", "Body", "padding", "serviceHeader", "margin", "fontWeight", "bg", "formatCurrency", "substring", "slice", "join", "serviceActions", "size", "variant", "marginRight", "actionButton", "class", "textAlign", "confirmButton", "show", "onHide", "onConfirm", "title", "message", "confirmButtonText", "Header", "closeButton", "Title", "Group", "Label", "Control", "placeholder", "onChange", "as", "rows", "Select", "Footer", "disabled", "handleClose", "onSave", "_c", "minHeight", "backgroundColor", "navbar<PERSON><PERSON>", "userInfo", "userName", "userAddress", "languageSelector", "helpButton", "textDecoration", "helpIcon", "display", "alignItems", "justifyContent", "width", "height", "borderRadius", "userIconCircle", "marginLeft", "max<PERSON><PERSON><PERSON>", "boxShadow", "borderBottom", "checklistItemLast", "marginBottom", "cursor", "border", "secondaryButton", "transition", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/create_hotel/BookingPropertyChecklist.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  <PERSON>v<PERSON>,\r\n  Container,\r\n  Button,\r\n  Row,\r\n  Col,\r\n  <PERSON>,\r\n  Badge,\r\n  Modal,\r\n  Form,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport RoomActions from \"@redux/room/actions\";\r\nimport { useAppSelector } from \"@redux/store\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport Utils from \"@utils/Utils\";\r\nimport Room from \"@pages/room/Room\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\n\r\nfunction BookingPropertyChecklist() {\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const [showModal, setShowModal] = React.useState(false);\r\n  const [showServiceModal, setShowServiceModal] = React.useState(false);\r\n  const [showRoomModal, setShowRoomModal] = React.useState(false);\r\n  const [editingService, setEditingService] = React.useState(null);\r\n  const [editingRoom, setEditingRoom] = React.useState(null);\r\n  const [currentService, setCurrentService] = React.useState({\r\n    name: \"\",\r\n    description: \"\",\r\n    price: \"\",\r\n    type: \"person\",\r\n  });\r\n  const [currentRoom, setCurrentRoom] = React.useState({\r\n    name: \"\",\r\n    type: \"Single Room\",\r\n    capacity: 1,\r\n    quantity: 1,\r\n    description: \"\",\r\n    price: \"\",\r\n    bed: [],\r\n    facilities: [],\r\n  });\r\n\r\n  const createHotel = useAppSelector((state) => state.Hotel.createHotel);\r\n  const createService = useAppSelector((state) => state.Hotel.createService);\r\n  const createRoomList = useAppSelector((state) => state.Room.createRoomList);\r\n  const createRoom = useAppSelector((state) => state.Room.createRoom);\r\n  console.log(\"createRoom\", createRoom);\r\n  console.log(\"createHotel\", createHotel);\r\n  console.log(\"createService\", createService);\r\n  console.log(\"createRoomList\", createRoomList);\r\n  const serviceTypes = [\r\n    { value: \"person\", label: \"Theo người\" },\r\n    { value: \"service\", label: \"Theo dịch vụ\" },\r\n    { value: \"room\", label: \"Theo phòng\" },\r\n    { value: \"day\", label: \"Theo ngày\" },\r\n    { value: \"night\", label: \"Theo đêm\" },\r\n    { value: \"month\", label: \"Theo tháng\" },\r\n    { value: \"year\", label: \"Theo năm\" },\r\n  ];\r\n\r\n  const roomTypes = [\r\n    \"Single Room\",\r\n    \"Double Room\",\r\n    \"Family Room\",\r\n    \"Suite\",\r\n    \"VIP Room\",\r\n    \"Deluxe Room\",\r\n  ];\r\n\r\n  const handleEditService = (service, index) => {\r\n    setEditingService(index);\r\n    setCurrentService({\r\n      ...service,\r\n      price: service.price.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\"),\r\n    });\r\n    setShowServiceModal(true);\r\n  };\r\n\r\n  const handleCreateService = () => {\r\n    setEditingService(null);\r\n    navigate(Routers.CreateService);\r\n  };\r\n\r\n  // Add missing service management functions\r\n  const handleDeleteService = (index) => {\r\n    dispatch({\r\n      type: HotelActions.DELETE_SERVICE_CREATE,\r\n      payload: { index },\r\n    });\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setCurrentService((prev) => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  const formatPrice = (price) => {\r\n    if (!price) return \"\";\r\n    // Remove all non-digit characters\r\n    const numericValue = price.toString().replace(/\\D/g, \"\");\r\n    // Format with dots as thousand separators\r\n    return numericValue.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\");\r\n  };\r\n\r\n  const handlePriceChange = (e) => {\r\n    const value = e.target.value;\r\n    // Remove all non-digit characters for internal storage\r\n    const numericValue = value.replace(/\\D/g, \"\");\r\n    setCurrentService((prev) => ({\r\n      ...prev,\r\n      price: numericValue,\r\n    }));\r\n  };\r\n\r\n  const handleSaveService = () => {\r\n    // Validate required fields\r\n    if (\r\n      !currentService.name ||\r\n      !currentService.description ||\r\n      !currentService.price\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const serviceData = {\r\n      ...currentService,\r\n      price: parseInt(currentService.price.replace(/\\D/g, \"\")) || 0,\r\n    };\r\n\r\n    if (editingService !== null) {\r\n      // Update existing service\r\n      dispatch({\r\n        type: HotelActions.EDIT_SERVICE_CREATE,\r\n        payload: {\r\n          index: editingService,\r\n          serviceData,\r\n        },\r\n      });\r\n    } else {\r\n      // Add new service\r\n      dispatch({\r\n        type: HotelActions.SAVE_SERVICE_TO_CREATE_LIST,\r\n        payload: serviceData,\r\n      });\r\n    }\r\n\r\n    setShowServiceModal(false);\r\n    setEditingService(null);\r\n    setCurrentService({\r\n      name: \"\",\r\n      description: \"\",\r\n      price: \"\",\r\n      type: \"person\",\r\n    });\r\n  };\r\n\r\n  // Room management handlers\r\n  const handleEditRoom = (room, index) => {\r\n    setEditingRoom(room);\r\n    setShowRoomModal(true);\r\n  };\r\n\r\n  const handleCreateRoom = () => {\r\n    navigate(Routers.CreateRoom);\r\n  };\r\n\r\n  const handleSaveRoom = (roomData) => {\r\n    if (editingRoom) {\r\n      // Update existing room\r\n      const roomIndex = createRoomList.findIndex(\r\n        (room) => room === editingRoom\r\n      );\r\n      dispatch({\r\n        type: RoomActions.EDIT_ROOM_IN_CREATE_LIST,\r\n        payload: {\r\n          index: roomIndex,\r\n          roomData,\r\n        },\r\n      });\r\n    } else {\r\n      // Add new room\r\n      dispatch({\r\n        type: RoomActions.SAVE_ROOM_TO_CREATE_LIST,\r\n        payload: roomData,\r\n      });\r\n    }\r\n    setShowRoomModal(false);\r\n  };\r\n\r\n  const handleDeleteRoom = (index) => {\r\n    dispatch({\r\n      type: RoomActions.DELETE_ROOM_FROM_CREATE_LIST,\r\n      payload: { index },\r\n    });\r\n    showToast.success(\"Bạn đã xóa phòng thành công!\");\r\n  };\r\n\r\n  const handleComfirm = () => {\r\n    dispatch({\r\n      type: HotelActions.CREATE_HOTEL,\r\n      payload: {\r\n        createHotel: createHotel,\r\n        createRoomList: createRoomList,\r\n        createService: createService,\r\n        onSuccess: () => {\r\n          setShowModal(false);\r\n        },\r\n      },\r\n    });\r\n    dispatch({ type: HotelActions.CLEAR_HOTEL_CREATE });\r\n    dispatch({ type: HotelActions.CLEAR_SERVICE_CREATE });\r\n    dispatch({ type: RoomActions.CLEAR_ROOM_CREATE_LIST });\r\n    navigate(Routers.WaitPendingPage);\r\n  };\r\n\r\n  return (\r\n    <div style={styles.bookingApp}>\r\n      {/* Navigation Bar */}\r\n      <Navbar style={styles.navbarCustom}>\r\n        <Container>\r\n          <Navbar.Brand href=\"#home\" className=\"text-white fw-bold\">\r\n            <b style={{ fontSize: 30 }}>\r\n              UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n            </b>\r\n          </Navbar.Brand>\r\n        </Container>\r\n      </Navbar>\r\n\r\n      {/* Main Content */}\r\n      <Container style={styles.mainContent}>\r\n        {/* Step 1 - Completed */}\r\n        <div style={styles.checklistItem}>\r\n          <Row className=\"align-items-center\">\r\n            <Col xs=\"auto\">\r\n              <div style={styles.stepIcon}>\r\n                <div style={styles.completedIcon}>✓</div>\r\n              </div>\r\n            </Col>\r\n            <Col>\r\n              <div style={styles.stepNumber}>Bước 1</div>\r\n              <div style={styles.stepTitle}>Thông tin chỗ nghỉ</div>\r\n              <div style={styles.stepDescription}>\r\n                Các thông tin cơ bản: Nhập tên chỗ nghỉ, địa chỉ, tiện nghi và\r\n                nhiều hơn nữa.\r\n              </div>\r\n            </Col>\r\n            <Col xs=\"auto\">\r\n              <a\r\n                onClick={() => {\r\n                  dispatch({\r\n                    type: HotelActions.EDIT_HOTEL_DESCRIPTION_CREATE,\r\n                    payload: {\r\n                      checkCreateHotel: false,\r\n                    },\r\n                  });\r\n                  navigate(Routers.BookingRegistration);\r\n                }}\r\n                style={styles.editLink}\r\n              >\r\n                Chỉnh sửa\r\n              </a>\r\n            </Col>\r\n          </Row>\r\n        </div>\r\n        {/* Step 2 */}\r\n        {/* <div style={styles.checklistItemLast}>\r\n          <Row className=\"align-items-center\">\r\n            <Col xs=\"auto\">\r\n              <div style={styles.stepIcon}>\r\n                <span\r\n                  role=\"img\"\r\n                  aria-label=\"document\"\r\n                  style={{ fontSize: \"24px\" }}\r\n                >\r\n                  📄\r\n                </span>\r\n              </div>\r\n            </Col>\r\n            <Col>\r\n              <div style={styles.stepNumber}>Bước 2</div>\r\n              <div style={styles.stepTitle}>Giấy tờ kinh doanh</div>\r\n              <div style={styles.stepDescription}>\r\n                Nhập thông tin thanh toán và hóa đơn trước khi mở để nhận đặt\r\n                phòng.\r\n                <br />\r\n                <span style={{ fontStyle: \"italic\", color: \"#888\" }}>\r\n                  * Có thể thêm sau đó\r\n                </span>\r\n              </div>\r\n            </Col>\r\n            <Col xs=\"auto\">\r\n              <Button\r\n                style={styles.secondaryButton}\r\n                onClick={() => {\r\n                  navigate(Routers.DocumentUpload);\r\n                }}\r\n              >\r\n                Thêm các thông tin cuối cùng\r\n              </Button>\r\n            </Col>\r\n          </Row>\r\n        </div> */}\r\n        {/* Step 3 */}\r\n        <div style={styles.checklistItem}>\r\n          <Row className=\"align-items-center\">\r\n            <Col xs=\"auto\">\r\n              <div style={styles.stepIcon}>\r\n                <span role=\"img\" aria-label=\"room\" style={{ fontSize: \"24px\" }}>\r\n                  🛏️\r\n                </span>\r\n              </div>\r\n            </Col>\r\n            <Col>\r\n              <div style={styles.stepNumber}>Bước 2</div>\r\n              <div style={styles.stepTitle}>Phòng</div>\r\n              <div style={styles.stepDescription}>\r\n                Hãy cho chúng tôi biết về phòng đầu tiên của Quý vị. Sau khi đã\r\n                thiết lập xong một căn, Quý vị có thể thêm nhiều căn nữa.\r\n                <br />\r\n                <span style={{ fontStyle: \"italic\", color: \"#888\" }}>\r\n                  * Có thể thêm sau đó\r\n                </span>\r\n              </div>\r\n              <ToastProvider />\r\n              {/* Room List */}\r\n              {Array.isArray(createRoomList) && createRoomList.length > 0 && (\r\n                <div style={{ marginTop: \"15px\" }}>\r\n                  <h6>Danh sách phòng đã tạo:</h6>\r\n                  <Row>\r\n                    {createRoomList.map((room, index) => (\r\n                      <Col md={12} key={index} className=\"mb-2\">\r\n                        <Card style={styles.serviceCard}>\r\n                          <Card.Body style={{ padding: \"10px\" }}>\r\n                            <div style={styles.serviceHeader}>\r\n                              <h6 style={{ margin: 0, fontWeight: \"bold\" }}>\r\n                                {room.name} - {room.type}\r\n                              </h6>\r\n                              <Badge bg=\"success\">\r\n                                {Utils.formatCurrency(room.price)}/đêm\r\n                              </Badge>\r\n                            </div>\r\n                            <Row className=\"mb-2\">\r\n                              <Col md={6}>\r\n                                <small>\r\n                                  <b>Sức chứa:</b> {room.capacity} người\r\n                                </small>\r\n                              </Col>\r\n                              <Col md={6}>\r\n                                <small>\r\n                                  <b>Số lượng:</b> {room.quantity} phòng\r\n                                </small>\r\n                              </Col>\r\n                            </Row>\r\n                            <p>\r\n                              <b>Mô tả: </b>\r\n                              {room.description.length > 100\r\n                                ? `${room.description.substring(0, 100)}...`\r\n                                : room.description}\r\n                            </p>\r\n                            {room.facilities && room.facilities.length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <small>\r\n                                  <b>Tiện nghi:</b>{\" \"}\r\n                                  {room.facilities.slice(0, 3).join(\", \")}\r\n                                  {room.facilities.length > 3 &&\r\n                                    ` và ${\r\n                                      room.facilities.length - 3\r\n                                    } tiện nghi khác`}\r\n                                </small>\r\n                              </div>\r\n                            )}\r\n                            <div style={styles.serviceActions}>\r\n                              <Button\r\n                                size=\"sm\"\r\n                                variant=\"outline-primary\"\r\n                                onClick={() => handleEditRoom(room, index)}\r\n                                style={{ marginRight: \"5px\" }}\r\n                              >\r\n                                Sửa\r\n                              </Button>\r\n                              <Button\r\n                                size=\"sm\"\r\n                                variant=\"outline-danger\"\r\n                                onClick={() => handleDeleteRoom(index)}\r\n                              >\r\n                                Xóa\r\n                              </Button>\r\n                            </div>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    ))}\r\n                  </Row>\r\n                </div>\r\n              )}\r\n            </Col>\r\n            <Col xs=\"auto\">\r\n              <Button style={styles.actionButton} onClick={handleCreateRoom}>\r\n                + Thêm phòng\r\n              </Button>\r\n            </Col>\r\n          </Row>\r\n        </div>\r\n\r\n        {/* Step 4 */}\r\n        <div style={styles.checklistItem}>\r\n          <Row className=\"align-items-center\">\r\n            <Col xs=\"auto\">\r\n              <div style={styles.stepIcon}>\r\n                <span\r\n                  role=\"img\"\r\n                  aria-label=\"service\"\r\n                  style={{ fontSize: \"24px\" }}\r\n                >\r\n                  <i class=\"bi bi-people\"></i>\r\n                </span>\r\n              </div>\r\n            </Col>\r\n            <Col>\r\n              <div style={styles.stepNumber}>Bước 3</div>\r\n              <div style={styles.stepTitle}>Dịch vụ đi kèm</div>\r\n              <div style={styles.stepDescription}>\r\n                Những dịch vụ đi kèm với phòng của quý vị. Sau khi đã thiết lập\r\n                xong tối thiểu 1 phòng.\r\n                <br />\r\n                <span style={{ fontStyle: \"italic\", color: \"#888\" }}>\r\n                  * Có thể thêm sau đó\r\n                </span>\r\n              </div>\r\n\r\n              {/* Service List */}\r\n              {Array.isArray(createService) && createService.length > 0 && (\r\n                <div style={{ marginTop: \"15px\" }}>\r\n                  <h6>Danh sách dịch vụ đã tạo:</h6>\r\n                  <Row>\r\n                    {createService.map((service, index) => (\r\n                      <Col md={12} key={index} className=\"mb-2\">\r\n                        <Card style={styles.serviceCard}>\r\n                          <Card.Body style={{ padding: \"10px\" }}>\r\n                            <div style={styles.serviceHeader}>\r\n                              <h6 style={{ margin: 0, fontWeight: \"bold\" }}>\r\n                                Tên dịch vụ: {service.name}\r\n                              </h6>\r\n                              <Badge bg=\"primary\">\r\n                                {Utils.formatCurrency(service.price)}/\r\n                                {service.type}\r\n                              </Badge>\r\n                            </div>\r\n                            <p>\r\n                              <b>Mô tả: </b>\r\n                              {service.description}\r\n                            </p>\r\n                            <div style={styles.serviceActions}>\r\n                              <Button\r\n                                size=\"sm\"\r\n                                variant=\"outline-primary\"\r\n                                onClick={() =>\r\n                                  handleEditService(service, index)\r\n                                }\r\n                                style={{ marginRight: \"5px\" }}\r\n                              >\r\n                                Sửa\r\n                              </Button>\r\n                              <Button\r\n                                size=\"sm\"\r\n                                variant=\"outline-danger\"\r\n                                onClick={() => handleDeleteService(index)}\r\n                              >\r\n                                Xóa\r\n                              </Button>\r\n                            </div>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    ))}\r\n                  </Row>\r\n                </div>\r\n              )}\r\n            </Col>\r\n            <Col xs=\"auto\">\r\n              <Button style={styles.actionButton} onClick={handleCreateService}>\r\n                + Thêm dịch vụ\r\n              </Button>\r\n            </Col>\r\n          </Row>\r\n        </div>\r\n\r\n        {/* Confirmation Button */}\r\n        <div style={{ textAlign: \"right\", marginTop: 20 }}>\r\n          <Button\r\n            style={styles.confirmButton}\r\n            onClick={() => {\r\n              setShowModal(true);\r\n            }}\r\n          >\r\n            Xác nhận hoàn tất\r\n          </Button>\r\n        </div>\r\n        <ConfirmationModal\r\n          show={showModal}\r\n          onHide={() => setShowModal(false)}\r\n          onConfirm={handleComfirm}\r\n          title=\"Xác nhận tạo chỗ nghỉ\"\r\n          message=\"Bạn có chắc chắn muốn tạo chỗ nghỉ này không? Hành động này sẽ không thể hoàn tác.\"\r\n          confirmButtonText=\"Tạo chỗ nghỉ\"\r\n          type=\"warning\"\r\n        />\r\n\r\n        {/* Service Modal */}\r\n        <Modal\r\n          show={showServiceModal}\r\n          onHide={() => setShowServiceModal(false)}\r\n          size=\"lg\"\r\n        >\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>\r\n              {editingService !== null\r\n                ? \"Chỉnh sửa dịch vụ\"\r\n                : \"Thêm dịch vụ mới\"}\r\n            </Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>\r\n            <Form>\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Tên dịch vụ *</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  placeholder=\"Ví dụ: Bữa sáng, Buffet tối, Spa...\"\r\n                  value={currentService.name}\r\n                  onChange={(e) => handleInputChange(\"name\", e.target.value)}\r\n                />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Mô tả dịch vụ *</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  rows={3}\r\n                  placeholder=\"Nhập mô tả chi tiết về dịch vụ...\"\r\n                  value={currentService.description}\r\n                  onChange={(e) =>\r\n                    handleInputChange(\"description\", e.target.value)\r\n                  }\r\n                />\r\n              </Form.Group>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Giá dịch vụ ($) *</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      placeholder=\"Nhập giá dịch vụ\"\r\n                      value={formatPrice(currentService.price)}\r\n                      onChange={handlePriceChange}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Loại tính phí *</Form.Label>\r\n                    <Form.Select\r\n                      value={currentService.type}\r\n                      onChange={(e) =>\r\n                        handleInputChange(\"type\", e.target.value)\r\n                      }\r\n                    >\r\n                      {serviceTypes.map((type) => (\r\n                        <option key={type.value} value={type.value}>\r\n                          {type.label}\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n            </Form>\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n            <Button\r\n              variant=\"secondary\"\r\n              onClick={() => setShowServiceModal(false)}\r\n            >\r\n              Hủy\r\n            </Button>\r\n            <Button\r\n              variant=\"primary\"\r\n              onClick={handleSaveService}\r\n              disabled={\r\n                !currentService.name ||\r\n                !currentService.description ||\r\n                !currentService.price\r\n              }\r\n            >\r\n              {editingService !== null ? \"Lưu thay đổi\" : \"Thêm dịch vụ\"}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n\r\n        {/* Room Modal */}\r\n        <Room\r\n          show={showRoomModal}\r\n          handleClose={() => setShowRoomModal(false)}\r\n          onSave={handleSaveRoom}\r\n          editingRoom={editingRoom}\r\n        />\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Define styles as a constant object\r\nconst styles = {\r\n  // Main container styles\r\n  bookingApp: {\r\n    minHeight: \"100vh\",\r\n  },\r\n\r\n  // Navbar styles\r\n  navbarCustom: {\r\n    backgroundColor: \"#003580\",\r\n    padding: \"10px 0\",\r\n  },\r\n  navbarBrand: {\r\n    color: \"#fff\",\r\n    fontWeight: \"bold\",\r\n  },\r\n  userInfo: {\r\n    color: \"#fff\",\r\n    textAlign: \"right\",\r\n    marginRight: \"15px\",\r\n  },\r\n  userName: {\r\n    fontWeight: \"bold\",\r\n    fontSize: \"16px\",\r\n  },\r\n  userAddress: {\r\n    fontSize: \"12px\",\r\n  },\r\n  languageSelector: {\r\n    marginRight: \"15px\",\r\n  },\r\n  helpButton: {\r\n    color: \"#fff\",\r\n    textDecoration: \"none\",\r\n  },\r\n  helpIcon: {\r\n    display: \"inline-flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    width: \"24px\",\r\n    height: \"24px\",\r\n    backgroundColor: \"#fff\",\r\n    color: \"#003580\",\r\n    borderRadius: \"50%\",\r\n    fontWeight: \"bold\",\r\n  },\r\n  userIconCircle: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    width: \"32px\",\r\n    height: \"32px\",\r\n    backgroundColor: \"#fff\",\r\n    borderRadius: \"50%\",\r\n    marginLeft: \"10px\",\r\n  },\r\n\r\n  // Main content styles\r\n  mainContent: {\r\n    maxWidth: \"800px\",\r\n    margin: \"30px auto\",\r\n    backgroundColor: \"#fff\",\r\n    borderRadius: \"4px\",\r\n    boxShadow: \"0 1px 4px rgba(0, 0, 0, 0.05)\",\r\n  },\r\n\r\n  // Checklist item styles\r\n  checklistItem: {\r\n    padding: \"20px\",\r\n    borderBottom: \"1px solid #e7e7e7\",\r\n  },\r\n  checklistItemLast: {\r\n    padding: \"20px\",\r\n    borderBottom: \"none\",\r\n  },\r\n  stepNumber: {\r\n    fontSize: \"14px\",\r\n    color: \"#6b6b6b\",\r\n    marginBottom: \"5px\",\r\n  },\r\n  stepTitle: {\r\n    fontSize: \"18px\",\r\n    fontWeight: \"bold\",\r\n    marginBottom: \"5px\",\r\n  },\r\n  stepDescription: {\r\n    fontSize: \"14px\",\r\n    color: \"#6b6b6b\",\r\n  },\r\n  stepIcon: {\r\n    width: \"40px\",\r\n    height: \"40px\",\r\n    marginRight: \"15px\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n  },\r\n  completedIcon: {\r\n    color: \"#fff\",\r\n    backgroundColor: \"#008009\",\r\n    borderRadius: \"50%\",\r\n    width: \"30px\",\r\n    height: \"30px\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n  },\r\n  editLink: {\r\n    color: \"#0071c2\",\r\n    textDecoration: \"none\",\r\n    fontSize: \"14px\",\r\n    cursor: \"pointer\",\r\n  },\r\n  actionButton: {\r\n    backgroundColor: \"#0071c2\",\r\n    border: \"none\",\r\n    padding: \"8px 15px\",\r\n    fontWeight: \"bold\",\r\n  },\r\n  secondaryButton: {\r\n    backgroundColor: \"#f5f5f5\",\r\n    border: \"1px solid #e7e7e7\",\r\n    color: \"#333\",\r\n    padding: \"8px 15px\",\r\n  },\r\n  serviceCard: {\r\n    border: \"1px solid #e7e7e7\",\r\n    borderRadius: \"8px\",\r\n    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\r\n    transition: \"all 0.3s ease\",\r\n  },\r\n  serviceHeader: {\r\n    display: \"flex\",\r\n    justifyContent: \"space-between\",\r\n    alignItems: \"center\",\r\n    marginBottom: \"5px\",\r\n  },\r\n  serviceActions: {\r\n    display: \"flex\",\r\n    justifyContent: \"flex-end\",\r\n    marginTop: \"10px\",\r\n  },\r\n  confirmButton: {\r\n    backgroundColor: \"#0071c2\",\r\n    border: \"none\",\r\n    padding: \"10px 20px\",\r\n    fontWeight: \"bold\",\r\n    color: \"white\",\r\n  },\r\n};\r\n\r\nexport default BookingPropertyChecklist;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,IAAI,QACC,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,SAASC,wBAAwBA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAG5B,KAAK,CAAC6B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,KAAK,CAAC6B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACG,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,KAAK,CAAC6B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,KAAK,CAAC6B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGrC,KAAK,CAAC6B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,KAAK,CAAC6B,QAAQ,CAAC;IACzDW,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7C,KAAK,CAAC6B,QAAQ,CAAC;IACnDW,IAAI,EAAE,EAAE;IACRG,IAAI,EAAE,aAAa;IACnBG,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXN,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTM,GAAG,EAAE,EAAE;IACPC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGnC,cAAc,CAAEoC,KAAK,IAAKA,KAAK,CAACC,KAAK,CAACF,WAAW,CAAC;EACtE,MAAMG,aAAa,GAAGtC,cAAc,CAAEoC,KAAK,IAAKA,KAAK,CAACC,KAAK,CAACC,aAAa,CAAC;EAC1E,MAAMC,cAAc,GAAGvC,cAAc,CAAEoC,KAAK,IAAKA,KAAK,CAACjC,IAAI,CAACoC,cAAc,CAAC;EAC3E,MAAMC,UAAU,GAAGxC,cAAc,CAAEoC,KAAK,IAAKA,KAAK,CAACjC,IAAI,CAACqC,UAAU,CAAC;EACnEC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,UAAU,CAAC;EACrCC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEP,WAAW,CAAC;EACvCM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEJ,aAAa,CAAC;EAC3CG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,cAAc,CAAC;EAC7C,MAAMI,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAa,CAAC,EACxC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC3C;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAa,CAAC,EACtC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAY,CAAC,EACpC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAW,CAAC,EACrC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAa,CAAC,EACvC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAW,CAAC,CACrC;EAED,MAAMC,SAAS,GAAG,CAChB,aAAa,EACb,aAAa,EACb,aAAa,EACb,OAAO,EACP,UAAU,EACV,aAAa,CACd;EAED,MAAMC,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,KAAK,KAAK;IAC5C7B,iBAAiB,CAAC6B,KAAK,CAAC;IACxBzB,iBAAiB,CAAC;MAChB,GAAGwB,OAAO;MACVrB,KAAK,EAAEqB,OAAO,CAACrB,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,uBAAuB,EAAE,GAAG;IACtE,CAAC,CAAC;IACFnC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMoC,mBAAmB,GAAGA,CAAA,KAAM;IAChChC,iBAAiB,CAAC,IAAI,CAAC;IACvBV,QAAQ,CAACf,OAAO,CAAC0D,aAAa,CAAC;EACjC,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIL,KAAK,IAAK;IACrCtC,QAAQ,CAAC;MACPiB,IAAI,EAAE9B,YAAY,CAACyD,qBAAqB;MACxCC,OAAO,EAAE;QAAEP;MAAM;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAACC,KAAK,EAAEd,KAAK,KAAK;IAC1CpB,iBAAiB,CAAEmC,IAAI,KAAM;MAC3B,GAAGA,IAAI;MACP,CAACD,KAAK,GAAGd;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgB,WAAW,GAAIjC,KAAK,IAAK;IAC7B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB;IACA,MAAMkC,YAAY,GAAGlC,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACxD;IACA,OAAOU,YAAY,CAACV,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC;EAC3D,CAAC;EAED,MAAMW,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMnB,KAAK,GAAGmB,CAAC,CAACC,MAAM,CAACpB,KAAK;IAC5B;IACA,MAAMiB,YAAY,GAAGjB,KAAK,CAACO,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC7C3B,iBAAiB,CAAEmC,IAAI,KAAM;MAC3B,GAAGA,IAAI;MACPhC,KAAK,EAAEkC;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,IACE,CAAC1C,cAAc,CAACE,IAAI,IACpB,CAACF,cAAc,CAACG,WAAW,IAC3B,CAACH,cAAc,CAACI,KAAK,EACrB;MACA;IACF;IAEA,MAAMuC,WAAW,GAAG;MAClB,GAAG3C,cAAc;MACjBI,KAAK,EAAEwC,QAAQ,CAAC5C,cAAc,CAACI,KAAK,CAACwB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI;IAC9D,CAAC;IAED,IAAIhC,cAAc,KAAK,IAAI,EAAE;MAC3B;MACAR,QAAQ,CAAC;QACPiB,IAAI,EAAE9B,YAAY,CAACsE,mBAAmB;QACtCZ,OAAO,EAAE;UACPP,KAAK,EAAE9B,cAAc;UACrB+C;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAvD,QAAQ,CAAC;QACPiB,IAAI,EAAE9B,YAAY,CAACuE,2BAA2B;QAC9Cb,OAAO,EAAEU;MACX,CAAC,CAAC;IACJ;IAEAlD,mBAAmB,CAAC,KAAK,CAAC;IAC1BI,iBAAiB,CAAC,IAAI,CAAC;IACvBI,iBAAiB,CAAC;MAChBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0C,cAAc,GAAGA,CAACC,IAAI,EAAEtB,KAAK,KAAK;IACtC3B,cAAc,CAACiD,IAAI,CAAC;IACpBrD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMsD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9D,QAAQ,CAACf,OAAO,CAAC8E,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMC,cAAc,GAAIC,QAAQ,IAAK;IACnC,IAAItD,WAAW,EAAE;MACf;MACA,MAAMuD,SAAS,GAAGrC,cAAc,CAACsC,SAAS,CACvCN,IAAI,IAAKA,IAAI,KAAKlD,WACrB,CAAC;MACDV,QAAQ,CAAC;QACPiB,IAAI,EAAE7B,WAAW,CAAC+E,wBAAwB;QAC1CtB,OAAO,EAAE;UACPP,KAAK,EAAE2B,SAAS;UAChBD;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAhE,QAAQ,CAAC;QACPiB,IAAI,EAAE7B,WAAW,CAACgF,wBAAwB;QAC1CvB,OAAO,EAAEmB;MACX,CAAC,CAAC;IACJ;IACAzD,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAM8D,gBAAgB,GAAI/B,KAAK,IAAK;IAClCtC,QAAQ,CAAC;MACPiB,IAAI,EAAE7B,WAAW,CAACkF,4BAA4B;MAC9CzB,OAAO,EAAE;QAAEP;MAAM;IACnB,CAAC,CAAC;IACF7C,SAAS,CAAC8E,OAAO,CAAC,8BAA8B,CAAC;EACnD,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BxE,QAAQ,CAAC;MACPiB,IAAI,EAAE9B,YAAY,CAACsF,YAAY;MAC/B5B,OAAO,EAAE;QACPrB,WAAW,EAAEA,WAAW;QACxBI,cAAc,EAAEA,cAAc;QAC9BD,aAAa,EAAEA,aAAa;QAC5B+C,SAAS,EAAEA,CAAA,KAAM;UACfxE,YAAY,CAAC,KAAK,CAAC;QACrB;MACF;IACF,CAAC,CAAC;IACFF,QAAQ,CAAC;MAAEiB,IAAI,EAAE9B,YAAY,CAACwF;IAAmB,CAAC,CAAC;IACnD3E,QAAQ,CAAC;MAAEiB,IAAI,EAAE9B,YAAY,CAACyF;IAAqB,CAAC,CAAC;IACrD5E,QAAQ,CAAC;MAAEiB,IAAI,EAAE7B,WAAW,CAACyF;IAAuB,CAAC,CAAC;IACtD9E,QAAQ,CAACf,OAAO,CAAC8F,eAAe,CAAC;EACnC,CAAC;EAED,oBACElF,OAAA;IAAKmF,KAAK,EAAEC,MAAM,CAACC,UAAW;IAAAC,QAAA,gBAE5BtF,OAAA,CAACrB,MAAM;MAACwG,KAAK,EAAEC,MAAM,CAACG,YAAa;MAAAD,QAAA,eACjCtF,OAAA,CAACpB,SAAS;QAAA0G,QAAA,eACRtF,OAAA,CAACrB,MAAM,CAAC6G,KAAK;UAACC,IAAI,EAAC,OAAO;UAACC,SAAS,EAAC,oBAAoB;UAAAJ,QAAA,eACvDtF,OAAA;YAAGmF,KAAK,EAAE;cAAEQ,QAAQ,EAAE;YAAG,CAAE;YAAAL,QAAA,GAAC,IACxB,eAAAtF,OAAA;cAAMmF,KAAK,EAAE;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAN,QAAA,EAAC;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGThG,OAAA,CAACpB,SAAS;MAACuG,KAAK,EAAEC,MAAM,CAACa,WAAY;MAAAX,QAAA,gBAEnCtF,OAAA;QAAKmF,KAAK,EAAEC,MAAM,CAACc,aAAc;QAAAZ,QAAA,eAC/BtF,OAAA,CAAClB,GAAG;UAAC4G,SAAS,EAAC,oBAAoB;UAAAJ,QAAA,gBACjCtF,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAC,MAAM;YAAAb,QAAA,eACZtF,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACgB,QAAS;cAAAd,QAAA,eAC1BtF,OAAA;gBAAKmF,KAAK,EAAEC,MAAM,CAACiB,aAAc;gBAAAf,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhG,OAAA,CAACjB,GAAG;YAAAuG,QAAA,gBACFtF,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACkB,UAAW;cAAAhB,QAAA,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ChG,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACmB,SAAU;cAAAjB,QAAA,EAAC;YAAkB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtDhG,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACoB,eAAgB;cAAAlB,QAAA,EAAC;YAGpC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhG,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAC,MAAM;YAAAb,QAAA,eACZtF,OAAA;cACEyG,OAAO,EAAEA,CAAA,KAAM;gBACbrG,QAAQ,CAAC;kBACPiB,IAAI,EAAE9B,YAAY,CAACmH,6BAA6B;kBAChDzD,OAAO,EAAE;oBACP0D,gBAAgB,EAAE;kBACpB;gBACF,CAAC,CAAC;gBACFxG,QAAQ,CAACf,OAAO,CAACwH,mBAAmB,CAAC;cACvC,CAAE;cACFzB,KAAK,EAAEC,MAAM,CAACyB,QAAS;cAAAvB,QAAA,EACxB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAwCNhG,OAAA;QAAKmF,KAAK,EAAEC,MAAM,CAACc,aAAc;QAAAZ,QAAA,eAC/BtF,OAAA,CAAClB,GAAG;UAAC4G,SAAS,EAAC,oBAAoB;UAAAJ,QAAA,gBACjCtF,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAC,MAAM;YAAAb,QAAA,eACZtF,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACgB,QAAS;cAAAd,QAAA,eAC1BtF,OAAA;gBAAM8G,IAAI,EAAC,KAAK;gBAAC,cAAW,MAAM;gBAAC3B,KAAK,EAAE;kBAAEQ,QAAQ,EAAE;gBAAO,CAAE;gBAAAL,QAAA,EAAC;cAEhE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhG,OAAA,CAACjB,GAAG;YAAAuG,QAAA,gBACFtF,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACkB,UAAW;cAAAhB,QAAA,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ChG,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACmB,SAAU;cAAAjB,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzChG,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACoB,eAAgB;cAAAlB,QAAA,GAAC,yOAGlC,eAAAtF,OAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhG,OAAA;gBAAMmF,KAAK,EAAE;kBAAE4B,SAAS,EAAE,QAAQ;kBAAEnB,KAAK,EAAE;gBAAO,CAAE;gBAAAN,QAAA,EAAC;cAErD;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhG,OAAA,CAACF,aAAa;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAEhBgB,KAAK,CAACC,OAAO,CAACjF,cAAc,CAAC,IAAIA,cAAc,CAACkF,MAAM,GAAG,CAAC,iBACzDlH,OAAA;cAAKmF,KAAK,EAAE;gBAAEgC,SAAS,EAAE;cAAO,CAAE;cAAA7B,QAAA,gBAChCtF,OAAA;gBAAAsF,QAAA,EAAI;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChChG,OAAA,CAAClB,GAAG;gBAAAwG,QAAA,EACDtD,cAAc,CAACoF,GAAG,CAAC,CAACpD,IAAI,EAAEtB,KAAK,kBAC9B1C,OAAA,CAACjB,GAAG;kBAACsI,EAAE,EAAE,EAAG;kBAAa3B,SAAS,EAAC,MAAM;kBAAAJ,QAAA,eACvCtF,OAAA,CAAChB,IAAI;oBAACmG,KAAK,EAAEC,MAAM,CAACkC,WAAY;oBAAAhC,QAAA,eAC9BtF,OAAA,CAAChB,IAAI,CAACuI,IAAI;sBAACpC,KAAK,EAAE;wBAAEqC,OAAO,EAAE;sBAAO,CAAE;sBAAAlC,QAAA,gBACpCtF,OAAA;wBAAKmF,KAAK,EAAEC,MAAM,CAACqC,aAAc;wBAAAnC,QAAA,gBAC/BtF,OAAA;0BAAImF,KAAK,EAAE;4BAAEuC,MAAM,EAAE,CAAC;4BAAEC,UAAU,EAAE;0BAAO,CAAE;0BAAArC,QAAA,GAC1CtB,IAAI,CAAC9C,IAAI,EAAC,KAAG,EAAC8C,IAAI,CAAC3C,IAAI;wBAAA;0BAAAwE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACLhG,OAAA,CAACf,KAAK;0BAAC2I,EAAE,EAAC,SAAS;0BAAAtC,QAAA,GAChB3F,KAAK,CAACkI,cAAc,CAAC7D,IAAI,CAAC5C,KAAK,CAAC,EAAC,cACpC;wBAAA;0BAAAyE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNhG,OAAA,CAAClB,GAAG;wBAAC4G,SAAS,EAAC,MAAM;wBAAAJ,QAAA,gBACnBtF,OAAA,CAACjB,GAAG;0BAACsI,EAAE,EAAE,CAAE;0BAAA/B,QAAA,eACTtF,OAAA;4BAAAsF,QAAA,gBACEtF,OAAA;8BAAAsF,QAAA,EAAG;4BAAS;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC,KAAC,EAAChC,IAAI,CAACxC,QAAQ,EAAC,kBAClC;0BAAA;4BAAAqE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNhG,OAAA,CAACjB,GAAG;0BAACsI,EAAE,EAAE,CAAE;0BAAA/B,QAAA,eACTtF,OAAA;4BAAAsF,QAAA,gBACEtF,OAAA;8BAAAsF,QAAA,EAAG;4BAAS;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC,KAAC,EAAChC,IAAI,CAACvC,QAAQ,EAAC,WAClC;0BAAA;4BAAAoE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNhG,OAAA;wBAAAsF,QAAA,gBACEtF,OAAA;0BAAAsF,QAAA,EAAG;wBAAO;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,EACbhC,IAAI,CAAC7C,WAAW,CAAC+F,MAAM,GAAG,GAAG,GAC1B,GAAGlD,IAAI,CAAC7C,WAAW,CAAC2G,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAC1C9D,IAAI,CAAC7C,WAAW;sBAAA;wBAAA0E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,EACHhC,IAAI,CAACrC,UAAU,IAAIqC,IAAI,CAACrC,UAAU,CAACuF,MAAM,GAAG,CAAC,iBAC5ClH,OAAA;wBAAK0F,SAAS,EAAC,MAAM;wBAAAJ,QAAA,eACnBtF,OAAA;0BAAAsF,QAAA,gBACEtF,OAAA;4BAAAsF,QAAA,EAAG;0BAAU;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,EAAC,GAAG,EACpBhC,IAAI,CAACrC,UAAU,CAACoG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EACtChE,IAAI,CAACrC,UAAU,CAACuF,MAAM,GAAG,CAAC,IACzB,OACElD,IAAI,CAACrC,UAAU,CAACuF,MAAM,GAAG,CAAC,iBACX;wBAAA;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CACN,eACDhG,OAAA;wBAAKmF,KAAK,EAAEC,MAAM,CAAC6C,cAAe;wBAAA3C,QAAA,gBAChCtF,OAAA,CAACnB,MAAM;0BACLqJ,IAAI,EAAC,IAAI;0BACTC,OAAO,EAAC,iBAAiB;0BACzB1B,OAAO,EAAEA,CAAA,KAAM1C,cAAc,CAACC,IAAI,EAAEtB,KAAK,CAAE;0BAC3CyC,KAAK,EAAE;4BAAEiD,WAAW,EAAE;0BAAM,CAAE;0BAAA9C,QAAA,EAC/B;wBAED;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACThG,OAAA,CAACnB,MAAM;0BACLqJ,IAAI,EAAC,IAAI;0BACTC,OAAO,EAAC,gBAAgB;0BACxB1B,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC/B,KAAK,CAAE;0BAAA4C,QAAA,EACxC;wBAED;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC,GA3DStD,KAAK;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4DlB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNhG,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAC,MAAM;YAAAb,QAAA,eACZtF,OAAA,CAACnB,MAAM;cAACsG,KAAK,EAAEC,MAAM,CAACiD,YAAa;cAAC5B,OAAO,EAAExC,gBAAiB;cAAAqB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhG,OAAA;QAAKmF,KAAK,EAAEC,MAAM,CAACc,aAAc;QAAAZ,QAAA,eAC/BtF,OAAA,CAAClB,GAAG;UAAC4G,SAAS,EAAC,oBAAoB;UAAAJ,QAAA,gBACjCtF,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAC,MAAM;YAAAb,QAAA,eACZtF,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACgB,QAAS;cAAAd,QAAA,eAC1BtF,OAAA;gBACE8G,IAAI,EAAC,KAAK;gBACV,cAAW,SAAS;gBACpB3B,KAAK,EAAE;kBAAEQ,QAAQ,EAAE;gBAAO,CAAE;gBAAAL,QAAA,eAE5BtF,OAAA;kBAAGsI,KAAK,EAAC;gBAAc;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhG,OAAA,CAACjB,GAAG;YAAAuG,QAAA,gBACFtF,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACkB,UAAW;cAAAhB,QAAA,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ChG,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACmB,SAAU;cAAAjB,QAAA,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClDhG,OAAA;cAAKmF,KAAK,EAAEC,MAAM,CAACoB,eAAgB;cAAAlB,QAAA,GAAC,oKAGlC,eAAAtF,OAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhG,OAAA;gBAAMmF,KAAK,EAAE;kBAAE4B,SAAS,EAAE,QAAQ;kBAAEnB,KAAK,EAAE;gBAAO,CAAE;gBAAAN,QAAA,EAAC;cAErD;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAGLgB,KAAK,CAACC,OAAO,CAAClF,aAAa,CAAC,IAAIA,aAAa,CAACmF,MAAM,GAAG,CAAC,iBACvDlH,OAAA;cAAKmF,KAAK,EAAE;gBAAEgC,SAAS,EAAE;cAAO,CAAE;cAAA7B,QAAA,gBAChCtF,OAAA;gBAAAsF,QAAA,EAAI;cAAyB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClChG,OAAA,CAAClB,GAAG;gBAAAwG,QAAA,EACDvD,aAAa,CAACqF,GAAG,CAAC,CAAC3E,OAAO,EAAEC,KAAK,kBAChC1C,OAAA,CAACjB,GAAG;kBAACsI,EAAE,EAAE,EAAG;kBAAa3B,SAAS,EAAC,MAAM;kBAAAJ,QAAA,eACvCtF,OAAA,CAAChB,IAAI;oBAACmG,KAAK,EAAEC,MAAM,CAACkC,WAAY;oBAAAhC,QAAA,eAC9BtF,OAAA,CAAChB,IAAI,CAACuI,IAAI;sBAACpC,KAAK,EAAE;wBAAEqC,OAAO,EAAE;sBAAO,CAAE;sBAAAlC,QAAA,gBACpCtF,OAAA;wBAAKmF,KAAK,EAAEC,MAAM,CAACqC,aAAc;wBAAAnC,QAAA,gBAC/BtF,OAAA;0BAAImF,KAAK,EAAE;4BAAEuC,MAAM,EAAE,CAAC;4BAAEC,UAAU,EAAE;0BAAO,CAAE;0BAAArC,QAAA,GAAC,4BAC/B,EAAC7C,OAAO,CAACvB,IAAI;wBAAA;0BAAA2E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC,eACLhG,OAAA,CAACf,KAAK;0BAAC2I,EAAE,EAAC,SAAS;0BAAAtC,QAAA,GAChB3F,KAAK,CAACkI,cAAc,CAACpF,OAAO,CAACrB,KAAK,CAAC,EAAC,GACrC,EAACqB,OAAO,CAACpB,IAAI;wBAAA;0BAAAwE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNhG,OAAA;wBAAAsF,QAAA,gBACEtF,OAAA;0BAAAsF,QAAA,EAAG;wBAAO;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,EACbvD,OAAO,CAACtB,WAAW;sBAAA;wBAAA0E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACJhG,OAAA;wBAAKmF,KAAK,EAAEC,MAAM,CAAC6C,cAAe;wBAAA3C,QAAA,gBAChCtF,OAAA,CAACnB,MAAM;0BACLqJ,IAAI,EAAC,IAAI;0BACTC,OAAO,EAAC,iBAAiB;0BACzB1B,OAAO,EAAEA,CAAA,KACPjE,iBAAiB,CAACC,OAAO,EAAEC,KAAK,CACjC;0BACDyC,KAAK,EAAE;4BAAEiD,WAAW,EAAE;0BAAM,CAAE;0BAAA9C,QAAA,EAC/B;wBAED;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACThG,OAAA,CAACnB,MAAM;0BACLqJ,IAAI,EAAC,IAAI;0BACTC,OAAO,EAAC,gBAAgB;0BACxB1B,OAAO,EAAEA,CAAA,KAAM1D,mBAAmB,CAACL,KAAK,CAAE;0BAAA4C,QAAA,EAC3C;wBAED;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC,GApCStD,KAAK;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqClB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNhG,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAC,MAAM;YAAAb,QAAA,eACZtF,OAAA,CAACnB,MAAM;cAACsG,KAAK,EAAEC,MAAM,CAACiD,YAAa;cAAC5B,OAAO,EAAE5D,mBAAoB;cAAAyC,QAAA,EAAC;YAElE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhG,OAAA;QAAKmF,KAAK,EAAE;UAAEoD,SAAS,EAAE,OAAO;UAAEpB,SAAS,EAAE;QAAG,CAAE;QAAA7B,QAAA,eAChDtF,OAAA,CAACnB,MAAM;UACLsG,KAAK,EAAEC,MAAM,CAACoD,aAAc;UAC5B/B,OAAO,EAAEA,CAAA,KAAM;YACbnG,YAAY,CAAC,IAAI,CAAC;UACpB,CAAE;UAAAgF,QAAA,EACH;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNhG,OAAA,CAACN,iBAAiB;QAChB+I,IAAI,EAAEpI,SAAU;QAChBqI,MAAM,EAAEA,CAAA,KAAMpI,YAAY,CAAC,KAAK,CAAE;QAClCqI,SAAS,EAAE/D,aAAc;QACzBgE,KAAK,EAAC,8CAAuB;QAC7BC,OAAO,EAAC,mKAAoF;QAC5FC,iBAAiB,EAAC,6BAAc;QAChCzH,IAAI,EAAC;MAAS;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGFhG,OAAA,CAACd,KAAK;QACJuJ,IAAI,EAAEjI,gBAAiB;QACvBkI,MAAM,EAAEA,CAAA,KAAMjI,mBAAmB,CAAC,KAAK,CAAE;QACzCyH,IAAI,EAAC,IAAI;QAAA5C,QAAA,gBAETtF,OAAA,CAACd,KAAK,CAAC6J,MAAM;UAACC,WAAW;UAAA1D,QAAA,eACvBtF,OAAA,CAACd,KAAK,CAAC+J,KAAK;YAAA3D,QAAA,EACT1E,cAAc,KAAK,IAAI,GACpB,mBAAmB,GACnB;UAAkB;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACfhG,OAAA,CAACd,KAAK,CAACqI,IAAI;UAAAjC,QAAA,eACTtF,OAAA,CAACb,IAAI;YAAAmG,QAAA,gBACHtF,OAAA,CAACb,IAAI,CAAC+J,KAAK;cAACxD,SAAS,EAAC,MAAM;cAAAJ,QAAA,gBAC1BtF,OAAA,CAACb,IAAI,CAACgK,KAAK;gBAAA7D,QAAA,EAAC;cAAa;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtChG,OAAA,CAACb,IAAI,CAACiK,OAAO;gBACX/H,IAAI,EAAC,MAAM;gBACXgI,WAAW,EAAC,0DAAqC;gBACjDhH,KAAK,EAAErB,cAAc,CAACE,IAAK;gBAC3BoI,QAAQ,EAAG9F,CAAC,IAAKN,iBAAiB,CAAC,MAAM,EAAEM,CAAC,CAACC,MAAM,CAACpB,KAAK;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAEbhG,OAAA,CAACb,IAAI,CAAC+J,KAAK;cAACxD,SAAS,EAAC,MAAM;cAAAJ,QAAA,gBAC1BtF,OAAA,CAACb,IAAI,CAACgK,KAAK;gBAAA7D,QAAA,EAAC;cAAe;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxChG,OAAA,CAACb,IAAI,CAACiK,OAAO;gBACXG,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC,oEAAmC;gBAC/ChH,KAAK,EAAErB,cAAc,CAACG,WAAY;gBAClCmI,QAAQ,EAAG9F,CAAC,IACVN,iBAAiB,CAAC,aAAa,EAAEM,CAAC,CAACC,MAAM,CAACpB,KAAK;cAChD;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAEbhG,OAAA,CAAClB,GAAG;cAAAwG,QAAA,gBACFtF,OAAA,CAACjB,GAAG;gBAACsI,EAAE,EAAE,CAAE;gBAAA/B,QAAA,eACTtF,OAAA,CAACb,IAAI,CAAC+J,KAAK;kBAACxD,SAAS,EAAC,MAAM;kBAAAJ,QAAA,gBAC1BtF,OAAA,CAACb,IAAI,CAACgK,KAAK;oBAAA7D,QAAA,EAAC;kBAAiB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1ChG,OAAA,CAACb,IAAI,CAACiK,OAAO;oBACX/H,IAAI,EAAC,MAAM;oBACXgI,WAAW,EAAC,oCAAkB;oBAC9BhH,KAAK,EAAEgB,WAAW,CAACrC,cAAc,CAACI,KAAK,CAAE;oBACzCkI,QAAQ,EAAE/F;kBAAkB;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNhG,OAAA,CAACjB,GAAG;gBAACsI,EAAE,EAAE,CAAE;gBAAA/B,QAAA,eACTtF,OAAA,CAACb,IAAI,CAAC+J,KAAK;kBAACxD,SAAS,EAAC,MAAM;kBAAAJ,QAAA,gBAC1BtF,OAAA,CAACb,IAAI,CAACgK,KAAK;oBAAA7D,QAAA,EAAC;kBAAe;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxChG,OAAA,CAACb,IAAI,CAACsK,MAAM;oBACVpH,KAAK,EAAErB,cAAc,CAACK,IAAK;oBAC3BiI,QAAQ,EAAG9F,CAAC,IACVN,iBAAiB,CAAC,MAAM,EAAEM,CAAC,CAACC,MAAM,CAACpB,KAAK,CACzC;oBAAAiD,QAAA,EAEAlD,YAAY,CAACgF,GAAG,CAAE/F,IAAI,iBACrBrB,OAAA;sBAAyBqC,KAAK,EAAEhB,IAAI,CAACgB,KAAM;sBAAAiD,QAAA,EACxCjE,IAAI,CAACiB;oBAAK,GADAjB,IAAI,CAACgB,KAAK;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACbhG,OAAA,CAACd,KAAK,CAACwK,MAAM;UAAApE,QAAA,gBACXtF,OAAA,CAACnB,MAAM;YACLsJ,OAAO,EAAC,WAAW;YACnB1B,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAAC,KAAK,CAAE;YAAA6E,QAAA,EAC3C;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA,CAACnB,MAAM;YACLsJ,OAAO,EAAC,SAAS;YACjB1B,OAAO,EAAE/C,iBAAkB;YAC3BiG,QAAQ,EACN,CAAC3I,cAAc,CAACE,IAAI,IACpB,CAACF,cAAc,CAACG,WAAW,IAC3B,CAACH,cAAc,CAACI,KACjB;YAAAkE,QAAA,EAEA1E,cAAc,KAAK,IAAI,GAAG,cAAc,GAAG;UAAc;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRhG,OAAA,CAACJ,IAAI;QACH6I,IAAI,EAAE/H,aAAc;QACpBkJ,WAAW,EAAEA,CAAA,KAAMjJ,gBAAgB,CAAC,KAAK,CAAE;QAC3CkJ,MAAM,EAAE1F,cAAe;QACvBrD,WAAW,EAAEA;MAAY;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;;AAEA;AAAA9F,EAAA,CAplBSD,wBAAwB;EAAA,QACdZ,WAAW,EACXC,WAAW,EAuBRG,cAAc,EACZA,cAAc,EACbA,cAAc,EAClBA,cAAc;AAAA;AAAAqK,EAAA,GA5B1B7J,wBAAwB;AAqlBjC,MAAMmF,MAAM,GAAG;EACb;EACAC,UAAU,EAAE;IACV0E,SAAS,EAAE;EACb,CAAC;EAED;EACAxE,YAAY,EAAE;IACZyE,eAAe,EAAE,SAAS;IAC1BxC,OAAO,EAAE;EACX,CAAC;EACDyC,WAAW,EAAE;IACXrE,KAAK,EAAE,MAAM;IACb+B,UAAU,EAAE;EACd,CAAC;EACDuC,QAAQ,EAAE;IACRtE,KAAK,EAAE,MAAM;IACb2C,SAAS,EAAE,OAAO;IAClBH,WAAW,EAAE;EACf,CAAC;EACD+B,QAAQ,EAAE;IACRxC,UAAU,EAAE,MAAM;IAClBhC,QAAQ,EAAE;EACZ,CAAC;EACDyE,WAAW,EAAE;IACXzE,QAAQ,EAAE;EACZ,CAAC;EACD0E,gBAAgB,EAAE;IAChBjC,WAAW,EAAE;EACf,CAAC;EACDkC,UAAU,EAAE;IACV1E,KAAK,EAAE,MAAM;IACb2E,cAAc,EAAE;EAClB,CAAC;EACDC,QAAQ,EAAE;IACRC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdb,eAAe,EAAE,MAAM;IACvBpE,KAAK,EAAE,SAAS;IAChBkF,YAAY,EAAE,KAAK;IACnBnD,UAAU,EAAE;EACd,CAAC;EACDoD,cAAc,EAAE;IACdN,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdb,eAAe,EAAE,MAAM;IACvBc,YAAY,EAAE,KAAK;IACnBE,UAAU,EAAE;EACd,CAAC;EAED;EACA/E,WAAW,EAAE;IACXgF,QAAQ,EAAE,OAAO;IACjBvD,MAAM,EAAE,WAAW;IACnBsC,eAAe,EAAE,MAAM;IACvBc,YAAY,EAAE,KAAK;IACnBI,SAAS,EAAE;EACb,CAAC;EAED;EACAhF,aAAa,EAAE;IACbsB,OAAO,EAAE,MAAM;IACf2D,YAAY,EAAE;EAChB,CAAC;EACDC,iBAAiB,EAAE;IACjB5D,OAAO,EAAE,MAAM;IACf2D,YAAY,EAAE;EAChB,CAAC;EACD7E,UAAU,EAAE;IACVX,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,SAAS;IAChByF,YAAY,EAAE;EAChB,CAAC;EACD9E,SAAS,EAAE;IACTZ,QAAQ,EAAE,MAAM;IAChBgC,UAAU,EAAE,MAAM;IAClB0D,YAAY,EAAE;EAChB,CAAC;EACD7E,eAAe,EAAE;IACfb,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE;EACT,CAAC;EACDQ,QAAQ,EAAE;IACRwE,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdzC,WAAW,EAAE,MAAM;IACnBqC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDtE,aAAa,EAAE;IACbT,KAAK,EAAE,MAAM;IACboE,eAAe,EAAE,SAAS;IAC1Bc,YAAY,EAAE,KAAK;IACnBF,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdJ,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACD9D,QAAQ,EAAE;IACRjB,KAAK,EAAE,SAAS;IAChB2E,cAAc,EAAE,MAAM;IACtB5E,QAAQ,EAAE,MAAM;IAChB2F,MAAM,EAAE;EACV,CAAC;EACDjD,YAAY,EAAE;IACZ2B,eAAe,EAAE,SAAS;IAC1BuB,MAAM,EAAE,MAAM;IACd/D,OAAO,EAAE,UAAU;IACnBG,UAAU,EAAE;EACd,CAAC;EACD6D,eAAe,EAAE;IACfxB,eAAe,EAAE,SAAS;IAC1BuB,MAAM,EAAE,mBAAmB;IAC3B3F,KAAK,EAAE,MAAM;IACb4B,OAAO,EAAE;EACX,CAAC;EACDF,WAAW,EAAE;IACXiE,MAAM,EAAE,mBAAmB;IAC3BT,YAAY,EAAE,KAAK;IACnBI,SAAS,EAAE,2BAA2B;IACtCO,UAAU,EAAE;EACd,CAAC;EACDhE,aAAa,EAAE;IACbgD,OAAO,EAAE,MAAM;IACfE,cAAc,EAAE,eAAe;IAC/BD,UAAU,EAAE,QAAQ;IACpBW,YAAY,EAAE;EAChB,CAAC;EACDpD,cAAc,EAAE;IACdwC,OAAO,EAAE,MAAM;IACfE,cAAc,EAAE,UAAU;IAC1BxD,SAAS,EAAE;EACb,CAAC;EACDqB,aAAa,EAAE;IACbwB,eAAe,EAAE,SAAS;IAC1BuB,MAAM,EAAE,MAAM;IACd/D,OAAO,EAAE,WAAW;IACpBG,UAAU,EAAE,MAAM;IAClB/B,KAAK,EAAE;EACT;AACF,CAAC;AAED,eAAe3F,wBAAwB;AAAC,IAAA6J,EAAA;AAAA4B,YAAA,CAAA5B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}