{"ast": null, "code": "import { useLayoutEffect } from 'react';\nvar index = useLayoutEffect;\nexport { index as default };", "map": {"version": 3, "names": ["useLayoutEffect", "index", "default"], "sources": ["E:/WDP301_UROOM/Owner/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.browser.esm.js"], "sourcesContent": ["import { useLayoutEffect } from 'react';\n\nvar index = useLayoutEffect ;\n\nexport { index as default };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AAEvC,IAAIC,KAAK,GAAGD,eAAe;AAE3B,SAASC,KAAK,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}