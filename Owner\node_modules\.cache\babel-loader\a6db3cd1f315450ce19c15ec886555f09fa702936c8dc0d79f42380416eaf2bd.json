{"ast": null, "code": "import { combineReducers } from 'redux';\nimport AuthReducer from './auth/reducer';\nimport HotelReducer from './hotel/reducer';\nimport HotelservicesReducer from './Hotelservices/reducer';\nimport ReportedFeedbackReducer from \"./reportedFeedback/reducer\";\nimport ReservationReducer from \"./reservation/reducer\";\nimport FeedbackReducer from \"./feedback/reducer\";\nimport MonthlyPaymentReducer from \"./monthlyPayment/reducer\";\nimport messageReducer from './message/reducer';\nimport RoomUnitReducer from './room_unit/reducer';\nimport SocketReducer from './socket/socketSlice';\nimport BankInfoReducer from './bankInfo/reducer'; // Thêm import\n\nimport DashboardReducer from './dashboard/reducer';\nimport { Room } from '@utils/Routes';\nimport roomReducer from './room/reducer';\nconst rootReducer = combineReducers({\n  Auth: AuthReducer,\n  Hotel: HotelReducer,\n  Hotelservices: HotelservicesReducer,\n  Room: roomReducer,\n  Feedback: FeedbackReducer,\n  ReportedFeedback: ReportedFeedbackReducer,\n  Reservation: ReservationReducer,\n  MonthlyPayment: MonthlyPaymentReducer,\n  Message: messageReducer,\n  Socket: SocketReducer,\n  RoomUnit: RoomUnitReducer,\n  BankInfo: BankInfoReducer,\n  // Thêm vào đây\n  Dashboard: DashboardReducer\n});\nexport default rootReducer;", "map": {"version": 3, "names": ["combineReducers", "AuthReducer", "HotelReducer", "HotelservicesReducer", "ReportedFeedbackReducer", "ReservationReducer", "FeedbackReducer", "MonthlyPaymentReducer", "messageReducer", "RoomUnitReducer", "SocketReducer", "BankInfoReducer", "DashboardReducer", "Room", "roomReducer", "rootReducer", "<PERSON><PERSON>", "Hotel", "Hotelservices", "<PERSON><PERSON><PERSON>", "ReportedFeedback", "Reservation", "MonthlyPayment", "Message", "Socket", "RoomUnit", "BankInfo", "Dashboard"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/root-reducer.js"], "sourcesContent": ["import { combineReducers } from 'redux';\r\nimport AuthReducer from './auth/reducer';\r\nimport HotelReducer from './hotel/reducer';\r\nimport HotelservicesReducer from './Hotelservices/reducer';\r\nimport ReportedFeedbackReducer from \"./reportedFeedback/reducer\";\r\nimport ReservationReducer from \"./reservation/reducer\";\r\nimport FeedbackReducer from \"./feedback/reducer\";\r\nimport MonthlyPaymentReducer from \"./monthlyPayment/reducer\";\r\nimport messageReducer from './message/reducer';\r\nimport RoomUnitReducer from './room_unit/reducer';\r\nimport SocketReducer from './socket/socketSlice';\r\nimport BankInfoReducer from './bankInfo/reducer'; // Thêm import\r\n\r\nimport DashboardReducer from './dashboard/reducer';\r\nimport { Room } from '@utils/Routes';\r\nimport roomReducer from './room/reducer';\r\n\r\nconst rootReducer = combineReducers({\r\n    Auth: AuthReducer,\r\n    Hotel: HotelReducer,\r\n    Hotelservices: HotelservicesReducer,\r\n    Room: roomReducer,\r\n    Feedback: FeedbackReducer,\r\n    ReportedFeedback: ReportedFeedbackReducer,\r\n    Reservation: ReservationReducer,\r\n    MonthlyPayment: MonthlyPaymentReducer,\r\n    Message: messageReducer,\r\n    Socket: SocketReducer,\r\n    RoomUnit: RoomUnitReducer,\r\n    BankInfo: BankInfoReducer, // Thêm vào đây\r\n    Dashboard: DashboardReducer,\r\n});\r\n\r\nexport default rootReducer;\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,uBAAuB,MAAM,4BAA4B;AAChE,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,qBAAqB,MAAM,0BAA0B;AAC5D,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,eAAe,MAAM,qBAAqB;AACjD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,eAAe,MAAM,oBAAoB,CAAC,CAAC;;AAElD,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,SAASC,IAAI,QAAQ,eAAe;AACpC,OAAOC,WAAW,MAAM,gBAAgB;AAExC,MAAMC,WAAW,GAAGf,eAAe,CAAC;EAChCgB,IAAI,EAAEf,WAAW;EACjBgB,KAAK,EAAEf,YAAY;EACnBgB,aAAa,EAAEf,oBAAoB;EACnCU,IAAI,EAAEC,WAAW;EACjBK,QAAQ,EAAEb,eAAe;EACzBc,gBAAgB,EAAEhB,uBAAuB;EACzCiB,WAAW,EAAEhB,kBAAkB;EAC/BiB,cAAc,EAAEf,qBAAqB;EACrCgB,OAAO,EAAEf,cAAc;EACvBgB,MAAM,EAAEd,aAAa;EACrBe,QAAQ,EAAEhB,eAAe;EACzBiB,QAAQ,EAAEf,eAAe;EAAE;EAC3BgB,SAAS,EAAEf;AACf,CAAC,CAAC;AAEF,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}