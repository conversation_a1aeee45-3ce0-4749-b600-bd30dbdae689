{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  StaticGenBailoutError: null,\n  isStaticGenBailoutError: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  StaticGenBailoutError: function () {\n    return StaticGenBailoutError;\n  },\n  isStaticGenBailoutError: function () {\n    return isStaticGenBailoutError;\n  }\n});\nconst NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT';\nclass StaticGenBailoutError extends Error {\n  constructor(...args) {\n    super(...args), this.code = NEXT_STATIC_GEN_BAILOUT;\n  }\n}\nfunction isStaticGenBailoutError(error) {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false;\n  }\n  return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}", "map": {"version": 3, "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "args", "code", "error"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\client\\components\\static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "mappings": ";;;;;;;;;;;;;;;;EAEaA,qBAAqB,WAAAA,CAAA;WAArBA,qBAAA;;EAIGC,uBAAuB,WAAAA,CAAA;WAAvBA,uBAAA;;;AANhB,MAAMC,uBAAA,GAA0B;AAEzB,MAAMF,qBAAA,SAA8BG,KAAA;;IAApC,SAAAC,IAAA,QACWC,IAAA,GAAOH,uBAAA;;AACzB;AAEO,SAASD,wBACdK,KAAc;EAEd,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,QAAQ,EAAE,UAAUA,KAAI,GAAI;IACrE,OAAO;EACT;EAEA,OAAOA,KAAA,CAAMD,IAAI,KAAKH,uBAAA;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}