{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\login_register\\\\DocumentUpload.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Container, Form, Button, Card, ProgressBar, Alert, Row, Col, Navbar, Spinner } from \"react-bootstrap\";\nimport { Upload, CheckCircle, XCircle, X, FileEarmark } from \"react-bootstrap-icons\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport \"../../../css/hotelHost/DocumentUpload.css\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport Factories from \"@redux/hotel/factories\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction DocumentUpload() {\n  _s();\n  const navigate = useNavigate();\n\n  // State for uploaded documents (from server)\n  const [uploadedDocuments, setUploadedDocuments] = useState({\n    businessLicense: null,\n    fireSafety: null,\n    taxCertificate: null,\n    otherDocuments: []\n  });\n\n  // State for local preview files\n  const [localDocuments, setLocalDocuments] = useState({\n    businessLicense: null,\n    fireSafety: null,\n    taxCertificate: null,\n    otherDocuments: []\n  });\n  const [uploadProgress, setUploadProgress] = useState({\n    businessLicense: 0,\n    fireSafety: 0,\n    taxCertificate: 0,\n    otherDocuments: 0\n  });\n  const [uploadStatus, setUploadStatus] = useState({\n    businessLicense: \"\",\n    fireSafety: \"\",\n    taxCertificate: \"\",\n    otherDocuments: \"\"\n  });\n  console.log(\"uploadedDocuments:\", uploadedDocuments);\n  console.log(\"localDocuments:\", localDocuments);\n  console.log(\"uploadProgress:\", uploadProgress);\n  console.log(\"uploadStatus:\", uploadStatus);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Validate file type and size\n  const validateFile = file => {\n    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];\n    const maxSize = 10 * 1024 * 1024; // 10MB\n\n    if (!allowedTypes.includes(file.type)) {\n      showToast.error(\"Chỉ chấp nhận file PDF, JPG, PNG\");\n      return false;\n    }\n    if (file.size > maxSize) {\n      showToast.error(\"Kích thước file không được vượt quá 10MB\");\n      return false;\n    }\n    return true;\n  };\n  const handleFileChange = (event, documentType) => {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n    if (documentType === 'otherDocuments') {\n      // Handle multiple files for other documents\n      const validFiles = Array.from(files).filter(validateFile);\n      if (validFiles.length > 0) {\n        const filesWithPreview = validFiles.map(file => ({\n          file: file,\n          name: file.name,\n          size: file.size,\n          type: file.type,\n          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null,\n          isLocal: true\n        }));\n        setLocalDocuments(prev => ({\n          ...prev,\n          [documentType]: [...prev[documentType], ...filesWithPreview]\n        }));\n      }\n    } else {\n      // Handle single file for required documents\n      const file = files[0];\n      if (validateFile(file)) {\n        const fileObj = {\n          file: file,\n          name: file.name,\n          size: file.size,\n          type: file.type,\n          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null,\n          isLocal: true\n        };\n        setLocalDocuments(prev => ({\n          ...prev,\n          [documentType]: fileObj\n        }));\n\n        // Simulate upload progress for UI feedback\n        simulateUploadProgress(documentType);\n      }\n    }\n\n    // Reset input value\n    event.target.value = '';\n  };\n  const simulateUploadProgress = documentType => {\n    let progress = 0;\n    setUploadStatus(prev => ({\n      ...prev,\n      [documentType]: \"uploading\"\n    }));\n    const interval = setInterval(() => {\n      progress += 20;\n      setUploadProgress(prev => ({\n        ...prev,\n        [documentType]: progress\n      }));\n      if (progress >= 100) {\n        clearInterval(interval);\n        setUploadStatus(prev => ({\n          ...prev,\n          [documentType]: \"ready\"\n        }));\n      }\n    }, 200);\n  };\n\n  // Remove local document\n  const removeLocalDocument = (documentType, index = null) => {\n    if (documentType === 'otherDocuments' && index !== null) {\n      const docToRemove = localDocuments[documentType][index];\n      if (docToRemove.preview) {\n        URL.revokeObjectURL(docToRemove.preview);\n      }\n      setLocalDocuments(prev => ({\n        ...prev,\n        [documentType]: prev[documentType].filter((_, i) => i !== index)\n      }));\n    } else {\n      const docToRemove = localDocuments[documentType];\n      if (docToRemove && docToRemove.preview) {\n        URL.revokeObjectURL(docToRemove.preview);\n      }\n      setLocalDocuments(prev => ({\n        ...prev,\n        [documentType]: null\n      }));\n      setUploadProgress(prev => ({\n        ...prev,\n        [documentType]: 0\n      }));\n      setUploadStatus(prev => ({\n        ...prev,\n        [documentType]: \"\"\n      }));\n    }\n  };\n\n  // Remove uploaded document\n  const removeUploadedDocument = async (documentType, publicId, index = null) => {\n    try {\n      const response = await Factories.deleteHotelDocuments([publicId]);\n      if (response.data && !response.data.error) {\n        if (documentType === 'otherDocuments' && index !== null) {\n          setUploadedDocuments(prev => ({\n            ...prev,\n            [documentType]: prev[documentType].filter((_, i) => i !== index)\n          }));\n        } else {\n          setUploadedDocuments(prev => ({\n            ...prev,\n            [documentType]: null\n          }));\n        }\n        showToast.success(\"Đã xóa tài liệu thành công!\");\n      } else {\n        var _response$data;\n        throw new Error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || \"Không thể xóa tài liệu\");\n      }\n    } catch (error) {\n      console.error('Error deleting document:', error);\n      showToast.error(\"Có lỗi xảy ra khi xóa tài liệu: \" + error.message);\n    }\n  };\n\n  // Upload documents to server\n  const uploadDocuments = async () => {\n    try {\n      setIsUploading(true);\n      const uploadedDocs = {\n        ...uploadedDocuments\n      };\n\n      // Upload single documents\n      for (const docType of ['businessLicense', 'fireSafety', 'taxCertificate']) {\n        const localDoc = localDocuments[docType];\n        if (localDoc && localDoc.file) {\n          const formData = new FormData();\n          formData.append('document', localDoc.file);\n          formData.append('documentType', docType);\n          const response = await Factories.uploadHotelDocument(formData);\n          if (response.data && !response.data.error) {\n            uploadedDocs[docType] = response.data.data;\n            // Clean up local preview\n            if (localDoc.preview) {\n              URL.revokeObjectURL(localDoc.preview);\n            }\n          }\n        }\n      }\n\n      // Upload other documents\n      const otherDocs = localDocuments.otherDocuments;\n      if (otherDocs.length > 0) {\n        for (const doc of otherDocs) {\n          if (doc.file) {\n            const formData = new FormData();\n            formData.append('document', doc.file);\n            formData.append('documentType', 'otherDocuments');\n            const response = await Factories.uploadHotelDocument(formData);\n            if (response.data && !response.data.error) {\n              uploadedDocs.otherDocuments.push(response.data.data);\n              // Clean up local preview\n              if (doc.preview) {\n                URL.revokeObjectURL(doc.preview);\n              }\n            }\n          }\n        }\n      }\n      setUploadedDocuments(uploadedDocs);\n\n      // Clear local documents after successful upload\n      setLocalDocuments({\n        businessLicense: null,\n        fireSafety: null,\n        taxCertificate: null,\n        otherDocuments: []\n      });\n      showToast.success(\"Upload tài liệu thành công!\");\n      return true;\n    } catch (error) {\n      console.error('Error uploading documents:', error);\n      showToast.error(\"Có lỗi xảy ra khi upload tài liệu: \" + error.message);\n      return false;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n\n    // Check required documents\n    const requiredDocs = ['businessLicense', 'fireSafety', 'taxCertificate'];\n    const missingDocs = requiredDocs.filter(docType => !uploadedDocuments[docType] && !localDocuments[docType]);\n    if (missingDocs.length > 0) {\n      showToast.error(\"Vui lòng upload đầy đủ tài liệu bắt buộc\");\n      return;\n    }\n\n    // Upload any pending local documents\n    const hasLocalDocs = Object.values(localDocuments).some(doc => doc && (doc.file || Array.isArray(doc) && doc.length > 0));\n    if (hasLocalDocs) {\n      const uploadSuccess = await uploadDocuments();\n      if (!uploadSuccess) return;\n    }\n\n    // Navigate to next page\n    navigate('/BookingPropertyChecklist');\n  };\n  const renderUploadStatus = status => {\n    switch (status) {\n      case \"success\":\n      case \"ready\":\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"text-success ms-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 16\n        }, this);\n      case \"uploading\":\n        return /*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          size: \"sm\",\n          className: \"ms-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 16\n        }, this);\n      case \"error\":\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"text-danger ms-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const renderDocumentPreview = (document, documentType, index = null) => {\n    var _document$type, _document$url, _document$url2;\n    const isImage = ((_document$type = document.type) === null || _document$type === void 0 ? void 0 : _document$type.startsWith('image/')) || ((_document$url = document.url) === null || _document$url === void 0 ? void 0 : _document$url.includes('.jpg')) || ((_document$url2 = document.url) === null || _document$url2 === void 0 ? void 0 : _document$url2.includes('.png'));\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"document-preview mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-content\",\n        children: [isImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: document.preview || document.url,\n          alt: document.name,\n          className: \"preview-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-icon\",\n          children: /*#__PURE__*/_jsxDEV(FileEarmark, {\n            size: 48\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"document-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"document-name\",\n            children: document.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"document-size\",\n            children: document.size ? (document.size / 1024 / 1024).toFixed(2) + ' MB' : 'Unknown size'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), document.isLocal && /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-warning\",\n            children: \"Ch\\u01B0a upload\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), !document.isLocal && /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-success\",\n            children: \"\\u2713 \\u0110\\xE3 l\\u01B0u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"danger\",\n        size: \"sm\",\n        className: \"remove-btn\",\n        onClick: () => {\n          if (document.isLocal) {\n            removeLocalDocument(documentType, index);\n          } else {\n            removeUploadedDocument(documentType, document.public_ID, index);\n          }\n        },\n        title: \"X\\xF3a t\\xE0i li\\u1EC7u\",\n        children: /*#__PURE__*/_jsxDEV(X, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, index || 'single', true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this);\n  };\n  const getDocumentCount = documentType => {\n    const uploaded = uploadedDocuments[documentType];\n    const local = localDocuments[documentType];\n    if (documentType === 'otherDocuments') {\n      return ((uploaded === null || uploaded === void 0 ? void 0 : uploaded.length) || 0) + ((local === null || local === void 0 ? void 0 : local.length) || 0);\n    } else {\n      return (uploaded ? 1 : 0) + (local ? 1 : 0);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.bookingApp,\n    children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navbar, {\n      style: styles.navbarCustom,\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n          href: \"#home\",\n          className: \"text-white fw-bold\",\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            style: {\n              fontSize: 30\n            },\n            children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#f8e71c\"\n              },\n              children: \"OO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), \"M\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"bg-secondary text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-0\",\n            children: \"Upload T\\xE0i Li\\u1EC7u Kinh Doanh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [isUploading && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this), \"\\u0110ang upload t\\xE0i li\\u1EC7u...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            className: \"mb-4\",\n            children: \"Vui l\\xF2ng upload c\\xE1c t\\xE0i li\\u1EC7u c\\u1EA7n thi\\u1EBFt \\u0111\\u1EC3 ho\\xE0n t\\u1EA5t \\u0111\\u0103ng k\\xFD. T\\u1EA5t c\\u1EA3 t\\xE0i li\\u1EC7u n\\xEAn \\u1EDF \\u0111\\u1ECBnh d\\u1EA1ng PDF ho\\u1EB7c h\\xECnh \\u1EA3nh (JPG, PNG).\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              className: \"g-4\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100\",\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        className: \"fw-bold\",\n                        children: [\"Gi\\u1EA5y Ph\\xE9p Kinh Doanh\", /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-danger\",\n                          children: \"*\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 424,\n                          columnNumber: 27\n                        }, this), renderUploadStatus(uploadStatus.businessLicense), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-secondary ms-2\",\n                          children: [getDocumentCount('businessLicense'), \"/1\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 426,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"upload-box\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"file\",\n                          accept: \".pdf,.jpg,.jpeg,.png\",\n                          onChange: e => handleFileChange(e, \"businessLicense\"),\n                          disabled: isUploading || getDocumentCount('businessLicense') >= 1,\n                          style: {\n                            display: getDocumentCount('businessLicense') >= 1 ? 'none' : 'block'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 433,\n                          columnNumber: 27\n                        }, this), getDocumentCount('businessLicense') === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"upload-placeholder\",\n                          children: [/*#__PURE__*/_jsxDEV(Upload, {\n                            size: 24\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 442,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: \"K\\xE9o th\\u1EA3 ho\\u1EB7c nh\\u1EA5n \\u0111\\u1EC3 ch\\u1ECDn file\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 443,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 441,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 432,\n                        columnNumber: 25\n                      }, this), uploadProgress.businessLicense > 0 && uploadProgress.businessLicense < 100 && /*#__PURE__*/_jsxDEV(ProgressBar, {\n                        now: uploadProgress.businessLicense,\n                        label: `${uploadProgress.businessLicense}%`,\n                        className: \"mt-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 450,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"document-list mt-3\",\n                        children: [uploadedDocuments.businessLicense && renderDocumentPreview(uploadedDocuments.businessLicense, 'businessLicense'), localDocuments.businessLicense && renderDocumentPreview(localDocuments.businessLicense, 'businessLicense')]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100\",\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        className: \"fw-bold\",\n                        children: [\"Gi\\u1EA5y Ch\\u1EE9ng Nh\\u1EADn PCCC\", /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-danger\",\n                          children: \"*\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 476,\n                          columnNumber: 27\n                        }, this), renderUploadStatus(uploadStatus.fireSafety), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-secondary ms-2\",\n                          children: [getDocumentCount('fireSafety'), \"/1\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 478,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"upload-box\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"file\",\n                          accept: \".pdf,.jpg,.jpeg,.png\",\n                          onChange: e => handleFileChange(e, \"fireSafety\"),\n                          disabled: isUploading || getDocumentCount('fireSafety') >= 1,\n                          style: {\n                            display: getDocumentCount('fireSafety') >= 1 ? 'none' : 'block'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 484,\n                          columnNumber: 27\n                        }, this), getDocumentCount('fireSafety') === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"upload-placeholder\",\n                          children: [/*#__PURE__*/_jsxDEV(Upload, {\n                            size: 24\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 493,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: \"K\\xE9o th\\u1EA3 ho\\u1EB7c nh\\u1EA5n \\u0111\\u1EC3 ch\\u1ECDn file\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 494,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 492,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 25\n                      }, this), uploadProgress.fireSafety > 0 && uploadProgress.fireSafety < 100 && /*#__PURE__*/_jsxDEV(ProgressBar, {\n                        now: uploadProgress.fireSafety,\n                        label: `${uploadProgress.fireSafety}%`,\n                        className: \"mt-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 500,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"document-list mt-3\",\n                        children: [uploadedDocuments.fireSafety && renderDocumentPreview(uploadedDocuments.fireSafety, 'fireSafety'), localDocuments.fireSafety && renderDocumentPreview(localDocuments.fireSafety, 'fireSafety')]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 507,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100\",\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        className: \"fw-bold\",\n                        children: [\"Gi\\u1EA5y Ch\\u1EE9ng Nh\\u1EADn Thu\\u1EBF\", /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-danger\",\n                          children: \"*\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 525,\n                          columnNumber: 27\n                        }, this), renderUploadStatus(uploadStatus.taxCertificate), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-secondary ms-2\",\n                          children: [getDocumentCount('taxCertificate'), \"/1\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 527,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 523,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"upload-box\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"file\",\n                          accept: \".pdf,.jpg,.jpeg,.png\",\n                          onChange: e => handleFileChange(e, \"taxCertificate\"),\n                          disabled: isUploading || getDocumentCount('taxCertificate') >= 1,\n                          style: {\n                            display: getDocumentCount('taxCertificate') >= 1 ? 'none' : 'block'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 533,\n                          columnNumber: 27\n                        }, this), getDocumentCount('taxCertificate') === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"upload-placeholder\",\n                          children: [/*#__PURE__*/_jsxDEV(Upload, {\n                            size: 24\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 542,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: \"K\\xE9o th\\u1EA3 ho\\u1EB7c nh\\u1EA5n \\u0111\\u1EC3 ch\\u1ECDn file\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 543,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 541,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 532,\n                        columnNumber: 25\n                      }, this), uploadProgress.taxCertificate > 0 && uploadProgress.taxCertificate < 100 && /*#__PURE__*/_jsxDEV(ProgressBar, {\n                        now: uploadProgress.taxCertificate,\n                        label: `${uploadProgress.taxCertificate}%`,\n                        className: \"mt-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 549,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"document-list mt-3\",\n                        children: [uploadedDocuments.taxCertificate && renderDocumentPreview(uploadedDocuments.taxCertificate, 'taxCertificate'), localDocuments.taxCertificate && renderDocumentPreview(localDocuments.taxCertificate, 'taxCertificate')]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100\",\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        className: \"fw-bold\",\n                        children: [\"T\\xE0i Li\\u1EC7u Kh\\xE1c (T\\xF9y ch\\u1ECDn)\", renderUploadStatus(uploadStatus.otherDocuments), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-secondary ms-2\",\n                          children: [getDocumentCount('otherDocuments'), \" files\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 575,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 572,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"upload-box\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"file\",\n                          accept: \".pdf,.jpg,.jpeg,.png\",\n                          onChange: e => handleFileChange(e, \"otherDocuments\"),\n                          multiple: true,\n                          disabled: isUploading\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 581,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"upload-placeholder\",\n                          children: [/*#__PURE__*/_jsxDEV(Upload, {\n                            size: 24\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 589,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: \"K\\xE9o th\\u1EA3 ho\\u1EB7c nh\\u1EA5n \\u0111\\u1EC3 ch\\u1ECDn nhi\\u1EC1u file\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 590,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 588,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 580,\n                        columnNumber: 25\n                      }, this), uploadProgress.otherDocuments > 0 && uploadProgress.otherDocuments < 100 && /*#__PURE__*/_jsxDEV(ProgressBar, {\n                        now: uploadProgress.otherDocuments,\n                        label: `${uploadProgress.otherDocuments}%`,\n                        className: \"mt-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"document-list mt-3\",\n                        children: [uploadedDocuments.otherDocuments.map((doc, index) => renderDocumentPreview(doc, 'otherDocuments', index)), localDocuments.otherDocuments.map((doc, index) => renderDocumentPreview(doc, 'otherDocuments', index))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 602,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-secondary\",\n                onClick: () => navigate(-1),\n                disabled: isUploading,\n                children: \"Quay l\\u1EA1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                disabled: isUploading,\n                children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    animation: \"border\",\n                    size: \"sm\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 23\n                  }, this), \"\\u0110ang x\\u1EED l\\xFD...\"]\n                }, void 0, true) : \"Gửi Tài Liệu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .upload-box {\n          position: relative;\n          border: 2px dashed #ced4da;\n          border-radius: 8px;\n          padding: 20px;\n          text-align: center;\n          background-color: #f8f9fa;\n          transition: all 0.3s ease;\n        }\n\n        .upload-box:hover {\n          border-color: #0071c2;\n          background-color: #f0f8ff;\n        }\n\n        .upload-placeholder {\n          pointer-events: none;\n        }\n\n        .upload-placeholder p {\n          margin: 8px 0 0 0;\n          color: #6c757d;\n          font-size: 14px;\n        }\n\n        .document-preview {\n          display: flex;\n          align-items: center;\n          padding: 8px;\n          border: 1px solid #dee2e6;\n          border-radius: 6px;\n          background-color: #f8f9fa;\n          position: relative;\n        }\n\n        .preview-content {\n          display: flex;\n          align-items: center;\n          flex: 1;\n          gap: 12px;\n        }\n\n        .preview-image {\n          width: 60px;\n          height: 60px;\n          object-fit: cover;\n          border-radius: 4px;\n        }\n\n        .file-icon {\n          width: 60px;\n          height: 60px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background-color: #e9ecef;\n          border-radius: 4px;\n          color: #6c757d;\n        }\n\n        .document-info {\n          flex: 1;\n        }\n\n        .document-name {\n          margin: 0;\n          font-weight: 500;\n          font-size: 14px;\n          color: #333;\n          word-break: break-word;\n        }\n\n        .document-size {\n          margin: 4px 0 0 0;\n          font-size: 12px;\n          color: #6c757d;\n        }\n\n        .remove-btn {\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 0;\n          margin-left: 8px;\n        }\n\n        .document-list {\n          max-height: 300px;\n          overflow-y: auto;\n        }\n\n        @media (max-width: 768px) {\n          .upload-box {\n            padding: 16px;\n          }\n          \n          .preview-image,\n          .file-icon {\n            width: 48px;\n            height: 48px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 381,\n    columnNumber: 5\n  }, this);\n}\n_s(DocumentUpload, \"ohxxNkdHLBIsK0I2C8x9dwQFSBg=\", false, function () {\n  return [useNavigate];\n});\n_c = DocumentUpload;\nconst styles = {\n  bookingApp: {\n    minHeight: \"100vh\",\n    backgroundColor: \"#f8f9fa\"\n  },\n  navbarCustom: {\n    backgroundColor: \"#003580\",\n    padding: \"10px 0\"\n  }\n};\nexport default DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");", "map": {"version": 3, "names": ["useState", "Container", "Form", "<PERSON><PERSON>", "Card", "ProgressBar", "<PERSON><PERSON>", "Row", "Col", "<PERSON><PERSON><PERSON>", "Spinner", "Upload", "CheckCircle", "XCircle", "X", "FileEarmark", "Routers", "useNavigate", "showToast", "ToastProvider", "Factories", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DocumentUpload", "_s", "navigate", "uploadedDocuments", "setUploadedDocuments", "businessLicense", "fireSafety", "taxCertificate", "otherDocuments", "localDocuments", "setLocalDocuments", "uploadProgress", "setUploadProgress", "uploadStatus", "setUploadStatus", "console", "log", "isUploading", "setIsUploading", "validateFile", "file", "allowedTypes", "maxSize", "includes", "type", "error", "size", "handleFileChange", "event", "documentType", "files", "target", "length", "validFiles", "Array", "from", "filter", "filesWithPreview", "map", "name", "preview", "startsWith", "URL", "createObjectURL", "isLocal", "prev", "fileObj", "simulateUploadProgress", "value", "progress", "interval", "setInterval", "clearInterval", "removeLocalDocument", "index", "doc<PERSON>o<PERSON>emove", "revokeObjectURL", "_", "i", "removeUploadedDocument", "publicId", "response", "deleteHotelDocuments", "data", "success", "_response$data", "Error", "message", "uploadDocuments", "uploadedDocs", "docType", "localDoc", "formData", "FormData", "append", "uploadHotelDocument", "otherDocs", "doc", "push", "handleSubmit", "preventDefault", "requiredDocs", "missingDocs", "hasLocalDocs", "Object", "values", "some", "isArray", "uploadSuccess", "renderUploadStatus", "status", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "animation", "renderDocumentPreview", "document", "_document$type", "_document$url", "_document$url2", "isImage", "url", "children", "src", "alt", "toFixed", "variant", "onClick", "public_ID", "title", "getDocumentCount", "uploaded", "local", "style", "styles", "bookingApp", "navbarCustom", "Brand", "href", "fontSize", "color", "Header", "Body", "onSubmit", "md", "Group", "Label", "Control", "accept", "onChange", "e", "disabled", "display", "now", "label", "multiple", "jsx", "_c", "minHeight", "backgroundColor", "padding", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/login_register/DocumentUpload.jsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport {\r\n  Contain<PERSON>,\r\n  <PERSON>,\r\n  <PERSON><PERSON>,\r\n  Card,\r\n  ProgressBar,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Col,\r\n  Nav<PERSON>,\r\n  Spinner,\r\n} from \"react-bootstrap\";\r\nimport { Upload, CheckCircle, XCircle, X, FileEarmark } from \"react-bootstrap-icons\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport \"../../../css/hotelHost/DocumentUpload.css\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport Factories from \"@redux/hotel/factories\";\r\n\r\nfunction DocumentUpload() {\r\n  const navigate = useNavigate();\r\n  \r\n  // State for uploaded documents (from server)\r\n  const [uploadedDocuments, setUploadedDocuments] = useState({\r\n    businessLicense: null,\r\n    fireSafety: null,\r\n    taxCertificate: null,\r\n    otherDocuments: [],\r\n  });\r\n\r\n  // State for local preview files\r\n  const [localDocuments, setLocalDocuments] = useState({\r\n    businessLicense: null,\r\n    fireSafety: null,\r\n    taxCertificate: null,\r\n    otherDocuments: [],\r\n  });\r\n\r\n  const [uploadProgress, setUploadProgress] = useState({\r\n    businessLicense: 0,\r\n    fireSafety: 0,\r\n    taxCertificate: 0,\r\n    otherDocuments: 0,\r\n  });\r\n\r\n  const [uploadStatus, setUploadStatus] = useState({\r\n    businessLicense: \"\",\r\n    fireSafety: \"\",\r\n    taxCertificate: \"\",\r\n    otherDocuments: \"\",\r\n  });\r\n\r\n  console.log(\"uploadedDocuments:\", uploadedDocuments);\r\n  console.log(\"localDocuments:\", localDocuments);\r\n  console.log(\"uploadProgress:\", uploadProgress);\r\n  console.log(\"uploadStatus:\", uploadStatus);\r\n\r\n  const [isUploading, setIsUploading] = useState(false);\r\n\r\n  // Validate file type and size\r\n  const validateFile = (file) => {\r\n    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];\r\n    const maxSize = 10 * 1024 * 1024; // 10MB\r\n\r\n    if (!allowedTypes.includes(file.type)) {\r\n      showToast.error(\"Chỉ chấp nhận file PDF, JPG, PNG\");\r\n      return false;\r\n    }\r\n\r\n    if (file.size > maxSize) {\r\n      showToast.error(\"Kích thước file không được vượt quá 10MB\");\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const handleFileChange = (event, documentType) => {\r\n    const files = event.target.files;\r\n    \r\n    if (!files || files.length === 0) return;\r\n\r\n    if (documentType === 'otherDocuments') {\r\n      // Handle multiple files for other documents\r\n      const validFiles = Array.from(files).filter(validateFile);\r\n      \r\n      if (validFiles.length > 0) {\r\n        const filesWithPreview = validFiles.map(file => ({\r\n          file: file,\r\n          name: file.name,\r\n          size: file.size,\r\n          type: file.type,\r\n          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null,\r\n          isLocal: true\r\n        }));\r\n\r\n        setLocalDocuments(prev => ({\r\n          ...prev,\r\n          [documentType]: [...prev[documentType], ...filesWithPreview]\r\n        }));\r\n      }\r\n    } else {\r\n      // Handle single file for required documents\r\n      const file = files[0];\r\n      \r\n      if (validateFile(file)) {\r\n        const fileObj = {\r\n          file: file,\r\n          name: file.name,\r\n          size: file.size,\r\n          type: file.type,\r\n          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null,\r\n          isLocal: true\r\n        };\r\n\r\n        setLocalDocuments(prev => ({\r\n          ...prev,\r\n          [documentType]: fileObj\r\n        }));\r\n\r\n        // Simulate upload progress for UI feedback\r\n        simulateUploadProgress(documentType);\r\n      }\r\n    }\r\n\r\n    // Reset input value\r\n    event.target.value = '';\r\n  };\r\n\r\n  const simulateUploadProgress = (documentType) => {\r\n    let progress = 0;\r\n    setUploadStatus(prev => ({ ...prev, [documentType]: \"uploading\" }));\r\n    \r\n    const interval = setInterval(() => {\r\n      progress += 20;\r\n      setUploadProgress(prev => ({\r\n        ...prev,\r\n        [documentType]: progress,\r\n      }));\r\n\r\n      if (progress >= 100) {\r\n        clearInterval(interval);\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [documentType]: \"ready\",\r\n        }));\r\n      }\r\n    }, 200);\r\n  };\r\n\r\n  // Remove local document\r\n  const removeLocalDocument = (documentType, index = null) => {\r\n    if (documentType === 'otherDocuments' && index !== null) {\r\n      const docToRemove = localDocuments[documentType][index];\r\n      if (docToRemove.preview) {\r\n        URL.revokeObjectURL(docToRemove.preview);\r\n      }\r\n      \r\n      setLocalDocuments(prev => ({\r\n        ...prev,\r\n        [documentType]: prev[documentType].filter((_, i) => i !== index)\r\n      }));\r\n    } else {\r\n      const docToRemove = localDocuments[documentType];\r\n      if (docToRemove && docToRemove.preview) {\r\n        URL.revokeObjectURL(docToRemove.preview);\r\n      }\r\n      \r\n      setLocalDocuments(prev => ({\r\n        ...prev,\r\n        [documentType]: null\r\n      }));\r\n      \r\n      setUploadProgress(prev => ({ ...prev, [documentType]: 0 }));\r\n      setUploadStatus(prev => ({ ...prev, [documentType]: \"\" }));\r\n    }\r\n  };\r\n\r\n  // Remove uploaded document\r\n  const removeUploadedDocument = async (documentType, publicId, index = null) => {\r\n    try {\r\n      const response = await Factories.deleteHotelDocuments([publicId]);\r\n      \r\n      if (response.data && !response.data.error) {\r\n        if (documentType === 'otherDocuments' && index !== null) {\r\n          setUploadedDocuments(prev => ({\r\n            ...prev,\r\n            [documentType]: prev[documentType].filter((_, i) => i !== index)\r\n          }));\r\n        } else {\r\n          setUploadedDocuments(prev => ({\r\n            ...prev,\r\n            [documentType]: null\r\n          }));\r\n        }\r\n        \r\n        showToast.success(\"Đã xóa tài liệu thành công!\");\r\n      } else {\r\n        throw new Error(response.data?.message || \"Không thể xóa tài liệu\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting document:', error);\r\n      showToast.error(\"Có lỗi xảy ra khi xóa tài liệu: \" + error.message);\r\n    }\r\n  };\r\n\r\n  // Upload documents to server\r\n  const uploadDocuments = async () => {\r\n    try {\r\n      setIsUploading(true);\r\n      const uploadedDocs = { ...uploadedDocuments };\r\n\r\n      // Upload single documents\r\n      for (const docType of ['businessLicense', 'fireSafety', 'taxCertificate']) {\r\n        const localDoc = localDocuments[docType];\r\n        if (localDoc && localDoc.file) {\r\n          const formData = new FormData();\r\n          formData.append('document', localDoc.file);\r\n          formData.append('documentType', docType);\r\n\r\n          const response = await Factories.uploadHotelDocument(formData);\r\n          \r\n          if (response.data && !response.data.error) {\r\n            uploadedDocs[docType] = response.data.data;\r\n            // Clean up local preview\r\n            if (localDoc.preview) {\r\n              URL.revokeObjectURL(localDoc.preview);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Upload other documents\r\n      const otherDocs = localDocuments.otherDocuments;\r\n      if (otherDocs.length > 0) {\r\n        for (const doc of otherDocs) {\r\n          if (doc.file) {\r\n            const formData = new FormData();\r\n            formData.append('document', doc.file);\r\n            formData.append('documentType', 'otherDocuments');\r\n\r\n            const response = await Factories.uploadHotelDocument(formData);\r\n            \r\n            if (response.data && !response.data.error) {\r\n              uploadedDocs.otherDocuments.push(response.data.data);\r\n              // Clean up local preview\r\n              if (doc.preview) {\r\n                URL.revokeObjectURL(doc.preview);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      setUploadedDocuments(uploadedDocs);\r\n      \r\n      // Clear local documents after successful upload\r\n      setLocalDocuments({\r\n        businessLicense: null,\r\n        fireSafety: null,\r\n        taxCertificate: null,\r\n        otherDocuments: [],\r\n      });\r\n\r\n      showToast.success(\"Upload tài liệu thành công!\");\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error uploading documents:', error);\r\n      showToast.error(\"Có lỗi xảy ra khi upload tài liệu: \" + error.message);\r\n      return false;\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n\r\n    // Check required documents\r\n    const requiredDocs = ['businessLicense', 'fireSafety', 'taxCertificate'];\r\n    const missingDocs = requiredDocs.filter(docType => \r\n      !uploadedDocuments[docType] && !localDocuments[docType]\r\n    );\r\n\r\n    if (missingDocs.length > 0) {\r\n      showToast.error(\"Vui lòng upload đầy đủ tài liệu bắt buộc\");\r\n      return;\r\n    }\r\n\r\n    // Upload any pending local documents\r\n    const hasLocalDocs = Object.values(localDocuments).some(doc => \r\n      doc && (doc.file || (Array.isArray(doc) && doc.length > 0))\r\n    );\r\n\r\n    if (hasLocalDocs) {\r\n      const uploadSuccess = await uploadDocuments();\r\n      if (!uploadSuccess) return;\r\n    }\r\n\r\n    // Navigate to next page\r\n    navigate('/BookingPropertyChecklist');\r\n  };\r\n\r\n  const renderUploadStatus = (status) => {\r\n    switch (status) {\r\n      case \"success\":\r\n      case \"ready\":\r\n        return <CheckCircle className=\"text-success ms-2\" />;\r\n      case \"uploading\":\r\n        return <Spinner animation=\"border\" size=\"sm\" className=\"ms-2\" />;\r\n      case \"error\":\r\n        return <XCircle className=\"text-danger ms-2\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const renderDocumentPreview = (document, documentType, index = null) => {\r\n    const isImage = document.type?.startsWith('image/') || document.url?.includes('.jpg') || document.url?.includes('.png');\r\n    \r\n    return (\r\n      <div className=\"document-preview mb-2\" key={index || 'single'}>\r\n        <div className=\"preview-content\">\r\n          {isImage ? (\r\n            <img \r\n              src={document.preview || document.url} \r\n              alt={document.name}\r\n              className=\"preview-image\"\r\n            />\r\n          ) : (\r\n            <div className=\"file-icon\">\r\n              <FileEarmark size={48} />\r\n            </div>\r\n          )}\r\n          <div className=\"document-info\">\r\n            <p className=\"document-name\">{document.name}</p>\r\n            <p className=\"document-size\">\r\n              {document.size ? (document.size / 1024 / 1024).toFixed(2) + ' MB' : 'Unknown size'}\r\n            </p>\r\n            {document.isLocal && (\r\n              <small className=\"text-warning\">Chưa upload</small>\r\n            )}\r\n            {!document.isLocal && (\r\n              <small className=\"text-success\">✓ Đã lưu</small>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <Button\r\n          variant=\"danger\"\r\n          size=\"sm\"\r\n          className=\"remove-btn\"\r\n          onClick={() => {\r\n            if (document.isLocal) {\r\n              removeLocalDocument(documentType, index);\r\n            } else {\r\n              removeUploadedDocument(documentType, document.public_ID, index);\r\n            }\r\n          }}\r\n          title=\"Xóa tài liệu\"\r\n        >\r\n          <X size={16} />\r\n        </Button>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const getDocumentCount = (documentType) => {\r\n    const uploaded = uploadedDocuments[documentType];\r\n    const local = localDocuments[documentType];\r\n    \r\n    if (documentType === 'otherDocuments') {\r\n      return (uploaded?.length || 0) + (local?.length || 0);\r\n    } else {\r\n      return (uploaded ? 1 : 0) + (local ? 1 : 0);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={styles.bookingApp}>\r\n      <ToastProvider />\r\n      \r\n      {/* Navigation Bar */}\r\n      <Navbar style={styles.navbarCustom}>\r\n        <Container>\r\n          <Navbar.Brand href=\"#home\" className=\"text-white fw-bold\">\r\n            <b style={{ fontSize: 30 }}>\r\n              UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n            </b>\r\n          </Navbar.Brand>\r\n        </Container>\r\n      </Navbar>\r\n\r\n      <Container className=\"py-5\">\r\n        <Card className=\"shadow-sm\">\r\n          <Card.Header className=\"bg-secondary text-white\">\r\n            <h4 className=\"mb-0\">Upload Tài Liệu Kinh Doanh</h4>\r\n          </Card.Header>\r\n          <Card.Body>\r\n            {isUploading && (\r\n              <Alert variant=\"info\" className=\"mb-4\">\r\n                <div className=\"d-flex align-items-center\">\r\n                  <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                  Đang upload tài liệu...\r\n                </div>\r\n              </Alert>\r\n            )}\r\n\r\n            <Alert variant=\"info\" className=\"mb-4\">\r\n              Vui lòng upload các tài liệu cần thiết để hoàn tất đăng ký. \r\n              Tất cả tài liệu nên ở định dạng PDF hoặc hình ảnh (JPG, PNG).\r\n            </Alert>\r\n\r\n            <Form onSubmit={handleSubmit}>\r\n              <Row className=\"g-4\">\r\n                {/* Business License */}\r\n                <Col md={6}>\r\n                  <Card className=\"h-100\">\r\n                    <Card.Body>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"fw-bold\">\r\n                          Giấy Phép Kinh Doanh\r\n                          <span className=\"text-danger\">*</span>\r\n                          {renderUploadStatus(uploadStatus.businessLicense)}\r\n                          <span className=\"badge bg-secondary ms-2\">\r\n                            {getDocumentCount('businessLicense')}/1\r\n                          </span>\r\n                        </Form.Label>\r\n\r\n                        {/* File Input */}\r\n                        <div className=\"upload-box\">\r\n                          <Form.Control\r\n                            type=\"file\"\r\n                            accept=\".pdf,.jpg,.jpeg,.png\"\r\n                            onChange={(e) => handleFileChange(e, \"businessLicense\")}\r\n                            disabled={isUploading || getDocumentCount('businessLicense') >= 1}\r\n                            style={{ display: getDocumentCount('businessLicense') >= 1 ? 'none' : 'block' }}\r\n                          />\r\n                          {getDocumentCount('businessLicense') === 0 && (\r\n                            <div className=\"upload-placeholder\">\r\n                              <Upload size={24} />\r\n                              <p>Kéo thả hoặc nhấn để chọn file</p>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n\r\n                        {/* Progress Bar */}\r\n                        {uploadProgress.businessLicense > 0 && uploadProgress.businessLicense < 100 && (\r\n                          <ProgressBar\r\n                            now={uploadProgress.businessLicense}\r\n                            label={`${uploadProgress.businessLicense}%`}\r\n                            className=\"mt-2\"\r\n                          />\r\n                        )}\r\n\r\n                        {/* Document Previews */}\r\n                        <div className=\"document-list mt-3\">\r\n                          {uploadedDocuments.businessLicense && \r\n                            renderDocumentPreview(uploadedDocuments.businessLicense, 'businessLicense')}\r\n                          {localDocuments.businessLicense && \r\n                            renderDocumentPreview(localDocuments.businessLicense, 'businessLicense')}\r\n                        </div>\r\n                      </Form.Group>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n\r\n                {/* Fire Safety Certificate */}\r\n                <Col md={6}>\r\n                  <Card className=\"h-100\">\r\n                    <Card.Body>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"fw-bold\">\r\n                          Giấy Chứng Nhận PCCC\r\n                          <span className=\"text-danger\">*</span>\r\n                          {renderUploadStatus(uploadStatus.fireSafety)}\r\n                          <span className=\"badge bg-secondary ms-2\">\r\n                            {getDocumentCount('fireSafety')}/1\r\n                          </span>\r\n                        </Form.Label>\r\n\r\n                        <div className=\"upload-box\">\r\n                          <Form.Control\r\n                            type=\"file\"\r\n                            accept=\".pdf,.jpg,.jpeg,.png\"\r\n                            onChange={(e) => handleFileChange(e, \"fireSafety\")}\r\n                            disabled={isUploading || getDocumentCount('fireSafety') >= 1}\r\n                            style={{ display: getDocumentCount('fireSafety') >= 1 ? 'none' : 'block' }}\r\n                          />\r\n                          {getDocumentCount('fireSafety') === 0 && (\r\n                            <div className=\"upload-placeholder\">\r\n                              <Upload size={24} />\r\n                              <p>Kéo thả hoặc nhấn để chọn file</p>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n\r\n                        {uploadProgress.fireSafety > 0 && uploadProgress.fireSafety < 100 && (\r\n                          <ProgressBar\r\n                            now={uploadProgress.fireSafety}\r\n                            label={`${uploadProgress.fireSafety}%`}\r\n                            className=\"mt-2\"\r\n                          />\r\n                        )}\r\n\r\n                        <div className=\"document-list mt-3\">\r\n                          {uploadedDocuments.fireSafety && \r\n                            renderDocumentPreview(uploadedDocuments.fireSafety, 'fireSafety')}\r\n                          {localDocuments.fireSafety && \r\n                            renderDocumentPreview(localDocuments.fireSafety, 'fireSafety')}\r\n                        </div>\r\n                      </Form.Group>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n\r\n                {/* Tax Certificate */}\r\n                <Col md={6}>\r\n                  <Card className=\"h-100\">\r\n                    <Card.Body>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"fw-bold\">\r\n                          Giấy Chứng Nhận Thuế\r\n                          <span className=\"text-danger\">*</span>\r\n                          {renderUploadStatus(uploadStatus.taxCertificate)}\r\n                          <span className=\"badge bg-secondary ms-2\">\r\n                            {getDocumentCount('taxCertificate')}/1\r\n                          </span>\r\n                        </Form.Label>\r\n\r\n                        <div className=\"upload-box\">\r\n                          <Form.Control\r\n                            type=\"file\"\r\n                            accept=\".pdf,.jpg,.jpeg,.png\"\r\n                            onChange={(e) => handleFileChange(e, \"taxCertificate\")}\r\n                            disabled={isUploading || getDocumentCount('taxCertificate') >= 1}\r\n                            style={{ display: getDocumentCount('taxCertificate') >= 1 ? 'none' : 'block' }}\r\n                          />\r\n                          {getDocumentCount('taxCertificate') === 0 && (\r\n                            <div className=\"upload-placeholder\">\r\n                              <Upload size={24} />\r\n                              <p>Kéo thả hoặc nhấn để chọn file</p>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n\r\n                        {uploadProgress.taxCertificate > 0 && uploadProgress.taxCertificate < 100 && (\r\n                          <ProgressBar\r\n                            now={uploadProgress.taxCertificate}\r\n                            label={`${uploadProgress.taxCertificate}%`}\r\n                            className=\"mt-2\"\r\n                          />\r\n                        )}\r\n\r\n                        <div className=\"document-list mt-3\">\r\n                          {uploadedDocuments.taxCertificate && \r\n                            renderDocumentPreview(uploadedDocuments.taxCertificate, 'taxCertificate')}\r\n                          {localDocuments.taxCertificate && \r\n                            renderDocumentPreview(localDocuments.taxCertificate, 'taxCertificate')}\r\n                        </div>\r\n                      </Form.Group>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n\r\n                {/* Other Documents */}\r\n                <Col md={6}>\r\n                  <Card className=\"h-100\">\r\n                    <Card.Body>\r\n                      <Form.Group>\r\n                        <Form.Label className=\"fw-bold\">\r\n                          Tài Liệu Khác (Tùy chọn)\r\n                          {renderUploadStatus(uploadStatus.otherDocuments)}\r\n                          <span className=\"badge bg-secondary ms-2\">\r\n                            {getDocumentCount('otherDocuments')} files\r\n                          </span>\r\n                        </Form.Label>\r\n\r\n                        <div className=\"upload-box\">\r\n                          <Form.Control\r\n                            type=\"file\"\r\n                            accept=\".pdf,.jpg,.jpeg,.png\"\r\n                            onChange={(e) => handleFileChange(e, \"otherDocuments\")}\r\n                            multiple\r\n                            disabled={isUploading}\r\n                          />\r\n                          <div className=\"upload-placeholder\">\r\n                            <Upload size={24} />\r\n                            <p>Kéo thả hoặc nhấn để chọn nhiều file</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {uploadProgress.otherDocuments > 0 && uploadProgress.otherDocuments < 100 && (\r\n                          <ProgressBar\r\n                            now={uploadProgress.otherDocuments}\r\n                            label={`${uploadProgress.otherDocuments}%`}\r\n                            className=\"mt-2\"\r\n                          />\r\n                        )}\r\n\r\n                        <div className=\"document-list mt-3\">\r\n                          {uploadedDocuments.otherDocuments.map((doc, index) => \r\n                            renderDocumentPreview(doc, 'otherDocuments', index)\r\n                          )}\r\n                          {localDocuments.otherDocuments.map((doc, index) => \r\n                            renderDocumentPreview(doc, 'otherDocuments', index)\r\n                          )}\r\n                        </div>\r\n                      </Form.Group>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n              </Row>\r\n\r\n              <div className=\"d-flex justify-content-between mt-4\">\r\n                <Button \r\n                  variant=\"outline-secondary\" \r\n                  onClick={() => navigate(-1)}\r\n                  disabled={isUploading}\r\n                >\r\n                  Quay lại\r\n                </Button>\r\n                <Button \r\n                  type=\"submit\" \r\n                  variant=\"primary\"\r\n                  disabled={isUploading}\r\n                >\r\n                  {isUploading ? (\r\n                    <>\r\n                      <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                      Đang xử lý...\r\n                    </>\r\n                  ) : (\r\n                    \"Gửi Tài Liệu\"\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </Container>\r\n\r\n      <style jsx>{`\r\n        .upload-box {\r\n          position: relative;\r\n          border: 2px dashed #ced4da;\r\n          border-radius: 8px;\r\n          padding: 20px;\r\n          text-align: center;\r\n          background-color: #f8f9fa;\r\n          transition: all 0.3s ease;\r\n        }\r\n\r\n        .upload-box:hover {\r\n          border-color: #0071c2;\r\n          background-color: #f0f8ff;\r\n        }\r\n\r\n        .upload-placeholder {\r\n          pointer-events: none;\r\n        }\r\n\r\n        .upload-placeholder p {\r\n          margin: 8px 0 0 0;\r\n          color: #6c757d;\r\n          font-size: 14px;\r\n        }\r\n\r\n        .document-preview {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 8px;\r\n          border: 1px solid #dee2e6;\r\n          border-radius: 6px;\r\n          background-color: #f8f9fa;\r\n          position: relative;\r\n        }\r\n\r\n        .preview-content {\r\n          display: flex;\r\n          align-items: center;\r\n          flex: 1;\r\n          gap: 12px;\r\n        }\r\n\r\n        .preview-image {\r\n          width: 60px;\r\n          height: 60px;\r\n          object-fit: cover;\r\n          border-radius: 4px;\r\n        }\r\n\r\n        .file-icon {\r\n          width: 60px;\r\n          height: 60px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          background-color: #e9ecef;\r\n          border-radius: 4px;\r\n          color: #6c757d;\r\n        }\r\n\r\n        .document-info {\r\n          flex: 1;\r\n        }\r\n\r\n        .document-name {\r\n          margin: 0;\r\n          font-weight: 500;\r\n          font-size: 14px;\r\n          color: #333;\r\n          word-break: break-word;\r\n        }\r\n\r\n        .document-size {\r\n          margin: 4px 0 0 0;\r\n          font-size: 12px;\r\n          color: #6c757d;\r\n        }\r\n\r\n        .remove-btn {\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          padding: 0;\r\n          margin-left: 8px;\r\n        }\r\n\r\n        .document-list {\r\n          max-height: 300px;\r\n          overflow-y: auto;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .upload-box {\r\n            padding: 16px;\r\n          }\r\n          \r\n          .preview-image,\r\n          .file-icon {\r\n            width: 48px;\r\n            height: 48px;\r\n          }\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst styles = {\r\n  bookingApp: {\r\n    minHeight: \"100vh\",\r\n    backgroundColor: \"#f8f9fa\",\r\n  },\r\n  navbarCustom: {\r\n    backgroundColor: \"#003580\",\r\n    padding: \"10px 0\"\r\n  },\r\n};\r\n\r\nexport default DocumentUpload;\r\n\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SACEC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,OAAO,QACF,iBAAiB;AACxB,SAASC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,CAAC,EAAEC,WAAW,QAAQ,uBAAuB;AACpF,OAAO,sCAAsC;AAC7C,OAAO,2CAA2C;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,OAAOC,SAAS,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACW,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC;IACzD8B,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC;IACnD8B,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAM,CAACG,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC;IACnD8B,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC;IAC/C8B,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEFO,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEb,iBAAiB,CAAC;EACpDY,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,cAAc,CAAC;EAC9CM,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEL,cAAc,CAAC;EAC9CI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,YAAY,CAAC;EAE1C,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM4C,YAAY,GAAIC,IAAI,IAAK;IAC7B,MAAMC,YAAY,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;IAChF,MAAMC,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;IAElC,IAAI,CAACD,YAAY,CAACE,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC/B,SAAS,CAACgC,KAAK,CAAC,kCAAkC,CAAC;MACnD,OAAO,KAAK;IACd;IAEA,IAAIL,IAAI,CAACM,IAAI,GAAGJ,OAAO,EAAE;MACvB7B,SAAS,CAACgC,KAAK,CAAC,0CAA0C,CAAC;MAC3D,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,YAAY,KAAK;IAChD,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAAM,CAACD,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IAElC,IAAIH,YAAY,KAAK,gBAAgB,EAAE;MACrC;MACA,MAAMI,UAAU,GAAGC,KAAK,CAACC,IAAI,CAACL,KAAK,CAAC,CAACM,MAAM,CAACjB,YAAY,CAAC;MAEzD,IAAIc,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE;QACzB,MAAMK,gBAAgB,GAAGJ,UAAU,CAACK,GAAG,CAAClB,IAAI,KAAK;UAC/CA,IAAI,EAAEA,IAAI;UACVmB,IAAI,EAAEnB,IAAI,CAACmB,IAAI;UACfb,IAAI,EAAEN,IAAI,CAACM,IAAI;UACfF,IAAI,EAAEJ,IAAI,CAACI,IAAI;UACfgB,OAAO,EAAEpB,IAAI,CAACI,IAAI,CAACiB,UAAU,CAAC,QAAQ,CAAC,GAAGC,GAAG,CAACC,eAAe,CAACvB,IAAI,CAAC,GAAG,IAAI;UAC1EwB,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;QAEHlC,iBAAiB,CAACmC,IAAI,KAAK;UACzB,GAAGA,IAAI;UACP,CAAChB,YAAY,GAAG,CAAC,GAAGgB,IAAI,CAAChB,YAAY,CAAC,EAAE,GAAGQ,gBAAgB;QAC7D,CAAC,CAAC,CAAC;MACL;IACF,CAAC,MAAM;MACL;MACA,MAAMjB,IAAI,GAAGU,KAAK,CAAC,CAAC,CAAC;MAErB,IAAIX,YAAY,CAACC,IAAI,CAAC,EAAE;QACtB,MAAM0B,OAAO,GAAG;UACd1B,IAAI,EAAEA,IAAI;UACVmB,IAAI,EAAEnB,IAAI,CAACmB,IAAI;UACfb,IAAI,EAAEN,IAAI,CAACM,IAAI;UACfF,IAAI,EAAEJ,IAAI,CAACI,IAAI;UACfgB,OAAO,EAAEpB,IAAI,CAACI,IAAI,CAACiB,UAAU,CAAC,QAAQ,CAAC,GAAGC,GAAG,CAACC,eAAe,CAACvB,IAAI,CAAC,GAAG,IAAI;UAC1EwB,OAAO,EAAE;QACX,CAAC;QAEDlC,iBAAiB,CAACmC,IAAI,KAAK;UACzB,GAAGA,IAAI;UACP,CAAChB,YAAY,GAAGiB;QAClB,CAAC,CAAC,CAAC;;QAEH;QACAC,sBAAsB,CAAClB,YAAY,CAAC;MACtC;IACF;;IAEA;IACAD,KAAK,CAACG,MAAM,CAACiB,KAAK,GAAG,EAAE;EACzB,CAAC;EAED,MAAMD,sBAAsB,GAAIlB,YAAY,IAAK;IAC/C,IAAIoB,QAAQ,GAAG,CAAC;IAChBnC,eAAe,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAChB,YAAY,GAAG;IAAY,CAAC,CAAC,CAAC;IAEnE,MAAMqB,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCF,QAAQ,IAAI,EAAE;MACdrC,iBAAiB,CAACiC,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAAChB,YAAY,GAAGoB;MAClB,CAAC,CAAC,CAAC;MAEH,IAAIA,QAAQ,IAAI,GAAG,EAAE;QACnBG,aAAa,CAACF,QAAQ,CAAC;QACvBpC,eAAe,CAAC+B,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAAChB,YAAY,GAAG;QAClB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMwB,mBAAmB,GAAGA,CAACxB,YAAY,EAAEyB,KAAK,GAAG,IAAI,KAAK;IAC1D,IAAIzB,YAAY,KAAK,gBAAgB,IAAIyB,KAAK,KAAK,IAAI,EAAE;MACvD,MAAMC,WAAW,GAAG9C,cAAc,CAACoB,YAAY,CAAC,CAACyB,KAAK,CAAC;MACvD,IAAIC,WAAW,CAACf,OAAO,EAAE;QACvBE,GAAG,CAACc,eAAe,CAACD,WAAW,CAACf,OAAO,CAAC;MAC1C;MAEA9B,iBAAiB,CAACmC,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAAChB,YAAY,GAAGgB,IAAI,CAAChB,YAAY,CAAC,CAACO,MAAM,CAAC,CAACqB,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK;MACjE,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL,MAAMC,WAAW,GAAG9C,cAAc,CAACoB,YAAY,CAAC;MAChD,IAAI0B,WAAW,IAAIA,WAAW,CAACf,OAAO,EAAE;QACtCE,GAAG,CAACc,eAAe,CAACD,WAAW,CAACf,OAAO,CAAC;MAC1C;MAEA9B,iBAAiB,CAACmC,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAAChB,YAAY,GAAG;MAClB,CAAC,CAAC,CAAC;MAEHjB,iBAAiB,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAChB,YAAY,GAAG;MAAE,CAAC,CAAC,CAAC;MAC3Df,eAAe,CAAC+B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAChB,YAAY,GAAG;MAAG,CAAC,CAAC,CAAC;IAC5D;EACF,CAAC;;EAED;EACA,MAAM8B,sBAAsB,GAAG,MAAAA,CAAO9B,YAAY,EAAE+B,QAAQ,EAAEN,KAAK,GAAG,IAAI,KAAK;IAC7E,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMlE,SAAS,CAACmE,oBAAoB,CAAC,CAACF,QAAQ,CAAC,CAAC;MAEjE,IAAIC,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACtC,KAAK,EAAE;QACzC,IAAII,YAAY,KAAK,gBAAgB,IAAIyB,KAAK,KAAK,IAAI,EAAE;UACvDlD,oBAAoB,CAACyC,IAAI,KAAK;YAC5B,GAAGA,IAAI;YACP,CAAChB,YAAY,GAAGgB,IAAI,CAAChB,YAAY,CAAC,CAACO,MAAM,CAAC,CAACqB,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK;UACjE,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLlD,oBAAoB,CAACyC,IAAI,KAAK;YAC5B,GAAGA,IAAI;YACP,CAAChB,YAAY,GAAG;UAClB,CAAC,CAAC,CAAC;QACL;QAEApC,SAAS,CAACuE,OAAO,CAAC,6BAA6B,CAAC;MAClD,CAAC,MAAM;QAAA,IAAAC,cAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,cAAA,GAAAJ,QAAQ,CAACE,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAeE,OAAO,KAAI,wBAAwB,CAAC;MACrE;IACF,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDhC,SAAS,CAACgC,KAAK,CAAC,kCAAkC,GAAGA,KAAK,CAAC0C,OAAO,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFlD,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMmD,YAAY,GAAG;QAAE,GAAGlE;MAAkB,CAAC;;MAE7C;MACA,KAAK,MAAMmE,OAAO,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,gBAAgB,CAAC,EAAE;QACzE,MAAMC,QAAQ,GAAG9D,cAAc,CAAC6D,OAAO,CAAC;QACxC,IAAIC,QAAQ,IAAIA,QAAQ,CAACnD,IAAI,EAAE;UAC7B,MAAMoD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAACnD,IAAI,CAAC;UAC1CoD,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEJ,OAAO,CAAC;UAExC,MAAMT,QAAQ,GAAG,MAAMlE,SAAS,CAACgF,mBAAmB,CAACH,QAAQ,CAAC;UAE9D,IAAIX,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACtC,KAAK,EAAE;YACzC4C,YAAY,CAACC,OAAO,CAAC,GAAGT,QAAQ,CAACE,IAAI,CAACA,IAAI;YAC1C;YACA,IAAIQ,QAAQ,CAAC/B,OAAO,EAAE;cACpBE,GAAG,CAACc,eAAe,CAACe,QAAQ,CAAC/B,OAAO,CAAC;YACvC;UACF;QACF;MACF;;MAEA;MACA,MAAMoC,SAAS,GAAGnE,cAAc,CAACD,cAAc;MAC/C,IAAIoE,SAAS,CAAC5C,MAAM,GAAG,CAAC,EAAE;QACxB,KAAK,MAAM6C,GAAG,IAAID,SAAS,EAAE;UAC3B,IAAIC,GAAG,CAACzD,IAAI,EAAE;YACZ,MAAMoD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;YAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEG,GAAG,CAACzD,IAAI,CAAC;YACrCoD,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAE,gBAAgB,CAAC;YAEjD,MAAMb,QAAQ,GAAG,MAAMlE,SAAS,CAACgF,mBAAmB,CAACH,QAAQ,CAAC;YAE9D,IAAIX,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACtC,KAAK,EAAE;cACzC4C,YAAY,CAAC7D,cAAc,CAACsE,IAAI,CAACjB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;cACpD;cACA,IAAIc,GAAG,CAACrC,OAAO,EAAE;gBACfE,GAAG,CAACc,eAAe,CAACqB,GAAG,CAACrC,OAAO,CAAC;cAClC;YACF;UACF;QACF;MACF;MAEApC,oBAAoB,CAACiE,YAAY,CAAC;;MAElC;MACA3D,iBAAiB,CAAC;QAChBL,eAAe,EAAE,IAAI;QACrBC,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,IAAI;QACpBC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEFf,SAAS,CAACuE,OAAO,CAAC,6BAA6B,CAAC;MAChD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDhC,SAAS,CAACgC,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAAC0C,OAAO,CAAC;MACtE,OAAO,KAAK;IACd,CAAC,SAAS;MACRjD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM6D,YAAY,GAAG,MAAOnD,KAAK,IAAK;IACpCA,KAAK,CAACoD,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMC,YAAY,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,gBAAgB,CAAC;IACxE,MAAMC,WAAW,GAAGD,YAAY,CAAC7C,MAAM,CAACkC,OAAO,IAC7C,CAACnE,iBAAiB,CAACmE,OAAO,CAAC,IAAI,CAAC7D,cAAc,CAAC6D,OAAO,CACxD,CAAC;IAED,IAAIY,WAAW,CAAClD,MAAM,GAAG,CAAC,EAAE;MAC1BvC,SAAS,CAACgC,KAAK,CAAC,0CAA0C,CAAC;MAC3D;IACF;;IAEA;IACA,MAAM0D,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC5E,cAAc,CAAC,CAAC6E,IAAI,CAACT,GAAG,IACzDA,GAAG,KAAKA,GAAG,CAACzD,IAAI,IAAKc,KAAK,CAACqD,OAAO,CAACV,GAAG,CAAC,IAAIA,GAAG,CAAC7C,MAAM,GAAG,CAAE,CAC5D,CAAC;IAED,IAAImD,YAAY,EAAE;MAChB,MAAMK,aAAa,GAAG,MAAMpB,eAAe,CAAC,CAAC;MAC7C,IAAI,CAACoB,aAAa,EAAE;IACtB;;IAEA;IACAtF,QAAQ,CAAC,2BAA2B,CAAC;EACvC,CAAC;EAED,MAAMuF,kBAAkB,GAAIC,MAAM,IAAK;IACrC,QAAQA,MAAM;MACZ,KAAK,SAAS;MACd,KAAK,OAAO;QACV,oBAAO7F,OAAA,CAACV,WAAW;UAACwG,SAAS,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,WAAW;QACd,oBAAOlG,OAAA,CAACZ,OAAO;UAAC+G,SAAS,EAAC,QAAQ;UAACtE,IAAI,EAAC,IAAI;UAACiE,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClE,KAAK,OAAO;QACV,oBAAOlG,OAAA,CAACT,OAAO;UAACuG,SAAS,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjD;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAME,qBAAqB,GAAGA,CAACC,QAAQ,EAAErE,YAAY,EAAEyB,KAAK,GAAG,IAAI,KAAK;IAAA,IAAA6C,cAAA,EAAAC,aAAA,EAAAC,cAAA;IACtE,MAAMC,OAAO,GAAG,EAAAH,cAAA,GAAAD,QAAQ,CAAC1E,IAAI,cAAA2E,cAAA,uBAAbA,cAAA,CAAe1D,UAAU,CAAC,QAAQ,CAAC,OAAA2D,aAAA,GAAIF,QAAQ,CAACK,GAAG,cAAAH,aAAA,uBAAZA,aAAA,CAAc7E,QAAQ,CAAC,MAAM,CAAC,OAAA8E,cAAA,GAAIH,QAAQ,CAACK,GAAG,cAAAF,cAAA,uBAAZA,cAAA,CAAc9E,QAAQ,CAAC,MAAM,CAAC;IAEvH,oBACE1B,OAAA;MAAK8F,SAAS,EAAC,uBAAuB;MAAAa,QAAA,gBACpC3G,OAAA;QAAK8F,SAAS,EAAC,iBAAiB;QAAAa,QAAA,GAC7BF,OAAO,gBACNzG,OAAA;UACE4G,GAAG,EAAEP,QAAQ,CAAC1D,OAAO,IAAI0D,QAAQ,CAACK,GAAI;UACtCG,GAAG,EAAER,QAAQ,CAAC3D,IAAK;UACnBoD,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,gBAEFlG,OAAA;UAAK8F,SAAS,EAAC,WAAW;UAAAa,QAAA,eACxB3G,OAAA,CAACP,WAAW;YAACoC,IAAI,EAAE;UAAG;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,eACDlG,OAAA;UAAK8F,SAAS,EAAC,eAAe;UAAAa,QAAA,gBAC5B3G,OAAA;YAAG8F,SAAS,EAAC,eAAe;YAAAa,QAAA,EAAEN,QAAQ,CAAC3D;UAAI;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDlG,OAAA;YAAG8F,SAAS,EAAC,eAAe;YAAAa,QAAA,EACzBN,QAAQ,CAACxE,IAAI,GAAG,CAACwE,QAAQ,CAACxE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEiF,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG;UAAc;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,EACHG,QAAQ,CAACtD,OAAO,iBACf/C,OAAA;YAAO8F,SAAS,EAAC,cAAc;YAAAa,QAAA,EAAC;UAAW;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACnD,EACA,CAACG,QAAQ,CAACtD,OAAO,iBAChB/C,OAAA;YAAO8F,SAAS,EAAC,cAAc;YAAAa,QAAA,EAAC;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAChD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlG,OAAA,CAACnB,MAAM;QACLkI,OAAO,EAAC,QAAQ;QAChBlF,IAAI,EAAC,IAAI;QACTiE,SAAS,EAAC,YAAY;QACtBkB,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIX,QAAQ,CAACtD,OAAO,EAAE;YACpBS,mBAAmB,CAACxB,YAAY,EAAEyB,KAAK,CAAC;UAC1C,CAAC,MAAM;YACLK,sBAAsB,CAAC9B,YAAY,EAAEqE,QAAQ,CAACY,SAAS,EAAExD,KAAK,CAAC;UACjE;QACF,CAAE;QACFyD,KAAK,EAAC,yBAAc;QAAAP,QAAA,eAEpB3G,OAAA,CAACR,CAAC;UAACqC,IAAI,EAAE;QAAG;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA,GAxCiCzC,KAAK,IAAI,QAAQ;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyCxD,CAAC;EAEV,CAAC;EAED,MAAMiB,gBAAgB,GAAInF,YAAY,IAAK;IACzC,MAAMoF,QAAQ,GAAG9G,iBAAiB,CAAC0B,YAAY,CAAC;IAChD,MAAMqF,KAAK,GAAGzG,cAAc,CAACoB,YAAY,CAAC;IAE1C,IAAIA,YAAY,KAAK,gBAAgB,EAAE;MACrC,OAAO,CAAC,CAAAoF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjF,MAAM,KAAI,CAAC,KAAK,CAAAkF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAElF,MAAM,KAAI,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAO,CAACiF,QAAQ,GAAG,CAAC,GAAG,CAAC,KAAKC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C;EACF,CAAC;EAED,oBACErH,OAAA;IAAKsH,KAAK,EAAEC,MAAM,CAACC,UAAW;IAAAb,QAAA,gBAC5B3G,OAAA,CAACH,aAAa;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGjBlG,OAAA,CAACb,MAAM;MAACmI,KAAK,EAAEC,MAAM,CAACE,YAAa;MAAAd,QAAA,eACjC3G,OAAA,CAACrB,SAAS;QAAAgI,QAAA,eACR3G,OAAA,CAACb,MAAM,CAACuI,KAAK;UAACC,IAAI,EAAC,OAAO;UAAC7B,SAAS,EAAC,oBAAoB;UAAAa,QAAA,eACvD3G,OAAA;YAAGsH,KAAK,EAAE;cAAEM,QAAQ,EAAE;YAAG,CAAE;YAAAjB,QAAA,GAAC,IACxB,eAAA3G,OAAA;cAAMsH,KAAK,EAAE;gBAAEO,KAAK,EAAE;cAAU,CAAE;cAAAlB,QAAA,EAAC;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAETlG,OAAA,CAACrB,SAAS;MAACmH,SAAS,EAAC,MAAM;MAAAa,QAAA,eACzB3G,OAAA,CAAClB,IAAI;QAACgH,SAAS,EAAC,WAAW;QAAAa,QAAA,gBACzB3G,OAAA,CAAClB,IAAI,CAACgJ,MAAM;UAAChC,SAAS,EAAC,yBAAyB;UAAAa,QAAA,eAC9C3G,OAAA;YAAI8F,SAAS,EAAC,MAAM;YAAAa,QAAA,EAAC;UAA0B;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACdlG,OAAA,CAAClB,IAAI,CAACiJ,IAAI;UAAApB,QAAA,GACPvF,WAAW,iBACVpB,OAAA,CAAChB,KAAK;YAAC+H,OAAO,EAAC,MAAM;YAACjB,SAAS,EAAC,MAAM;YAAAa,QAAA,eACpC3G,OAAA;cAAK8F,SAAS,EAAC,2BAA2B;cAAAa,QAAA,gBACxC3G,OAAA,CAACZ,OAAO;gBAAC+G,SAAS,EAAC,QAAQ;gBAACtE,IAAI,EAAC,IAAI;gBAACiE,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAEDlG,OAAA,CAAChB,KAAK;YAAC+H,OAAO,EAAC,MAAM;YAACjB,SAAS,EAAC,MAAM;YAAAa,QAAA,EAAC;UAGvC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAERlG,OAAA,CAACpB,IAAI;YAACoJ,QAAQ,EAAE9C,YAAa;YAAAyB,QAAA,gBAC3B3G,OAAA,CAACf,GAAG;cAAC6G,SAAS,EAAC,KAAK;cAAAa,QAAA,gBAElB3G,OAAA,CAACd,GAAG;gBAAC+I,EAAE,EAAE,CAAE;gBAAAtB,QAAA,eACT3G,OAAA,CAAClB,IAAI;kBAACgH,SAAS,EAAC,OAAO;kBAAAa,QAAA,eACrB3G,OAAA,CAAClB,IAAI,CAACiJ,IAAI;oBAAApB,QAAA,eACR3G,OAAA,CAACpB,IAAI,CAACsJ,KAAK;sBAAAvB,QAAA,gBACT3G,OAAA,CAACpB,IAAI,CAACuJ,KAAK;wBAACrC,SAAS,EAAC,SAAS;wBAAAa,QAAA,GAAC,8BAE9B,eAAA3G,OAAA;0BAAM8F,SAAS,EAAC,aAAa;0BAAAa,QAAA,EAAC;wBAAC;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,EACrCN,kBAAkB,CAAC5E,YAAY,CAACR,eAAe,CAAC,eACjDR,OAAA;0BAAM8F,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,GACtCQ,gBAAgB,CAAC,iBAAiB,CAAC,EAAC,IACvC;wBAAA;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eAGblG,OAAA;wBAAK8F,SAAS,EAAC,YAAY;wBAAAa,QAAA,gBACzB3G,OAAA,CAACpB,IAAI,CAACwJ,OAAO;0BACXzG,IAAI,EAAC,MAAM;0BACX0G,MAAM,EAAC,sBAAsB;0BAC7BC,QAAQ,EAAGC,CAAC,IAAKzG,gBAAgB,CAACyG,CAAC,EAAE,iBAAiB,CAAE;0BACxDC,QAAQ,EAAEpH,WAAW,IAAI+F,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAE;0BAClEG,KAAK,EAAE;4BAAEmB,OAAO,EAAEtB,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG;0BAAQ;wBAAE;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjF,CAAC,EACDiB,gBAAgB,CAAC,iBAAiB,CAAC,KAAK,CAAC,iBACxCnH,OAAA;0BAAK8F,SAAS,EAAC,oBAAoB;0BAAAa,QAAA,gBACjC3G,OAAA,CAACX,MAAM;4BAACwC,IAAI,EAAE;0BAAG;4BAAAkE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACpBlG,OAAA;4BAAA2G,QAAA,EAAG;0BAA8B;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAGLpF,cAAc,CAACN,eAAe,GAAG,CAAC,IAAIM,cAAc,CAACN,eAAe,GAAG,GAAG,iBACzER,OAAA,CAACjB,WAAW;wBACV2J,GAAG,EAAE5H,cAAc,CAACN,eAAgB;wBACpCmI,KAAK,EAAE,GAAG7H,cAAc,CAACN,eAAe,GAAI;wBAC5CsF,SAAS,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CACF,eAGDlG,OAAA;wBAAK8F,SAAS,EAAC,oBAAoB;wBAAAa,QAAA,GAChCrG,iBAAiB,CAACE,eAAe,IAChC4F,qBAAqB,CAAC9F,iBAAiB,CAACE,eAAe,EAAE,iBAAiB,CAAC,EAC5EI,cAAc,CAACJ,eAAe,IAC7B4F,qBAAqB,CAACxF,cAAc,CAACJ,eAAe,EAAE,iBAAiB,CAAC;sBAAA;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGNlG,OAAA,CAACd,GAAG;gBAAC+I,EAAE,EAAE,CAAE;gBAAAtB,QAAA,eACT3G,OAAA,CAAClB,IAAI;kBAACgH,SAAS,EAAC,OAAO;kBAAAa,QAAA,eACrB3G,OAAA,CAAClB,IAAI,CAACiJ,IAAI;oBAAApB,QAAA,eACR3G,OAAA,CAACpB,IAAI,CAACsJ,KAAK;sBAAAvB,QAAA,gBACT3G,OAAA,CAACpB,IAAI,CAACuJ,KAAK;wBAACrC,SAAS,EAAC,SAAS;wBAAAa,QAAA,GAAC,qCAE9B,eAAA3G,OAAA;0BAAM8F,SAAS,EAAC,aAAa;0BAAAa,QAAA,EAAC;wBAAC;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,EACrCN,kBAAkB,CAAC5E,YAAY,CAACP,UAAU,CAAC,eAC5CT,OAAA;0BAAM8F,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,GACtCQ,gBAAgB,CAAC,YAAY,CAAC,EAAC,IAClC;wBAAA;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eAEblG,OAAA;wBAAK8F,SAAS,EAAC,YAAY;wBAAAa,QAAA,gBACzB3G,OAAA,CAACpB,IAAI,CAACwJ,OAAO;0BACXzG,IAAI,EAAC,MAAM;0BACX0G,MAAM,EAAC,sBAAsB;0BAC7BC,QAAQ,EAAGC,CAAC,IAAKzG,gBAAgB,CAACyG,CAAC,EAAE,YAAY,CAAE;0BACnDC,QAAQ,EAAEpH,WAAW,IAAI+F,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAE;0BAC7DG,KAAK,EAAE;4BAAEmB,OAAO,EAAEtB,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG;0BAAQ;wBAAE;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E,CAAC,EACDiB,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,iBACnCnH,OAAA;0BAAK8F,SAAS,EAAC,oBAAoB;0BAAAa,QAAA,gBACjC3G,OAAA,CAACX,MAAM;4BAACwC,IAAI,EAAE;0BAAG;4BAAAkE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACpBlG,OAAA;4BAAA2G,QAAA,EAAG;0BAA8B;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAELpF,cAAc,CAACL,UAAU,GAAG,CAAC,IAAIK,cAAc,CAACL,UAAU,GAAG,GAAG,iBAC/DT,OAAA,CAACjB,WAAW;wBACV2J,GAAG,EAAE5H,cAAc,CAACL,UAAW;wBAC/BkI,KAAK,EAAE,GAAG7H,cAAc,CAACL,UAAU,GAAI;wBACvCqF,SAAS,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CACF,eAEDlG,OAAA;wBAAK8F,SAAS,EAAC,oBAAoB;wBAAAa,QAAA,GAChCrG,iBAAiB,CAACG,UAAU,IAC3B2F,qBAAqB,CAAC9F,iBAAiB,CAACG,UAAU,EAAE,YAAY,CAAC,EAClEG,cAAc,CAACH,UAAU,IACxB2F,qBAAqB,CAACxF,cAAc,CAACH,UAAU,EAAE,YAAY,CAAC;sBAAA;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGNlG,OAAA,CAACd,GAAG;gBAAC+I,EAAE,EAAE,CAAE;gBAAAtB,QAAA,eACT3G,OAAA,CAAClB,IAAI;kBAACgH,SAAS,EAAC,OAAO;kBAAAa,QAAA,eACrB3G,OAAA,CAAClB,IAAI,CAACiJ,IAAI;oBAAApB,QAAA,eACR3G,OAAA,CAACpB,IAAI,CAACsJ,KAAK;sBAAAvB,QAAA,gBACT3G,OAAA,CAACpB,IAAI,CAACuJ,KAAK;wBAACrC,SAAS,EAAC,SAAS;wBAAAa,QAAA,GAAC,0CAE9B,eAAA3G,OAAA;0BAAM8F,SAAS,EAAC,aAAa;0BAAAa,QAAA,EAAC;wBAAC;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,EACrCN,kBAAkB,CAAC5E,YAAY,CAACN,cAAc,CAAC,eAChDV,OAAA;0BAAM8F,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,GACtCQ,gBAAgB,CAAC,gBAAgB,CAAC,EAAC,IACtC;wBAAA;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eAEblG,OAAA;wBAAK8F,SAAS,EAAC,YAAY;wBAAAa,QAAA,gBACzB3G,OAAA,CAACpB,IAAI,CAACwJ,OAAO;0BACXzG,IAAI,EAAC,MAAM;0BACX0G,MAAM,EAAC,sBAAsB;0BAC7BC,QAAQ,EAAGC,CAAC,IAAKzG,gBAAgB,CAACyG,CAAC,EAAE,gBAAgB,CAAE;0BACvDC,QAAQ,EAAEpH,WAAW,IAAI+F,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAE;0BACjEG,KAAK,EAAE;4BAAEmB,OAAO,EAAEtB,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG;0BAAQ;wBAAE;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChF,CAAC,EACDiB,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,iBACvCnH,OAAA;0BAAK8F,SAAS,EAAC,oBAAoB;0BAAAa,QAAA,gBACjC3G,OAAA,CAACX,MAAM;4BAACwC,IAAI,EAAE;0BAAG;4BAAAkE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACpBlG,OAAA;4BAAA2G,QAAA,EAAG;0BAA8B;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAELpF,cAAc,CAACJ,cAAc,GAAG,CAAC,IAAII,cAAc,CAACJ,cAAc,GAAG,GAAG,iBACvEV,OAAA,CAACjB,WAAW;wBACV2J,GAAG,EAAE5H,cAAc,CAACJ,cAAe;wBACnCiI,KAAK,EAAE,GAAG7H,cAAc,CAACJ,cAAc,GAAI;wBAC3CoF,SAAS,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CACF,eAEDlG,OAAA;wBAAK8F,SAAS,EAAC,oBAAoB;wBAAAa,QAAA,GAChCrG,iBAAiB,CAACI,cAAc,IAC/B0F,qBAAqB,CAAC9F,iBAAiB,CAACI,cAAc,EAAE,gBAAgB,CAAC,EAC1EE,cAAc,CAACF,cAAc,IAC5B0F,qBAAqB,CAACxF,cAAc,CAACF,cAAc,EAAE,gBAAgB,CAAC;sBAAA;wBAAAqF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGNlG,OAAA,CAACd,GAAG;gBAAC+I,EAAE,EAAE,CAAE;gBAAAtB,QAAA,eACT3G,OAAA,CAAClB,IAAI;kBAACgH,SAAS,EAAC,OAAO;kBAAAa,QAAA,eACrB3G,OAAA,CAAClB,IAAI,CAACiJ,IAAI;oBAAApB,QAAA,eACR3G,OAAA,CAACpB,IAAI,CAACsJ,KAAK;sBAAAvB,QAAA,gBACT3G,OAAA,CAACpB,IAAI,CAACuJ,KAAK;wBAACrC,SAAS,EAAC,SAAS;wBAAAa,QAAA,GAAC,6CAE9B,EAACf,kBAAkB,CAAC5E,YAAY,CAACL,cAAc,CAAC,eAChDX,OAAA;0BAAM8F,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,GACtCQ,gBAAgB,CAAC,gBAAgB,CAAC,EAAC,QACtC;wBAAA;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eAEblG,OAAA;wBAAK8F,SAAS,EAAC,YAAY;wBAAAa,QAAA,gBACzB3G,OAAA,CAACpB,IAAI,CAACwJ,OAAO;0BACXzG,IAAI,EAAC,MAAM;0BACX0G,MAAM,EAAC,sBAAsB;0BAC7BC,QAAQ,EAAGC,CAAC,IAAKzG,gBAAgB,CAACyG,CAAC,EAAE,gBAAgB,CAAE;0BACvDK,QAAQ;0BACRJ,QAAQ,EAAEpH;wBAAY;0BAAA2E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACFlG,OAAA;0BAAK8F,SAAS,EAAC,oBAAoB;0BAAAa,QAAA,gBACjC3G,OAAA,CAACX,MAAM;4BAACwC,IAAI,EAAE;0BAAG;4BAAAkE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACpBlG,OAAA;4BAAA2G,QAAA,EAAG;0BAAoC;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAELpF,cAAc,CAACH,cAAc,GAAG,CAAC,IAAIG,cAAc,CAACH,cAAc,GAAG,GAAG,iBACvEX,OAAA,CAACjB,WAAW;wBACV2J,GAAG,EAAE5H,cAAc,CAACH,cAAe;wBACnCgI,KAAK,EAAE,GAAG7H,cAAc,CAACH,cAAc,GAAI;wBAC3CmF,SAAS,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CACF,eAEDlG,OAAA;wBAAK8F,SAAS,EAAC,oBAAoB;wBAAAa,QAAA,GAChCrG,iBAAiB,CAACK,cAAc,CAAC8B,GAAG,CAAC,CAACuC,GAAG,EAAEvB,KAAK,KAC/C2C,qBAAqB,CAACpB,GAAG,EAAE,gBAAgB,EAAEvB,KAAK,CACpD,CAAC,EACA7C,cAAc,CAACD,cAAc,CAAC8B,GAAG,CAAC,CAACuC,GAAG,EAAEvB,KAAK,KAC5C2C,qBAAqB,CAACpB,GAAG,EAAE,gBAAgB,EAAEvB,KAAK,CACpD,CAAC;sBAAA;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlG,OAAA;cAAK8F,SAAS,EAAC,qCAAqC;cAAAa,QAAA,gBAClD3G,OAAA,CAACnB,MAAM;gBACLkI,OAAO,EAAC,mBAAmB;gBAC3BC,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,CAAC,CAAC,CAAE;gBAC5BmI,QAAQ,EAAEpH,WAAY;gBAAAuF,QAAA,EACvB;cAED;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlG,OAAA,CAACnB,MAAM;gBACL8C,IAAI,EAAC,QAAQ;gBACboF,OAAO,EAAC,SAAS;gBACjByB,QAAQ,EAAEpH,WAAY;gBAAAuF,QAAA,EAErBvF,WAAW,gBACVpB,OAAA,CAAAE,SAAA;kBAAAyG,QAAA,gBACE3G,OAAA,CAACZ,OAAO;oBAAC+G,SAAS,EAAC,QAAQ;oBAACtE,IAAI,EAAC,IAAI;oBAACiE,SAAS,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,8BAE3D;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEZlG,OAAA;MAAO6I,GAAG;MAAAlC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAC9F,EAAA,CA3tBQD,cAAc;EAAA,QACJR,WAAW;AAAA;AAAAmJ,EAAA,GADrB3I,cAAc;AA6tBvB,MAAMoH,MAAM,GAAG;EACbC,UAAU,EAAE;IACVuB,SAAS,EAAE,OAAO;IAClBC,eAAe,EAAE;EACnB,CAAC;EACDvB,YAAY,EAAE;IACZuB,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE;EACX;AACF,CAAC;AAED,eAAe9I,cAAc;AAAC,IAAA2I,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}