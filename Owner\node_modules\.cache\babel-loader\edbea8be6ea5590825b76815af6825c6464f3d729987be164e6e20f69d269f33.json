{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\AI\\\\DataAnalysisAI.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend, Filler } from \"chart.js\";\nimport RoomAvailabilityCalendar from \"@pages/hotel_host/RoomAvailabilityCalendar\";\nimport Transaction from \"@pages/hotel_host/Transaction\";\nimport AdditionalServicesPage from \"../service/AdditionalServicesPage\";\nimport RoomListingPage from \"../../room/RoomListingPage\";\nimport { useNavigate, useSearchParams } from \"react-router-dom\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport \"bootstrap/dist/js/bootstrap.bundle.min\";\nimport * as Routers from \"../../../utils/Routes\";\nimport MyAccountHotelPage from \"../information/MyAccountHotelPage\";\nimport HotelManagement from \"../hotel/HotelManagement\";\nimport ListFeedbackHotelPage from \"../Feedback/ListFeedbackHotelPage\";\nimport Chat from \"../Chat\";\nimport { useAppSelector } from \"@redux/store\";\nimport { Dropdown, Image } from \"react-bootstrap\";\nimport { useAppDispatch } from \"@redux/store\";\nimport DashBoardPage from \"../dash_board/DashBoardPage\";\nimport InsightAiPage from \"./InSightAiPage\";\nimport RevenuePage from \"../revenue/RevenuePage\";\nimport AuthActions from \"@redux/auth/actions\";\nimport { disconnectSocket } from \"@redux/socket/socketSlice\";\nimport { Manager } from \"socket.io-client\";\nimport ManagementBooking from \"@pages/management_booking/ManagementBooking\";\nimport MyReportPage from \"../report/MyReportPage\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend, Filler);\nfunction App() {\n  _s();\n  var _Auth$image, _Auth$image2, _Auth$image3;\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [activeTab, setActiveTab] = useState(() => {\n    return searchParams.get(\"tab\") || \"revenue\";\n  });\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  useEffect(() => {\n    setSearchParams({\n      tab: activeTab\n    });\n  }, [activeTab, setSearchParams]);\n  const Socket = useAppSelector(state => state.Socket.socket);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-brand d-flex align-items-center justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-building fs-2 me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-0\",\n            children: \"Hotel partner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav flex-column\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"revenue\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"revenue\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-graph-up nav-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Doanh thu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"transaction\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"transaction\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-credit-card nav-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Giao d\\u1ECBch\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"hotels\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"hotels\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-building\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginLeft: \"10px\"\n                },\n                children: \"Qu\\u1EA3n l\\xFD kh\\xE1ch s\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"services\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"services\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-people nav-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Qu\\u1EA3n l\\xFD d\\u1ECBch v\\u1EE5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"rooms\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"rooms\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-door-closed nav-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Qu\\u1EA3n l\\xFD lo\\u1EA1i ph\\xF2ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"bookings\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"bookings\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-calendar-check nav-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0110\\u1EB7t ph\\xF2ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"management_bookings\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"management_bookings\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi-clipboard-data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginLeft: \"10px\"\n                },\n                children: \"Qu\\u1EA3n l\\xFD \\u0111\\u1EB7t ph\\xF2ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"feedbacks\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"feedbacks\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi-chat-left-text\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginLeft: \"10px\"\n                },\n                children: \"\\u0110\\xE1nh gi\\xE1 kh\\xE1ch s\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"mess\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"mess\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-chat-dots nav-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Tin nh\\u1EAFn\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"my_report\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"my_report\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-exclamation-triangle-fill\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginLeft: '5px'\n                },\n                children: \"Qu\\u1EA3n l\\xFD b\\xE1o c\\xE1o\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: `nav-link ${activeTab === \"setting\" ? \"active\" : \"\"}`,\n              href: \"#\",\n              onClick: () => {\n                setActiveTab(\"setting\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-gear nav-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"C\\xE0i \\u0111\\u1EB7t\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-auto p-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ai-assistant-card p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-robot fs-3 me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-0\",\n                children: \"Tr\\u1EE3 l\\xFD AI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"small mb-2\",\n              children: \"H\\u1ECFi t\\xF4i b\\u1EA5t c\\u1EE9 \\u0111i\\u1EC1u g\\xEC v\\u1EC1 kh\\xE1ch s\\u1EA1n c\\u1EE7a b\\u1EA1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control form-control-sm\",\n                placeholder: \"Nh\\u1EADp c\\xE2u h\\u1ECFi...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-primary btn-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-send\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"navbar navbar-expand-lg navbar-light bg-white shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"container-fluid\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm\",\n              type: \"button\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-list fs-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center\",\n              children: Auth._id !== -1 && /*#__PURE__*/_jsxDEV(Dropdown, {\n                align: \"end\",\n                children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n                  variant: \"light\",\n                  className: \"login-btn d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                    style: {\n                      display: \"inline-block\",\n                      maxWidth: \"150px\",\n                      // hoặc width tùy bạn\n                      whiteSpace: \"nowrap\",\n                      overflow: \"hidden\",\n                      textOverflow: \"ellipsis\"\n                    },\n                    children: Auth.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 23\n                  }, this), \" \", /*#__PURE__*/_jsxDEV(Image, {\n                    src: (Auth === null || Auth === void 0 ? void 0 : (_Auth$image = Auth.image) === null || _Auth$image === void 0 ? void 0 : _Auth$image.url) != \"\" && (Auth === null || Auth === void 0 ? void 0 : (_Auth$image2 = Auth.image) === null || _Auth$image2 === void 0 ? void 0 : _Auth$image2.url) != undefined ? Auth === null || Auth === void 0 ? void 0 : (_Auth$image3 = Auth.image) === null || _Auth$image3 === void 0 ? void 0 : _Auth$image3.url : \"https://i.pinimg.com/736x/8f/1c/a2/8f1ca2029e2efceebd22fa05cca423d7.jpg\",\n                    roundedCircle: true,\n                    width: \"30\",\n                    height: \"30\",\n                    className: \"ms-2 me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n                  children: /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                    onClick: () => {\n                      dispatch(disconnectSocket());\n                      dispatch({\n                        type: AuthActions.LOGOUT\n                      });\n                      navigate(Routers.HomeHotel, {\n                        state: {\n                          message: \"Logout account successfully !!!\"\n                        }\n                      });\n                    },\n                    children: \"\\u0110\\u0103ng xu\\u1EA5t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container-fluid\",\n          children: [activeTab === \"ai-insights\" && /*#__PURE__*/_jsxDEV(InsightAiPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 45\n          }, this), activeTab === \"bookings\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(RoomAvailabilityCalendar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"transaction\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(Transaction, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"hotels\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(HotelManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"rooms\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(RoomListingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"services\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(AdditionalServicesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"settings\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(MyAccountHotelPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"feedbacks\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(ListFeedbackHotelPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"mess\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"setting\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(MyAccountHotelPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), activeTab === \"revenue\" && /*#__PURE__*/_jsxDEV(RevenuePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 41\n          }, this), activeTab === \"management_bookings\" && /*#__PURE__*/_jsxDEV(ManagementBooking, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 53\n          }, this), activeTab === \"my_report\" && /*#__PURE__*/_jsxDEV(MyReportPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 43\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          body {\n            margin: 0;\n            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n            background-color: #f8f9fa;\n            overflow-x: hidden;\n          }\n          \n          .sidebar {\n            width: 250px;\n            min-height: 100vh;\n            padding: 0;\n            display: flex;\n            flex-direction: column;\n            position: fixed;\n            left: 0;\n            top: 0;\n            bottom: 0;\n            z-index: 100;\n            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);\n            background-color: #212529;\n            color: white;\n          }\n          \n          .sidebar-brand {\n            background-color: #212529;\n            border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n            padding: 20px 0;\n          }\n          \n          .main-content {\n            flex-grow: 1;\n            margin-left: 250px;\n            min-height: 100vh;\n            background-color: #f8f9fa;\n          }\n          \n          .nav-item {\n            margin-bottom: 2px;\n          }\n          \n          .nav-link {\n            padding: 12px 20px;\n            transition: all 0.3s;\n            color: rgba(255, 255, 255, 0.8);\n            display: flex;\n            align-items: center;\n          }\n          \n          .nav-link:hover {\n            background-color: rgba(255, 255, 255, 0.1);\n            color: #fff !important;\n          }\n          \n          .nav-link.active {\n            background-color: #0d6efd;\n            color: #fff;\n          }\n          \n          .nav-icon {\n            width: 20px;\n            margin-right: 10px;\n            text-align: center;\n          }\n          \n          .card {\n            border-radius: 10px;\n            transition: transform 0.3s, box-shadow 0.3s;\n            margin-bottom: 20px;\n            border: none;\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n          }\n          \n          .card:hover {\n            transform: translateY(-3px);\n            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\n          }\n          \n          .stat-icon {\n            width: 50px;\n            height: 50px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 50%;\n          }\n          \n          .light-primary {\n            background-color: rgba(13, 110, 253, 0.1);\n            color: #0d6efd;\n          }\n          \n          .light-success {\n            background-color: rgba(25, 135, 84, 0.1);\n            color: #198754;\n          }\n          \n          .light-warning {\n            background-color: rgba(255, 193, 7, 0.1);\n            color: #ffc107;\n          }\n          \n          .light-info {\n            background-color: rgba(13, 202, 240, 0.1);\n            color: #0dcaf0;\n          }\n          \n          .ai-assistant-card {\n            background-color: rgba(255, 255, 255, 0.1);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n            border-radius: 8px;\n          }\n          \n          .ai-insight-item {\n            transition: all 0.3s;\n            border-radius: 8px;\n            padding: 15px;\n            margin-bottom: 15px;\n            background-color: #f8f9fa;\n            border-left: 5px solid #0d6efd;\n          }\n          \n          .ai-insight-item:hover {\n            transform: translateX(5px);\n            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n          }\n          \n          .insight-icon {\n            width: 50px;\n            height: 50px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 50%;\n          }\n          \n          .chart-container {\n            height: 250px;\n          }\n          \n          .table th {\n            font-weight: 600;\n            font-size: 0.85rem;\n          }\n          \n          .table-hover tbody tr:hover {\n            background-color: rgba(13, 110, 253, 0.05);\n          }\n          \n          @media (max-width: 992px) {\n            .sidebar {\n              width: 70px !important;\n            }\n            \n            .sidebar .nav-link span, .sidebar-brand h4 {\n              display: none;\n            }\n            \n            .main-content {\n              margin-left: 70px !important;\n            }\n          }\n          \n          @media (max-width: 768px) {\n            .sidebar {\n              width: 0 !important;\n              transform: translateX(-100%);\n            }\n            \n            .main-content {\n              margin-left: 0 !important;\n            }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(App, \"hOyTFzwNMmFCL7VjSUFuLhJHGjY=\", false, function () {\n  return [useSearchParams, useAppSelector, useNavigate, useAppDispatch, useAppSelector];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useEffect", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Filler", "RoomAvailabilityCalendar", "Transaction", "AdditionalServicesPage", "RoomListingPage", "useNavigate", "useSearchParams", "Routers", "MyAccountHotelPage", "HotelManagement", "ListFeedbackHotelPage", "Cha<PERSON>", "useAppSelector", "Dropdown", "Image", "useAppDispatch", "DashBoardPage", "InsightAiPage", "RevenuePage", "AuthActions", "disconnectSocket", "Manager", "ManagementBooking", "MyReportPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "App", "_s", "_Auth$image", "_Auth$image2", "_Auth$image3", "searchParams", "setSearchParams", "activeTab", "setActiveTab", "get", "<PERSON><PERSON>", "state", "navigate", "dispatch", "tab", "Socket", "socket", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "style", "marginLeft", "type", "placeholder", "_id", "align", "Toggle", "variant", "display", "max<PERSON><PERSON><PERSON>", "whiteSpace", "overflow", "textOverflow", "name", "src", "image", "url", "undefined", "roundedCircle", "width", "height", "<PERSON><PERSON>", "<PERSON><PERSON>", "LOGOUT", "HomeHotel", "message", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/AI/DataAnalysisAI.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler,\r\n} from \"chart.js\";\r\nimport RoomAvailabilityCalendar from \"@pages/hotel_host/RoomAvailabilityCalendar\";\r\nimport Transaction from \"@pages/hotel_host/Transaction\";\r\nimport AdditionalServicesPage from \"../service/AdditionalServicesPage\";\r\nimport RoomListingPage from \"../../room/RoomListingPage\";\r\nimport { useNavigate, useSearchParams } from \"react-router-dom\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport \"bootstrap/dist/js/bootstrap.bundle.min\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport MyAccountHotelPage from \"../information/MyAccountHotelPage\";\r\nimport HotelManagement from \"../hotel/HotelManagement\";\r\nimport ListFeedbackHotelPage from \"../Feedback/ListFeedbackHotelPage\";\r\nimport Chat from \"../Chat\";\r\nimport { useAppSelector } from \"@redux/store\";\r\nimport { Dropdown, Image } from \"react-bootstrap\";\r\nimport { useAppDispatch } from \"@redux/store\";\r\nimport DashBoardPage from \"../dash_board/DashBoardPage\";\r\nimport InsightAiPage from \"./InSightAiPage\";\r\nimport RevenuePage from \"../revenue/RevenuePage\";\r\nimport AuthActions from \"@redux/auth/actions\";\r\nimport { disconnectSocket } from \"@redux/socket/socketSlice\";\r\nimport { Manager } from \"socket.io-client\";\r\nimport ManagementBooking from \"@pages/management_booking/ManagementBooking\";\r\nimport MyReportPage from \"../report/MyReportPage\";\r\n\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler\r\n);\r\n\r\nfunction App() {\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n  const [activeTab, setActiveTab] = useState(() => {\r\n    return searchParams.get(\"tab\") || \"revenue\";\r\n  });\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n\r\n  useEffect(() => {\r\n    setSearchParams({ tab: activeTab });\r\n  }, [activeTab, setSearchParams]);\r\n  const Socket = useAppSelector((state) => state.Socket.socket);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"d-flex\">\r\n        {/* Sidebar */}\r\n        <div className=\"sidebar\">\r\n          <div className=\"sidebar-brand d-flex align-items-center justify-content-center\">\r\n            <i className=\"bi bi-building fs-2 me-2\"></i>\r\n            <h4 className=\"mb-0\">Hotel partner</h4>\r\n          </div>\r\n\r\n          <ul className=\"nav flex-column\">\r\n            {/* <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"dashboard\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"dashboard\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-speedometer2 nav-icon\"></i>\r\n                <span>Dashboard</span>\r\n              </a>\r\n            </li> */}\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"revenue\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"revenue\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-graph-up nav-icon\"></i>\r\n                <span>Doanh thu</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"transaction\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"transaction\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-credit-card nav-icon\"></i>\r\n                <span>Giao dịch</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${activeTab === \"hotels\" ? \"active\" : \"\"}`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"hotels\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-building\"></i>\r\n                <span style={{ marginLeft: \"10px\" }}>Quản lý khách sạn</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"services\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"services\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-people nav-icon\"></i>\r\n                <span>Quản lý dịch vụ</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${activeTab === \"rooms\" ? \"active\" : \"\"}`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"rooms\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-door-closed nav-icon\"></i>\r\n                <span>Quản lý loại phòng</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"bookings\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"bookings\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-calendar-check nav-icon\"></i>\r\n                <span>Đặt phòng</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"management_bookings\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"management_bookings\");\r\n                }}\r\n              >\r\n                <i className=\"bi-clipboard-data\" />\r\n                <span style={{ marginLeft: \"10px\" }}>Quản lý đặt phòng</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"feedbacks\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"feedbacks\");\r\n                }}\r\n              >\r\n                <i className=\"bi-chat-left-text\" />\r\n                <span style={{ marginLeft: \"10px\" }}>Đánh giá khách sạn</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${activeTab === \"mess\" ? \"active\" : \"\"}`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"mess\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-chat-dots nav-icon\"></i>\r\n                <span>Tin nhắn</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"my_report\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"my_report\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-exclamation-triangle-fill\"></i>\r\n                <span style={{marginLeft: '5px'}}>Quản lý báo cáo</span>\r\n              </a>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <a\r\n                className={`nav-link ${\r\n                  activeTab === \"setting\" ? \"active\" : \"\"\r\n                }`}\r\n                href=\"#\"\r\n                onClick={() => {\r\n                  setActiveTab(\"setting\");\r\n                }}\r\n              >\r\n                <i className=\"bi bi-gear nav-icon\"></i>\r\n                <span>Cài đặt</span>\r\n              </a>\r\n            </li>\r\n          </ul>\r\n\r\n          <div className=\"mt-auto p-3\">\r\n            <div className=\"ai-assistant-card p-3\">\r\n              <div className=\"d-flex align-items-center mb-3\">\r\n                <i className=\"bi bi-robot fs-3 me-2\"></i>\r\n                <h6 className=\"mb-0\">Trợ lý AI</h6>\r\n              </div>\r\n              <p className=\"small mb-2\">\r\n                Hỏi tôi bất cứ điều gì về khách sạn của bạn\r\n              </p>\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  className=\"form-control form-control-sm\"\r\n                  placeholder=\"Nhập câu hỏi...\"\r\n                />\r\n                <button className=\"btn btn-primary btn-sm\">\r\n                  <i className=\"bi bi-send\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Main content */}\r\n        <div className=\"main-content\">\r\n          {/* Header */}\r\n          <nav className=\"navbar navbar-expand-lg navbar-light bg-white shadow-sm\">\r\n            <div className=\"container-fluid\">\r\n              <button className=\"btn btn-sm\" type=\"button\">\r\n                <i className=\"bi bi-list fs-5\"></i>\r\n              </button>\r\n              <div className=\"d-flex align-items-center\">\r\n                {/* <div className=\"dropdown me-3\">\r\n                  <button\r\n                    className=\"btn btn-sm position-relative\"\r\n                    type=\"button\"\r\n                  >\r\n                    <i className=\"bi bi-bell fs-5\"></i>\r\n                    <span className=\"position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger\">\r\n                      3\r\n                    </span>\r\n                  </button>\r\n                </div> */}\r\n                {Auth._id !== -1 && (\r\n                  <Dropdown align=\"end\">\r\n                    <Dropdown.Toggle\r\n                      variant=\"light\"\r\n                      className=\"login-btn d-flex align-items-center\"\r\n                    >\r\n                      <a\r\n                        style={{\r\n                          display: \"inline-block\",\r\n                          maxWidth: \"150px\", // hoặc width tùy bạn\r\n                          whiteSpace: \"nowrap\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                        }}\r\n                      >\r\n                        {Auth.name}\r\n                      </a>{\" \"}\r\n                      <Image\r\n                        src={\r\n                          Auth?.image?.url != \"\" &&\r\n                          Auth?.image?.url != undefined\r\n                            ? Auth?.image?.url\r\n                            : \"https://i.pinimg.com/736x/8f/1c/a2/8f1ca2029e2efceebd22fa05cca423d7.jpg\"\r\n                        }\r\n                        roundedCircle\r\n                        width=\"30\"\r\n                        height=\"30\"\r\n                        className=\"ms-2 me-2\"\r\n                      />\r\n                    </Dropdown.Toggle>\r\n                    <Dropdown.Menu>\r\n                      <Dropdown.Item\r\n                        onClick={() => {\r\n                          dispatch(disconnectSocket());\r\n                          dispatch({ type: AuthActions.LOGOUT });\r\n                          navigate(Routers.HomeHotel, {\r\n                            state: {\r\n                              message: \"Logout account successfully !!!\",\r\n                            },\r\n                          });\r\n                        }}\r\n                      >\r\n                        Đăng xuất\r\n                      </Dropdown.Item>\r\n                    </Dropdown.Menu>\r\n                  </Dropdown>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* Dashboard Content */}\r\n          <div className=\"container-fluid\">\r\n            {/* {activeTab === \"dashboard\" && (\r\n              <DashBoardPage setActiveTab={setActiveTab} />\r\n            )} */}\r\n\r\n            {activeTab === \"ai-insights\" && <InsightAiPage />}\r\n\r\n            {activeTab === \"bookings\" && (\r\n              <>\r\n                <RoomAvailabilityCalendar />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === \"transaction\" && (\r\n              <>\r\n                <Transaction />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === \"hotels\" && (\r\n              <>\r\n                <HotelManagement />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === \"rooms\" && (\r\n              <>\r\n                <RoomListingPage />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === \"services\" && (\r\n              <>\r\n                <AdditionalServicesPage />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === \"settings\" && (\r\n              <>\r\n                <MyAccountHotelPage />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === \"feedbacks\" && (\r\n              <>\r\n                <ListFeedbackHotelPage />\r\n              </>\r\n            )}\r\n            {activeTab === \"mess\" && (\r\n              <>\r\n                <Chat />\r\n              </>\r\n            )}\r\n            {activeTab === \"setting\" && (\r\n              <>\r\n                <MyAccountHotelPage />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === \"revenue\" && <RevenuePage />}\r\n            {activeTab === \"management_bookings\" && <ManagementBooking />}\r\n            {activeTab === \"my_report\" && <MyReportPage />}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <style>\r\n        {`\r\n          body {\r\n            margin: 0;\r\n            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\r\n            background-color: #f8f9fa;\r\n            overflow-x: hidden;\r\n          }\r\n          \r\n          .sidebar {\r\n            width: 250px;\r\n            min-height: 100vh;\r\n            padding: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            position: fixed;\r\n            left: 0;\r\n            top: 0;\r\n            bottom: 0;\r\n            z-index: 100;\r\n            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);\r\n            background-color: #212529;\r\n            color: white;\r\n          }\r\n          \r\n          .sidebar-brand {\r\n            background-color: #212529;\r\n            border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n            padding: 20px 0;\r\n          }\r\n          \r\n          .main-content {\r\n            flex-grow: 1;\r\n            margin-left: 250px;\r\n            min-height: 100vh;\r\n            background-color: #f8f9fa;\r\n          }\r\n          \r\n          .nav-item {\r\n            margin-bottom: 2px;\r\n          }\r\n          \r\n          .nav-link {\r\n            padding: 12px 20px;\r\n            transition: all 0.3s;\r\n            color: rgba(255, 255, 255, 0.8);\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n          \r\n          .nav-link:hover {\r\n            background-color: rgba(255, 255, 255, 0.1);\r\n            color: #fff !important;\r\n          }\r\n          \r\n          .nav-link.active {\r\n            background-color: #0d6efd;\r\n            color: #fff;\r\n          }\r\n          \r\n          .nav-icon {\r\n            width: 20px;\r\n            margin-right: 10px;\r\n            text-align: center;\r\n          }\r\n          \r\n          .card {\r\n            border-radius: 10px;\r\n            transition: transform 0.3s, box-shadow 0.3s;\r\n            margin-bottom: 20px;\r\n            border: none;\r\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n          }\r\n          \r\n          .card:hover {\r\n            transform: translateY(-3px);\r\n            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\r\n          }\r\n          \r\n          .stat-icon {\r\n            width: 50px;\r\n            height: 50px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border-radius: 50%;\r\n          }\r\n          \r\n          .light-primary {\r\n            background-color: rgba(13, 110, 253, 0.1);\r\n            color: #0d6efd;\r\n          }\r\n          \r\n          .light-success {\r\n            background-color: rgba(25, 135, 84, 0.1);\r\n            color: #198754;\r\n          }\r\n          \r\n          .light-warning {\r\n            background-color: rgba(255, 193, 7, 0.1);\r\n            color: #ffc107;\r\n          }\r\n          \r\n          .light-info {\r\n            background-color: rgba(13, 202, 240, 0.1);\r\n            color: #0dcaf0;\r\n          }\r\n          \r\n          .ai-assistant-card {\r\n            background-color: rgba(255, 255, 255, 0.1);\r\n            border: 1px solid rgba(255, 255, 255, 0.2);\r\n            border-radius: 8px;\r\n          }\r\n          \r\n          .ai-insight-item {\r\n            transition: all 0.3s;\r\n            border-radius: 8px;\r\n            padding: 15px;\r\n            margin-bottom: 15px;\r\n            background-color: #f8f9fa;\r\n            border-left: 5px solid #0d6efd;\r\n          }\r\n          \r\n          .ai-insight-item:hover {\r\n            transform: translateX(5px);\r\n            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n          }\r\n          \r\n          .insight-icon {\r\n            width: 50px;\r\n            height: 50px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border-radius: 50%;\r\n          }\r\n          \r\n          .chart-container {\r\n            height: 250px;\r\n          }\r\n          \r\n          .table th {\r\n            font-weight: 600;\r\n            font-size: 0.85rem;\r\n          }\r\n          \r\n          .table-hover tbody tr:hover {\r\n            background-color: rgba(13, 110, 253, 0.05);\r\n          }\r\n          \r\n          @media (max-width: 992px) {\r\n            .sidebar {\r\n              width: 70px !important;\r\n            }\r\n            \r\n            .sidebar .nav-link span, .sidebar-brand h4 {\r\n              display: none;\r\n            }\r\n            \r\n            .main-content {\r\n              margin-left: 70px !important;\r\n            }\r\n          }\r\n          \r\n          @media (max-width: 768px) {\r\n            .sidebar {\r\n              width: 0 !important;\r\n              transform: translateX(-100%);\r\n            }\r\n            \r\n            .main-content {\r\n              margin-left: 0 !important;\r\n            }\r\n          }\r\n        `}\r\n      </style>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,UAAU;AACjB,OAAOC,wBAAwB,MAAM,4CAA4C;AACjF,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,OAAO,sCAAsC;AAC7C,OAAO,wCAAwC;AAC/C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,QAAQ,EAAEC,KAAK,QAAQ,iBAAiB;AACjD,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElDrC,OAAO,CAACsC,QAAQ,CACdrC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,MACF,CAAC;AAED,SAAS6B,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA;EACb,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,eAAe,CAAC,CAAC;EACzD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,MAAM;IAC/C,OAAO+C,YAAY,CAACI,GAAG,CAAC,KAAK,CAAC,IAAI,SAAS;EAC7C,CAAC,CAAC;EACF,MAAMC,IAAI,GAAG3B,cAAc,CAAE4B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAG3B,cAAc,CAAC,CAAC;EAEjC3B,SAAS,CAAC,MAAM;IACd+C,eAAe,CAAC;MAAEQ,GAAG,EAAEP;IAAU,CAAC,CAAC;EACrC,CAAC,EAAE,CAACA,SAAS,EAAED,eAAe,CAAC,CAAC;EAChC,MAAMS,MAAM,GAAGhC,cAAc,CAAE4B,KAAK,IAAKA,KAAK,CAACI,MAAM,CAACC,MAAM,CAAC;EAE7D,oBACEpB,OAAA,CAAAE,SAAA;IAAAmB,QAAA,gBACErB,OAAA;MAAKsB,SAAS,EAAC,QAAQ;MAAAD,QAAA,gBAErBrB,OAAA;QAAKsB,SAAS,EAAC,SAAS;QAAAD,QAAA,gBACtBrB,OAAA;UAAKsB,SAAS,EAAC,gEAAgE;UAAAD,QAAA,gBAC7ErB,OAAA;YAAGsB,SAAS,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C1B,OAAA;YAAIsB,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAEN1B,OAAA;UAAIsB,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAe7BrB,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EACtC;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,SAAS,CAAC;cACzB,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3C1B,OAAA;gBAAAqB,QAAA,EAAM;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAC1C;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,aAAa,CAAC;cAC7B,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C1B,OAAA;gBAAAqB,QAAA,EAAM;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YAAYX,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChEgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,QAAQ,CAAC;cACxB,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClC1B,OAAA;gBAAM6B,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAO,CAAE;gBAAAT,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EACvC;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,UAAU,CAAC;cAC1B,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzC1B,OAAA;gBAAAqB,QAAA,EAAM;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YAAYX,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC/DgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,OAAO,CAAC;cACvB,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C1B,OAAA;gBAAAqB,QAAA,EAAM;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EACvC;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,UAAU,CAAC;cAC1B,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjD1B,OAAA;gBAAAqB,QAAA,EAAM;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,qBAAqB,GAAG,QAAQ,GAAG,EAAE,EAClD;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,qBAAqB,CAAC;cACrC,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnC1B,OAAA;gBAAM6B,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAO,CAAE;gBAAAT,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EACxC;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,WAAW,CAAC;cAC3B,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnC1B,OAAA;gBAAM6B,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAO,CAAE;gBAAAT,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YAAYX,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC9DgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,MAAM,CAAC;cACtB,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5C1B,OAAA;gBAAAqB,QAAA,EAAM;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EACxC;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,WAAW,CAAC;cAC3B,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD1B,OAAA;gBAAM6B,KAAK,EAAE;kBAACC,UAAU,EAAE;gBAAK,CAAE;gBAAAT,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL1B,OAAA;YAAIsB,SAAS,EAAC,UAAU;YAAAD,QAAA,eACtBrB,OAAA;cACEsB,SAAS,EAAE,YACTX,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EACtC;cACHgB,IAAI,EAAC,GAAG;cACRC,OAAO,EAAEA,CAAA,KAAM;gBACbhB,YAAY,CAAC,SAAS,CAAC;cACzB,CAAE;cAAAS,QAAA,gBAEFrB,OAAA;gBAAGsB,SAAS,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvC1B,OAAA;gBAAAqB,QAAA,EAAM;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEL1B,OAAA;UAAKsB,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC1BrB,OAAA;YAAKsB,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBACpCrB,OAAA;cAAKsB,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7CrB,OAAA;gBAAGsB,SAAS,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzC1B,OAAA;gBAAIsB,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN1B,OAAA;cAAGsB,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAE1B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ1B,OAAA;cAAKsB,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1BrB,OAAA;gBACE+B,IAAI,EAAC,MAAM;gBACXT,SAAS,EAAC,8BAA8B;gBACxCU,WAAW,EAAC;cAAiB;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACF1B,OAAA;gBAAQsB,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,eACxCrB,OAAA;kBAAGsB,SAAS,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1B,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAE3BrB,OAAA;UAAKsB,SAAS,EAAC,yDAAyD;UAAAD,QAAA,eACtErB,OAAA;YAAKsB,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BrB,OAAA;cAAQsB,SAAS,EAAC,YAAY;cAACS,IAAI,EAAC,QAAQ;cAAAV,QAAA,eAC1CrB,OAAA;gBAAGsB,SAAS,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACT1B,OAAA;cAAKsB,SAAS,EAAC,2BAA2B;cAAAD,QAAA,EAYvCP,IAAI,CAACmB,GAAG,KAAK,CAAC,CAAC,iBACdjC,OAAA,CAACZ,QAAQ;gBAAC8C,KAAK,EAAC,KAAK;gBAAAb,QAAA,gBACnBrB,OAAA,CAACZ,QAAQ,CAAC+C,MAAM;kBACdC,OAAO,EAAC,OAAO;kBACfd,SAAS,EAAC,qCAAqC;kBAAAD,QAAA,gBAE/CrB,OAAA;oBACE6B,KAAK,EAAE;sBACLQ,OAAO,EAAE,cAAc;sBACvBC,QAAQ,EAAE,OAAO;sBAAE;sBACnBC,UAAU,EAAE,QAAQ;sBACpBC,QAAQ,EAAE,QAAQ;sBAClBC,YAAY,EAAE;oBAChB,CAAE;oBAAApB,QAAA,EAEDP,IAAI,CAAC4B;kBAAI;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,EAAC,GAAG,eACR1B,OAAA,CAACX,KAAK;oBACJsD,GAAG,EACD,CAAA7B,IAAI,aAAJA,IAAI,wBAAAR,WAAA,GAAJQ,IAAI,CAAE8B,KAAK,cAAAtC,WAAA,uBAAXA,WAAA,CAAauC,GAAG,KAAI,EAAE,IACtB,CAAA/B,IAAI,aAAJA,IAAI,wBAAAP,YAAA,GAAJO,IAAI,CAAE8B,KAAK,cAAArC,YAAA,uBAAXA,YAAA,CAAasC,GAAG,KAAIC,SAAS,GACzBhC,IAAI,aAAJA,IAAI,wBAAAN,YAAA,GAAJM,IAAI,CAAE8B,KAAK,cAAApC,YAAA,uBAAXA,YAAA,CAAaqC,GAAG,GAChB,yEACL;oBACDE,aAAa;oBACbC,KAAK,EAAC,IAAI;oBACVC,MAAM,EAAC,IAAI;oBACX3B,SAAS,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACa,CAAC,eAClB1B,OAAA,CAACZ,QAAQ,CAAC8D,IAAI;kBAAA7B,QAAA,eACZrB,OAAA,CAACZ,QAAQ,CAAC+D,IAAI;oBACZvB,OAAO,EAAEA,CAAA,KAAM;sBACbX,QAAQ,CAACtB,gBAAgB,CAAC,CAAC,CAAC;sBAC5BsB,QAAQ,CAAC;wBAAEc,IAAI,EAAErC,WAAW,CAAC0D;sBAAO,CAAC,CAAC;sBACtCpC,QAAQ,CAAClC,OAAO,CAACuE,SAAS,EAAE;wBAC1BtC,KAAK,EAAE;0BACLuC,OAAO,EAAE;wBACX;sBACF,CAAC,CAAC;oBACJ,CAAE;oBAAAjC,QAAA,EACH;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1B,OAAA;UAAKsB,SAAS,EAAC,iBAAiB;UAAAD,QAAA,GAK7BV,SAAS,KAAK,aAAa,iBAAIX,OAAA,CAACR,aAAa;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAEhDf,SAAS,KAAK,UAAU,iBACvBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACxB,wBAAwB;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBAC5B,CACH,EAEAf,SAAS,KAAK,aAAa,iBAC1BX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACvB,WAAW;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBACf,CACH,EAEAf,SAAS,KAAK,QAAQ,iBACrBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAAChB,eAAe;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBACnB,CACH,EAEAf,SAAS,KAAK,OAAO,iBACpBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACrB,eAAe;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBACnB,CACH,EAEAf,SAAS,KAAK,UAAU,iBACvBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACtB,sBAAsB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBAC1B,CACH,EAEAf,SAAS,KAAK,UAAU,iBACvBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACjB,kBAAkB;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBACtB,CACH,EAEAf,SAAS,KAAK,WAAW,iBACxBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACf,qBAAqB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBACzB,CACH,EACAf,SAAS,KAAK,MAAM,iBACnBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACd,IAAI;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBACR,CACH,EACAf,SAAS,KAAK,SAAS,iBACtBX,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACErB,OAAA,CAACjB,kBAAkB;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,gBACtB,CACH,EAEAf,SAAS,KAAK,SAAS,iBAAIX,OAAA,CAACP,WAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1Cf,SAAS,KAAK,qBAAqB,iBAAIX,OAAA,CAACH,iBAAiB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC5Df,SAAS,KAAK,WAAW,iBAAIX,OAAA,CAACF,YAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1B,OAAA;MAAAqB,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA,eACR,CAAC;AAEP;AAACrB,EAAA,CAhhBQD,GAAG;EAAA,QAC8BvB,eAAe,EAI1CM,cAAc,EACVP,WAAW,EACXU,cAAc,EAKhBH,cAAc;AAAA;AAAAoE,EAAA,GAZtBnD,GAAG;AAkhBZ,eAAeA,GAAG;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}