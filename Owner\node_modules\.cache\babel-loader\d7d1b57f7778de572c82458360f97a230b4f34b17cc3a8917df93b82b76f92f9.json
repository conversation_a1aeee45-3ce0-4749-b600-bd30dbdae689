{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  isHangingPromiseRejectionError: null,\n  makeHangingPromise: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  isHangingPromiseRejectionError: function () {\n    return isHangingPromiseRejectionError;\n  },\n  makeHangingPromise: function () {\n    return makeHangingPromise;\n  }\n});\nfunction isHangingPromiseRejectionError(err) {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false;\n  }\n  return err.digest === HANGING_PROMISE_REJECTION;\n}\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION';\nclass HangingPromiseRejectionError extends Error {\n  constructor(expression) {\n    super(`During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by <PERSON>act but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`), this.expression = expression, this.digest = HANGING_PROMISE_REJECTION;\n  }\n}\nconst abortListenersBySignal = new WeakMap();\nfunction makeHangingPromise(signal, expression) {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression));\n  } else {\n    const hangingPromise = new Promise((_, reject) => {\n      const boundRejection = reject.bind(null, new HangingPromiseRejectionError(expression));\n      let currentListeners = abortListenersBySignal.get(signal);\n      if (currentListeners) {\n        currentListeners.push(boundRejection);\n      } else {\n        const listeners = [boundRejection];\n        abortListenersBySignal.set(signal, listeners);\n        signal.addEventListener('abort', () => {\n          for (let i = 0; i < listeners.length; i++) {\n            listeners[i]();\n          }\n        }, {\n          once: true\n        });\n      }\n    });\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject);\n    return hangingPromise;\n  }\n}\nfunction ignoreReject() {}", "map": {"version": 3, "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\server\\dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "mappings": ";;;;;;;;;;;;;;;;EAAgBA,8BAA8B,WAAAA,CAAA;WAA9BA,8BAAA;;EAgCAC,kBAAkB,WAAAA,CAAA;WAAlBA,kBAAA;;;AAhCT,SAASD,+BACdE,GAAY;EAEZ,IAAI,OAAOA,GAAA,KAAQ,YAAYA,GAAA,KAAQ,QAAQ,EAAE,YAAYA,GAAE,GAAI;IACjE,OAAO;EACT;EAEA,OAAOA,GAAA,CAAIC,MAAM,KAAKC,yBAAA;AACxB;AAEA,MAAMA,yBAAA,GAA4B;AAElC,MAAMC,4BAAA,SAAqCC,KAAA;EAGzCC,YAAYC,UAAkC,EAAE;IAC9C,KAAK,CACH,wBAAwBA,UAAA,wGAAkHA,UAAA,uJAAiK,QAFnRA,UAAA,GAAAA,UAAA,OAFZL,MAAA,GAASC,yBAAA;EAMzB;AACF;AAGA,MAAMK,sBAAA,GAAyB,IAAIC,OAAA;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;EAElB,IAAIG,MAAA,CAAOC,OAAO,EAAE;IAClB,OAAOC,OAAA,CAAQC,MAAM,CAAC,IAAIT,4BAAA,CAA6BG,UAAA;EACzD,OAAO;IACL,MAAMO,cAAA,GAAiB,IAAIF,OAAA,CAAW,CAACG,CAAA,EAAGF,MAAA;MACxC,MAAMG,cAAA,GAAiBH,MAAA,CAAOI,IAAI,CAChC,MACA,IAAIb,4BAAA,CAA6BG,UAAA;MAEnC,IAAIW,gBAAA,GAAmBV,sBAAA,CAAuBW,GAAG,CAACT,MAAA;MAClD,IAAIQ,gBAAA,EAAkB;QACpBA,gBAAA,CAAiBE,IAAI,CAACJ,cAAA;MACxB,OAAO;QACL,MAAMK,SAAA,GAAY,CAACL,cAAA,CAAe;QAClCR,sBAAA,CAAuBc,GAAG,CAACZ,MAAA,EAAQW,SAAA;QACnCX,MAAA,CAAOa,gBAAgB,CACrB,SACA;UACE,KAAK,IAAIC,CAAA,GAAI,GAAGA,CAAA,GAAIH,SAAA,CAAUI,MAAM,EAAED,CAAA,IAAK;YACzCH,SAAS,CAACG,CAAA,CAAE;UACd;QACF,GACA;UAAEE,IAAA,EAAM;QAAK;MAEjB;IACF;IACA;IACA;IACA;IACAZ,cAAA,CAAea,KAAK,CAACC,YAAA;IACrB,OAAOd,cAAA;EACT;AACF;AAEA,SAASc,aAAA,GAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}