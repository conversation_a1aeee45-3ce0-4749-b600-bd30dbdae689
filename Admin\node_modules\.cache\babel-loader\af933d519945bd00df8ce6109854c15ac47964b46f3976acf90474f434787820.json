{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Admin\\\\src\\\\pages\\\\dashboard\\\\DashboardPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardPage = ({\n  setActiveTab\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    data: dashboardData,\n    loading,\n    error\n  } = useSelector(state => state.AdminDashboard);\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n\n  // Fetch dashboard data on component mount and when period changes\n  useEffect(() => {\n    dispatch({\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\n      payload: {\n        params: {\n          period: selectedPeriod\n        },\n        onSuccess: data => {\n          console.log('Dashboard data loaded successfully:', data);\n        },\n        onFailed: error => {\n          console.error('Failed to load dashboard data:', error);\n        }\n      }\n    });\n  }, [dispatch, selectedPeriod]);\n\n  // Handle period change\n  const handlePeriodChange = period => {\n    setSelectedPeriod(period);\n  };\n\n  // Dữ liệu biểu đồ doanh thu\n  const revenueData = {\n    labels: [\"T1\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\", \"T8\", \"T9\", \"T10\", \"T11\", \"T12\"],\n    datasets: [{\n      label: \"Doanh thu thực tế\",\n      data: [12500, 13200, 15400, 18900, 21500, 25800, 28900, 27600, 24300, 19800, 16500, 22100],\n      borderColor: \"#4361ee\",\n      backgroundColor: \"rgba(67, 97, 238, 0.1)\",\n      tension: 0.4,\n      fill: true\n    }, {\n      label: \"Dự đoán (AI)\",\n      data: [12000, 13000, 15000, 19000, 22000, 26000, 29000, 28000, 24000, 20000, 17000, 23000],\n      borderColor: \"#f72585\",\n      borderDash: [5, 5],\n      tension: 0.4,\n      fill: false\n    }]\n  };\n\n  // Dữ liệu biểu đồ phân bố khách sạn theo khu vực\n  const hotelDistributionData = {\n    labels: [\"Miền Bắc\", \"Miền Trung\", \"Miền Nam\", \"Tây Nguyên\", \"Ven biển\"],\n    datasets: [{\n      data: [35, 25, 30, 5, 15],\n      backgroundColor: [\"#4361ee\", \"#3a0ca3\", \"#4cc9f0\", \"#f72585\", \"#7209b7\"],\n      borderWidth: 1\n    }]\n  };\n\n  // Dữ liệu biểu đồ phân loại khách sạn\n  const hotelCategoryData = {\n    labels: [\"5 sao\", \"4 sao\", \"3 sao\", \"2 sao\", \"Khác\"],\n    datasets: [{\n      data: [15, 25, 35, 20, 5],\n      backgroundColor: [\"#4cc9f0\", \"#4361ee\", \"#3a0ca3\", \"#7209b7\", \"#f72585\"],\n      borderWidth: 1\n    }]\n  };\n\n  // Dữ liệu yêu cầu phê duyệt gần đây\n  const recentApprovals = [{\n    id: \"A-7829\",\n    hotelName: \"Luxury Palace Hotel\",\n    owner: \"Nguyễn Văn A\",\n    location: \"Hà Nội\",\n    category: \"5 sao\",\n    submittedDate: \"15/06/2025\",\n    status: \"Đang chờ\"\n  }, {\n    id: \"A-7830\",\n    hotelName: \"Seaside Resort & Spa\",\n    owner: \"Trần Thị B\",\n    location: \"Đà Nẵng\",\n    category: \"4 sao\",\n    submittedDate: \"16/06/2025\",\n    status: \"Đang xem xét\"\n  }, {\n    id: \"A-7831\",\n    hotelName: \"City Center Hotel\",\n    owner: \"Lê Văn C\",\n    location: \"TP.HCM\",\n    category: \"3 sao\",\n    submittedDate: \"16/06/2025\",\n    status: \"Đang chờ\"\n  }, {\n    id: \"A-7832\",\n    hotelName: \"Mountain View Lodge\",\n    owner: \"Phạm Thị D\",\n    location: \"Đà Lạt\",\n    category: \"4 sao\",\n    submittedDate: \"17/06/2025\",\n    status: \"Đang xem xét\"\n  }, {\n    id: \"A-7833\",\n    hotelName: \"Riverside Boutique Hotel\",\n    owner: \"Hoàng Văn E\",\n    location: \"Huế\",\n    category: \"4 sao\",\n    submittedDate: \"18/06/2025\",\n    status: \"Đang chờ\"\n  }];\n\n  // Dữ liệu báo cáo feedback gần đây\n  const recentReports = [{\n    id: \"R-7829\",\n    customerName: \"Nguyễn Văn X\",\n    hotelName: \"Luxury Palace Hotel\",\n    reportType: \"Vi phạm chính sách\",\n    submittedDate: \"15/06/2025\",\n    status: \"Chưa xử lý\",\n    severity: \"Cao\"\n  }, {\n    id: \"R-7830\",\n    customerName: \"Trần Thị Y\",\n    hotelName: \"Seaside Resort & Spa\",\n    reportType: \"Chất lượng dịch vụ\",\n    submittedDate: \"16/06/2025\",\n    status: \"Đang xử lý\",\n    severity: \"Trung bình\"\n  }, {\n    id: \"R-7831\",\n    customerName: \"Lê Văn Z\",\n    hotelName: \"City Center Hotel\",\n    reportType: \"Sai thông tin\",\n    submittedDate: \"16/06/2025\",\n    status: \"Chưa xử lý\",\n    severity: \"Thấp\"\n  }, {\n    id: \"R-7832\",\n    customerName: \"Phạm Thị K\",\n    hotelName: \"Mountain View Lodge\",\n    reportType: \"Vi phạm chính sách\",\n    submittedDate: \"17/06/2025\",\n    status: \"Đang xử lý\",\n    severity: \"Cao\"\n  }];\n\n  // Dữ liệu danh sách hotel host\n  const hotelHosts = [{\n    id: \"H-7829\",\n    name: \"Nguyễn Văn A\",\n    email: \"<EMAIL>\",\n    phone: \"0901234567\",\n    hotels: 3,\n    joinDate: \"15/01/2023\",\n    status: \"Hoạt động\"\n  }, {\n    id: \"H-7830\",\n    name: \"Trần Thị B\",\n    email: \"<EMAIL>\",\n    phone: \"0912345678\",\n    hotels: 2,\n    joinDate: \"20/03/2023\",\n    status: \"Hoạt động\"\n  }, {\n    id: \"H-7831\",\n    name: \"Lê Văn C\",\n    email: \"<EMAIL>\",\n    phone: \"0923456789\",\n    hotels: 1,\n    joinDate: \"05/05/2023\",\n    status: \"Tạm khóa\"\n  }, {\n    id: \"H-7832\",\n    name: \"Phạm Thị D\",\n    email: \"<EMAIL>\",\n    phone: \"0934567890\",\n    hotels: 4,\n    joinDate: \"12/07/2023\",\n    status: \"Hoạt động\"\n  }, {\n    id: \"H-7833\",\n    name: \"Hoàng Văn E\",\n    email: \"<EMAIL>\",\n    phone: \"0945678901\",\n    hotels: 2,\n    joinDate: \"30/09/2023\",\n    status: \"Hoạt động\"\n  }];\n\n  // Lấy màu cho trạng thái\n  const getStatusColor = status => {\n    switch (status) {\n      case \"Đã thanh toán\":\n      case \"Hoạt động\":\n        return \"success\";\n      case \"Đang xử lý\":\n      case \"Đang xem xét\":\n      case \"Đang chờ\":\n        return \"warning\";\n      case \"Tạm khóa\":\n      case \"Chưa xử lý\":\n        return \"danger\";\n      default:\n        return \"secondary\";\n    }\n  };\n\n  // Lấy màu cho mức độ nghiêm trọng\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case \"Cao\":\n        return \"danger\";\n      case \"Trung bình\":\n        return \"warning\";\n      case \"Thấp\":\n        return \"info\";\n      default:\n        return \"secondary\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"T\\u1ED5ng quan h\\u1EC7 th\\u1ED1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-filter\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"H\\xF4m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"Tu\\u1EA7n n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              selected: true,\n              children: \"Th\\xE1ng n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"N\\u0103m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), \" Xu\\u1EA5t b\\xE1o c\\xE1o\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: overviewStats.totalHotels\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon hotels\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-building\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: overviewStats.activeHotels\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xE1ch s\\u1EA1n ho\\u1EA1t \\u0111\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon active\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-check-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: overviewStats.pendingApprovals\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon pending\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-hourglass-split\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: overviewStats.totalCustomers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon customers\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-people\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Doanh thu h\\u1EC7 th\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"btn-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Ng\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Tu\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-primary\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"N\\u0103m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-body\",\n        children: /*#__PURE__*/_jsxDEV(Line, {\n          data: revenueData,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                position: \"top\"\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: false,\n                grid: {\n                  drawBorder: false\n                },\n                ticks: {\n                  callback: value => value / 1000 + \"K\"\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n b\\u1ED1 kh\\xE1ch s\\u1EA1n theo khu v\\u1EF1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: /*#__PURE__*/_jsxDEV(Doughnut, {\n            data: hotelDistributionData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\"\n                }\n              },\n              cutout: \"70%\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: /*#__PURE__*/_jsxDEV(Pie, {\n            data: hotelCategoryData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\"\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"recent-activities\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activity-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Y\\xEAu c\\u1EA7u ph\\xEA duy\\u1EC7t g\\u1EA7n \\u0111\\xE2y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            onClick: () => setActiveTab(\"approvals\"),\n            className: \"view-all\",\n            children: \"Xem t\\u1EA5t c\\u1EA3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\xEAn kh\\xE1ch s\\u1EA1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EE7 s\\u1EDF h\\u1EEFu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\u1ECBa \\u0111i\\u1EC3m\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ng\\xE0y g\\u1EEDi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Thao t\\xE1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recentApprovals.slice(0, 3).map(approval => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.hotelName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.owner\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.location\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.submittedDate\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge bg-${getStatusColor(approval.status)}`,\n                      children: approval.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-primary\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 473,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 472,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-success\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-check-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 476,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 475,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-danger\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-x-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 479,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 478,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 23\n                  }, this)]\n                }, approval.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activity-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"B\\xE1o c\\xE1o vi ph\\u1EA1m g\\u1EA7n \\u0111\\xE2y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            onClick: () => setActiveTab(\"reports\"),\n            className: \"view-all\",\n            children: \"Xem t\\u1EA5t c\\u1EA3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Kh\\xE1ch h\\xE0ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Kh\\xE1ch s\\u1EA1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Lo\\u1EA1i b\\xE1o c\\xE1o\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"M\\u1EE9c \\u0111\\u1ED9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Thao t\\xE1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recentReports.slice(0, 3).map(report => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.customerName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.hotelName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.reportType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge bg-${getSeverityColor(report.severity)}`,\n                      children: report.severity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge bg-${getStatusColor(report.status)}`,\n                      children: report.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-primary\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 544,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 543,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-warning\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-pencil\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 547,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 546,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 23\n                  }, this)]\n                }, report.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 280,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"pBlSUJ1rKJpORKZOoh/qh4nmKVQ=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "Line", "Bar", "Pie", "Doughnut", "useDispatch", "useSelector", "AdminDashboardActions", "jsxDEV", "_jsxDEV", "DashboardPage", "setActiveTab", "_s", "dispatch", "data", "dashboardData", "loading", "error", "state", "AdminDashboard", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "type", "FETCH_ADMIN_DASHBOARD_METRICS", "payload", "params", "period", "onSuccess", "console", "log", "onFailed", "handlePeriodChange", "revenueData", "labels", "datasets", "label", "borderColor", "backgroundColor", "tension", "fill", "borderDash", "hotelDistributionData", "borderWidth", "hotelCategoryData", "recentApprovals", "id", "hotelName", "owner", "location", "category", "submittedDate", "status", "recentReports", "customerName", "reportType", "severity", "hotelHosts", "name", "email", "phone", "hotels", "joinDate", "getStatusColor", "getSeverityColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selected", "overviewStats", "totalHotels", "activeHotels", "pendingApprovals", "totalCustomers", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "scales", "y", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "value", "x", "display", "cutout", "href", "onClick", "slice", "map", "approval", "report", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/pages/dashboard/DashboardPage.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\r\n\r\nconst DashboardPage = ({setActiveTab}) => {\r\n  const dispatch = useDispatch();\r\n  const { data: dashboardData, loading, error } = useSelector(state => state.AdminDashboard);\r\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\r\n\r\n  // Fetch dashboard data on component mount and when period changes\r\n  useEffect(() => {\r\n    dispatch({\r\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\r\n      payload: {\r\n        params: { period: selectedPeriod },\r\n        onSuccess: (data) => {\r\n          console.log('Dashboard data loaded successfully:', data);\r\n        },\r\n        onFailed: (error) => {\r\n          console.error('Failed to load dashboard data:', error);\r\n        }\r\n      }\r\n    });\r\n  }, [dispatch, selectedPeriod]);\r\n\r\n  // Handle period change\r\n  const handlePeriodChange = (period) => {\r\n    setSelectedPeriod(period);\r\n  };\r\n\r\n  // Dữ liệu biểu đồ doanh thu\r\n  const revenueData = {\r\n    labels: [\r\n      \"T1\",\r\n      \"T2\",\r\n      \"T3\",\r\n      \"T4\",\r\n      \"T5\",\r\n      \"T6\",\r\n      \"T7\",\r\n      \"T8\",\r\n      \"T9\",\r\n      \"T10\",\r\n      \"T11\",\r\n      \"T12\",\r\n    ],\r\n    datasets: [\r\n      {\r\n        label: \"Doanh thu thực tế\",\r\n        data: [\r\n          12500, 13200, 15400, 18900, 21500, 25800, 28900, 27600, 24300, 19800,\r\n          16500, 22100,\r\n        ],\r\n        borderColor: \"#4361ee\",\r\n        backgroundColor: \"rgba(67, 97, 238, 0.1)\",\r\n        tension: 0.4,\r\n        fill: true,\r\n      },\r\n      {\r\n        label: \"Dự đoán (AI)\",\r\n        data: [\r\n          12000, 13000, 15000, 19000, 22000, 26000, 29000, 28000, 24000, 20000,\r\n          17000, 23000,\r\n        ],\r\n        borderColor: \"#f72585\",\r\n        borderDash: [5, 5],\r\n        tension: 0.4,\r\n        fill: false,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Dữ liệu biểu đồ phân bố khách sạn theo khu vực\r\n  const hotelDistributionData = {\r\n    labels: [\"Miền Bắc\", \"Miền Trung\", \"Miền Nam\", \"Tây Nguyên\", \"Ven biển\"],\r\n    datasets: [\r\n      {\r\n        data: [35, 25, 30, 5, 15],\r\n        backgroundColor: [\r\n          \"#4361ee\",\r\n          \"#3a0ca3\",\r\n          \"#4cc9f0\",\r\n          \"#f72585\",\r\n          \"#7209b7\",\r\n        ],\r\n        borderWidth: 1,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Dữ liệu biểu đồ phân loại khách sạn\r\n  const hotelCategoryData = {\r\n    labels: [\"5 sao\", \"4 sao\", \"3 sao\", \"2 sao\", \"Khác\"],\r\n    datasets: [\r\n      {\r\n        data: [15, 25, 35, 20, 5],\r\n        backgroundColor: [\r\n          \"#4cc9f0\",\r\n          \"#4361ee\",\r\n          \"#3a0ca3\",\r\n          \"#7209b7\",\r\n          \"#f72585\",\r\n        ],\r\n        borderWidth: 1,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Dữ liệu yêu cầu phê duyệt gần đây\r\n  const recentApprovals = [\r\n    {\r\n      id: \"A-7829\",\r\n      hotelName: \"Luxury Palace Hotel\",\r\n      owner: \"Nguyễn Văn A\",\r\n      location: \"Hà Nội\",\r\n      category: \"5 sao\",\r\n      submittedDate: \"15/06/2025\",\r\n      status: \"Đang chờ\",\r\n    },\r\n    {\r\n      id: \"A-7830\",\r\n      hotelName: \"Seaside Resort & Spa\",\r\n      owner: \"Trần Thị B\",\r\n      location: \"Đà Nẵng\",\r\n      category: \"4 sao\",\r\n      submittedDate: \"16/06/2025\",\r\n      status: \"Đang xem xét\",\r\n    },\r\n    {\r\n      id: \"A-7831\",\r\n      hotelName: \"City Center Hotel\",\r\n      owner: \"Lê Văn C\",\r\n      location: \"TP.HCM\",\r\n      category: \"3 sao\",\r\n      submittedDate: \"16/06/2025\",\r\n      status: \"Đang chờ\",\r\n    },\r\n    {\r\n      id: \"A-7832\",\r\n      hotelName: \"Mountain View Lodge\",\r\n      owner: \"Phạm Thị D\",\r\n      location: \"Đà Lạt\",\r\n      category: \"4 sao\",\r\n      submittedDate: \"17/06/2025\",\r\n      status: \"Đang xem xét\",\r\n    },\r\n    {\r\n      id: \"A-7833\",\r\n      hotelName: \"Riverside Boutique Hotel\",\r\n      owner: \"Hoàng Văn E\",\r\n      location: \"Huế\",\r\n      category: \"4 sao\",\r\n      submittedDate: \"18/06/2025\",\r\n      status: \"Đang chờ\",\r\n    },\r\n  ];\r\n\r\n  // Dữ liệu báo cáo feedback gần đây\r\n  const recentReports = [\r\n    {\r\n      id: \"R-7829\",\r\n      customerName: \"Nguyễn Văn X\",\r\n      hotelName: \"Luxury Palace Hotel\",\r\n      reportType: \"Vi phạm chính sách\",\r\n      submittedDate: \"15/06/2025\",\r\n      status: \"Chưa xử lý\",\r\n      severity: \"Cao\",\r\n    },\r\n    {\r\n      id: \"R-7830\",\r\n      customerName: \"Trần Thị Y\",\r\n      hotelName: \"Seaside Resort & Spa\",\r\n      reportType: \"Chất lượng dịch vụ\",\r\n      submittedDate: \"16/06/2025\",\r\n      status: \"Đang xử lý\",\r\n      severity: \"Trung bình\",\r\n    },\r\n    {\r\n      id: \"R-7831\",\r\n      customerName: \"Lê Văn Z\",\r\n      hotelName: \"City Center Hotel\",\r\n      reportType: \"Sai thông tin\",\r\n      submittedDate: \"16/06/2025\",\r\n      status: \"Chưa xử lý\",\r\n      severity: \"Thấp\",\r\n    },\r\n    {\r\n      id: \"R-7832\",\r\n      customerName: \"Phạm Thị K\",\r\n      hotelName: \"Mountain View Lodge\",\r\n      reportType: \"Vi phạm chính sách\",\r\n      submittedDate: \"17/06/2025\",\r\n      status: \"Đang xử lý\",\r\n      severity: \"Cao\",\r\n    },\r\n  ];\r\n\r\n  // Dữ liệu danh sách hotel host\r\n  const hotelHosts = [\r\n    {\r\n      id: \"H-7829\",\r\n      name: \"Nguyễn Văn A\",\r\n      email: \"<EMAIL>\",\r\n      phone: \"0901234567\",\r\n      hotels: 3,\r\n      joinDate: \"15/01/2023\",\r\n      status: \"Hoạt động\",\r\n    },\r\n    {\r\n      id: \"H-7830\",\r\n      name: \"Trần Thị B\",\r\n      email: \"<EMAIL>\",\r\n      phone: \"0912345678\",\r\n      hotels: 2,\r\n      joinDate: \"20/03/2023\",\r\n      status: \"Hoạt động\",\r\n    },\r\n    {\r\n      id: \"H-7831\",\r\n      name: \"Lê Văn C\",\r\n      email: \"<EMAIL>\",\r\n      phone: \"0923456789\",\r\n      hotels: 1,\r\n      joinDate: \"05/05/2023\",\r\n      status: \"Tạm khóa\",\r\n    },\r\n    {\r\n      id: \"H-7832\",\r\n      name: \"Phạm Thị D\",\r\n      email: \"<EMAIL>\",\r\n      phone: \"0934567890\",\r\n      hotels: 4,\r\n      joinDate: \"12/07/2023\",\r\n      status: \"Hoạt động\",\r\n    },\r\n    {\r\n      id: \"H-7833\",\r\n      name: \"Hoàng Văn E\",\r\n      email: \"<EMAIL>\",\r\n      phone: \"0945678901\",\r\n      hotels: 2,\r\n      joinDate: \"30/09/2023\",\r\n      status: \"Hoạt động\",\r\n    },\r\n  ];\r\n\r\n  // Lấy màu cho trạng thái\r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case \"Đã thanh toán\":\r\n      case \"Hoạt động\":\r\n        return \"success\";\r\n      case \"Đang xử lý\":\r\n      case \"Đang xem xét\":\r\n      case \"Đang chờ\":\r\n        return \"warning\";\r\n      case \"Tạm khóa\":\r\n      case \"Chưa xử lý\":\r\n        return \"danger\";\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n\r\n  // Lấy màu cho mức độ nghiêm trọng\r\n  const getSeverityColor = (severity) => {\r\n    switch (severity) {\r\n      case \"Cao\":\r\n        return \"danger\";\r\n      case \"Trung bình\":\r\n        return \"warning\";\r\n      case \"Thấp\":\r\n        return \"info\";\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n  return (\r\n    <div className=\"dashboard-content\">\r\n      <div className=\"page-header\">\r\n        <h1>Tổng quan hệ thống</h1>\r\n        <div className=\"page-actions\">\r\n          <div className=\"date-filter\">\r\n            <select className=\"form-select\">\r\n              <option>Hôm nay</option>\r\n              <option>Tuần này</option>\r\n              <option selected>Tháng này</option>\r\n              <option>Năm nay</option>\r\n            </select>\r\n          </div>\r\n          <button className=\"btn btn-primary\">\r\n            <i className=\"bi bi-download\"></i> Xuất báo cáo\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"stats-cards\">\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{overviewStats.totalHotels}</h3>\r\n            <p>Tổng số khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon hotels\">\r\n            <i className=\"bi bi-building\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{overviewStats.activeHotels}</h3>\r\n            <p>Khách sạn hoạt động</p>\r\n          </div>\r\n          <div className=\"stat-card-icon active\">\r\n            <i className=\"bi bi-check-circle\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{overviewStats.pendingApprovals}</h3>\r\n            <p>Chờ phê duyệt</p>\r\n          </div>\r\n          <div className=\"stat-card-icon pending\">\r\n            <i className=\"bi bi-hourglass-split\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{overviewStats.totalCustomers}</h3>\r\n            <p>Tổng số khách hàng</p>\r\n          </div>\r\n          <div className=\"stat-card-icon customers\">\r\n            <i className=\"bi bi-people\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Revenue Chart */}\r\n      <div className=\"chart-container\">\r\n        <div className=\"chart-header\">\r\n          <h2>Doanh thu hệ thống</h2>\r\n          <div className=\"chart-actions\">\r\n            <div className=\"btn-group\">\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Ngày</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Tuần</button>\r\n              <button className=\"btn btn-sm btn-primary\">Tháng</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Năm</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-body\">\r\n          <Line\r\n            data={revenueData}\r\n            options={{\r\n              responsive: true,\r\n              maintainAspectRatio: false,\r\n              plugins: {\r\n                legend: {\r\n                  position: \"top\",\r\n                },\r\n              },\r\n              scales: {\r\n                y: {\r\n                  beginAtZero: false,\r\n                  grid: {\r\n                    drawBorder: false,\r\n                  },\r\n                  ticks: {\r\n                    callback: (value) => value / 1000 + \"K\",\r\n                  },\r\n                },\r\n                x: {\r\n                  grid: {\r\n                    display: false,\r\n                  },\r\n                },\r\n              },\r\n            }}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Distribution Charts */}\r\n      <div className=\"charts-row\">\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân bố khách sạn theo khu vực</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            <Doughnut\r\n              data={hotelDistributionData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"bottom\",\r\n                  },\r\n                },\r\n                cutout: \"70%\",\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân loại khách sạn</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            <Pie\r\n              data={hotelCategoryData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"bottom\",\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recent Activities */}\r\n      <div className=\"recent-activities\">\r\n        <div className=\"activity-container\">\r\n          <div className=\"activity-header\">\r\n            <h2>Yêu cầu phê duyệt gần đây</h2>\r\n            <a\r\n              href=\"#\"\r\n              onClick={() => setActiveTab(\"approvals\")}\r\n              className=\"view-all\"\r\n            >\r\n              Xem tất cả\r\n            </a>\r\n          </div>\r\n          <div className=\"activity-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>ID</th>\r\n                    <th>Tên khách sạn</th>\r\n                    <th>Chủ sở hữu</th>\r\n                    <th>Địa điểm</th>\r\n                    <th>Ngày gửi</th>\r\n                    <th>Trạng thái</th>\r\n                    <th>Thao tác</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {recentApprovals.slice(0, 3).map((approval) => (\r\n                    <tr key={approval.id}>\r\n                      <td>{approval.id}</td>\r\n                      <td>{approval.hotelName}</td>\r\n                      <td>{approval.owner}</td>\r\n                      <td>{approval.location}</td>\r\n                      <td>{approval.submittedDate}</td>\r\n                      <td>\r\n                        <span\r\n                          className={`badge bg-${getStatusColor(\r\n                            approval.status\r\n                          )}`}\r\n                        >\r\n                          {approval.status}\r\n                        </span>\r\n                      </td>\r\n                      <td>\r\n                        <div className=\"action-buttons\">\r\n                          <button className=\"btn btn-sm btn-primary\">\r\n                            <i className=\"bi bi-eye\"></i>\r\n                          </button>\r\n                          <button className=\"btn btn-sm btn-success\">\r\n                            <i className=\"bi bi-check-lg\"></i>\r\n                          </button>\r\n                          <button className=\"btn btn-sm btn-danger\">\r\n                            <i className=\"bi bi-x-lg\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"activity-container\">\r\n          <div className=\"activity-header\">\r\n            <h2>Báo cáo vi phạm gần đây</h2>\r\n            <a\r\n              href=\"#\"\r\n              onClick={() => setActiveTab(\"reports\")}\r\n              className=\"view-all\"\r\n            >\r\n              Xem tất cả\r\n            </a>\r\n          </div>\r\n          <div className=\"activity-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>ID</th>\r\n                    <th>Khách hàng</th>\r\n                    <th>Khách sạn</th>\r\n                    <th>Loại báo cáo</th>\r\n                    <th>Mức độ</th>\r\n                    <th>Trạng thái</th>\r\n                    <th>Thao tác</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {recentReports.slice(0, 3).map((report) => (\r\n                    <tr key={report.id}>\r\n                      <td>{report.id}</td>\r\n                      <td>{report.customerName}</td>\r\n                      <td>{report.hotelName}</td>\r\n                      <td>{report.reportType}</td>\r\n                      <td>\r\n                        <span\r\n                          className={`badge bg-${getSeverityColor(\r\n                            report.severity\r\n                          )}`}\r\n                        >\r\n                          {report.severity}\r\n                        </span>\r\n                      </td>\r\n                      <td>\r\n                        <span\r\n                          className={`badge bg-${getStatusColor(\r\n                            report.status\r\n                          )}`}\r\n                        >\r\n                          {report.status}\r\n                        </span>\r\n                      </td>\r\n                      <td>\r\n                        <div className=\"action-buttons\">\r\n                          <button className=\"btn btn-sm btn-primary\">\r\n                            <i className=\"bi bi-eye\"></i>\r\n                          </button>\r\n                          <button className=\"btn btn-sm btn-warning\">\r\n                            <i className=\"bi bi-pencil\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,aAAa,GAAGA,CAAC;EAACC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI,EAAEC,aAAa;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGX,WAAW,CAACY,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;EAC1F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,OAAO,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACda,QAAQ,CAAC;MACPS,IAAI,EAAEf,qBAAqB,CAACgB,6BAA6B;MACzDC,OAAO,EAAE;QACPC,MAAM,EAAE;UAAEC,MAAM,EAAEN;QAAe,CAAC;QAClCO,SAAS,EAAGb,IAAI,IAAK;UACnBc,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEf,IAAI,CAAC;QAC1D,CAAC;QACDgB,QAAQ,EAAGb,KAAK,IAAK;UACnBW,OAAO,CAACX,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,QAAQ,EAAEO,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMW,kBAAkB,GAAIL,MAAM,IAAK;IACrCL,iBAAiB,CAACK,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMM,WAAW,GAAG;IAClBC,MAAM,EAAE,CACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,CACN;IACDC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,mBAAmB;MAC1BrB,IAAI,EAAE,CACJ,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,CACb;MACDsB,WAAW,EAAE,SAAS;MACtBC,eAAe,EAAE,wBAAwB;MACzCC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC,EACD;MACEJ,KAAK,EAAE,cAAc;MACrBrB,IAAI,EAAE,CACJ,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,CACb;MACDsB,WAAW,EAAE,SAAS;MACtBI,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBF,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAG;IAC5BR,MAAM,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEpB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MACzBuB,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDK,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG;IACxBV,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;IACpDC,QAAQ,EAAE,CACR;MACEpB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MACzBuB,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDK,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,CACtB;IACEC,EAAE,EAAE,QAAQ;IACZC,SAAS,EAAE,qBAAqB;IAChCC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,OAAO;IACjBC,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZC,SAAS,EAAE,sBAAsB;IACjCC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,OAAO;IACjBC,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZC,SAAS,EAAE,mBAAmB;IAC9BC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,OAAO;IACjBC,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZC,SAAS,EAAE,qBAAqB;IAChCC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,OAAO;IACjBC,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZC,SAAS,EAAE,0BAA0B;IACrCC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,OAAO;IACjBC,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE;EACV,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACEP,EAAE,EAAE,QAAQ;IACZQ,YAAY,EAAE,cAAc;IAC5BP,SAAS,EAAE,qBAAqB;IAChCQ,UAAU,EAAE,oBAAoB;IAChCJ,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE,YAAY;IACpBI,QAAQ,EAAE;EACZ,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZQ,YAAY,EAAE,YAAY;IAC1BP,SAAS,EAAE,sBAAsB;IACjCQ,UAAU,EAAE,oBAAoB;IAChCJ,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE,YAAY;IACpBI,QAAQ,EAAE;EACZ,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZQ,YAAY,EAAE,UAAU;IACxBP,SAAS,EAAE,mBAAmB;IAC9BQ,UAAU,EAAE,eAAe;IAC3BJ,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE,YAAY;IACpBI,QAAQ,EAAE;EACZ,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZQ,YAAY,EAAE,YAAY;IAC1BP,SAAS,EAAE,qBAAqB;IAChCQ,UAAU,EAAE,oBAAoB;IAChCJ,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE,YAAY;IACpBI,QAAQ,EAAE;EACZ,CAAC,CACF;;EAED;EACA,MAAMC,UAAU,GAAG,CACjB;IACEX,EAAE,EAAE,QAAQ;IACZY,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,YAAY;IACtBV,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZY,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,YAAY;IACtBV,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZY,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,YAAY;IACtBV,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZY,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,YAAY;IACtBV,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZY,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,YAAY;IACtBV,MAAM,EAAE;EACV,CAAC,CACF;;EAED;EACA,MAAMW,cAAc,GAAIX,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,eAAe;MACpB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,YAAY;MACjB,KAAK,cAAc;MACnB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;MACf,KAAK,YAAY;QACf,OAAO,QAAQ;MACjB;QACE,OAAO,WAAW;IACtB;EACF,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAIR,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,MAAM;MACf;QACE,OAAO,WAAW;IACtB;EACF,CAAC;EACD,oBACE9C,OAAA;IAAKuD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCxD,OAAA;MAAKuD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxD,OAAA;QAAAwD,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B5D,OAAA;QAAKuD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BxD,OAAA;YAAQuD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BxD,OAAA;cAAAwD,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxB5D,OAAA;cAAAwD,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzB5D,OAAA;cAAQ6D,QAAQ;cAAAL,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC5D,OAAA;cAAAwD,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5D,OAAA;UAAQuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBACjCxD,OAAA;YAAGuD,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,4BACpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAKuD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxD,OAAA;YAAAwD,QAAA,EAAKM,aAAa,CAACC;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpC5D,OAAA;YAAAwD,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCxD,OAAA;YAAGuD,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAKuD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxD,OAAA;YAAAwD,QAAA,EAAKM,aAAa,CAACE;UAAY;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5D,OAAA;YAAAwD,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCxD,OAAA;YAAGuD,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAKuD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxD,OAAA;YAAAwD,QAAA,EAAKM,aAAa,CAACG;UAAgB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzC5D,OAAA;YAAAwD,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCxD,OAAA;YAAGuD,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAKuD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxD,OAAA;YAAAwD,QAAA,EAAKM,aAAa,CAACI;UAAc;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC5D,OAAA;YAAAwD,QAAA,EAAG;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCxD,OAAA;YAAGuD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BxD,OAAA;QAAKuD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxD,OAAA;UAAAwD,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B5D,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BxD,OAAA;YAAKuD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxD,OAAA;cAAQuD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClE5D,OAAA;cAAQuD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClE5D,OAAA;cAAQuD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzD5D,OAAA;cAAQuD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBxD,OAAA,CAACR,IAAI;UACHa,IAAI,EAAEkB,WAAY;UAClB4C,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACPC,MAAM,EAAE;gBACNC,QAAQ,EAAE;cACZ;YACF,CAAC;YACDC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,KAAK;gBAClBC,IAAI,EAAE;kBACJC,UAAU,EAAE;gBACd,CAAC;gBACDC,KAAK,EAAE;kBACLC,QAAQ,EAAGC,KAAK,IAAKA,KAAK,GAAG,IAAI,GAAG;gBACtC;cACF,CAAC;cACDC,CAAC,EAAE;gBACDL,IAAI,EAAE;kBACJM,OAAO,EAAE;gBACX;cACF;YACF;UACF;QAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBxD,OAAA;QAAKuD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCxD,OAAA;UAAKuD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxD,OAAA;YAAAwD,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBxD,OAAA,CAACL,QAAQ;YACPU,IAAI,EAAE2B,qBAAsB;YAC5BmC,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE;gBACZ;cACF,CAAC;cACDW,MAAM,EAAE;YACV;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCxD,OAAA;UAAKuD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxD,OAAA;YAAAwD,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBxD,OAAA,CAACN,GAAG;YACFW,IAAI,EAAE6B,iBAAkB;YACxBiC,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE;gBACZ;cACF;YACF;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxD,OAAA;QAAKuD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCxD,OAAA;UAAKuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxD,OAAA;YAAAwD,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClC5D,OAAA;YACEoF,IAAI,EAAC,GAAG;YACRC,OAAO,EAAEA,CAAA,KAAMnF,YAAY,CAAC,WAAW,CAAE;YACzCqD,SAAS,EAAC,UAAU;YAAAC,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BxD,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BxD,OAAA;cAAOuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCxD,OAAA;gBAAAwD,QAAA,eACExD,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,EAAI;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACX5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR5D,OAAA;gBAAAwD,QAAA,EACGrB,eAAe,CAACmD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,QAAQ,iBACxCxF,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,EAAKgC,QAAQ,CAACpD;kBAAE;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtB5D,OAAA;oBAAAwD,QAAA,EAAKgC,QAAQ,CAACnD;kBAAS;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7B5D,OAAA;oBAAAwD,QAAA,EAAKgC,QAAQ,CAAClD;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzB5D,OAAA;oBAAAwD,QAAA,EAAKgC,QAAQ,CAACjD;kBAAQ;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5B5D,OAAA;oBAAAwD,QAAA,EAAKgC,QAAQ,CAAC/C;kBAAa;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjC5D,OAAA;oBAAAwD,QAAA,eACExD,OAAA;sBACEuD,SAAS,EAAE,YAAYF,cAAc,CACnCmC,QAAQ,CAAC9C,MACX,CAAC,EAAG;sBAAAc,QAAA,EAEHgC,QAAQ,CAAC9C;oBAAM;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL5D,OAAA;oBAAAwD,QAAA,eACExD,OAAA;sBAAKuD,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BxD,OAAA;wBAAQuD,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxCxD,OAAA;0BAAGuD,SAAS,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eACT5D,OAAA;wBAAQuD,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxCxD,OAAA;0BAAGuD,SAAS,EAAC;wBAAgB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACT5D,OAAA;wBAAQuD,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,eACvCxD,OAAA;0BAAGuD,SAAS,EAAC;wBAAY;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GA3BE4B,QAAQ,CAACpD,EAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4BhB,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCxD,OAAA;UAAKuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxD,OAAA;YAAAwD,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC5D,OAAA;YACEoF,IAAI,EAAC,GAAG;YACRC,OAAO,EAAEA,CAAA,KAAMnF,YAAY,CAAC,SAAS,CAAE;YACvCqD,SAAS,EAAC,UAAU;YAAAC,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BxD,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BxD,OAAA;cAAOuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCxD,OAAA;gBAAAwD,QAAA,eACExD,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,EAAI;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACX5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnB5D,OAAA;oBAAAwD,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR5D,OAAA;gBAAAwD,QAAA,EACGb,aAAa,CAAC2C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEE,MAAM,iBACpCzF,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,EAAKiC,MAAM,CAACrD;kBAAE;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpB5D,OAAA;oBAAAwD,QAAA,EAAKiC,MAAM,CAAC7C;kBAAY;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9B5D,OAAA;oBAAAwD,QAAA,EAAKiC,MAAM,CAACpD;kBAAS;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3B5D,OAAA;oBAAAwD,QAAA,EAAKiC,MAAM,CAAC5C;kBAAU;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5B5D,OAAA;oBAAAwD,QAAA,eACExD,OAAA;sBACEuD,SAAS,EAAE,YAAYD,gBAAgB,CACrCmC,MAAM,CAAC3C,QACT,CAAC,EAAG;sBAAAU,QAAA,EAEHiC,MAAM,CAAC3C;oBAAQ;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL5D,OAAA;oBAAAwD,QAAA,eACExD,OAAA;sBACEuD,SAAS,EAAE,YAAYF,cAAc,CACnCoC,MAAM,CAAC/C,MACT,CAAC,EAAG;sBAAAc,QAAA,EAEHiC,MAAM,CAAC/C;oBAAM;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL5D,OAAA;oBAAAwD,QAAA,eACExD,OAAA;sBAAKuD,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BxD,OAAA;wBAAQuD,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxCxD,OAAA;0BAAGuD,SAAS,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eACT5D,OAAA;wBAAQuD,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxCxD,OAAA;0BAAGuD,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAhCE6B,MAAM,CAACrD,EAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCd,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CA3iBIF,aAAa;EAAA,QACAL,WAAW,EACoBC,WAAW;AAAA;AAAA6F,EAAA,GAFvDzF,aAAa;AA6iBnB,eAAeA,aAAa;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}