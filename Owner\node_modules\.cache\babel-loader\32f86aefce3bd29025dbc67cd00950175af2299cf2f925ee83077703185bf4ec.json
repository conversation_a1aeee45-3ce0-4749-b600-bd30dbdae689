{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\RoomAvailabilityCalendar.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Row, Col, Card, Badge, Button, Form, Tooltip, OverlayTrigger, Modal, Table } from \"react-bootstrap\";\nimport { FaFilter, FaCalendarAlt, FaUser, FaInfoCircle, FaCheck, FaTimes, FaClock, FaSignInAlt, FaSignOutAlt, FaPrint, FaWifi, FaUtensils, FaWineGlassAlt, FaCar, FaSpa, FaSwimmingPool } from \"react-icons/fa\";\nimport { useAppSelector, useAppDispatch } from \"@redux/store\";\nimport RoomUnitActions from \"@redux/room_unit/action\";\nimport \"../../css/hotelHost/BookingSchedule.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RoomAvailabilityCalendar() {\n  _s();\n  var _rooms$find, _rooms$find2, _rooms$find3, _rooms$find4, _rooms$find5, _rooms$find6, _rooms$find7, _rooms$find8;\n  const dispatch = useAppDispatch();\n\n  // Lấy dữ liệu từ Redux store với fallback\n  const {\n    rooms = [],\n    bookings: rawBookings = [],\n    availableServices = [],\n    filters = {},\n    loading = false,\n    error = null\n  } = useAppSelector(state => state.RoomUnit);\n\n  // Nếu availableServices trống, khởi tạo dữ liệu mẫu\n  const defaultServices = [{\n    id: 1,\n    name: \"WiFi\",\n    price: 5,\n    icon: FaWifi\n  }, {\n    id: 2,\n    name: \"Bữa sáng\",\n    price: 15,\n    icon: FaUtensils\n  }, {\n    id: 3,\n    name: \"Minibar\",\n    price: 20,\n    icon: FaWineGlassAlt\n  }, {\n    id: 4,\n    name: \"Đỗ xe\",\n    price: 10,\n    icon: FaCar\n  }, {\n    id: 5,\n    name: \"Spa\",\n    price: 50,\n    icon: FaSpa\n  }, {\n    id: 6,\n    name: \"Hồ bơi\",\n    price: 25,\n    icon: FaSwimmingPool\n  }];\n  const services = availableServices.length > 0 ? availableServices : defaultServices;\n\n  // Chuyển đổi bookings về đúng kiểu Date\n  const bookings = rawBookings.map(b => ({\n    ...b,\n    startDate: b.startDate instanceof Date ? b.startDate : new Date(b.startDate),\n    endDate: b.endDate instanceof Date ? b.endDate : new Date(b.endDate)\n  }));\n\n  // State cho ngày hiện tại và phạm vi hiển thị\n  const [currentDate, setCurrentDate] = useState(new Date());\n  const [viewDays, setViewDays] = useState(14); // Số ngày hiển thị\n  const [selectedRoom, setSelectedRoom] = useState(null);\n  const [showBookingModal, setShowBookingModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [selectedBooking, setSelectedBooking] = useState(null);\n  const [filterRoomType, setFilterRoomType] = useState(\"all\");\n\n  // State mới cho quản lý phòng\n  const [showAddRoomModal, setShowAddRoomModal] = useState(false);\n  const [showEditRoomModal, setShowEditRoomModal] = useState(false);\n  const [roomToEdit, setRoomToEdit] = useState(null);\n\n  // State mới cho nhận phòng và trả phòng\n  const [showCheckInModal, setShowCheckInModal] = useState(false);\n  const [showCheckOutModal, setShowCheckOutModal] = useState(false);\n  const [checkInBooking, setCheckInBooking] = useState(null);\n  const [checkOutBooking, setCheckOutBooking] = useState(null);\n  const [selectedServices, setSelectedServices] = useState([]);\n  const [paymentMethod, setPaymentMethod] = useState(\"credit\");\n\n  // Tạo ngày cho lịch\n  const getDates = () => {\n    const dates = [];\n    const startDate = new Date(currentDate);\n    for (let i = 0; i < viewDays; i++) {\n      const date = new Date(startDate);\n      date.setDate(date.getDate() + i);\n      dates.push(date);\n    }\n    return dates;\n  };\n\n  // Định dạng ngày để hiển thị\n  const formatDate = date => {\n    return date.toLocaleDateString(\"vi-VN\", {\n      month: \"short\",\n      day: \"numeric\"\n    });\n  };\n\n  // Định dạng ngày trong tuần\n  const formatDayOfWeek = date => {\n    return date.toLocaleDateString(\"vi-VN\", {\n      weekday: \"short\"\n    });\n  };\n\n  // Kiểm tra xem phòng có được đặt vào một ngày cụ thể không\n  const isRoomBooked = (roomId, date) => {\n    return bookings.some(booking => booking.roomId === roomId && date >= booking.startDate && date < booking.endDate);\n  };\n\n  // Lấy thông tin đặt phòng cho một phòng và ngày cụ thể\n  const getBooking = (roomId, date) => {\n    return bookings.find(booking => booking.roomId === roomId && date >= booking.startDate && date < booking.endDate);\n  };\n\n  // Kiểm tra xem hôm nay có phải là ngày nhận phòng cho một đặt phòng không\n  const isCheckInDate = (booking, date) => {\n    return date.toDateString() === booking.startDate.toDateString();\n  };\n\n  // Kiểm tra xem hôm nay có phải là ngày trả phòng cho một đặt phòng không\n  const isCheckOutDate = (booking, date) => {\n    const checkoutDate = new Date(booking.endDate);\n    checkoutDate.setDate(checkoutDate.getDate() - 1);\n    return date.toDateString() === checkoutDate.toDateString();\n  };\n\n  // Xử lý điều hướng ngày\n  const navigateDate = days => {\n    const newDate = new Date(currentDate);\n    newDate.setDate(newDate.getDate() + days);\n    setCurrentDate(newDate);\n  };\n\n  // Xử lý khi nhấp vào phòng để đặt\n  const handleRoomClick = (room, date) => {\n    const booking = getBooking(room.id, date);\n    if (booking) {\n      // Hiển thị chi tiết đặt phòng\n      setSelectedBooking(booking);\n      setShowDetailsModal(true);\n    } else {\n      // Hiển thị biểu mẫu đặt phòng\n      setSelectedRoom({\n        ...room,\n        date\n      });\n      setShowBookingModal(true);\n    }\n  };\n\n  // Xử lý đặt phòng mới\n  const handleAddBooking = e => {\n    e.preventDefault();\n\n    // Lấy dữ liệu biểu mẫu\n    const guestName = e.target.guestName.value;\n    const startDate = new Date(selectedRoom.date);\n    const nights = Number.parseInt(e.target.nights.value);\n    const guestCount = Number.parseInt(e.target.guestCount.value);\n    const idNumber = e.target.idNumber.value;\n    const phoneNumber = e.target.phoneNumber.value;\n    const email = e.target.email.value;\n\n    // Tính ngày kết thúc\n    const endDate = new Date(startDate);\n    endDate.setDate(endDate.getDate() + nights);\n\n    // Tạo đặt phòng mới\n    const newBooking = {\n      id: bookings.length + 1,\n      roomId: selectedRoom.id,\n      guestName,\n      startDate,\n      endDate,\n      status: \"confirmed\",\n      type: selectedRoom.type.toLowerCase(),\n      guestCount,\n      paymentStatus: \"pending\",\n      checkedIn: false,\n      checkedOut: false,\n      services: [],\n      depositAmount: 0,\n      idNumber,\n      phoneNumber,\n      email\n    };\n\n    // Thêm đặt phòng vào danh sách\n    dispatch({\n      type: RoomUnitActions.ADD_BOOKING_SUCCESS,\n      payload: newBooking\n    });\n\n    // Đóng modal\n    setShowBookingModal(false);\n  };\n\n  // Mở modal chỉnh sửa phòng\n  const openEditRoomModal = room => {\n    setRoomToEdit(room);\n    setShowEditRoomModal(true);\n  };\n\n  // Mở modal nhận phòng\n  const openCheckInModal = booking => {\n    setCheckInBooking(booking);\n    setShowCheckInModal(true);\n  };\n\n  // Mở modal trả phòng\n  const openCheckOutModal = booking => {\n    console.log('Opening checkout modal for booking:', booking); // Debug\n    setCheckOutBooking(booking);\n    setSelectedServices([]);\n    setShowCheckOutModal(true);\n  };\n\n  // Xử lý xác nhận nhận phòng\n  const handleCheckIn = (bookingId, depositAmount) => {\n    dispatch({\n      type: RoomUnitActions.CHECK_IN_SUCCESS,\n      payload: {\n        bookingId,\n        depositAmount\n      }\n    });\n    setShowCheckInModal(false);\n  };\n\n  // Xử lý xác nhận trả phòng\n  const handleCheckOut = (bookingId, selectedServices) => {\n    dispatch({\n      type: RoomUnitActions.CHECK_OUT_SUCCESS,\n      payload: {\n        bookingId,\n        selectedServices\n      }\n    });\n    setShowCheckOutModal(false);\n  };\n\n  // Chuyển đổi lựa chọn dịch vụ\n  const toggleService = service => {\n    if (selectedServices.some(s => s.id === service.id)) {\n      setSelectedServices(selectedServices.filter(s => s.id !== service.id));\n    } else {\n      setSelectedServices([...selectedServices, service]);\n    }\n  };\n\n  // Tính tổng hóa đơn cho trả phòng\n  const calculateBill = booking => {\n    if (!booking) {\n      return {\n        roomCharge: 0,\n        serviceCharge: 0,\n        total: 0,\n        deposit: 0,\n        balance: 0\n      };\n    }\n    try {\n      // Lấy giá phòng\n      const room = rooms.find(r => r.id === booking.roomId);\n      const roomPrice = room ? room.price : 0;\n\n      // Tính số đêm\n      const nights = Math.round((booking.endDate - booking.startDate) / (1000 * 60 * 60 * 24));\n\n      // Tính phí phòng\n      const roomCharge = roomPrice * nights;\n\n      // Tính phí dịch vụ - kiểm tra an toàn\n      const serviceCharge = Array.isArray(selectedServices) ? selectedServices.reduce((total, service) => total + (service.price || 0), 0) : 0;\n\n      // Tính tổng\n      const total = roomCharge + serviceCharge;\n\n      // Tính số tiền còn lại (tổng - đặt cọc)\n      const deposit = booking.depositAmount || 0;\n      const balance = total - deposit;\n      return {\n        roomCharge,\n        serviceCharge,\n        total,\n        deposit,\n        balance\n      };\n    } catch (error) {\n      console.error('Error calculating bill:', error);\n      return {\n        roomCharge: 0,\n        serviceCharge: 0,\n        total: 0,\n        deposit: 0,\n        balance: 0\n      };\n    }\n  };\n\n  // Lọc phòng dựa trên loại\n  const filteredRooms = filterRoomType === \"all\" ? rooms : filterRoomType === \"available\" ? rooms.filter(room => !bookings.some(booking => booking.roomId === room.id && currentDate >= booking.startDate && currentDate < booking.endDate && !booking.checkedOut)) : rooms.filter(room => room.type.toLowerCase() === filterRoomType.toLowerCase());\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content_1 p-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" text-black d-flex justify-content-between align-items-center mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"T\\u1ED5ng quan kh\\xE1ch s\\u1EA1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-primary\",\n            className: \"me-2\",\n            onClick: () => navigateDate(-viewDays),\n            children: \"<< Tr\\u01B0\\u1EDBc\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: \"black\"\n              },\n              className: \"mt-1 me-2\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"me-2\",\n                style: {\n                  justifyContent: \"center\",\n                  alignItems: \"center\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), formatDate(currentDate), \" - \", formatDate(getDates()[getDates().length - 1])]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-primary\",\n            onClick: () => navigateDate(viewDays),\n            children: \"Sau >>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        className: \"p-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-filters p-3  border-bottom\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            className: \"align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaFilter, {\n                  className: \"me-2 text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"fw-bold\",\n                  children: \"B\\u1ED9 l\\u1ECDc:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Lo\\u1EA1i ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: filterRoomType,\n                  onChange: e => setFilterRoomType(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"T\\u1EA5t c\\u1EA3 ph\\xF2ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"available\",\n                    children: \"Ph\\xF2ng tr\\u1ED1ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Single room\",\n                    children: \"Ph\\xF2ng \\u0111\\u01A1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Double room\",\n                    children: \"Ph\\xF2ng \\u0111\\xF4i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Family room\",\n                    children: \"Ph\\xF2ng gia \\u0111\\xECnh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Ph\\u1EA1m vi hi\\u1EC3n th\\u1ECB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: viewDays,\n                  onChange: e => setViewDays(Number.parseInt(e.target.value)),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"7\",\n                    children: \"7 ng\\xE0y\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"14\",\n                    children: \"14 ng\\xE0y\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"30\",\n                    children: \"30 ng\\xE0y\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          className: \"mt-3 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"availability-summary\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"py-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"T\\xF3m t\\u1EAFt t\\xECnh tr\\u1EA1ng ph\\xF2ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"success\",\n                      className: \"ms-2\",\n                      children: [rooms.filter(r => !bookings.some(b => b.roomId === r.id && currentDate >= b.startDate && currentDate < b.endDate && !b.checkedOut)).length, \" \", \"Tr\\u1ED1ng\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"danger\",\n                      className: \"ms-2\",\n                      children: [rooms.filter(r => bookings.some(b => b.roomId === r.id && currentDate >= b.startDate && currentDate < b.endDate && b.paymentStatus === \"paid\" && !b.checkedOut)).length, \" \", \"\\u0110\\xE3 thu\\xEA\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"warning\",\n                      className: \"ms-2\",\n                      children: [rooms.filter(r => bookings.some(b => b.roomId === r.id && currentDate >= b.startDate && currentDate < b.endDate && b.paymentStatus === \"pending\" && !b.checkedOut)).length, \" \", \"\\u0110ang ch\\u1EDD\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"calendar-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"calendar-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"calendar-cell room-header\",\n                children: [\"Ph\\xF2ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"room-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Nh\\u1EA5p v\\xE0o ph\\xF2ng \\u0111\\u1EC3 xem t\\xF9y ch\\u1ECDn!!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), getDates().map((date, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `calendar-cell date-header ${date.getDay() === 0 || date.getDay() === 6 ? \"weekend\" : \"\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"day-of-week\",\n                  children: formatDayOfWeek(date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"date\",\n                  children: formatDate(date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), filteredRooms.map(room => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"calendar-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"calendar-cell room-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"room-header-row\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"room-number\",\n                    children: room.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"room-type\",\n                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: room.type === \"Single room\" ? \"info\" : room.type === \"Double room\" ? \"primary\" : \"success\",\n                    children: room.type === \"Single room\" ? \"Phòng Đơn\" : room.type === \"Double room\" ? \"Phòng Đôi\" : \"Phòng Gia Đình\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"room-capacity\",\n                  children: [/*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 23\n                  }, this), \" \", room.capacity]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"room-price\",\n                  children: [\"$\", room.price, \"/\\u0111\\xEAm\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), getDates().map((date, dateIndex) => {\n                const isBooked = isRoomBooked(room.id, date);\n                const booking = isBooked ? getBooking(room.id, date) : null;\n                const isPending = booking && booking.paymentStatus === \"pending\";\n                const isCheckedOut = booking && booking.checkedOut;\n                const isToday = date.toDateString() === new Date().toDateString();\n                const isCheckIn = booking && isCheckInDate(booking, date);\n                const isCheckOut = booking && isCheckOutDate(booking, date);\n\n                // Xác định lớp ô\n                let cellClass = \"available\";\n                if (isBooked) {\n                  if (isCheckedOut) {\n                    cellClass = \"available\";\n                  } else if (isPending) {\n                    cellClass = \"pending\";\n                  } else {\n                    cellClass = \"booked\";\n                  }\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `calendar-cell date-cell ${cellClass} ${date.getDay() === 0 || date.getDay() === 6 ? \"weekend\" : \"\"}`,\n                  onClick: () => !isBooked && handleRoomClick(room, date),\n                  children: isBooked && !isCheckedOut ? /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                    placement: \"top\",\n                    overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: booking.guestName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 507,\n                        columnNumber: 33\n                      }, this), \"Nh\\u1EADn ph\\xF2ng: \", booking.startDate.toLocaleDateString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 509,\n                        columnNumber: 33\n                      }, this), \"Tr\\u1EA3 ph\\xF2ng: \", booking.endDate.toLocaleDateString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 33\n                      }, this), \"Tr\\u1EA1ng th\\xE1i: \", booking.paymentStatus === \"paid\" ? \"Đã thanh toán\" : \"Chưa thanh toán\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 513,\n                        columnNumber: 33\n                      }, this), booking.checkedIn ? \"Đã nhận phòng\" : \"Chưa nhận phòng\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 31\n                    }, this),\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"booking-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"guest-name\",\n                        children: booking.guestName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 519,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `status-icon ${isPending ? \"pending-icon\" : \"booked-icon\"}`,\n                        children: isPending ? /*#__PURE__*/_jsxDEV(FaClock, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 521,\n                          columnNumber: 46\n                        }, this) : /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 521,\n                          columnNumber: 60\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 31\n                      }, this), !booking.checkedIn && isCheckIn && isToday && /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"sm\",\n                        variant: \"warning\",\n                        className: \"check-action-btn\",\n                        onClick: e => {\n                          e.stopPropagation();\n                          openCheckInModal(booking);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(FaSignInAlt, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 535,\n                          columnNumber: 35\n                        }, this), \" Nh\\u1EADn ph\\xF2ng\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 33\n                      }, this), booking.checkedIn && isCheckOut && isToday && !booking.checkedOut && /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"sm\",\n                        variant: \"danger\",\n                        className: \"check-action-btn\",\n                        onClick: e => {\n                          e.stopPropagation();\n                          openCheckOutModal(booking);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 550,\n                          columnNumber: 35\n                        }, this), \" Tr\\u1EA3 ph\\xF2ng\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"available-cell\",\n                    children: /*#__PURE__*/_jsxDEV(FaCheck, {\n                      className: \"available-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 27\n                  }, this)\n                }, dateIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 23\n                }, this);\n              })]\n            }, room.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this)), filteredRooms.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"calendar-row empty-state\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"calendar-cell empty-message\",\n                colSpan: getDates().length + 1,\n                children: [\"Kh\\xF4ng c\\xF3 ph\\xF2ng ph\\xF9 h\\u1EE3p v\\u1EDBi b\\u1ED9 l\\u1ECDc \\u0111\\xE3 ch\\u1ECDn.\", \" \", /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"link\",\n                  onClick: () => setShowAddRoomModal(true),\n                  children: \"Th\\xEAm ph\\xF2ng m\\u1EDBi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showBookingModal,\n      onHide: () => setShowBookingModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [\"\\u0110\\u1EB7t ph\\xF2ng \", selectedRoom === null || selectedRoom === void 0 ? void 0 : selectedRoom.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedRoom && /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: handleAddBooking,\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"T\\xEAn kh\\xE1ch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              name: \"guestName\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"S\\u1ED1 CMND/CCCD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              name: \"idNumber\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"tel\",\n              name: \"phoneNumber\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"email\",\n              name: \"email\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Ng\\xE0y nh\\u1EADn ph\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: selectedRoom.date.toLocaleDateString(),\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"S\\u1ED1 \\u0111\\xEAm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"number\",\n              name: \"nights\",\n              min: \"1\",\n              defaultValue: \"1\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"S\\u1ED1 kh\\xE1ch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"number\",\n              name: \"guestCount\",\n              min: \"1\",\n              max: selectedRoom.capacity,\n              defaultValue: \"1\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: [\"S\\u1EE9c ch\\u1EE9a t\\u1ED1i \\u0111a: \", selectedRoom.capacity]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Lo\\u1EA1i ph\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: selectedRoom.type === \"Single room\" ? \"Đơn\" : selectedRoom.type === \"Double room\" ? \"Đôi\" : \"Suite\",\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Gi\\xE1 m\\u1ED7i \\u0111\\xEAm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: `$${selectedRoom.price}`,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-grid\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              type: \"submit\",\n              children: \"X\\xE1c nh\\u1EADn \\u0111\\u1EB7t ph\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showDetailsModal,\n      onHide: () => setShowDetailsModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        className: \"bg-primary text-white\",\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Chi ti\\u1EBFt \\u0111\\u1EB7t ph\\xF2ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedBooking && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [\"Ph\\xF2ng \", selectedBooking.roomId, \" - \", selectedBooking.guestName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"booking-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Nh\\u1EADn ph\\xF2ng:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: selectedBooking.startDate.toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Tr\\u1EA3 ph\\xF2ng:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: selectedBooking.endDate.toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"S\\u1ED1 \\u0111\\xEAm:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: Math.round((selectedBooking.endDate - selectedBooking.startDate) / (1000 * 60 * 60 * 24))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Lo\\u1EA1i ph\\xF2ng:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedBooking.type === \"Single room\" ? \"info\" : selectedBooking.type === \"Double room\" ? \"primary\" : \"success\",\n                  children: selectedBooking.type === \"Single room\" ? \"Đơn\" : selectedBooking.type === \"Double room\" ? \"Đôi\" : \"Suite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"S\\u1ED1 kh\\xE1ch:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: selectedBooking.guestCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Tr\\u1EA1ng th\\xE1i:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedBooking.status === \"confirmed\" ? \"success\" : \"warning\",\n                  children: selectedBooking.status === \"confirmed\" ? \"Đã xác nhận\" : \"Chờ xác nhận\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Thanh to\\xE1n:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedBooking.paymentStatus === \"paid\" ? \"success\" : selectedBooking.paymentStatus === \"partially paid\" ? \"info\" : \"warning\",\n                  children: selectedBooking.paymentStatus === \"paid\" ? \"Đã thanh toán\" : selectedBooking.paymentStatus === \"partially paid\" ? \"Thanh toán một phần\" : \"Chưa thanh toán\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Nh\\u1EADn ph\\xF2ng:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedBooking.checkedIn ? \"success\" : \"secondary\",\n                  children: selectedBooking.checkedIn ? \"Đã nhận phòng\" : \"Chưa nhận phòng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Tr\\u1EA3 ph\\xF2ng:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedBooking.checkedOut ? \"success\" : \"secondary\",\n                  children: selectedBooking.checkedOut ? \"Đã trả phòng\" : \"Chưa trả phòng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"Li\\xEAn h\\u1EC7:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: [selectedBooking.phoneNumber, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 21\n                }, this), selectedBooking.email]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this), selectedBooking.services && selectedBooking.services.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-label\",\n                children: \"D\\u1ECBch v\\u1EE5:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-value\",\n                children: selectedBooking.services.map(service => /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: \"info\",\n                  className: \"me-1 mb-1\",\n                  children: [service.name, \" ($\", service.price, \")\"]\n                }, service.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 d-flex justify-content-between\",\n            children: [!selectedBooking.checkedIn ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"warning\",\n              onClick: () => openCheckInModal(selectedBooking),\n              children: [/*#__PURE__*/_jsxDEV(FaSignInAlt, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 21\n              }, this), \" Nh\\u1EADn ph\\xF2ng\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 19\n            }, this) : !selectedBooking.checkedOut ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              onClick: () => openCheckOutModal(selectedBooking),\n              children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 21\n              }, this), \" Tr\\u1EA3 ph\\xF2ng\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 21\n              }, this), \" Xem chi ti\\u1EBFt \\u0111\\u1EA7y \\u0111\\u1EE7\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 19\n              }, this), \" H\\u1EE7y \\u0111\\u1EB7t ph\\xF2ng\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 658,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showCheckInModal,\n      onHide: () => setShowCheckInModal(false),\n      centered: true,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        className: \"bg-warning text-white\",\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaSignInAlt, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this), \" Nh\\u1EADn ph\\xF2ng\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: checkInBooking && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"bg-light\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: \"Th\\xF4ng tin kh\\xE1ch\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"T\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 821,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.guestName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"S\\u1ED1 CMND/CCCD:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 824,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.idNumber]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0110i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 827,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.phoneNumber]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Email:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.email]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"bg-light\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: \"Chi ti\\u1EBFt \\u0111\\u1EB7t ph\\xF2ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Ph\\xF2ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.roomId, \" (\", ((_rooms$find = rooms.find(r => r.id === checkInBooking.roomId)) === null || _rooms$find === void 0 ? void 0 : _rooms$find.type) === \"Single room\" ? \"Đơn\" : ((_rooms$find2 = rooms.find(r => r.id === checkInBooking.roomId)) === null || _rooms$find2 === void 0 ? void 0 : _rooms$find2.type) === \"Double room\" ? \"Đôi\" : \"Suite\", \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Nh\\u1EADn ph\\xF2ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.startDate.toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tr\\u1EA3 ph\\xF2ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 854,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.endDate.toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"S\\u1ED1 \\u0111\\xEAm:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 857,\n                      columnNumber: 25\n                    }, this), \" \", Math.round((checkInBooking.endDate - checkInBooking.startDate) / (1000 * 60 * 60 * 24))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"S\\u1ED1 kh\\xE1ch:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 861,\n                      columnNumber: 25\n                    }, this), \" \", checkInBooking.guestCount]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-light\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"Th\\xF4ng tin thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Gi\\xE1 ph\\xF2ng m\\u1ED7i \\u0111\\xEAm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 877,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        value: `$${((_rooms$find3 = rooms.find(r => r.id === checkInBooking.roomId)) === null || _rooms$find3 === void 0 ? void 0 : _rooms$find3.price) || 0}`,\n                        disabled: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 878,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"T\\u1ED5ng ti\\u1EC1n ph\\xF2ng\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        value: `$${(((_rooms$find4 = rooms.find(r => r.id === checkInBooking.roomId)) === null || _rooms$find4 === void 0 ? void 0 : _rooms$find4.price) || 0) * Math.round((checkInBooking.endDate - checkInBooking.startDate) / (1000 * 60 * 60 * 24))}`,\n                        disabled: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 888,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 886,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\u1EB7t c\\u1ECDc\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 902,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"number\",\n                        id: \"depositAmount\",\n                        defaultValue: ((_rooms$find5 = rooms.find(r => r.id === checkInBooking.roomId)) === null || _rooms$find5 === void 0 ? void 0 : _rooms$find5.price) || 0,\n                        min: \"0\",\n                        step: \"0.01\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 903,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 901,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 914,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                        value: paymentMethod,\n                        onChange: e => setPaymentMethod(e.target.value),\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"credit\",\n                          children: \"Th\\u1EBB t\\xEDn d\\u1EE5ng\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 916,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"cash\",\n                          children: \"Ti\\u1EC1n m\\u1EB7t\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 917,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"bank\",\n                          children: \"Chuy\\u1EC3n kho\\u1EA3n\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 918,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 915,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 913,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 912,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-between\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              onClick: () => setShowCheckInModal(false),\n              children: \"H\\u1EE7y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"warning\",\n              onClick: () => {\n                const depositAmount = Number.parseFloat(document.getElementById(\"depositAmount\").value);\n                handleCheckIn(checkInBooking.id, depositAmount);\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaSignInAlt, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 19\n              }, this), \" X\\xE1c nh\\u1EADn nh\\u1EADn ph\\xF2ng\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showCheckOutModal,\n      onHide: () => setShowCheckOutModal(false),\n      centered: true,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        className: \"bg-danger text-white\",\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 13\n          }, this), \" Tr\\u1EA3 ph\\xF2ng\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: checkOutBooking ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"bg-light\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: \"Th\\xF4ng tin kh\\xE1ch\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"T\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 964,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.guestName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"S\\u1ED1 CMND/CCCD:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 967,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.idNumber]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0110i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 970,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.phoneNumber]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 969,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Email:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 973,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.email]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"bg-light\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: \"Chi ti\\u1EBFt \\u0111\\u1EB7t ph\\xF2ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Ph\\xF2ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.roomId, \" (\", ((_rooms$find6 = rooms.find(r => r.id === checkOutBooking.roomId)) === null || _rooms$find6 === void 0 ? void 0 : _rooms$find6.type) === \"Single room\" ? \"Đơn\" : ((_rooms$find7 = rooms.find(r => r.id === checkOutBooking.roomId)) === null || _rooms$find7 === void 0 ? void 0 : _rooms$find7.type) === \"Double room\" ? \"Đôi\" : \"Suite\", \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Nh\\u1EADn ph\\xF2ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 994,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.startDate.toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 993,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tr\\u1EA3 ph\\xF2ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 997,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.endDate.toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"S\\u1ED1 \\u0111\\xEAm:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1000,\n                      columnNumber: 25\n                    }, this), \" \", Math.round((checkOutBooking.endDate - checkOutBooking.startDate) / (1000 * 60 * 60 * 24))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"S\\u1ED1 kh\\xE1ch:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1004,\n                      columnNumber: 25\n                    }, this), \" \", checkOutBooking.guestCount]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-light\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"D\\u1ECBch v\\u1EE5 b\\u1ED5 sung \\u0111\\xE3 s\\u1EED d\\u1EE5ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Ch\\u1ECDn c\\xE1c d\\u1ECBch v\\u1EE5 b\\u1ED5 sung m\\xE0 kh\\xE1ch \\u0111\\xE3 s\\u1EED d\\u1EE5ng:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: Array.isArray(availableServices) && availableServices.length > 0 ? availableServices.map(service => /*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"mb-2\",\n                  children: /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    id: `service-${service.id}`,\n                    label: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.icon && /*#__PURE__*/_jsxDEV(service.icon, {\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1026,\n                        columnNumber: 50\n                      }, this), service.name, \" ($\", service.price, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 31\n                    }, this),\n                    checked: selectedServices.some(s => s.id === service.id),\n                    onChange: () => toggleService(service)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1021,\n                    columnNumber: 27\n                  }, this)\n                }, service.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 25\n                }, this)) : /*#__PURE__*/_jsxDEV(Col, {\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Kh\\xF4ng c\\xF3 d\\u1ECBch v\\u1EE5 b\\u1ED5 sung n\\xE0o.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-light\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"T\\xF3m t\\u1EAFt h\\xF3a \\u0111\\u01A1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(Table, {\n                striped: true,\n                bordered: true,\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"M\\xF4 t\\u1EA3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1052,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"text-end\",\n                      children: \"S\\u1ED1 ti\\u1EC1n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1053,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1051,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1050,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: [\"Ti\\u1EC1n ph\\xF2ng (\", Math.round((checkOutBooking.endDate - checkOutBooking.startDate) / (1000 * 60 * 60 * 24)), \" \", \"\\u0111\\xEAm @ $\", ((_rooms$find8 = rooms.find(r => r.id === checkOutBooking.roomId)) === null || _rooms$find8 === void 0 ? void 0 : _rooms$find8.price) || 0, \"/\\u0111\\xEAm)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1058,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-end\",\n                      children: [\"$\", calculateBill(checkOutBooking).roomCharge.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1064,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1057,\n                    columnNumber: 23\n                  }, this), selectedServices.map(service => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: service.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-end\",\n                      children: [\"$\", service.price.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1069,\n                      columnNumber: 27\n                    }, this)]\n                  }, service.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1067,\n                    columnNumber: 25\n                  }, this)), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"fw-bold\",\n                      children: \"T\\u1ED5ng chi ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1073,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-end fw-bold\",\n                      children: [\"$\", (calculateBill(checkOutBooking).roomCharge + calculateBill(checkOutBooking).serviceCharge).toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1074,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: \"\\u0110\\xE3 \\u0111\\u1EB7t c\\u1ECDc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1082,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-end\",\n                      children: [\"-$\", (checkOutBooking.depositAmount || 0).toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1083,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1081,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"table-primary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"fw-bold\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n thanh to\\xE1n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1088,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-end fw-bold\",\n                      children: [\"$\", calculateBill(checkOutBooking).balance.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1089,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: paymentMethod,\n                    onChange: e => setPaymentMethod(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"credit\",\n                      children: \"Th\\u1EBB t\\xEDn d\\u1EE5ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"cash\",\n                      children: \"Ti\\u1EC1n m\\u1EB7t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1099,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"bank\",\n                      children: \"Chuy\\u1EC3n kho\\u1EA3n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1100,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1094,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1044,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-between\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              onClick: () => setShowCheckOutModal(false),\n              children: \"H\\u1EE7y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                onClick: () => handleCheckOut(checkOutBooking.id, selectedServices),\n                children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1113,\n                  columnNumber: 21\n                }, this), \" X\\xE1c nh\\u1EADn tr\\u1EA3 ph\\xF2ng\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1107,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xF4ng th\\u1EC3 t\\u1EA3i th\\xF4ng tin \\u0111\\u1EB7t ph\\xF2ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1120,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowCheckOutModal(false),\n            children: \"\\u0110\\xF3ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1119,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 953,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 947,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 313,\n    columnNumber: 5\n  }, this);\n}\n_s(RoomAvailabilityCalendar, \"2gEeYj+KwZswcaYamOIPQgWXTsU=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\n_c = RoomAvailabilityCalendar;\nexport default RoomAvailabilityCalendar;\nvar _c;\n$RefreshReg$(_c, \"RoomAvailabilityCalendar\");", "map": {"version": 3, "names": ["useState", "Row", "Col", "Card", "Badge", "<PERSON><PERSON>", "Form", "<PERSON><PERSON><PERSON>", "OverlayTrigger", "Modal", "Table", "FaFilter", "FaCalendarAlt", "FaUser", "FaInfoCircle", "FaCheck", "FaTimes", "FaClock", "FaSignInAlt", "FaSignOutAlt", "FaPrint", "FaWifi", "FaUtensils", "FaWineGlassAlt", "FaCar", "FaSpa", "FaSwimmingPool", "useAppSelector", "useAppDispatch", "RoomUnitActions", "jsxDEV", "_jsxDEV", "RoomAvailabilityCalendar", "_s", "_rooms$find", "_rooms$find2", "_rooms$find3", "_rooms$find4", "_rooms$find5", "_rooms$find6", "_rooms$find7", "_rooms$find8", "dispatch", "rooms", "bookings", "rawBookings", "availableServices", "filters", "loading", "error", "state", "RoomUnit", "defaultServices", "id", "name", "price", "icon", "services", "length", "map", "b", "startDate", "Date", "endDate", "currentDate", "setCurrentDate", "viewDays", "setViewDays", "selected<PERSON><PERSON>", "setSelectedRoom", "showBookingModal", "setShowBookingModal", "showDetailsModal", "setShowDetailsModal", "selectedBooking", "setSelectedBooking", "filterRoomType", "setFilterRoomType", "showAddRoomModal", "setShowAddRoomModal", "showEditRoomModal", "setShowEditRoomModal", "roomToEdit", "setRoomToEdit", "showCheckInModal", "setShowCheckInModal", "showCheckOutModal", "setShowCheckOutModal", "checkInBooking", "setCheckInBooking", "checkOutBooking", "setCheckOutBooking", "selectedServices", "setSelectedServices", "paymentMethod", "setPaymentMethod", "getDates", "dates", "i", "date", "setDate", "getDate", "push", "formatDate", "toLocaleDateString", "month", "day", "formatDayOfWeek", "weekday", "isRoomBooked", "roomId", "some", "booking", "getBooking", "find", "isCheckInDate", "toDateString", "isCheckOutDate", "checkoutDate", "navigateDate", "days", "newDate", "handleRoomClick", "room", "handleAddBooking", "e", "preventDefault", "<PERSON><PERSON><PERSON>", "target", "value", "nights", "Number", "parseInt", "guestCount", "idNumber", "phoneNumber", "email", "newBooking", "status", "type", "toLowerCase", "paymentStatus", "checkedIn", "checkedOut", "depositAmount", "ADD_BOOKING_SUCCESS", "payload", "openEditRoomModal", "openCheckInModal", "openCheckOutModal", "console", "log", "handleCheckIn", "bookingId", "CHECK_IN_SUCCESS", "handleCheckOut", "CHECK_OUT_SUCCESS", "toggleService", "service", "s", "filter", "calculateBill", "roomCharge", "serviceCharge", "total", "deposit", "balance", "r", "roomPrice", "Math", "round", "Array", "isArray", "reduce", "filteredRooms", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "style", "color", "justifyContent", "alignItems", "Body", "md", "Group", "Label", "Select", "onChange", "bg", "index", "getDay", "capacity", "dateIndex", "isBooked", "isPending", "isCheckedOut", "isToday", "isCheckIn", "isCheckOut", "cellClass", "placement", "overlay", "size", "stopPropagation", "colSpan", "show", "onHide", "centered", "Header", "closeButton", "Title", "onSubmit", "Control", "required", "disabled", "min", "defaultValue", "max", "Text", "step", "parseFloat", "document", "getElementById", "Check", "label", "checked", "striped", "bordered", "toFixed", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/RoomAvailabilityCalendar.jsx"], "sourcesContent": ["import { useState } from \"react\"\r\nimport { Row, Col, Card, Badge, Button, Form, Tooltip, OverlayTrigger, Modal, Table } from \"react-bootstrap\"\r\nimport {\r\n  FaFilter,\r\n  FaCalendarAlt,\r\n  FaUser,\r\n  FaInfoCircle,\r\n  FaCheck,\r\n  FaTimes,\r\n  FaClock,\r\n  FaSignInAlt,\r\n  FaSignOutAlt,\r\n  FaPrint,\r\n  FaWifi,\r\n  FaUtensils,\r\n  FaWineGlassAlt,\r\n  FaCar,\r\n  FaSpa,\r\n  FaSwimmingPool,\r\n} from \"react-icons/fa\"\r\nimport { useAppSelector, useAppDispatch } from \"@redux/store\"\r\nimport RoomUnitActions from \"@redux/room_unit/action\"\r\nimport \"../../css/hotelHost/BookingSchedule.css\"\r\n\r\nfunction RoomAvailabilityCalendar() {\r\n  const dispatch = useAppDispatch()\r\n\r\n  // Lấy dữ liệu từ Redux store với fallback\r\n  const { \r\n    rooms = [], \r\n    bookings: rawBookings = [], \r\n    availableServices = [], \r\n    filters = {}, \r\n    loading = false, \r\n    error = null \r\n  } = useAppSelector((state) => state.RoomUnit)\r\n\r\n  // Nếu availableServices trống, khởi tạo dữ liệu mẫu\r\n  const defaultServices = [\r\n    { id: 1, name: \"WiFi\", price: 5, icon: FaWifi },\r\n    { id: 2, name: \"Bữa sáng\", price: 15, icon: FaUtensils },\r\n    { id: 3, name: \"Minibar\", price: 20, icon: FaWineGlassAlt },\r\n    { id: 4, name: \"Đỗ xe\", price: 10, icon: FaCar },\r\n    { id: 5, name: \"Spa\", price: 50, icon: FaSpa },\r\n    { id: 6, name: \"Hồ bơi\", price: 25, icon: FaSwimmingPool },\r\n  ]\r\n\r\n  const services = availableServices.length > 0 ? availableServices : defaultServices\r\n  \r\n  // Chuyển đổi bookings về đúng kiểu Date\r\n  const bookings = rawBookings.map(b => ({\r\n    ...b,\r\n    startDate: b.startDate instanceof Date ? b.startDate : new Date(b.startDate),\r\n    endDate: b.endDate instanceof Date ? b.endDate : new Date(b.endDate),\r\n  }))\r\n\r\n  // State cho ngày hiện tại và phạm vi hiển thị\r\n  const [currentDate, setCurrentDate] = useState(new Date())\r\n  const [viewDays, setViewDays] = useState(14) // Số ngày hiển thị\r\n  const [selectedRoom, setSelectedRoom] = useState(null)\r\n  const [showBookingModal, setShowBookingModal] = useState(false)\r\n  const [showDetailsModal, setShowDetailsModal] = useState(false)\r\n  const [selectedBooking, setSelectedBooking] = useState(null)\r\n  const [filterRoomType, setFilterRoomType] = useState(\"all\")\r\n\r\n  // State mới cho quản lý phòng\r\n  const [showAddRoomModal, setShowAddRoomModal] = useState(false)\r\n  const [showEditRoomModal, setShowEditRoomModal] = useState(false)\r\n  const [roomToEdit, setRoomToEdit] = useState(null)\r\n\r\n  // State mới cho nhận phòng và trả phòng\r\n  const [showCheckInModal, setShowCheckInModal] = useState(false)\r\n  const [showCheckOutModal, setShowCheckOutModal] = useState(false)\r\n  const [checkInBooking, setCheckInBooking] = useState(null)\r\n  const [checkOutBooking, setCheckOutBooking] = useState(null)\r\n  const [selectedServices, setSelectedServices] = useState([])\r\n  const [paymentMethod, setPaymentMethod] = useState(\"credit\")\r\n\r\n  // Tạo ngày cho lịch\r\n  const getDates = () => {\r\n    const dates = []\r\n    const startDate = new Date(currentDate)\r\n\r\n    for (let i = 0; i < viewDays; i++) {\r\n      const date = new Date(startDate)\r\n      date.setDate(date.getDate() + i)\r\n      dates.push(date)\r\n    }\r\n\r\n    return dates\r\n  }\r\n\r\n  // Định dạng ngày để hiển thị\r\n  const formatDate = (date) => {\r\n    return date.toLocaleDateString(\"vi-VN\", { month: \"short\", day: \"numeric\" })\r\n  }\r\n\r\n  // Định dạng ngày trong tuần\r\n  const formatDayOfWeek = (date) => {\r\n    return date.toLocaleDateString(\"vi-VN\", { weekday: \"short\" })\r\n  }\r\n\r\n  // Kiểm tra xem phòng có được đặt vào một ngày cụ thể không\r\n  const isRoomBooked = (roomId, date) => {\r\n    return bookings.some((booking) => booking.roomId === roomId && date >= booking.startDate && date < booking.endDate)\r\n  }\r\n\r\n  // Lấy thông tin đặt phòng cho một phòng và ngày cụ thể\r\n  const getBooking = (roomId, date) => {\r\n    return bookings.find((booking) => booking.roomId === roomId && date >= booking.startDate && date < booking.endDate)\r\n  }\r\n\r\n  // Kiểm tra xem hôm nay có phải là ngày nhận phòng cho một đặt phòng không\r\n  const isCheckInDate = (booking, date) => {\r\n    return date.toDateString() === booking.startDate.toDateString()\r\n  }\r\n\r\n  // Kiểm tra xem hôm nay có phải là ngày trả phòng cho một đặt phòng không\r\n  const isCheckOutDate = (booking, date) => {\r\n    const checkoutDate = new Date(booking.endDate)\r\n    checkoutDate.setDate(checkoutDate.getDate() - 1)\r\n    return date.toDateString() === checkoutDate.toDateString()\r\n  }\r\n\r\n  // Xử lý điều hướng ngày\r\n  const navigateDate = (days) => {\r\n    const newDate = new Date(currentDate)\r\n    newDate.setDate(newDate.getDate() + days)\r\n    setCurrentDate(newDate)\r\n  }\r\n\r\n  // Xử lý khi nhấp vào phòng để đặt\r\n  const handleRoomClick = (room, date) => {\r\n    const booking = getBooking(room.id, date)\r\n\r\n    if (booking) {\r\n      // Hiển thị chi tiết đặt phòng\r\n      setSelectedBooking(booking)\r\n      setShowDetailsModal(true)\r\n    } else {\r\n      // Hiển thị biểu mẫu đặt phòng\r\n      setSelectedRoom({ ...room, date })\r\n      setShowBookingModal(true)\r\n    }\r\n  }\r\n\r\n  // Xử lý đặt phòng mới\r\n  const handleAddBooking = (e) => {\r\n    e.preventDefault()\r\n\r\n    // Lấy dữ liệu biểu mẫu\r\n    const guestName = e.target.guestName.value\r\n    const startDate = new Date(selectedRoom.date)\r\n    const nights = Number.parseInt(e.target.nights.value)\r\n    const guestCount = Number.parseInt(e.target.guestCount.value)\r\n    const idNumber = e.target.idNumber.value\r\n    const phoneNumber = e.target.phoneNumber.value\r\n    const email = e.target.email.value\r\n\r\n    // Tính ngày kết thúc\r\n    const endDate = new Date(startDate)\r\n    endDate.setDate(endDate.getDate() + nights)\r\n\r\n    // Tạo đặt phòng mới\r\n    const newBooking = {\r\n      id: bookings.length + 1,\r\n      roomId: selectedRoom.id,\r\n      guestName,\r\n      startDate,\r\n      endDate,\r\n      status: \"confirmed\",\r\n      type: selectedRoom.type.toLowerCase(),\r\n      guestCount,\r\n      paymentStatus: \"pending\",\r\n      checkedIn: false,\r\n      checkedOut: false,\r\n      services: [],\r\n      depositAmount: 0,\r\n      idNumber,\r\n      phoneNumber,\r\n      email,\r\n    }\r\n\r\n    // Thêm đặt phòng vào danh sách\r\n    dispatch({\r\n      type: RoomUnitActions.ADD_BOOKING_SUCCESS,\r\n      payload: newBooking,\r\n    })\r\n\r\n    // Đóng modal\r\n    setShowBookingModal(false)\r\n  }\r\n\r\n  // Mở modal chỉnh sửa phòng\r\n  const openEditRoomModal = (room) => {\r\n    setRoomToEdit(room)\r\n    setShowEditRoomModal(true)\r\n  }\r\n\r\n  // Mở modal nhận phòng\r\n  const openCheckInModal = (booking) => {\r\n    setCheckInBooking(booking)\r\n    setShowCheckInModal(true)\r\n  }\r\n\r\n  // Mở modal trả phòng\r\n  const openCheckOutModal = (booking) => {\r\n    console.log('Opening checkout modal for booking:', booking) // Debug\r\n    setCheckOutBooking(booking)\r\n    setSelectedServices([])\r\n    setShowCheckOutModal(true)\r\n  }\r\n\r\n  // Xử lý xác nhận nhận phòng\r\n  const handleCheckIn = (bookingId, depositAmount) => {\r\n    dispatch({\r\n      type: RoomUnitActions.CHECK_IN_SUCCESS,\r\n      payload: { bookingId, depositAmount },\r\n    })\r\n    setShowCheckInModal(false)\r\n  }\r\n\r\n  // Xử lý xác nhận trả phòng\r\n  const handleCheckOut = (bookingId, selectedServices) => {\r\n    dispatch({\r\n      type: RoomUnitActions.CHECK_OUT_SUCCESS,\r\n      payload: { bookingId, selectedServices },\r\n    })\r\n    setShowCheckOutModal(false)\r\n  }\r\n\r\n  // Chuyển đổi lựa chọn dịch vụ\r\n  const toggleService = (service) => {\r\n    if (selectedServices.some((s) => s.id === service.id)) {\r\n      setSelectedServices(selectedServices.filter((s) => s.id !== service.id))\r\n    } else {\r\n      setSelectedServices([...selectedServices, service])\r\n    }\r\n  }\r\n\r\n  // Tính tổng hóa đơn cho trả phòng\r\n  const calculateBill = (booking) => {\r\n    if (!booking) {\r\n      return {\r\n        roomCharge: 0,\r\n        serviceCharge: 0,\r\n        total: 0,\r\n        deposit: 0,\r\n        balance: 0,\r\n      }\r\n    }\r\n\r\n    try {\r\n      // Lấy giá phòng\r\n      const room = rooms.find((r) => r.id === booking.roomId)\r\n      const roomPrice = room ? room.price : 0\r\n\r\n      // Tính số đêm\r\n      const nights = Math.round((booking.endDate - booking.startDate) / (1000 * 60 * 60 * 24))\r\n\r\n      // Tính phí phòng\r\n      const roomCharge = roomPrice * nights\r\n\r\n      // Tính phí dịch vụ - kiểm tra an toàn\r\n      const serviceCharge = Array.isArray(selectedServices) \r\n        ? selectedServices.reduce((total, service) => total + (service.price || 0), 0)\r\n        : 0\r\n\r\n      // Tính tổng\r\n      const total = roomCharge + serviceCharge\r\n\r\n      // Tính số tiền còn lại (tổng - đặt cọc)\r\n      const deposit = booking.depositAmount || 0\r\n      const balance = total - deposit\r\n\r\n      return {\r\n        roomCharge,\r\n        serviceCharge,\r\n        total,\r\n        deposit,\r\n        balance,\r\n      }\r\n    } catch (error) {\r\n      console.error('Error calculating bill:', error)\r\n      return {\r\n        roomCharge: 0,\r\n        serviceCharge: 0,\r\n        total: 0,\r\n        deposit: 0,\r\n        balance: 0,\r\n      }\r\n    }\r\n  }\r\n\r\n  // Lọc phòng dựa trên loại\r\n  const filteredRooms =\r\n    filterRoomType === \"all\"\r\n      ? rooms\r\n      : filterRoomType === \"available\"\r\n        ? rooms.filter(\r\n            (room) =>\r\n              !bookings.some(\r\n                (booking) =>\r\n                  booking.roomId === room.id &&\r\n                  currentDate >= booking.startDate &&\r\n                  currentDate < booking.endDate &&\r\n                  !booking.checkedOut,\r\n              ),\r\n          )\r\n        : rooms.filter((room) => room.type.toLowerCase() === filterRoomType.toLowerCase())\r\n\r\n  return (\r\n    <div className=\"d-flex\">\r\n      <div className=\"main-content_1 p-3\">\r\n        <div className=\" text-black d-flex justify-content-between align-items-center mb-3\">\r\n          <h4>Tổng quan khách sạn</h4>\r\n          <div className=\"d-flex\">\r\n            <Button variant=\"outline-primary\" className=\"me-2\" onClick={() => navigateDate(-viewDays)}>\r\n              &lt;&lt; Trước\r\n            </Button>\r\n            <div className=\"d-flex\">\r\n              <div style={{ color: \"black\" }} className=\"mt-1 me-2\">\r\n                <FaCalendarAlt className=\"me-2\" style={{ justifyContent: \"center\", alignItems: \"center\" }} />\r\n                {formatDate(currentDate)} - {formatDate(getDates()[getDates().length - 1])}\r\n              </div>\r\n            </div>\r\n\r\n            <Button variant=\"outline-primary\" onClick={() => navigateDate(viewDays)}>\r\n              Sau &gt;&gt;\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <Card.Body className=\"p-0\">\r\n          {/* Bộ lọc và Hành động */}\r\n          <div className=\"calendar-filters p-3  border-bottom\">\r\n            <Row className=\"align-items-center\">\r\n              <Col md={3}>\r\n                <div className=\"d-flex align-items-center\">\r\n                  <FaFilter className=\"me-2 text-primary\" />\r\n                  <span className=\"fw-bold\">Bộ lọc:</span>\r\n                </div>\r\n              </Col>\r\n              <Col md={3}>\r\n                <Form.Group>\r\n                  <Form.Label>Loại phòng</Form.Label>\r\n                  <Form.Select value={filterRoomType} onChange={(e) => setFilterRoomType(e.target.value)}>\r\n                    <option value=\"all\">Tất cả phòng</option>\r\n                    <option value=\"available\">Phòng trống</option>\r\n                    <option value=\"Single room\">Phòng đơn</option>\r\n                    <option value=\"Double room\">Phòng đôi</option>\r\n                    <option value=\"Family room\">Phòng gia đình</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={3}>\r\n                <Form.Group>\r\n                  <Form.Label>Phạm vi hiển thị</Form.Label>\r\n                  <Form.Select value={viewDays} onChange={(e) => setViewDays(Number.parseInt(e.target.value))}>\r\n                    <option value=\"7\">7 ngày</option>\r\n                    <option value=\"14\">14 ngày</option>\r\n                    <option value=\"30\">30 ngày</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n          </div>\r\n\r\n          {/* Thêm tóm tắt tình trạng phòng ở đầu */}\r\n          <Row className=\"mt-3 mb-2\">\r\n            <Col>\r\n              <Card className=\"availability-summary\">\r\n                <Card.Body className=\"py-2\">\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <strong>Tóm tắt tình trạng phòng:</strong>\r\n                      <Badge bg=\"success\" className=\"ms-2\">\r\n                        {\r\n                          rooms.filter(\r\n                            (r) =>\r\n                              !bookings.some(\r\n                                (b) =>\r\n                                  b.roomId === r.id &&\r\n                                  currentDate >= b.startDate &&\r\n                                  currentDate < b.endDate &&\r\n                                  !b.checkedOut,\r\n                              ),\r\n                          ).length\r\n                        }{\" \"}\r\n                        Trống\r\n                      </Badge>\r\n                      <Badge bg=\"danger\" className=\"ms-2\">\r\n                        {\r\n                          rooms.filter((r) =>\r\n                            bookings.some(\r\n                              (b) =>\r\n                                b.roomId === r.id &&\r\n                                currentDate >= b.startDate &&\r\n                                currentDate < b.endDate &&\r\n                                b.paymentStatus === \"paid\" &&\r\n                                !b.checkedOut,\r\n                            ),\r\n                          ).length\r\n                        }{\" \"}\r\n                        Đã thuê\r\n                      </Badge>\r\n                      <Badge bg=\"warning\" className=\"ms-2\">\r\n                        {\r\n                          rooms.filter((r) =>\r\n                            bookings.some(\r\n                              (b) =>\r\n                                b.roomId === r.id &&\r\n                                currentDate >= b.startDate &&\r\n                                currentDate < b.endDate &&\r\n                                b.paymentStatus === \"pending\" &&\r\n                                !b.checkedOut,\r\n                            ),\r\n                          ).length\r\n                        }{\" \"}\r\n                        Đang chờ\r\n                      </Badge>\r\n                    </div>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n\r\n          {/* Lưới lịch */}\r\n          <div className=\"calendar-container\">\r\n            <div className=\"calendar-grid\">\r\n              {/* Hàng tiêu đề với ngày */}\r\n              <div className=\"calendar-header\">\r\n                <div className=\"calendar-cell room-header\">\r\n                  Phòng\r\n                  <div className=\"room-actions\">\r\n                    <small>Nhấp vào phòng để xem tùy chọn!!</small>\r\n                  </div>\r\n                </div>\r\n                {getDates().map((date, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`calendar-cell date-header ${\r\n                      date.getDay() === 0 || date.getDay() === 6 ? \"weekend\" : \"\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"day-of-week\">{formatDayOfWeek(date)}</div>\r\n                    <div className=\"date\">{formatDate(date)}</div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              {/* Hàng phòng */}\r\n              {filteredRooms.map((room) => (\r\n                <div key={room.id} className=\"calendar-row\">\r\n                  <div className=\"calendar-cell room-info\">\r\n                    <div className=\"room-header-row\">\r\n                      <div className=\"room-number\">{room.name}</div>\r\n                    </div>\r\n                    <div className=\"room-type\">\r\n                      <Badge bg={room.type === \"Single room\" ? \"info\" : room.type === \"Double room\" ? \"primary\" : \"success\"}>\r\n                        {room.type === \"Single room\" ? \"Phòng Đơn\" : room.type === \"Double room\" ? \"Phòng Đôi\" : \"Phòng Gia Đình\"}\r\n                      </Badge>\r\n                    </div>\r\n                    <div className=\"room-capacity\">\r\n                      <FaUser /> {room.capacity}\r\n                    </div>\r\n                    <div className=\"room-price\">${room.price}/đêm</div>\r\n                  </div>\r\n\r\n                  {/* Ô ngày */}\r\n                  {getDates().map((date, dateIndex) => {\r\n                    const isBooked = isRoomBooked(room.id, date)\r\n                    const booking = isBooked ? getBooking(room.id, date) : null\r\n                    const isPending = booking && booking.paymentStatus === \"pending\"\r\n                    const isCheckedOut = booking && booking.checkedOut\r\n                    const isToday = date.toDateString() === new Date().toDateString()\r\n                    const isCheckIn = booking && isCheckInDate(booking, date)\r\n                    const isCheckOut = booking && isCheckOutDate(booking, date)\r\n\r\n                    // Xác định lớp ô\r\n                    let cellClass = \"available\"\r\n                    if (isBooked) {\r\n                      if (isCheckedOut) {\r\n                        cellClass = \"available\"\r\n                      } else if (isPending) {\r\n                        cellClass = \"pending\"\r\n                      } else {\r\n                        cellClass = \"booked\"\r\n                      }\r\n                    }\r\n\r\n                    return (\r\n                      <div\r\n                        key={dateIndex}\r\n                        className={`calendar-cell date-cell ${cellClass} ${\r\n                          date.getDay() === 0 || date.getDay() === 6 ? \"weekend\" : \"\"\r\n                        }`}\r\n                        onClick={() => !isBooked && handleRoomClick(room, date)}\r\n                      >\r\n                        {isBooked && !isCheckedOut ? (\r\n                          <OverlayTrigger\r\n                            placement=\"top\"\r\n                            overlay={\r\n                              <Tooltip>\r\n                                <strong>{booking.guestName}</strong>\r\n                                <br />\r\n                                Nhận phòng: {booking.startDate.toLocaleDateString()}\r\n                                <br />\r\n                                Trả phòng: {booking.endDate.toLocaleDateString()}\r\n                                <br />\r\n                                Trạng thái: {booking.paymentStatus === \"paid\" ? \"Đã thanh toán\" : \"Chưa thanh toán\"}\r\n                                <br />\r\n                                {booking.checkedIn ? \"Đã nhận phòng\" : \"Chưa nhận phòng\"}\r\n                              </Tooltip>\r\n                            }\r\n                          >\r\n                            <div className=\"booking-info\">\r\n                              <div className=\"guest-name\">{booking.guestName}</div>\r\n                              <div className={`status-icon ${isPending ? \"pending-icon\" : \"booked-icon\"}`}>\r\n                                {isPending ? <FaClock /> : <FaTimes />}\r\n                              </div>\r\n\r\n                              {/* Nút nhận phòng cho đặt phòng đang chờ vào ngày nhận phòng */}\r\n                              {!booking.checkedIn && isCheckIn && isToday && (\r\n                                <Button\r\n                                  size=\"sm\"\r\n                                  variant=\"warning\"\r\n                                  className=\"check-action-btn\"\r\n                                  onClick={(e) => {\r\n                                    e.stopPropagation()\r\n                                    openCheckInModal(booking)\r\n                                  }}\r\n                                >\r\n                                  <FaSignInAlt className=\"me-1\" /> Nhận phòng\r\n                                </Button>\r\n                              )}\r\n\r\n                              {/* Nút trả phòng cho phòng đã đặt vào ngày trả phòng */}\r\n                              {booking.checkedIn && isCheckOut && isToday && !booking.checkedOut && (\r\n                                <Button\r\n                                  size=\"sm\"\r\n                                  variant=\"danger\"\r\n                                  className=\"check-action-btn\"\r\n                                  onClick={(e) => {\r\n                                    e.stopPropagation()\r\n                                    openCheckOutModal(booking)\r\n                                  }}\r\n                                >\r\n                                  <FaSignOutAlt className=\"me-1\" /> Trả phòng\r\n                                </Button>\r\n                              )}\r\n                            </div>\r\n                          </OverlayTrigger>\r\n                        ) : (\r\n                          <div className=\"available-cell\">\r\n                            <FaCheck className=\"available-icon\" />\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )\r\n                  })}\r\n                </div>\r\n              ))}\r\n\r\n              {/* Trạng thái trống nếu không có phòng phù hợp với bộ lọc */}\r\n              {filteredRooms.length === 0 && (\r\n                <div className=\"calendar-row empty-state\">\r\n                  <div className=\"calendar-cell empty-message\" colSpan={getDates().length + 1}>\r\n                    Không có phòng phù hợp với bộ lọc đã chọn.{\" \"}\r\n                    <Button variant=\"link\" onClick={() => setShowAddRoomModal(true)}>\r\n                      Thêm phòng mới\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </Card.Body>\r\n      </div>\r\n\r\n      {/* Modal đặt phòng */}\r\n      <Modal show={showBookingModal} onHide={() => setShowBookingModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Đặt phòng {selectedRoom?.name}</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedRoom && (\r\n            <Form onSubmit={handleAddBooking}>\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Tên khách</Form.Label>\r\n                <Form.Control type=\"text\" name=\"guestName\" required />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Số CMND/CCCD</Form.Label>\r\n                <Form.Control type=\"text\" name=\"idNumber\" required />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Số điện thoại</Form.Label>\r\n                <Form.Control type=\"tel\" name=\"phoneNumber\" required />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Email</Form.Label>\r\n                <Form.Control type=\"email\" name=\"email\" required />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Ngày nhận phòng</Form.Label>\r\n                <Form.Control type=\"text\" value={selectedRoom.date.toLocaleDateString()} disabled />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Số đêm</Form.Label>\r\n                <Form.Control type=\"number\" name=\"nights\" min=\"1\" defaultValue=\"1\" required />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Số khách</Form.Label>\r\n                <Form.Control\r\n                  type=\"number\"\r\n                  name=\"guestCount\"\r\n                  min=\"1\"\r\n                  max={selectedRoom.capacity}\r\n                  defaultValue=\"1\"\r\n                  required\r\n                />\r\n                <Form.Text className=\"text-muted\">Sức chứa tối đa: {selectedRoom.capacity}</Form.Text>\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Loại phòng</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  value={selectedRoom.type === \"Single room\" ? \"Đơn\" : selectedRoom.type === \"Double room\" ? \"Đôi\" : \"Suite\"}\r\n                  disabled\r\n                />\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Giá mỗi đêm</Form.Label>\r\n                <Form.Control type=\"text\" value={`$${selectedRoom.price}`} disabled />\r\n              </Form.Group>\r\n\r\n              <div className=\"d-grid\">\r\n                <Button variant=\"primary\" type=\"submit\">\r\n                  Xác nhận đặt phòng\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          )}\r\n        </Modal.Body>\r\n      </Modal>\r\n\r\n      {/* Modal chi tiết đặt phòng */}\r\n      <Modal show={showDetailsModal} onHide={() => setShowDetailsModal(false)} centered>\r\n        <Modal.Header closeButton className=\"bg-primary text-white\">\r\n          <Modal.Title>Chi tiết đặt phòng</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedBooking && (\r\n            <div>\r\n              <h4>\r\n                Phòng {selectedBooking.roomId} - {selectedBooking.guestName}\r\n              </h4>\r\n\r\n              <div className=\"booking-details\">\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Nhận phòng:</div>\r\n                  <div className=\"detail-value\">{selectedBooking.startDate.toLocaleDateString()}</div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Trả phòng:</div>\r\n                  <div className=\"detail-value\">{selectedBooking.endDate.toLocaleDateString()}</div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Số đêm:</div>\r\n                  <div className=\"detail-value\">\r\n                    {Math.round((selectedBooking.endDate - selectedBooking.startDate) / (1000 * 60 * 60 * 24))}\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Loại phòng:</div>\r\n                  <div className=\"detail-value\">\r\n                    <Badge\r\n                      bg={\r\n                        selectedBooking.type === \"Single room\"\r\n                          ? \"info\"\r\n                          : selectedBooking.type === \"Double room\"\r\n                            ? \"primary\"\r\n                            : \"success\"\r\n                      }\r\n                    >\r\n                      {selectedBooking.type === \"Single room\" ? \"Đơn\" : selectedBooking.type === \"Double room\" ? \"Đôi\" : \"Suite\"}\r\n                    </Badge>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Số khách:</div>\r\n                  <div className=\"detail-value\">{selectedBooking.guestCount}</div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Trạng thái:</div>\r\n                  <div className=\"detail-value\">\r\n                    <Badge bg={selectedBooking.status === \"confirmed\" ? \"success\" : \"warning\"}>\r\n                      {selectedBooking.status === \"confirmed\" ? \"Đã xác nhận\" : \"Chờ xác nhận\"}\r\n                    </Badge>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Thanh toán:</div>\r\n                  <div className=\"detail-value\">\r\n                    <Badge\r\n                      bg={\r\n                        selectedBooking.paymentStatus === \"paid\"\r\n                          ? \"success\"\r\n                          : selectedBooking.paymentStatus === \"partially paid\"\r\n                            ? \"info\"\r\n                            : \"warning\"\r\n                      }\r\n                    >\r\n                      {selectedBooking.paymentStatus === \"paid\"\r\n                        ? \"Đã thanh toán\"\r\n                        : selectedBooking.paymentStatus === \"partially paid\"\r\n                          ? \"Thanh toán một phần\"\r\n                          : \"Chưa thanh toán\"}\r\n                    </Badge>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Nhận phòng:</div>\r\n                  <div className=\"detail-value\">\r\n                    <Badge bg={selectedBooking.checkedIn ? \"success\" : \"secondary\"}>\r\n                      {selectedBooking.checkedIn ? \"Đã nhận phòng\" : \"Chưa nhận phòng\"}\r\n                    </Badge>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Trả phòng:</div>\r\n                  <div className=\"detail-value\">\r\n                    <Badge bg={selectedBooking.checkedOut ? \"success\" : \"secondary\"}>\r\n                      {selectedBooking.checkedOut ? \"Đã trả phòng\" : \"Chưa trả phòng\"}\r\n                    </Badge>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"detail-item\">\r\n                  <div className=\"detail-label\">Liên hệ:</div>\r\n                  <div className=\"detail-value\">\r\n                    {selectedBooking.phoneNumber}\r\n                    <br />\r\n                    {selectedBooking.email}\r\n                  </div>\r\n                </div>\r\n\r\n                {selectedBooking.services && selectedBooking.services.length > 0 && (\r\n                  <div className=\"detail-item\">\r\n                    <div className=\"detail-label\">Dịch vụ:</div>\r\n                    <div className=\"detail-value\">\r\n                      {selectedBooking.services.map((service) => (\r\n                        <Badge key={service.id} bg=\"info\" className=\"me-1 mb-1\">\r\n                          {service.name} (${service.price})\r\n                        </Badge>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"mt-4 d-flex justify-content-between\">\r\n                {!selectedBooking.checkedIn ? (\r\n                  <Button variant=\"warning\" onClick={() => openCheckInModal(selectedBooking)}>\r\n                    <FaSignInAlt className=\"me-2\" /> Nhận phòng\r\n                  </Button>\r\n                ) : !selectedBooking.checkedOut ? (\r\n                  <Button variant=\"danger\" onClick={() => openCheckOutModal(selectedBooking)}>\r\n                    <FaSignOutAlt className=\"me-2\" /> Trả phòng\r\n                  </Button>\r\n                ) : (\r\n                  <Button variant=\"outline-primary\">\r\n                    <FaInfoCircle className=\"me-2\" /> Xem chi tiết đầy đủ\r\n                  </Button>\r\n                )}\r\n                <Button variant=\"outline-danger\">\r\n                  <FaTimes className=\"me-2\" /> Hủy đặt phòng\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n      </Modal>\r\n\r\n      {/* Modal nhận phòng */}\r\n      <Modal show={showCheckInModal} onHide={() => setShowCheckInModal(false)} centered size=\"lg\">\r\n        <Modal.Header closeButton className=\"bg-warning text-white\">\r\n          <Modal.Title>\r\n            <FaSignInAlt className=\"me-2\" /> Nhận phòng\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {checkInBooking && (\r\n            <div>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Card className=\"mb-3\">\r\n                    <Card.Header className=\"bg-light\">\r\n                      <h5 className=\"mb-0\">Thông tin khách</h5>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      <p>\r\n                        <strong>Tên:</strong> {checkInBooking.guestName}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Số CMND/CCCD:</strong> {checkInBooking.idNumber}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Điện thoại:</strong> {checkInBooking.phoneNumber}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Email:</strong> {checkInBooking.email}\r\n                      </p>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Card className=\"mb-3\">\r\n                    <Card.Header className=\"bg-light\">\r\n                      <h5 className=\"mb-0\">Chi tiết đặt phòng</h5>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      <p>\r\n                        <strong>Phòng:</strong> {checkInBooking.roomId} (\r\n                        {rooms.find((r) => r.id === checkInBooking.roomId)?.type === \"Single room\"\r\n                          ? \"Đơn\"\r\n                          : rooms.find((r) => r.id === checkInBooking.roomId)?.type === \"Double room\"\r\n                            ? \"Đôi\"\r\n                            : \"Suite\"}\r\n                        )\r\n                      </p>\r\n                      <p>\r\n                        <strong>Nhận phòng:</strong> {checkInBooking.startDate.toLocaleDateString()}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Trả phòng:</strong> {checkInBooking.endDate.toLocaleDateString()}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Số đêm:</strong>{\" \"}\r\n                        {Math.round((checkInBooking.endDate - checkInBooking.startDate) / (1000 * 60 * 60 * 24))}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Số khách:</strong> {checkInBooking.guestCount}\r\n                      </p>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Card className=\"mb-3\">\r\n                <Card.Header className=\"bg-light\">\r\n                  <h5 className=\"mb-0\">Thông tin thanh toán</h5>\r\n                </Card.Header>\r\n                <Card.Body>\r\n                  <Form>\r\n                    <Row>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label>Giá phòng mỗi đêm</Form.Label>\r\n                          <Form.Control\r\n                            type=\"text\"\r\n                            value={`$${rooms.find((r) => r.id === checkInBooking.roomId)?.price || 0}`}\r\n                            disabled\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label>Tổng tiền phòng</Form.Label>\r\n                          <Form.Control\r\n                            type=\"text\"\r\n                            value={`$${\r\n                              (rooms.find((r) => r.id === checkInBooking.roomId)?.price || 0) *\r\n                              Math.round((checkInBooking.endDate - checkInBooking.startDate) / (1000 * 60 * 60 * 24))\r\n                            }`}\r\n                            disabled\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n                    <Row>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label>Số tiền đặt cọc</Form.Label>\r\n                          <Form.Control\r\n                            type=\"number\"\r\n                            id=\"depositAmount\"\r\n                            defaultValue={rooms.find((r) => r.id === checkInBooking.roomId)?.price || 0}\r\n                            min=\"0\"\r\n                            step=\"0.01\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label>Phương thức thanh toán</Form.Label>\r\n                          <Form.Select value={paymentMethod} onChange={(e) => setPaymentMethod(e.target.value)}>\r\n                            <option value=\"credit\">Thẻ tín dụng</option>\r\n                            <option value=\"cash\">Tiền mặt</option>\r\n                            <option value=\"bank\">Chuyển khoản</option>\r\n                          </Form.Select>\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n                  </Form>\r\n                </Card.Body>\r\n              </Card>\r\n\r\n              <div className=\"d-flex justify-content-between\">\r\n                <Button variant=\"secondary\" onClick={() => setShowCheckInModal(false)}>\r\n                  Hủy\r\n                </Button>\r\n                <Button\r\n                  variant=\"warning\"\r\n                  onClick={() => {\r\n                    const depositAmount = Number.parseFloat(document.getElementById(\"depositAmount\").value)\r\n                    handleCheckIn(checkInBooking.id, depositAmount)\r\n                  }}\r\n                >\r\n                  <FaSignInAlt className=\"me-2\" /> Xác nhận nhận phòng\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n      </Modal>\r\n\r\n      {/* Modal trả phòng */}\r\n      <Modal show={showCheckOutModal} onHide={() => setShowCheckOutModal(false)} centered size=\"lg\">\r\n        <Modal.Header closeButton className=\"bg-danger text-white\">\r\n          <Modal.Title>\r\n            <FaSignOutAlt className=\"me-2\" /> Trả phòng\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {checkOutBooking ? (\r\n            <div>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Card className=\"mb-3\">\r\n                    <Card.Header className=\"bg-light\">\r\n                      <h5 className=\"mb-0\">Thông tin khách</h5>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      <p>\r\n                        <strong>Tên:</strong> {checkOutBooking.guestName}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Số CMND/CCCD:</strong> {checkOutBooking.idNumber}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Điện thoại:</strong> {checkOutBooking.phoneNumber}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Email:</strong> {checkOutBooking.email}\r\n                      </p>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Card className=\"mb-3\">\r\n                    <Card.Header className=\"bg-light\">\r\n                      <h5 className=\"mb-0\">Chi tiết đặt phòng</h5>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      <p>\r\n                        <strong>Phòng:</strong> {checkOutBooking.roomId} (\r\n                        {rooms.find((r) => r.id === checkOutBooking.roomId)?.type === \"Single room\"\r\n                          ? \"Đơn\"\r\n                          : rooms.find((r) => r.id === checkOutBooking.roomId)?.type === \"Double room\"\r\n                            ? \"Đôi\"\r\n                            : \"Suite\"}\r\n                        )\r\n                      </p>\r\n                      <p>\r\n                        <strong>Nhận phòng:</strong> {checkOutBooking.startDate.toLocaleDateString()}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Trả phòng:</strong> {checkOutBooking.endDate.toLocaleDateString()}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Số đêm:</strong>{\" \"}\r\n                        {Math.round((checkOutBooking.endDate - checkOutBooking.startDate) / (1000 * 60 * 60 * 24))}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Số khách:</strong> {checkOutBooking.guestCount}\r\n                      </p>\r\n                    </Card.Body>\r\n                  </Card>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Card className=\"mb-3\">\r\n                <Card.Header className=\"bg-light\">\r\n                  <h5 className=\"mb-0\">Dịch vụ bổ sung đã sử dụng</h5>\r\n                </Card.Header>\r\n                <Card.Body>\r\n                  <p>Chọn các dịch vụ bổ sung mà khách đã sử dụng:</p>\r\n                  <Row>\r\n                    {Array.isArray(availableServices) && availableServices.length > 0 ? (\r\n                      availableServices.map((service) => (\r\n                        <Col md={4} key={service.id} className=\"mb-2\">\r\n                          <Form.Check\r\n                            type=\"checkbox\"\r\n                            id={`service-${service.id}`}\r\n                            label={\r\n                              <span>\r\n                                {service.icon && <service.icon className=\"me-2\" />}\r\n                                {service.name} (${service.price})\r\n                              </span>\r\n                            }\r\n                            checked={selectedServices.some((s) => s.id === service.id)}\r\n                            onChange={() => toggleService(service)}\r\n                          />\r\n                        </Col>\r\n                      ))\r\n                    ) : (\r\n                      <Col>\r\n                        <p className=\"text-muted\">Không có dịch vụ bổ sung nào.</p>\r\n                      </Col>\r\n                    )}\r\n                  </Row>\r\n                </Card.Body>\r\n              </Card>\r\n\r\n              <Card className=\"mb-3\">\r\n                <Card.Header className=\"bg-light\">\r\n                  <h5 className=\"mb-0\">Tóm tắt hóa đơn</h5>\r\n                </Card.Header>\r\n                <Card.Body>\r\n                  <Table striped bordered>\r\n                    <thead>\r\n                      <tr>\r\n                        <th>Mô tả</th>\r\n                        <th className=\"text-end\">Số tiền</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <tr>\r\n                        <td>\r\n                          Tiền phòng (\r\n                          {Math.round((checkOutBooking.endDate - checkOutBooking.startDate) / (1000 * 60 * 60 * 24))}{\" \"}\r\n                          đêm @ ${rooms.find((r) => r.id === checkOutBooking.roomId)?.price || 0}\r\n                          /đêm)\r\n                        </td>\r\n                        <td className=\"text-end\">${calculateBill(checkOutBooking).roomCharge.toFixed(2)}</td>\r\n                      </tr>\r\n                      {selectedServices.map((service) => (\r\n                        <tr key={service.id}>\r\n                          <td>{service.name}</td>\r\n                          <td className=\"text-end\">${service.price.toFixed(2)}</td>\r\n                        </tr>\r\n                      ))}\r\n                      <tr>\r\n                        <td className=\"fw-bold\">Tổng chi phí</td>\r\n                        <td className=\"text-end fw-bold\">\r\n                          $\r\n                          {(\r\n                            calculateBill(checkOutBooking).roomCharge + calculateBill(checkOutBooking).serviceCharge\r\n                          ).toFixed(2)}\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Đã đặt cọc</td>\r\n                        <td className=\"text-end\">\r\n                          -${(checkOutBooking.depositAmount || 0).toFixed(2)}\r\n                        </td>\r\n                      </tr>\r\n                      <tr className=\"table-primary\">\r\n                        <td className=\"fw-bold\">Số tiền cần thanh toán</td>\r\n                        <td className=\"text-end fw-bold\">${calculateBill(checkOutBooking).balance.toFixed(2)}</td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </Table>\r\n\r\n                  <Form>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label>Phương thức thanh toán</Form.Label>\r\n                      <Form.Select value={paymentMethod} onChange={(e) => setPaymentMethod(e.target.value)}>\r\n                        <option value=\"credit\">Thẻ tín dụng</option>\r\n                        <option value=\"cash\">Tiền mặt</option>\r\n                        <option value=\"bank\">Chuyển khoản</option>\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </Form>\r\n                </Card.Body>\r\n              </Card>\r\n\r\n              <div className=\"d-flex justify-content-between\">\r\n                <Button variant=\"secondary\" onClick={() => setShowCheckOutModal(false)}>\r\n                  Hủy\r\n                </Button>\r\n                <div>\r\n                  <Button variant=\"danger\" onClick={() => handleCheckOut(checkOutBooking.id, selectedServices)}>\r\n                    <FaSignOutAlt className=\"me-2\" /> Xác nhận trả phòng\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center p-4\">\r\n              <p>Không thể tải thông tin đặt phòng.</p>\r\n              <Button variant=\"secondary\" onClick={() => setShowCheckOutModal(false)}>\r\n                Đóng\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n      </Modal>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default RoomAvailabilityCalendar\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC5G,SACEC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,YAAY,EACZC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,cAAc,EACdC,KAAK,EACLC,KAAK,EACLC,cAAc,QACT,gBAAgB;AACvB,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAO,yCAAyC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,wBAAwBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;EAClC,MAAMC,QAAQ,GAAGd,cAAc,CAAC,CAAC;;EAEjC;EACA,MAAM;IACJe,KAAK,GAAG,EAAE;IACVC,QAAQ,EAAEC,WAAW,GAAG,EAAE;IAC1BC,iBAAiB,GAAG,EAAE;IACtBC,OAAO,GAAG,CAAC,CAAC;IACZC,OAAO,GAAG,KAAK;IACfC,KAAK,GAAG;EACV,CAAC,GAAGtB,cAAc,CAAEuB,KAAK,IAAKA,KAAK,CAACC,QAAQ,CAAC;;EAE7C;EACA,MAAMC,eAAe,GAAG,CACtB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,CAAC;IAAEC,IAAI,EAAEnC;EAAO,CAAC,EAC/C;IAAEgC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAElC;EAAW,CAAC,EACxD;IAAE+B,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAEjC;EAAe,CAAC,EAC3D;IAAE8B,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAEhC;EAAM,CAAC,EAChD;IAAE6B,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAE/B;EAAM,CAAC,EAC9C;IAAE4B,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAE9B;EAAe,CAAC,CAC3D;EAED,MAAM+B,QAAQ,GAAGX,iBAAiB,CAACY,MAAM,GAAG,CAAC,GAAGZ,iBAAiB,GAAGM,eAAe;;EAEnF;EACA,MAAMR,QAAQ,GAAGC,WAAW,CAACc,GAAG,CAACC,CAAC,KAAK;IACrC,GAAGA,CAAC;IACJC,SAAS,EAAED,CAAC,CAACC,SAAS,YAAYC,IAAI,GAAGF,CAAC,CAACC,SAAS,GAAG,IAAIC,IAAI,CAACF,CAAC,CAACC,SAAS,CAAC;IAC5EE,OAAO,EAAEH,CAAC,CAACG,OAAO,YAAYD,IAAI,GAAGF,CAAC,CAACG,OAAO,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,OAAO;EACrE,CAAC,CAAC,CAAC;;EAEH;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,IAAI8D,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC,EAAC;EAC7C,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC8E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwF,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8F,aAAa,EAAEC,gBAAgB,CAAC,GAAG/F,QAAQ,CAAC,QAAQ,CAAC;;EAE5D;EACA,MAAMgG,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMpC,SAAS,GAAG,IAAIC,IAAI,CAACE,WAAW,CAAC;IAEvC,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,QAAQ,EAAEgC,CAAC,EAAE,EAAE;MACjC,MAAMC,IAAI,GAAG,IAAIrC,IAAI,CAACD,SAAS,CAAC;MAChCsC,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;MAChCD,KAAK,CAACK,IAAI,CAACH,IAAI,CAAC;IAClB;IAEA,OAAOF,KAAK;EACd,CAAC;;EAED;EACA,MAAMM,UAAU,GAAIJ,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAU,CAAC,CAAC;EAC7E,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIR,IAAI,IAAK;IAChC,OAAOA,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;MAAEI,OAAO,EAAE;IAAQ,CAAC,CAAC;EAC/D,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAACC,MAAM,EAAEX,IAAI,KAAK;IACrC,OAAOvD,QAAQ,CAACmE,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAACF,MAAM,KAAKA,MAAM,IAAIX,IAAI,IAAIa,OAAO,CAACnD,SAAS,IAAIsC,IAAI,GAAGa,OAAO,CAACjD,OAAO,CAAC;EACrH,CAAC;;EAED;EACA,MAAMkD,UAAU,GAAGA,CAACH,MAAM,EAAEX,IAAI,KAAK;IACnC,OAAOvD,QAAQ,CAACsE,IAAI,CAAEF,OAAO,IAAKA,OAAO,CAACF,MAAM,KAAKA,MAAM,IAAIX,IAAI,IAAIa,OAAO,CAACnD,SAAS,IAAIsC,IAAI,GAAGa,OAAO,CAACjD,OAAO,CAAC;EACrH,CAAC;;EAED;EACA,MAAMoD,aAAa,GAAGA,CAACH,OAAO,EAAEb,IAAI,KAAK;IACvC,OAAOA,IAAI,CAACiB,YAAY,CAAC,CAAC,KAAKJ,OAAO,CAACnD,SAAS,CAACuD,YAAY,CAAC,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAACL,OAAO,EAAEb,IAAI,KAAK;IACxC,MAAMmB,YAAY,GAAG,IAAIxD,IAAI,CAACkD,OAAO,CAACjD,OAAO,CAAC;IAC9CuD,YAAY,CAAClB,OAAO,CAACkB,YAAY,CAACjB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD,OAAOF,IAAI,CAACiB,YAAY,CAAC,CAAC,KAAKE,YAAY,CAACF,YAAY,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMG,YAAY,GAAIC,IAAI,IAAK;IAC7B,MAAMC,OAAO,GAAG,IAAI3D,IAAI,CAACE,WAAW,CAAC;IACrCyD,OAAO,CAACrB,OAAO,CAACqB,OAAO,CAACpB,OAAO,CAAC,CAAC,GAAGmB,IAAI,CAAC;IACzCvD,cAAc,CAACwD,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,IAAI,EAAExB,IAAI,KAAK;IACtC,MAAMa,OAAO,GAAGC,UAAU,CAACU,IAAI,CAACtE,EAAE,EAAE8C,IAAI,CAAC;IAEzC,IAAIa,OAAO,EAAE;MACX;MACArC,kBAAkB,CAACqC,OAAO,CAAC;MAC3BvC,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACL;MACAJ,eAAe,CAAC;QAAE,GAAGsD,IAAI;QAAExB;MAAK,CAAC,CAAC;MAClC5B,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMqD,gBAAgB,GAAIC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,SAAS,GAAGF,CAAC,CAACG,MAAM,CAACD,SAAS,CAACE,KAAK;IAC1C,MAAMpE,SAAS,GAAG,IAAIC,IAAI,CAACM,YAAY,CAAC+B,IAAI,CAAC;IAC7C,MAAM+B,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACP,CAAC,CAACG,MAAM,CAACE,MAAM,CAACD,KAAK,CAAC;IACrD,MAAMI,UAAU,GAAGF,MAAM,CAACC,QAAQ,CAACP,CAAC,CAACG,MAAM,CAACK,UAAU,CAACJ,KAAK,CAAC;IAC7D,MAAMK,QAAQ,GAAGT,CAAC,CAACG,MAAM,CAACM,QAAQ,CAACL,KAAK;IACxC,MAAMM,WAAW,GAAGV,CAAC,CAACG,MAAM,CAACO,WAAW,CAACN,KAAK;IAC9C,MAAMO,KAAK,GAAGX,CAAC,CAACG,MAAM,CAACQ,KAAK,CAACP,KAAK;;IAElC;IACA,MAAMlE,OAAO,GAAG,IAAID,IAAI,CAACD,SAAS,CAAC;IACnCE,OAAO,CAACqC,OAAO,CAACrC,OAAO,CAACsC,OAAO,CAAC,CAAC,GAAG6B,MAAM,CAAC;;IAE3C;IACA,MAAMO,UAAU,GAAG;MACjBpF,EAAE,EAAET,QAAQ,CAACc,MAAM,GAAG,CAAC;MACvBoD,MAAM,EAAE1C,YAAY,CAACf,EAAE;MACvB0E,SAAS;MACTlE,SAAS;MACTE,OAAO;MACP2E,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAEvE,YAAY,CAACuE,IAAI,CAACC,WAAW,CAAC,CAAC;MACrCP,UAAU;MACVQ,aAAa,EAAE,SAAS;MACxBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBtF,QAAQ,EAAE,EAAE;MACZuF,aAAa,EAAE,CAAC;MAChBV,QAAQ;MACRC,WAAW;MACXC;IACF,CAAC;;IAED;IACA9F,QAAQ,CAAC;MACPiG,IAAI,EAAE9G,eAAe,CAACoH,mBAAmB;MACzCC,OAAO,EAAET;IACX,CAAC,CAAC;;IAEF;IACAlE,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM4E,iBAAiB,GAAIxB,IAAI,IAAK;IAClCxC,aAAa,CAACwC,IAAI,CAAC;IACnB1C,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAIpC,OAAO,IAAK;IACpCvB,iBAAiB,CAACuB,OAAO,CAAC;IAC1B3B,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMgE,iBAAiB,GAAIrC,OAAO,IAAK;IACrCsC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEvC,OAAO,CAAC,EAAC;IAC5DrB,kBAAkB,CAACqB,OAAO,CAAC;IAC3BnB,mBAAmB,CAAC,EAAE,CAAC;IACvBN,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMiE,aAAa,GAAGA,CAACC,SAAS,EAAET,aAAa,KAAK;IAClDtG,QAAQ,CAAC;MACPiG,IAAI,EAAE9G,eAAe,CAAC6H,gBAAgB;MACtCR,OAAO,EAAE;QAAEO,SAAS;QAAET;MAAc;IACtC,CAAC,CAAC;IACF3D,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMsE,cAAc,GAAGA,CAACF,SAAS,EAAE7D,gBAAgB,KAAK;IACtDlD,QAAQ,CAAC;MACPiG,IAAI,EAAE9G,eAAe,CAAC+H,iBAAiB;MACvCV,OAAO,EAAE;QAAEO,SAAS;QAAE7D;MAAiB;IACzC,CAAC,CAAC;IACFL,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMsE,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAIlE,gBAAgB,CAACmB,IAAI,CAAEgD,CAAC,IAAKA,CAAC,CAAC1G,EAAE,KAAKyG,OAAO,CAACzG,EAAE,CAAC,EAAE;MACrDwC,mBAAmB,CAACD,gBAAgB,CAACoE,MAAM,CAAED,CAAC,IAAKA,CAAC,CAAC1G,EAAE,KAAKyG,OAAO,CAACzG,EAAE,CAAC,CAAC;IAC1E,CAAC,MAAM;MACLwC,mBAAmB,CAAC,CAAC,GAAGD,gBAAgB,EAAEkE,OAAO,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMG,aAAa,GAAIjD,OAAO,IAAK;IACjC,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO;QACLkD,UAAU,EAAE,CAAC;QACbC,aAAa,EAAE,CAAC;QAChBC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,IAAI;MACF;MACA,MAAM3C,IAAI,GAAGhF,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAK2D,OAAO,CAACF,MAAM,CAAC;MACvD,MAAM0D,SAAS,GAAG7C,IAAI,GAAGA,IAAI,CAACpE,KAAK,GAAG,CAAC;;MAEvC;MACA,MAAM2E,MAAM,GAAGuC,IAAI,CAACC,KAAK,CAAC,CAAC1D,OAAO,CAACjD,OAAO,GAAGiD,OAAO,CAACnD,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;;MAExF;MACA,MAAMqG,UAAU,GAAGM,SAAS,GAAGtC,MAAM;;MAErC;MACA,MAAMiC,aAAa,GAAGQ,KAAK,CAACC,OAAO,CAAChF,gBAAgB,CAAC,GACjDA,gBAAgB,CAACiF,MAAM,CAAC,CAACT,KAAK,EAAEN,OAAO,KAAKM,KAAK,IAAIN,OAAO,CAACvG,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAC5E,CAAC;;MAEL;MACA,MAAM6G,KAAK,GAAGF,UAAU,GAAGC,aAAa;;MAExC;MACA,MAAME,OAAO,GAAGrD,OAAO,CAACgC,aAAa,IAAI,CAAC;MAC1C,MAAMsB,OAAO,GAAGF,KAAK,GAAGC,OAAO;MAE/B,OAAO;QACLH,UAAU;QACVC,aAAa;QACbC,KAAK;QACLC,OAAO;QACPC;MACF,CAAC;IACH,CAAC,CAAC,OAAOrH,KAAK,EAAE;MACdqG,OAAO,CAACrG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QACLiH,UAAU,EAAE,CAAC;QACbC,aAAa,EAAE,CAAC;QAChBC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GACjBlG,cAAc,KAAK,KAAK,GACpBjC,KAAK,GACLiC,cAAc,KAAK,WAAW,GAC5BjC,KAAK,CAACqH,MAAM,CACTrC,IAAI,IACH,CAAC/E,QAAQ,CAACmE,IAAI,CACXC,OAAO,IACNA,OAAO,CAACF,MAAM,KAAKa,IAAI,CAACtE,EAAE,IAC1BW,WAAW,IAAIgD,OAAO,CAACnD,SAAS,IAChCG,WAAW,GAAGgD,OAAO,CAACjD,OAAO,IAC7B,CAACiD,OAAO,CAAC+B,UACb,CACJ,CAAC,GACDpG,KAAK,CAACqH,MAAM,CAAErC,IAAI,IAAKA,IAAI,CAACgB,IAAI,CAACC,WAAW,CAAC,CAAC,KAAKhE,cAAc,CAACgE,WAAW,CAAC,CAAC,CAAC;EAExF,oBACE7G,OAAA;IAAKgJ,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBjJ,OAAA;MAAKgJ,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCjJ,OAAA;QAAKgJ,SAAS,EAAC,oEAAoE;QAAAC,QAAA,gBACjFjJ,OAAA;UAAAiJ,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BrJ,OAAA;UAAKgJ,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBjJ,OAAA,CAAC1B,MAAM;YAACgL,OAAO,EAAC,iBAAiB;YAACN,SAAS,EAAC,MAAM;YAACO,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,CAACrD,QAAQ,CAAE;YAAA8G,QAAA,EAAC;UAE3F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrJ,OAAA;YAAKgJ,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrBjJ,OAAA;cAAKwJ,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAQ,CAAE;cAACT,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACnDjJ,OAAA,CAACnB,aAAa;gBAACmK,SAAS,EAAC,MAAM;gBAACQ,KAAK,EAAE;kBAAEE,cAAc,EAAE,QAAQ;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC5F7E,UAAU,CAACvC,WAAW,CAAC,EAAC,KAAG,EAACuC,UAAU,CAACP,QAAQ,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAACtC,MAAM,GAAG,CAAC,CAAC,CAAC;YAAA;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrJ,OAAA,CAAC1B,MAAM;YAACgL,OAAO,EAAC,iBAAiB;YAACC,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAACrD,QAAQ,CAAE;YAAA8G,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;QAACZ,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAExBjJ,OAAA;UAAKgJ,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDjJ,OAAA,CAAC9B,GAAG;YAAC8K,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCjJ,OAAA,CAAC7B,GAAG;cAAC0L,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACTjJ,OAAA;gBAAKgJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCjJ,OAAA,CAACpB,QAAQ;kBAACoK,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1CrJ,OAAA;kBAAMgJ,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrJ,OAAA,CAAC7B,GAAG;cAAC0L,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACTjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;gBAAAb,QAAA,gBACTjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;kBAAAd,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCrJ,OAAA,CAACzB,IAAI,CAACyL,MAAM;kBAAC9D,KAAK,EAAErD,cAAe;kBAACoH,QAAQ,EAAGnE,CAAC,IAAKhD,iBAAiB,CAACgD,CAAC,CAACG,MAAM,CAACC,KAAK,CAAE;kBAAA+C,QAAA,gBACrFjJ,OAAA;oBAAQkG,KAAK,EAAC,KAAK;oBAAA+C,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzCrJ,OAAA;oBAAQkG,KAAK,EAAC,WAAW;oBAAA+C,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CrJ,OAAA;oBAAQkG,KAAK,EAAC,aAAa;oBAAA+C,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CrJ,OAAA;oBAAQkG,KAAK,EAAC,aAAa;oBAAA+C,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CrJ,OAAA;oBAAQkG,KAAK,EAAC,aAAa;oBAAA+C,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNrJ,OAAA,CAAC7B,GAAG;cAAC0L,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACTjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;gBAAAb,QAAA,gBACTjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;kBAAAd,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzCrJ,OAAA,CAACzB,IAAI,CAACyL,MAAM;kBAAC9D,KAAK,EAAE/D,QAAS;kBAAC8H,QAAQ,EAAGnE,CAAC,IAAK1D,WAAW,CAACgE,MAAM,CAACC,QAAQ,CAACP,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC,CAAE;kBAAA+C,QAAA,gBAC1FjJ,OAAA;oBAAQkG,KAAK,EAAC,GAAG;oBAAA+C,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjCrJ,OAAA;oBAAQkG,KAAK,EAAC,IAAI;oBAAA+C,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnCrJ,OAAA;oBAAQkG,KAAK,EAAC,IAAI;oBAAA+C,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrJ,OAAA,CAAC9B,GAAG;UAAC8K,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBjJ,OAAA,CAAC7B,GAAG;YAAA8K,QAAA,eACFjJ,OAAA,CAAC5B,IAAI;cAAC4K,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACpCjJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;gBAACZ,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACzBjJ,OAAA;kBAAKgJ,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChEjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CrJ,OAAA,CAAC3B,KAAK;sBAAC6L,EAAE,EAAC,SAAS;sBAAClB,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAEhCrI,KAAK,CAACqH,MAAM,CACTO,CAAC,IACA,CAAC3H,QAAQ,CAACmE,IAAI,CACXnD,CAAC,IACAA,CAAC,CAACkD,MAAM,KAAKyD,CAAC,CAAClH,EAAE,IACjBW,WAAW,IAAIJ,CAAC,CAACC,SAAS,IAC1BG,WAAW,GAAGJ,CAAC,CAACG,OAAO,IACvB,CAACH,CAAC,CAACmF,UACP,CACJ,CAAC,CAACrF,MAAM,EACR,GAAG,EAAC,YAER;oBAAA;sBAAAuH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrJ,OAAA,CAAC3B,KAAK;sBAAC6L,EAAE,EAAC,QAAQ;sBAAClB,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAE/BrI,KAAK,CAACqH,MAAM,CAAEO,CAAC,IACb3H,QAAQ,CAACmE,IAAI,CACVnD,CAAC,IACAA,CAAC,CAACkD,MAAM,KAAKyD,CAAC,CAAClH,EAAE,IACjBW,WAAW,IAAIJ,CAAC,CAACC,SAAS,IAC1BG,WAAW,GAAGJ,CAAC,CAACG,OAAO,IACvBH,CAAC,CAACiF,aAAa,KAAK,MAAM,IAC1B,CAACjF,CAAC,CAACmF,UACP,CACF,CAAC,CAACrF,MAAM,EACR,GAAG,EAAC,oBAER;oBAAA;sBAAAuH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrJ,OAAA,CAAC3B,KAAK;sBAAC6L,EAAE,EAAC,SAAS;sBAAClB,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAEhCrI,KAAK,CAACqH,MAAM,CAAEO,CAAC,IACb3H,QAAQ,CAACmE,IAAI,CACVnD,CAAC,IACAA,CAAC,CAACkD,MAAM,KAAKyD,CAAC,CAAClH,EAAE,IACjBW,WAAW,IAAIJ,CAAC,CAACC,SAAS,IAC1BG,WAAW,GAAGJ,CAAC,CAACG,OAAO,IACvBH,CAAC,CAACiF,aAAa,KAAK,SAAS,IAC7B,CAACjF,CAAC,CAACmF,UACP,CACF,CAAC,CAACrF,MAAM,EACR,GAAG,EAAC,oBAER;oBAAA;sBAAAuH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrJ,OAAA;UAAKgJ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCjJ,OAAA;YAAKgJ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5BjJ,OAAA;cAAKgJ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BjJ,OAAA;gBAAKgJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,GAAC,UAEzC,eAAAjJ,OAAA;kBAAKgJ,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BjJ,OAAA;oBAAAiJ,QAAA,EAAO;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLpF,QAAQ,CAAC,CAAC,CAACrC,GAAG,CAAC,CAACwC,IAAI,EAAE+F,KAAK,kBAC1BnK,OAAA;gBAEEgJ,SAAS,EAAE,6BACT5E,IAAI,CAACgG,MAAM,CAAC,CAAC,KAAK,CAAC,IAAIhG,IAAI,CAACgG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,EAC1D;gBAAAnB,QAAA,gBAEHjJ,OAAA;kBAAKgJ,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAErE,eAAe,CAACR,IAAI;gBAAC;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DrJ,OAAA;kBAAKgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEzE,UAAU,CAACJ,IAAI;gBAAC;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GANzCc,KAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOP,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLN,aAAa,CAACnH,GAAG,CAAEgE,IAAI,iBACtB5F,OAAA;cAAmBgJ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzCjJ,OAAA;gBAAKgJ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCjJ,OAAA;kBAAKgJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BjJ,OAAA;oBAAKgJ,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAErD,IAAI,CAACrE;kBAAI;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNrJ,OAAA;kBAAKgJ,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBjJ,OAAA,CAAC3B,KAAK;oBAAC6L,EAAE,EAAEtE,IAAI,CAACgB,IAAI,KAAK,aAAa,GAAG,MAAM,GAAGhB,IAAI,CAACgB,IAAI,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;oBAAAqC,QAAA,EACnGrD,IAAI,CAACgB,IAAI,KAAK,aAAa,GAAG,WAAW,GAAGhB,IAAI,CAACgB,IAAI,KAAK,aAAa,GAAG,WAAW,GAAG;kBAAgB;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNrJ,OAAA;kBAAKgJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BjJ,OAAA,CAAClB,MAAM;oBAAAoK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,KAAC,EAACzD,IAAI,CAACyE,QAAQ;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNrJ,OAAA;kBAAKgJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,GAAC,EAACrD,IAAI,CAACpE,KAAK,EAAC,cAAI;gBAAA;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EAGLpF,QAAQ,CAAC,CAAC,CAACrC,GAAG,CAAC,CAACwC,IAAI,EAAEkG,SAAS,KAAK;gBACnC,MAAMC,QAAQ,GAAGzF,YAAY,CAACc,IAAI,CAACtE,EAAE,EAAE8C,IAAI,CAAC;gBAC5C,MAAMa,OAAO,GAAGsF,QAAQ,GAAGrF,UAAU,CAACU,IAAI,CAACtE,EAAE,EAAE8C,IAAI,CAAC,GAAG,IAAI;gBAC3D,MAAMoG,SAAS,GAAGvF,OAAO,IAAIA,OAAO,CAAC6B,aAAa,KAAK,SAAS;gBAChE,MAAM2D,YAAY,GAAGxF,OAAO,IAAIA,OAAO,CAAC+B,UAAU;gBAClD,MAAM0D,OAAO,GAAGtG,IAAI,CAACiB,YAAY,CAAC,CAAC,KAAK,IAAItD,IAAI,CAAC,CAAC,CAACsD,YAAY,CAAC,CAAC;gBACjE,MAAMsF,SAAS,GAAG1F,OAAO,IAAIG,aAAa,CAACH,OAAO,EAAEb,IAAI,CAAC;gBACzD,MAAMwG,UAAU,GAAG3F,OAAO,IAAIK,cAAc,CAACL,OAAO,EAAEb,IAAI,CAAC;;gBAE3D;gBACA,IAAIyG,SAAS,GAAG,WAAW;gBAC3B,IAAIN,QAAQ,EAAE;kBACZ,IAAIE,YAAY,EAAE;oBAChBI,SAAS,GAAG,WAAW;kBACzB,CAAC,MAAM,IAAIL,SAAS,EAAE;oBACpBK,SAAS,GAAG,SAAS;kBACvB,CAAC,MAAM;oBACLA,SAAS,GAAG,QAAQ;kBACtB;gBACF;gBAEA,oBACE7K,OAAA;kBAEEgJ,SAAS,EAAE,2BAA2B6B,SAAS,IAC7CzG,IAAI,CAACgG,MAAM,CAAC,CAAC,KAAK,CAAC,IAAIhG,IAAI,CAACgG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,EAC1D;kBACHb,OAAO,EAAEA,CAAA,KAAM,CAACgB,QAAQ,IAAI5E,eAAe,CAACC,IAAI,EAAExB,IAAI,CAAE;kBAAA6E,QAAA,EAEvDsB,QAAQ,IAAI,CAACE,YAAY,gBACxBzK,OAAA,CAACvB,cAAc;oBACbqM,SAAS,EAAC,KAAK;oBACfC,OAAO,eACL/K,OAAA,CAACxB,OAAO;sBAAAyK,QAAA,gBACNjJ,OAAA;wBAAAiJ,QAAA,EAAShE,OAAO,CAACe;sBAAS;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eACpCrJ,OAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,wBACM,EAACpE,OAAO,CAACnD,SAAS,CAAC2C,kBAAkB,CAAC,CAAC,eACnDzE,OAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,uBACK,EAACpE,OAAO,CAACjD,OAAO,CAACyC,kBAAkB,CAAC,CAAC,eAChDzE,OAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,wBACM,EAACpE,OAAO,CAAC6B,aAAa,KAAK,MAAM,GAAG,eAAe,GAAG,iBAAiB,eACnF9G,OAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EACLpE,OAAO,CAAC8B,SAAS,GAAG,eAAe,GAAG,iBAAiB;oBAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CACV;oBAAAJ,QAAA,eAEDjJ,OAAA;sBAAKgJ,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BjJ,OAAA;wBAAKgJ,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAEhE,OAAO,CAACe;sBAAS;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrDrJ,OAAA;wBAAKgJ,SAAS,EAAE,eAAewB,SAAS,GAAG,cAAc,GAAG,aAAa,EAAG;wBAAAvB,QAAA,EACzEuB,SAAS,gBAAGxK,OAAA,CAACd,OAAO;0BAAAgK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGrJ,OAAA,CAACf,OAAO;0BAAAiK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC,EAGL,CAACpE,OAAO,CAAC8B,SAAS,IAAI4D,SAAS,IAAID,OAAO,iBACzC1K,OAAA,CAAC1B,MAAM;wBACL0M,IAAI,EAAC,IAAI;wBACT1B,OAAO,EAAC,SAAS;wBACjBN,SAAS,EAAC,kBAAkB;wBAC5BO,OAAO,EAAGzD,CAAC,IAAK;0BACdA,CAAC,CAACmF,eAAe,CAAC,CAAC;0BACnB5D,gBAAgB,CAACpC,OAAO,CAAC;wBAC3B,CAAE;wBAAAgE,QAAA,gBAEFjJ,OAAA,CAACb,WAAW;0BAAC6J,SAAS,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,uBAClC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CACT,EAGApE,OAAO,CAAC8B,SAAS,IAAI6D,UAAU,IAAIF,OAAO,IAAI,CAACzF,OAAO,CAAC+B,UAAU,iBAChEhH,OAAA,CAAC1B,MAAM;wBACL0M,IAAI,EAAC,IAAI;wBACT1B,OAAO,EAAC,QAAQ;wBAChBN,SAAS,EAAC,kBAAkB;wBAC5BO,OAAO,EAAGzD,CAAC,IAAK;0BACdA,CAAC,CAACmF,eAAe,CAAC,CAAC;0BACnB3D,iBAAiB,CAACrC,OAAO,CAAC;wBAC5B,CAAE;wBAAAgE,QAAA,gBAEFjJ,OAAA,CAACZ,YAAY;0BAAC4J,SAAS,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,sBACnC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC,gBAEjBrJ,OAAA;oBAAKgJ,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,eAC7BjJ,OAAA,CAAChB,OAAO;sBAACgK,SAAS,EAAC;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBACN,GAhEIiB,SAAS;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiEX,CAAC;cAEV,CAAC,CAAC;YAAA,GA3GMzD,IAAI,CAACtE,EAAE;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4GZ,CACN,CAAC,EAGDN,aAAa,CAACpH,MAAM,KAAK,CAAC,iBACzB3B,OAAA;cAAKgJ,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCjJ,OAAA;gBAAKgJ,SAAS,EAAC,6BAA6B;gBAACkC,OAAO,EAAEjH,QAAQ,CAAC,CAAC,CAACtC,MAAM,GAAG,CAAE;gBAAAsH,QAAA,GAAC,yFACjC,EAAC,GAAG,eAC9CjJ,OAAA,CAAC1B,MAAM;kBAACgL,OAAO,EAAC,MAAM;kBAACC,OAAO,EAAEA,CAAA,KAAMvG,mBAAmB,CAAC,IAAI,CAAE;kBAAAiG,QAAA,EAAC;gBAEjE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGNrJ,OAAA,CAACtB,KAAK;MAACyM,IAAI,EAAE5I,gBAAiB;MAAC6I,MAAM,EAAEA,CAAA,KAAM5I,mBAAmB,CAAC,KAAK,CAAE;MAAC6I,QAAQ;MAAApC,QAAA,gBAC/EjJ,OAAA,CAACtB,KAAK,CAAC4M,MAAM;QAACC,WAAW;QAAAtC,QAAA,eACvBjJ,OAAA,CAACtB,KAAK,CAAC8M,KAAK;UAAAvC,QAAA,GAAC,yBAAU,EAAC5G,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEd,IAAI;QAAA;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACfrJ,OAAA,CAACtB,KAAK,CAACkL,IAAI;QAAAX,QAAA,EACR5G,YAAY,iBACXrC,OAAA,CAACzB,IAAI;UAACkN,QAAQ,EAAE5F,gBAAiB;UAAAoD,QAAA,gBAC/BjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cAAC9E,IAAI,EAAC,MAAM;cAACrF,IAAI,EAAC,WAAW;cAACoK,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cAAC9E,IAAI,EAAC,MAAM;cAACrF,IAAI,EAAC,UAAU;cAACoK,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cAAC9E,IAAI,EAAC,KAAK;cAACrF,IAAI,EAAC,aAAa;cAACoK,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9BrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cAAC9E,IAAI,EAAC,OAAO;cAACrF,IAAI,EAAC,OAAO;cAACoK,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cAAC9E,IAAI,EAAC,MAAM;cAACV,KAAK,EAAE7D,YAAY,CAAC+B,IAAI,CAACK,kBAAkB,CAAC,CAAE;cAACmH,QAAQ;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cAAC9E,IAAI,EAAC,QAAQ;cAACrF,IAAI,EAAC,QAAQ;cAACsK,GAAG,EAAC,GAAG;cAACC,YAAY,EAAC,GAAG;cAACH,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cACX9E,IAAI,EAAC,QAAQ;cACbrF,IAAI,EAAC,YAAY;cACjBsK,GAAG,EAAC,GAAG;cACPE,GAAG,EAAE1J,YAAY,CAACgI,QAAS;cAC3ByB,YAAY,EAAC,GAAG;cAChBH,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrJ,OAAA,CAACzB,IAAI,CAACyN,IAAI;cAAChD,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,uCAAiB,EAAC5G,YAAY,CAACgI,QAAQ;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cACX9E,IAAI,EAAC,MAAM;cACXV,KAAK,EAAE7D,YAAY,CAACuE,IAAI,KAAK,aAAa,GAAG,KAAK,GAAGvE,YAAY,CAACuE,IAAI,KAAK,aAAa,GAAG,KAAK,GAAG,OAAQ;cAC3GgF,QAAQ;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEbrJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;cAAAd,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;cAAC9E,IAAI,EAAC,MAAM;cAACV,KAAK,EAAE,IAAI7D,YAAY,CAACb,KAAK,EAAG;cAACoK,QAAQ;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAEbrJ,OAAA;YAAKgJ,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrBjJ,OAAA,CAAC1B,MAAM;cAACgL,OAAO,EAAC,SAAS;cAAC1C,IAAI,EAAC,QAAQ;cAAAqC,QAAA,EAAC;YAExC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRrJ,OAAA,CAACtB,KAAK;MAACyM,IAAI,EAAE1I,gBAAiB;MAAC2I,MAAM,EAAEA,CAAA,KAAM1I,mBAAmB,CAAC,KAAK,CAAE;MAAC2I,QAAQ;MAAApC,QAAA,gBAC/EjJ,OAAA,CAACtB,KAAK,CAAC4M,MAAM;QAACC,WAAW;QAACvC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACzDjJ,OAAA,CAACtB,KAAK,CAAC8M,KAAK;UAAAvC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACfrJ,OAAA,CAACtB,KAAK,CAACkL,IAAI;QAAAX,QAAA,EACRtG,eAAe,iBACd3C,OAAA;UAAAiJ,QAAA,gBACEjJ,OAAA;YAAAiJ,QAAA,GAAI,WACI,EAACtG,eAAe,CAACoC,MAAM,EAAC,KAAG,EAACpC,eAAe,CAACqD,SAAS;UAAA;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAELrJ,OAAA;YAAKgJ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BjJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEtG,eAAe,CAACb,SAAS,CAAC2C,kBAAkB,CAAC;cAAC;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEtG,eAAe,CAACX,OAAO,CAACyC,kBAAkB,CAAC;cAAC;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC1BP,IAAI,CAACC,KAAK,CAAC,CAAChG,eAAe,CAACX,OAAO,GAAGW,eAAe,CAACb,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;cAAC;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BjJ,OAAA,CAAC3B,KAAK;kBACJ6L,EAAE,EACAvH,eAAe,CAACiE,IAAI,KAAK,aAAa,GAClC,MAAM,GACNjE,eAAe,CAACiE,IAAI,KAAK,aAAa,GACpC,SAAS,GACT,SACP;kBAAAqC,QAAA,EAEAtG,eAAe,CAACiE,IAAI,KAAK,aAAa,GAAG,KAAK,GAAGjE,eAAe,CAACiE,IAAI,KAAK,aAAa,GAAG,KAAK,GAAG;gBAAO;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEtG,eAAe,CAAC2D;cAAU;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BjJ,OAAA,CAAC3B,KAAK;kBAAC6L,EAAE,EAAEvH,eAAe,CAACgE,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAU;kBAAAsC,QAAA,EACvEtG,eAAe,CAACgE,MAAM,KAAK,WAAW,GAAG,aAAa,GAAG;gBAAc;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BjJ,OAAA,CAAC3B,KAAK;kBACJ6L,EAAE,EACAvH,eAAe,CAACmE,aAAa,KAAK,MAAM,GACpC,SAAS,GACTnE,eAAe,CAACmE,aAAa,KAAK,gBAAgB,GAChD,MAAM,GACN,SACP;kBAAAmC,QAAA,EAEAtG,eAAe,CAACmE,aAAa,KAAK,MAAM,GACrC,eAAe,GACfnE,eAAe,CAACmE,aAAa,KAAK,gBAAgB,GAChD,qBAAqB,GACrB;gBAAiB;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BjJ,OAAA,CAAC3B,KAAK;kBAAC6L,EAAE,EAAEvH,eAAe,CAACoE,SAAS,GAAG,SAAS,GAAG,WAAY;kBAAAkC,QAAA,EAC5DtG,eAAe,CAACoE,SAAS,GAAG,eAAe,GAAG;gBAAiB;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BjJ,OAAA,CAAC3B,KAAK;kBAAC6L,EAAE,EAAEvH,eAAe,CAACqE,UAAU,GAAG,SAAS,GAAG,WAAY;kBAAAiC,QAAA,EAC7DtG,eAAe,CAACqE,UAAU,GAAG,cAAc,GAAG;gBAAgB;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrJ,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC1BtG,eAAe,CAAC6D,WAAW,eAC5BxG,OAAA;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EACL1G,eAAe,CAAC8D,KAAK;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL1G,eAAe,CAACjB,QAAQ,IAAIiB,eAAe,CAACjB,QAAQ,CAACC,MAAM,GAAG,CAAC,iBAC9D3B,OAAA;cAAKgJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CrJ,OAAA;gBAAKgJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC1BtG,eAAe,CAACjB,QAAQ,CAACE,GAAG,CAAEmG,OAAO,iBACpC/H,OAAA,CAAC3B,KAAK;kBAAkB6L,EAAE,EAAC,MAAM;kBAAClB,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACpDlB,OAAO,CAACxG,IAAI,EAAC,KAAG,EAACwG,OAAO,CAACvG,KAAK,EAAC,GAClC;gBAAA,GAFYuG,OAAO,CAACzG,EAAE;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrJ,OAAA;YAAKgJ,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GACjD,CAACtG,eAAe,CAACoE,SAAS,gBACzB/G,OAAA,CAAC1B,MAAM;cAACgL,OAAO,EAAC,SAAS;cAACC,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAAC1E,eAAe,CAAE;cAAAsG,QAAA,gBACzEjJ,OAAA,CAACb,WAAW;gBAAC6J,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAClC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GACP,CAAC1G,eAAe,CAACqE,UAAU,gBAC7BhH,OAAA,CAAC1B,MAAM;cAACgL,OAAO,EAAC,QAAQ;cAACC,OAAO,EAAEA,CAAA,KAAMjC,iBAAiB,CAAC3E,eAAe,CAAE;cAAAsG,QAAA,gBACzEjJ,OAAA,CAACZ,YAAY;gBAAC4J,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBACnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETrJ,OAAA,CAAC1B,MAAM;cAACgL,OAAO,EAAC,iBAAiB;cAAAL,QAAA,gBAC/BjJ,OAAA,CAACjB,YAAY;gBAACiK,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iDACnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACDrJ,OAAA,CAAC1B,MAAM;cAACgL,OAAO,EAAC,gBAAgB;cAAAL,QAAA,gBAC9BjJ,OAAA,CAACf,OAAO;gBAAC+J,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oCAC9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRrJ,OAAA,CAACtB,KAAK;MAACyM,IAAI,EAAE9H,gBAAiB;MAAC+H,MAAM,EAAEA,CAAA,KAAM9H,mBAAmB,CAAC,KAAK,CAAE;MAAC+H,QAAQ;MAACL,IAAI,EAAC,IAAI;MAAA/B,QAAA,gBACzFjJ,OAAA,CAACtB,KAAK,CAAC4M,MAAM;QAACC,WAAW;QAACvC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACzDjJ,OAAA,CAACtB,KAAK,CAAC8M,KAAK;UAAAvC,QAAA,gBACVjJ,OAAA,CAACb,WAAW;YAAC6J,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAClC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfrJ,OAAA,CAACtB,KAAK,CAACkL,IAAI;QAAAX,QAAA,EACRxF,cAAc,iBACbzD,OAAA;UAAAiJ,QAAA,gBACEjJ,OAAA,CAAC9B,GAAG;YAAA+K,QAAA,gBACFjJ,OAAA,CAAC7B,GAAG;cAAC0L,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACTjJ,OAAA,CAAC5B,IAAI;gBAAC4K,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpBjJ,OAAA,CAAC5B,IAAI,CAACkN,MAAM;kBAACtC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eAC/BjJ,OAAA;oBAAIgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACdrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;kBAAAX,QAAA,gBACRjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAACuC,SAAS;kBAAA;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAAC8C,QAAQ;kBAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAAC+C,WAAW;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAACgD,KAAK;kBAAA;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrJ,OAAA,CAAC7B,GAAG;cAAC0L,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACTjJ,OAAA,CAAC5B,IAAI;gBAAC4K,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpBjJ,OAAA,CAAC5B,IAAI,CAACkN,MAAM;kBAACtC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eAC/BjJ,OAAA;oBAAIgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACdrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;kBAAAX,QAAA,gBACRjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAACsB,MAAM,EAAC,IAC/C,EAAC,EAAA5E,WAAA,GAAAS,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKmC,cAAc,CAACsB,MAAM,CAAC,cAAA5E,WAAA,uBAAjDA,WAAA,CAAmDyG,IAAI,MAAK,aAAa,GACtE,KAAK,GACL,EAAAxG,YAAA,GAAAQ,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKmC,cAAc,CAACsB,MAAM,CAAC,cAAA3E,YAAA,uBAAjDA,YAAA,CAAmDwG,IAAI,MAAK,aAAa,GACvE,KAAK,GACL,OAAO,EAAC,GAEhB;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAAC3B,SAAS,CAAC2C,kBAAkB,CAAC,CAAC;kBAAA;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAACzB,OAAO,CAACyC,kBAAkB,CAAC,CAAC;kBAAA;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAAC,GAAG,EAC3BX,IAAI,CAACC,KAAK,CAAC,CAAClF,cAAc,CAACzB,OAAO,GAAGyB,cAAc,CAAC3B,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;kBAAA;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvF,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5F,cAAc,CAAC6C,UAAU;kBAAA;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrJ,OAAA,CAAC5B,IAAI;YAAC4K,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACpBjJ,OAAA,CAAC5B,IAAI,CAACkN,MAAM;cAACtC,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC/BjJ,OAAA;gBAAIgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACdrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;cAAAX,QAAA,eACRjJ,OAAA,CAACzB,IAAI;gBAAA0K,QAAA,gBACHjJ,OAAA,CAAC9B,GAAG;kBAAA+K,QAAA,gBACFjJ,OAAA,CAAC7B,GAAG;oBAAC0L,EAAE,EAAE,CAAE;oBAAAZ,QAAA,eACTjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;wBAAAd,QAAA,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC1CrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;wBACX9E,IAAI,EAAC,MAAM;wBACXV,KAAK,EAAE,IAAI,EAAA7F,YAAA,GAAAO,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKmC,cAAc,CAACsB,MAAM,CAAC,cAAA1E,YAAA,uBAAjDA,YAAA,CAAmDmB,KAAK,KAAI,CAAC,EAAG;wBAC3EoK,QAAQ;sBAAA;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNrJ,OAAA,CAAC7B,GAAG;oBAAC0L,EAAE,EAAE,CAAE;oBAAAZ,QAAA,eACTjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;wBAAAd,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACxCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;wBACX9E,IAAI,EAAC,MAAM;wBACXV,KAAK,EAAE,IACL,CAAC,EAAA5F,YAAA,GAAAM,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKmC,cAAc,CAACsB,MAAM,CAAC,cAAAzE,YAAA,uBAAjDA,YAAA,CAAmDkB,KAAK,KAAI,CAAC,IAC9DkH,IAAI,CAACC,KAAK,CAAC,CAAClF,cAAc,CAACzB,OAAO,GAAGyB,cAAc,CAAC3B,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EACtF;wBACH8J,QAAQ;sBAAA;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrJ,OAAA,CAAC9B,GAAG;kBAAA+K,QAAA,gBACFjJ,OAAA,CAAC7B,GAAG;oBAAC0L,EAAE,EAAE,CAAE;oBAAAZ,QAAA,eACTjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;wBAAAd,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACxCrJ,OAAA,CAACzB,IAAI,CAACmN,OAAO;wBACX9E,IAAI,EAAC,QAAQ;wBACbtF,EAAE,EAAC,eAAe;wBAClBwK,YAAY,EAAE,EAAAvL,YAAA,GAAAK,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKmC,cAAc,CAACsB,MAAM,CAAC,cAAAxE,YAAA,uBAAjDA,YAAA,CAAmDiB,KAAK,KAAI,CAAE;wBAC5EqK,GAAG,EAAC,GAAG;wBACPI,IAAI,EAAC;sBAAM;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNrJ,OAAA,CAAC7B,GAAG;oBAAC0L,EAAE,EAAE,CAAE;oBAAAZ,QAAA,eACTjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;wBAAAd,QAAA,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/CrJ,OAAA,CAACzB,IAAI,CAACyL,MAAM;wBAAC9D,KAAK,EAAEnC,aAAc;wBAACkG,QAAQ,EAAGnE,CAAC,IAAK9B,gBAAgB,CAAC8B,CAAC,CAACG,MAAM,CAACC,KAAK,CAAE;wBAAA+C,QAAA,gBACnFjJ,OAAA;0BAAQkG,KAAK,EAAC,QAAQ;0BAAA+C,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5CrJ,OAAA;0BAAQkG,KAAK,EAAC,MAAM;0BAAA+C,QAAA,EAAC;wBAAQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACtCrJ,OAAA;0BAAQkG,KAAK,EAAC,MAAM;0BAAA+C,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEPrJ,OAAA;YAAKgJ,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CjJ,OAAA,CAAC1B,MAAM;cAACgL,OAAO,EAAC,WAAW;cAACC,OAAO,EAAEA,CAAA,KAAMjG,mBAAmB,CAAC,KAAK,CAAE;cAAA2F,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrJ,OAAA,CAAC1B,MAAM;cACLgL,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMtC,aAAa,GAAGb,MAAM,CAAC8F,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,CAAClG,KAAK,CAAC;gBACvFuB,aAAa,CAAChE,cAAc,CAACnC,EAAE,EAAE2F,aAAa,CAAC;cACjD,CAAE;cAAAgC,QAAA,gBAEFjJ,OAAA,CAACb,WAAW;gBAAC6J,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAClC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRrJ,OAAA,CAACtB,KAAK;MAACyM,IAAI,EAAE5H,iBAAkB;MAAC6H,MAAM,EAAEA,CAAA,KAAM5H,oBAAoB,CAAC,KAAK,CAAE;MAAC6H,QAAQ;MAACL,IAAI,EAAC,IAAI;MAAA/B,QAAA,gBAC3FjJ,OAAA,CAACtB,KAAK,CAAC4M,MAAM;QAACC,WAAW;QAACvC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACxDjJ,OAAA,CAACtB,KAAK,CAAC8M,KAAK;UAAAvC,QAAA,gBACVjJ,OAAA,CAACZ,YAAY;YAAC4J,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBACnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfrJ,OAAA,CAACtB,KAAK,CAACkL,IAAI;QAAAX,QAAA,EACRtF,eAAe,gBACd3D,OAAA;UAAAiJ,QAAA,gBACEjJ,OAAA,CAAC9B,GAAG;YAAA+K,QAAA,gBACFjJ,OAAA,CAAC7B,GAAG;cAAC0L,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACTjJ,OAAA,CAAC5B,IAAI;gBAAC4K,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpBjJ,OAAA,CAAC5B,IAAI,CAACkN,MAAM;kBAACtC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eAC/BjJ,OAAA;oBAAIgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACdrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;kBAAAX,QAAA,gBACRjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAACqC,SAAS;kBAAA;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAAC4C,QAAQ;kBAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAAC6C,WAAW;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAAC8C,KAAK;kBAAA;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrJ,OAAA,CAAC7B,GAAG;cAAC0L,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACTjJ,OAAA,CAAC5B,IAAI;gBAAC4K,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpBjJ,OAAA,CAAC5B,IAAI,CAACkN,MAAM;kBAACtC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eAC/BjJ,OAAA;oBAAIgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACdrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;kBAAAX,QAAA,gBACRjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAACoB,MAAM,EAAC,IAChD,EAAC,EAAAvE,YAAA,GAAAI,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKqC,eAAe,CAACoB,MAAM,CAAC,cAAAvE,YAAA,uBAAlDA,YAAA,CAAoDoG,IAAI,MAAK,aAAa,GACvE,KAAK,GACL,EAAAnG,YAAA,GAAAG,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKqC,eAAe,CAACoB,MAAM,CAAC,cAAAtE,YAAA,uBAAlDA,YAAA,CAAoDmG,IAAI,MAAK,aAAa,GACxE,KAAK,GACL,OAAO,EAAC,GAEhB;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAAC7B,SAAS,CAAC2C,kBAAkB,CAAC,CAAC;kBAAA;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAAC3B,OAAO,CAACyC,kBAAkB,CAAC,CAAC;kBAAA;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAAC,GAAG,EAC3BX,IAAI,CAACC,KAAK,CAAC,CAAChF,eAAe,CAAC3B,OAAO,GAAG2B,eAAe,CAAC7B,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;kBAAA;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC,eACJrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,eAAe,CAAC2C,UAAU;kBAAA;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrJ,OAAA,CAAC5B,IAAI;YAAC4K,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACpBjJ,OAAA,CAAC5B,IAAI,CAACkN,MAAM;cAACtC,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC/BjJ,OAAA;gBAAIgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACdrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;cAAAX,QAAA,gBACRjJ,OAAA;gBAAAiJ,QAAA,EAAG;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDrJ,OAAA,CAAC9B,GAAG;gBAAA+K,QAAA,EACDL,KAAK,CAACC,OAAO,CAAC9H,iBAAiB,CAAC,IAAIA,iBAAiB,CAACY,MAAM,GAAG,CAAC,GAC/DZ,iBAAiB,CAACa,GAAG,CAAEmG,OAAO,iBAC5B/H,OAAA,CAAC7B,GAAG;kBAAC0L,EAAE,EAAE,CAAE;kBAAkBb,SAAS,EAAC,MAAM;kBAAAC,QAAA,eAC3CjJ,OAAA,CAACzB,IAAI,CAAC8N,KAAK;oBACTzF,IAAI,EAAC,UAAU;oBACftF,EAAE,EAAE,WAAWyG,OAAO,CAACzG,EAAE,EAAG;oBAC5BgL,KAAK,eACHtM,OAAA;sBAAAiJ,QAAA,GACGlB,OAAO,CAACtG,IAAI,iBAAIzB,OAAA,CAAC+H,OAAO,CAACtG,IAAI;wBAACuH,SAAS,EAAC;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACjDtB,OAAO,CAACxG,IAAI,EAAC,KAAG,EAACwG,OAAO,CAACvG,KAAK,EAAC,GAClC;oBAAA;sBAAA0H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;oBACDkD,OAAO,EAAE1I,gBAAgB,CAACmB,IAAI,CAAEgD,CAAC,IAAKA,CAAC,CAAC1G,EAAE,KAAKyG,OAAO,CAACzG,EAAE,CAAE;oBAC3D2I,QAAQ,EAAEA,CAAA,KAAMnC,aAAa,CAACC,OAAO;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC,GAZatB,OAAO,CAACzG,EAAE;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAatB,CACN,CAAC,gBAEFrJ,OAAA,CAAC7B,GAAG;kBAAA8K,QAAA,eACFjJ,OAAA;oBAAGgJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAA6B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEPrJ,OAAA,CAAC5B,IAAI;YAAC4K,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACpBjJ,OAAA,CAAC5B,IAAI,CAACkN,MAAM;cAACtC,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC/BjJ,OAAA;gBAAIgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACdrJ,OAAA,CAAC5B,IAAI,CAACwL,IAAI;cAAAX,QAAA,gBACRjJ,OAAA,CAACrB,KAAK;gBAAC6N,OAAO;gBAACC,QAAQ;gBAAAxD,QAAA,gBACrBjJ,OAAA;kBAAAiJ,QAAA,eACEjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAI;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACdrJ,OAAA;sBAAIgJ,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRrJ,OAAA;kBAAAiJ,QAAA,gBACEjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,GAAI,sBAEF,EAACP,IAAI,CAACC,KAAK,CAAC,CAAChF,eAAe,CAAC3B,OAAO,GAAG2B,eAAe,CAAC7B,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,EAAC,iBACzF,EAAC,EAAApB,YAAA,GAAAE,KAAK,CAACuE,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAAClH,EAAE,KAAKqC,eAAe,CAACoB,MAAM,CAAC,cAAArE,YAAA,uBAAlDA,YAAA,CAAoDc,KAAK,KAAI,CAAC,EAAC,eAEzE;oBAAA;sBAAA0H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrJ,OAAA;sBAAIgJ,SAAS,EAAC,UAAU;sBAAAC,QAAA,GAAC,GAAC,EAACf,aAAa,CAACvE,eAAe,CAAC,CAACwE,UAAU,CAACuE,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,EACJxF,gBAAgB,CAACjC,GAAG,CAAEmG,OAAO,iBAC5B/H,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAKlB,OAAO,CAACxG;oBAAI;sBAAA2H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvBrJ,OAAA;sBAAIgJ,SAAS,EAAC,UAAU;sBAAAC,QAAA,GAAC,GAAC,EAAClB,OAAO,CAACvG,KAAK,CAACkL,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,GAFlDtB,OAAO,CAACzG,EAAE;oBAAA4H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGf,CACL,CAAC,eACFrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAIgJ,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzCrJ,OAAA;sBAAIgJ,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,GAAC,GAE/B,EAAC,CACCf,aAAa,CAACvE,eAAe,CAAC,CAACwE,UAAU,GAAGD,aAAa,CAACvE,eAAe,CAAC,CAACyE,aAAa,EACxFsE,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACLrJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAAiJ,QAAA,EAAI;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBrJ,OAAA;sBAAIgJ,SAAS,EAAC,UAAU;sBAAAC,QAAA,GAAC,IACrB,EAAC,CAACtF,eAAe,CAACsD,aAAa,IAAI,CAAC,EAAEyF,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACLrJ,OAAA;oBAAIgJ,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC3BjJ,OAAA;sBAAIgJ,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnDrJ,OAAA;sBAAIgJ,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,GAAC,GAAC,EAACf,aAAa,CAACvE,eAAe,CAAC,CAAC4E,OAAO,CAACmE,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAERrJ,OAAA,CAACzB,IAAI;gBAAA0K,QAAA,eACHjJ,OAAA,CAACzB,IAAI,CAACuL,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BjJ,OAAA,CAACzB,IAAI,CAACwL,KAAK;oBAAAd,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/CrJ,OAAA,CAACzB,IAAI,CAACyL,MAAM;oBAAC9D,KAAK,EAAEnC,aAAc;oBAACkG,QAAQ,EAAGnE,CAAC,IAAK9B,gBAAgB,CAAC8B,CAAC,CAACG,MAAM,CAACC,KAAK,CAAE;oBAAA+C,QAAA,gBACnFjJ,OAAA;sBAAQkG,KAAK,EAAC,QAAQ;sBAAA+C,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CrJ,OAAA;sBAAQkG,KAAK,EAAC,MAAM;sBAAA+C,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCrJ,OAAA;sBAAQkG,KAAK,EAAC,MAAM;sBAAA+C,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEPrJ,OAAA;YAAKgJ,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CjJ,OAAA,CAAC1B,MAAM;cAACgL,OAAO,EAAC,WAAW;cAACC,OAAO,EAAEA,CAAA,KAAM/F,oBAAoB,CAAC,KAAK,CAAE;cAAAyF,QAAA,EAAC;YAExE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrJ,OAAA;cAAAiJ,QAAA,eACEjJ,OAAA,CAAC1B,MAAM;gBAACgL,OAAO,EAAC,QAAQ;gBAACC,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAACjE,eAAe,CAACrC,EAAE,EAAEuC,gBAAgB,CAAE;gBAAAoF,QAAA,gBAC3FjJ,OAAA,CAACZ,YAAY;kBAAC4J,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uCACnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENrJ,OAAA;UAAKgJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BjJ,OAAA;YAAAiJ,QAAA,EAAG;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzCrJ,OAAA,CAAC1B,MAAM;YAACgL,OAAO,EAAC,WAAW;YAACC,OAAO,EAAEA,CAAA,KAAM/F,oBAAoB,CAAC,KAAK,CAAE;YAAAyF,QAAA,EAAC;UAExE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAACnJ,EAAA,CAjlCQD,wBAAwB;EAAA,QACdJ,cAAc,EAU3BD,cAAc;AAAA;AAAA+M,EAAA,GAXX1M,wBAAwB;AAmlCjC,eAAeA,wBAAwB;AAAA,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}