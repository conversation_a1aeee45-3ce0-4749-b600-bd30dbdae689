{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\create_hotel\\\\BookingPropertyDescription.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useState, useEffect } from \"react\";\nimport { Navbar, Container, Button, Form, Card, ProgressBar, Row, Col, Alert, Spinner } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { ArrowLeft, Upload, X, Star } from \"lucide-react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAppDispatch, useAppSelector } from \"@redux/store\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport * as Routers from \"../../../utils/Routes\";\nimport Factories from \"@redux/hotel/factories\"; // Import factories\n\n// Star rating options\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst starOptions = [{\n  value: 1,\n  label: \"1 sao\"\n}, {\n  value: 2,\n  label: \"2 sao\"\n}, {\n  value: 3,\n  label: \"3 sao\"\n}, {\n  value: 4,\n  label: \"4 sao\"\n}, {\n  value: 5,\n  label: \"5 sao\"\n}];\nexport default function BookingPropertyDescription() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const createHotel = useAppSelector(state => state.Hotel.createHotel);\n  console.log(\"createHotel:   \", createHotel);\n\n  // Initialize state with values from Redux store\n  const [star, setStar] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.star) || 1);\n  const [description, setDescription] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.description) || \"\");\n  const [images, setImages] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.images) || []);\n  const [localImages, setLocalImages] = useState([]); // For local file preview\n\n  const [dragActive, setDragActive] = useState(false);\n  const [validationErrors, setValidationErrors] = useState([]);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Validate form\n  const validateForm = () => {\n    const errors = [];\n    if (!description.trim()) {\n      errors.push(\"Vui lòng nhập mô tả về khách sạn\");\n    } else if (description.trim().length < 50) {\n      errors.push(\"Mô tả khách sạn phải có ít nhất 50 ký tự\");\n    }\n    const totalImages = images.length + localImages.length;\n    if (totalImages < 5) {\n      errors.push(`Vui lòng upload đủ 5 hình ảnh (hiện tại: ${totalImages}/5)`);\n    }\n    return errors;\n  };\n\n  // Validate whenever form data changes\n  useEffect(() => {\n    const errors = validateForm();\n    setValidationErrors(errors);\n  }, [star, description, images, localImages]);\n  const handleImageChange = event => {\n    const files = Array.from(event.target.files || []);\n    handleFiles(files);\n  };\n  const handleFiles = files => {\n    // Validate file types\n    const validTypes = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/webp\"];\n    const invalidFiles = files.filter(file => !validTypes.includes(file.type));\n    if (invalidFiles.length > 0) {\n      showToast.error(\"Chỉ chấp nhận file ảnh định dạng JPG, PNG, WEBP\");\n      return;\n    }\n\n    // Validate file sizes (max 5MB each)\n    const oversizedFiles = files.filter(file => file.size > 5 * 1024 * 1024);\n    if (oversizedFiles.length > 0) {\n      showToast.error(\"Kích thước file không được vượt quá 5MB\");\n      return;\n    }\n\n    // Check total number of images\n    const totalImages = images.length + localImages.length + files.length;\n    if (totalImages > 5) {\n      showToast.warning(\"Chỉ được upload tối đa 5 hình ảnh\");\n      return;\n    }\n\n    // Create preview objects for local files\n    const filesWithPreview = files.map(file => ({\n      file: file,\n      preview: URL.createObjectURL(file),\n      name: file.name,\n      size: file.size,\n      isLocal: true\n    }));\n    setLocalImages(prev => [...prev, ...filesWithPreview]);\n  };\n  const removeLocalImage = index => {\n    const imageToRemove = localImages[index];\n    if (imageToRemove && imageToRemove.preview) {\n      URL.revokeObjectURL(imageToRemove.preview);\n    }\n    setLocalImages(localImages.filter((_, i) => i !== index));\n  };\n  const removeUploadedImage = async index => {\n    const imageToRemove = images[index];\n    try {\n      // Call API to delete from Cloudinary\n      const response = await Factories.deleteHotelImages([imageToRemove.public_ID]);\n      if (response.data && !response.data.error) {\n        // Remove from local state\n        setImages(images.filter((_, i) => i !== index));\n        showToast.success(\"Đã xóa ảnh thành công!\");\n      } else {\n        var _response$data;\n        throw new Error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || \"Không thể xóa ảnh\");\n      }\n    } catch (error) {\n      console.error('Error deleting image:', error);\n      showToast.error(\"Có lỗi xảy ra khi xóa ảnh: \" + error.message);\n    }\n  };\n  const uploadImages = async () => {\n    if (localImages.length === 0) return [];\n    try {\n      setIsUploading(true);\n      const formData = new FormData();\n      localImages.forEach(imgObj => {\n        formData.append('images', imgObj.file);\n      });\n      const response = await Factories.uploadHotelImages(formData);\n      if (response.data && !response.data.error) {\n        // Cleanup local previews\n        localImages.forEach(img => {\n          if (img.preview) {\n            URL.revokeObjectURL(img.preview);\n          }\n        });\n        showToast.success(\"Upload ảnh thành công!\");\n        return response.data.data.images;\n      } else {\n        var _response$data2;\n        throw new Error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Upload failed\");\n      }\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      showToast.error(\"Có lỗi xảy ra khi upload ảnh: \" + error.message);\n      return [];\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleDrag = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === \"dragenter\" || e.type === \"dragover\") {\n      setDragActive(true);\n    } else if (e.type === \"dragleave\") {\n      setDragActive(false);\n    }\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      const files = Array.from(e.dataTransfer.files);\n      handleFiles(files);\n    }\n  };\n  const handleContinue = async () => {\n    const errors = validateForm();\n    if (errors.length > 0) {\n      showToast.error(\"Vui lòng kiểm tra lại thông tin đã nhập\");\n      return;\n    }\n    try {\n      let uploadedImages = [];\n\n      // Upload new images if any\n      if (localImages.length > 0) {\n        uploadedImages = await uploadImages();\n        if (uploadedImages.length === 0) {\n          return; // Upload failed\n        }\n      }\n\n      // Combine existing and new images\n      const allImages = [...images, ...uploadedImages];\n\n      // Clear local images after successful upload\n      setLocalImages([]);\n\n      // Dispatch action to save data\n      dispatch({\n        type: HotelActions.SAVE_HOTEL_DESCRIPTION_CREATE,\n        payload: {\n          star,\n          description: description.trim(),\n          images: allImages,\n          checkCreateHotel: true\n        }\n      });\n      showToast.success(\"Đã lưu thông tin mô tả khách sạn thành công!\");\n      navigate(Routers.BookingPropertyChecklist);\n    } catch (error) {\n      console.error('Error in handleContinue:', error);\n      showToast.error(\"Có lỗi xảy ra: \" + error.message);\n    }\n  };\n  const handleBack = () => {\n    // Save current data before going back\n    dispatch({\n      type: HotelActions.SAVE_HOTEL_DESCRIPTION_CREATE,\n      payload: {\n        star,\n        description: description.trim(),\n        images: images // Only save uploaded images, not local previews\n      }\n    });\n\n    // Cleanup local images\n    localImages.forEach(img => {\n      if (img.preview) {\n        URL.revokeObjectURL(img.preview);\n      }\n    });\n    navigate(Routers.BookingPropertyCheckInOut);\n  };\n\n  // Cleanup URLs when component unmounts\n  useEffect(() => {\n    return () => {\n      localImages.forEach(img => {\n        if (img.preview) {\n          URL.revokeObjectURL(img.preview);\n        }\n      });\n    };\n  }, []);\n  const isFormValid = validationErrors.length === 0;\n  const totalImages = images.length + localImages.length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"booking-app\",\n    children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navbar, {\n      style: {\n        backgroundColor: \"#003580\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n          href: \"#home\",\n          className: \"text-white fw-bold\",\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            style: {\n              fontSize: 30\n            },\n            children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#f8e71c\"\n              },\n              children: \"OO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), \"M\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-label mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Th\\xF4ng tin c\\u01A1 b\\u1EA3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n          style: {\n            height: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 20\n          }, 1, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 20\n          }, 2, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 20\n          }, 3, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 20\n          }, 4, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 20\n          }, 5, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"main-content py-4\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 7,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"main-heading\",\n              children: \"Ti\\xEAu chu\\u1EA9n v\\xE0 m\\xF4 t\\u1EA3 v\\u1EC1 kh\\xE1ch s\\u1EA1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), validationErrors.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"danger\",\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n              children: \"Vui l\\xF2ng ki\\u1EC3m tra l\\u1EA1i:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"mb-0\",\n              children: validationErrors.map((error, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: error\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this), isUploading && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), \"\\u0110ang upload \\u1EA3nh...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"facility-form-card\",\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"fw-bold\",\n                      children: [/*#__PURE__*/_jsxDEV(Star, {\n                        className: \"me-2\",\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 25\n                      }, this), \"Ti\\xEAu chu\\u1EA9n kh\\xE1ch s\\u1EA1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 46\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      className: \"form-input\",\n                      value: star,\n                      onChange: e => setStar(Number(e.target.value)),\n                      children: starOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: option.value,\n                        children: option.label\n                      }, option.value, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  className: \"d-flex align-items-end\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"star-display\",\n                    children: [...Array(5)].map((_, index) => /*#__PURE__*/_jsxDEV(Star, {\n                      size: 24,\n                      className: index < star ? \"star-filled\" : \"star-empty\",\n                      fill: index < star ? \"#ffc107\" : \"none\",\n                      color: index < star ? \"#ffc107\" : \"#dee2e6\"\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"fw-bold\",\n                      children: [\"M\\xF4 t\\u1EA3 v\\u1EC1 kh\\xE1ch s\\u1EA1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 372,\n                        columnNumber: 44\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      as: \"textarea\",\n                      rows: 4,\n                      placeholder: \"Nh\\u1EADp m\\xF4 t\\u1EA3 chi ti\\u1EBFt v\\u1EC1 kh\\xE1ch s\\u1EA1n c\\u1EE7a b\\u1EA1n... (t\\u1ED1i thi\\u1EC3u 50 k\\xFD t\\u1EF1)\",\n                      className: \"form-input\",\n                      value: description,\n                      onChange: e => setDescription(e.target.value),\n                      isInvalid: validationErrors.some(error => error.includes(\"mô tả\"))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                      className: \"text-muted\",\n                      children: [description.length, \"/50 k\\xFD t\\u1EF1 t\\u1ED1i thi\\u1EC3u\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"fw-bold\",\n                      children: [\"H\\xECnh \\u1EA3nh kh\\xE1ch s\\u1EA1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 44\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-muted ms-2\",\n                        children: [\"(\", totalImages, \"/5 \\u1EA3nh)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 394,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `upload-area ${dragActive ? \"drag-active\" : \"\"} ${totalImages >= 5 ? \"disabled\" : \"\"}`,\n                      onDragEnter: handleDrag,\n                      onDragLeave: handleDrag,\n                      onDragOver: handleDrag,\n                      onDrop: handleDrop,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"upload-content\",\n                        children: [/*#__PURE__*/_jsxDEV(Upload, {\n                          size: 48,\n                          className: \"upload-icon\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 408,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                          children: \"K\\xE9o th\\u1EA3 \\u1EA3nh v\\xE0o \\u0111\\xE2y ho\\u1EB7c\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 409,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"file\",\n                          multiple: true,\n                          accept: \"image/*\",\n                          onChange: handleImageChange,\n                          disabled: totalImages >= 5 || isUploading,\n                          className: \"d-none\",\n                          id: \"image-upload\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 410,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          as: \"label\",\n                          htmlFor: \"image-upload\",\n                          variant: \"outline-primary\",\n                          disabled: totalImages >= 5 || isUploading,\n                          children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                              animation: \"border\",\n                              size: \"sm\",\n                              className: \"me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 427,\n                              columnNumber: 33\n                            }, this), \"\\u0110ang upload...\"]\n                          }, void 0, true) : \"Chọn ảnh\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 419,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"upload-note\",\n                          children: \"Ch\\u1EA5p nh\\u1EADn JPG, PNG, WEBP. T\\u1ED1i \\u0111a 5MB m\\u1ED7i \\u1EA3nh.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 434,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 23\n                    }, this), (images.length > 0 || localImages.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"image-preview mt-3\",\n                      children: /*#__PURE__*/_jsxDEV(Row, {\n                        children: [images.map((img, index) => /*#__PURE__*/_jsxDEV(Col, {\n                          md: 4,\n                          className: \"mb-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"preview-container\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: img.url || \"/placeholder.svg\",\n                              alt: `Uploaded ${index + 1}`,\n                              className: \"preview-image\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 446,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"danger\",\n                              size: \"sm\",\n                              className: \"remove-button\",\n                              onClick: () => removeUploadedImage(index),\n                              title: \"X\\xF3a \\u1EA3nh \\u0111\\xE3 upload\",\n                              children: /*#__PURE__*/_jsxDEV(X, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 458,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 451,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"image-info\",\n                              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                                children: \"\\u0110\\xE3 upload\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 461,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 462,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                                className: \"text-success\",\n                                children: \"\\u2713 L\\u01B0u tr\\xEAn cloud\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 463,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 460,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 445,\n                            columnNumber: 33\n                          }, this)\n                        }, `uploaded-${index}`, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 444,\n                          columnNumber: 31\n                        }, this)), localImages.map((imgObj, index) => /*#__PURE__*/_jsxDEV(Col, {\n                          md: 4,\n                          className: \"mb-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"preview-container\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: imgObj.preview,\n                              alt: `Preview ${index + 1}`,\n                              className: \"preview-image\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 473,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"danger\",\n                              size: \"sm\",\n                              className: \"remove-button\",\n                              onClick: () => removeLocalImage(index),\n                              title: \"X\\xF3a \\u1EA3nh ch\\u01B0a upload\",\n                              children: /*#__PURE__*/_jsxDEV(X, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 485,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 478,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"image-info\",\n                              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                                children: imgObj.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 488,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 489,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                                className: \"text-warning\",\n                                children: \"Ch\\u01B0a upload\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 490,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 487,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 472,\n                            columnNumber: 33\n                          }, this)\n                        }, `local-${index}`, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 471,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this), isFormValid && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"success\",\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-summary\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: \"T\\xF3m t\\u1EAFt th\\xF4ng tin:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Ti\\xEAu chu\\u1EA9n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 25\n                    }, this), \" \", star, \" sao\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"M\\xF4 t\\u1EA3:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 25\n                    }, this), \" \", description.length, \" k\\xFD t\\u1EF1\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"H\\xECnh \\u1EA3nh:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 25\n                    }, this), \" \", totalImages, \"/5 \\u1EA3nh\", localImages.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-warning ms-2\",\n                      children: [\"(\", localImages.length, \" \\u1EA3nh ch\\u01B0a upload)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"navigation-buttons mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              className: \"back-button\",\n              onClick: handleBack,\n              title: \"Quay l\\u1EA1i\",\n              disabled: isUploading,\n              children: /*#__PURE__*/_jsxDEV(ArrowLeft, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              className: \"continue-button\",\n              onClick: handleContinue,\n              disabled: !isFormValid || isUploading,\n              children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                  animation: \"border\",\n                  size: \"sm\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 21\n                }, this), \"\\u0110ang x\\u1EED l\\xFD...\"]\n              }, void 0, true) : \"Tiếp tục\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-cards\",\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-icon lightbulb\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      role: \"img\",\n                      \"aria-label\": \"lightbulb\",\n                      children: \"\\uD83D\\uDCA1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"info-title\",\n                      children: \"Nh\\u1EEFng ti\\xEAu chu\\u1EA9n c\\u1EE7a kh\\xE1ch s\\u1EA1n c\\u1EE7a m\\xECnh?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"info-text mt-3\",\n                      children: \"Qu\\xFD v\\u1ECB c\\xF3 th\\u1EC3 d\\u1EC5 d\\xE0ng t\\xF9y ch\\u1EC9nh c\\xE1c quy t\\u1EAFc chung n\\xE0y sau v\\xE0 c\\xE1c quy t\\u1EAFc chung b\\u1ED5 sung c\\xF3 th\\u1EC3 \\u0111\\u01B0\\u1EE3c c\\xE0i \\u0111\\u1EB7t trong trang Ch\\xEDnh s\\xE1ch tr\\xEAn extranet sau khi ho\\xE0n t\\u1EA5t \\u0111\\u0103ng k\\xFD.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-icon tips\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      role: \"img\",\n                      \"aria-label\": \"tips\",\n                      children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"info-title\",\n                      children: \"M\\u1EB9o upload \\u1EA3nh hi\\u1EC7u qu\\u1EA3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"info-list mt-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"\\u1EA2nh c\\xF3 th\\u1EC3 preview tr\\u01B0\\u1EDBc khi upload\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"\\u1EA2nh m\\xE0u xanh \\u0111\\xE3 l\\u01B0u tr\\xEAn cloud an to\\xE0n\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 588,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"\\u1EA2nh m\\xE0u v\\xE0ng ch\\u01B0a upload, c\\u1EA7n ti\\u1EBFp t\\u1EE5c \\u0111\\u1EC3 l\\u01B0u\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 589,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"C\\xF3 th\\u1EC3 x\\xF3a \\u1EA3nh b\\u1EA5t k\\u1EF3 l\\xFAc n\\xE0o\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .booking-app {\n          min-height: 100vh;\n          background-color: #f8f9fa;\n        }\n\n        .progress-section {\n          margin: 0 auto;\n        }\n\n        .progress-label {\n          font-size: 14px;\n          color: #333;\n        }\n\n        .main-content {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .main-heading {\n          font-size: 28px;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 20px;\n        }\n\n        .facility-form-card {\n          background-color: #fff;\n          border-radius: 8px;\n          padding: 24px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          border: 1px solid #e9ecef;\n        }\n\n        .form-input {\n          border: 1px solid #ced4da;\n          border-radius: 6px;\n          font-size: 16px;\n          transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n        }\n\n        .form-input:focus {\n          border-color: #0071c2;\n          box-shadow: 0 0 0 0.2rem rgba(0, 113, 194, 0.25);\n        }\n\n        .form-input.is-invalid {\n          border-color: #dc3545;\n        }\n\n        .star-display {\n          display: flex;\n          gap: 4px;\n          align-items: center;\n        }\n\n        .star-filled {\n          color: #ffc107;\n        }\n\n        .star-empty {\n          color: #dee2e6;\n        }\n\n        .upload-area {\n          border: 2px dashed #ced4da;\n          border-radius: 8px;\n          padding: 40px 20px;\n          text-align: center;\n          background-color: #f8f9fa;\n          transition: all 0.3s ease;\n          cursor: pointer;\n        }\n\n        .upload-area:hover:not(.disabled) {\n          border-color: #0071c2;\n          background-color: #f0f8ff;\n        }\n\n        .upload-area.drag-active {\n          border-color: #0071c2;\n          background-color: #e3f2fd;\n        }\n\n        .upload-area.disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .upload-content {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 12px;\n        }\n\n        .upload-icon {\n          color: #6c757d;\n        }\n\n        .upload-note {\n          font-size: 12px;\n          color: #6c757d;\n          margin: 0;\n        }\n\n        .preview-container {\n          position: relative;\n          border-radius: 8px;\n          overflow: hidden;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n\n        .preview-image {\n          width: 100%;\n          height: 200px;\n          object-fit: cover;\n          display: block;\n        }\n\n        .remove-button {\n          position: absolute;\n          top: 8px;\n          right: 8px;\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 0;\n        }\n\n        .image-info {\n          position: absolute;\n          bottom: 0;\n          left: 0;\n          right: 0;\n          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\n          color: white;\n          padding: 8px;\n          font-size: 12px;\n        }\n\n        .form-summary h6 {\n          color: #155724;\n          margin-bottom: 8px;\n        }\n\n        .navigation-buttons {\n          display: flex;\n          gap: 12px;\n        }\n\n        .back-button {\n          width: 50px;\n          height: 45px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-color: #0071c2;\n          color: #0071c2;\n          border-radius: 6px;\n        }\n\n        .back-button:hover {\n          background-color: #0071c2;\n          color: white;\n        }\n\n        .continue-button {\n          flex: 1;\n          height: 45px;\n          background-color: #0071c2;\n          border: none;\n          font-weight: 600;\n          border-radius: 6px;\n          transition: background-color 0.15s ease-in-out;\n        }\n\n        .continue-button:hover:not(:disabled) {\n          background-color: #005999;\n        }\n\n        .continue-button:disabled {\n          background-color: #6c757d;\n          border-color: #6c757d;\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .info-card {\n          background-color: #fff;\n          border-radius: 8px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          border: 1px solid #e9ecef;\n          transition: transform 0.2s ease-in-out;\n        }\n\n        .info-card:hover {\n          transform: translateY(-2px);\n        }\n\n        .info-icon {\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 50%;\n          font-size: 20px;\n          background-color: #f8f9fa;\n          margin-right: 16px;\n          flex-shrink: 0;\n        }\n\n        .lightbulb {\n          background-color: #fff3cd;\n          color: #856404;\n        }\n\n        .tips {\n          background-color: #d1ecf1;\n          color: #0c5460;\n        }\n\n        .info-content {\n          flex: 1;\n        }\n\n        .info-title {\n          font-size: 16px;\n          font-weight: 600;\n          margin-bottom: 0;\n          color: #333;\n        }\n\n        .info-list {\n          padding-left: 20px;\n          margin-bottom: 0;\n        }\n\n        .info-list li {\n          margin-bottom: 8px;\n          color: #666;\n        }\n\n        .info-text {\n          font-size: 14px;\n          color: #666;\n          margin-bottom: 0;\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .main-heading {\n            font-size: 24px;\n          }\n          \n          .facility-form-card {\n            padding: 16px;\n          }\n          \n          .upload-area {\n            padding: 20px 16px;\n          }\n          \n          .navigation-buttons {\n            flex-direction: column;\n          }\n          \n          .back-button {\n            width: 100%;\n            order: 2;\n          }\n          \n          .continue-button {\n            order: 1;\n            margin-bottom: 12px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 5\n  }, this);\n}\n_s(BookingPropertyDescription, \"hDrxnnWgKvyuNodpzegBZjrOzLw=\", false, function () {\n  return [useNavigate, useAppDispatch, useAppSelector];\n});\n_c = BookingPropertyDescription;\nvar _c;\n$RefreshReg$(_c, \"BookingPropertyDescription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "Container", "<PERSON><PERSON>", "Form", "Card", "ProgressBar", "Row", "Col", "<PERSON><PERSON>", "Spinner", "ArrowLeft", "Upload", "X", "Star", "useNavigate", "useAppDispatch", "useAppSelector", "showToast", "ToastProvider", "HotelActions", "Routers", "Factories", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "starOptions", "value", "label", "BookingPropertyDescription", "_s", "navigate", "dispatch", "createHotel", "state", "Hotel", "console", "log", "star", "setStar", "description", "setDescription", "images", "setImages", "localImages", "setLocalImages", "dragActive", "setDragActive", "validationErrors", "setValidationErrors", "isUploading", "setIsUploading", "validateForm", "errors", "trim", "push", "length", "totalImages", "handleImageChange", "event", "files", "Array", "from", "target", "handleFiles", "validTypes", "invalidFiles", "filter", "file", "includes", "type", "error", "oversizedFiles", "size", "warning", "filesWithPreview", "map", "preview", "URL", "createObjectURL", "name", "isLocal", "prev", "removeLocalImage", "index", "imageToRemove", "revokeObjectURL", "_", "i", "removeUploadedImage", "response", "deleteHotelImages", "public_ID", "data", "success", "_response$data", "Error", "message", "uploadImages", "formData", "FormData", "for<PERSON>ach", "imgObj", "append", "uploadHotelImages", "img", "_response$data2", "handleDrag", "e", "preventDefault", "stopPropagation", "handleDrop", "dataTransfer", "handleContinue", "uploadedImages", "allImages", "SAVE_HOTEL_DESCRIPTION_CREATE", "payload", "checkCreateHotel", "BookingPropertyChecklist", "handleBack", "BookingPropertyCheckInOut", "isFormValid", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundColor", "Brand", "href", "fontSize", "color", "height", "variant", "now", "md", "Heading", "animation", "Group", "Label", "Select", "onChange", "Number", "option", "fill", "Control", "as", "rows", "placeholder", "isInvalid", "some", "Text", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "multiple", "accept", "disabled", "id", "htmlFor", "src", "url", "alt", "onClick", "title", "Body", "role", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/create_hotel/BookingPropertyDescription.jsx"], "sourcesContent": ["import React from \"react\"\r\nimport { useState, useEffect } from \"react\"\r\nimport { <PERSON>v<PERSON>, Container, Button, Form, Card, ProgressBar, Row, Col, <PERSON><PERSON>, Spin<PERSON> } from \"react-bootstrap\"\r\nimport \"bootstrap/dist/css/bootstrap.min.css\"\r\nimport { ArrowLeft, Upload, X, Star } from \"lucide-react\"\r\nimport { useNavigate } from \"react-router-dom\"\r\nimport { useAppDispatch, useAppSelector } from \"@redux/store\"\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\"\r\nimport HotelActions from \"@redux/hotel/actions\"\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport Factories from \"@redux/hotel/factories\"; // Import factories\r\n\r\n// Star rating options\r\nconst starOptions = [\r\n  { value: 1, label: \"1 sao\" },\r\n  { value: 2, label: \"2 sao\" },\r\n  { value: 3, label: \"3 sao\" },\r\n  { value: 4, label: \"4 sao\" },\r\n  { value: 5, label: \"5 sao\" },\r\n]\r\n\r\nexport default function BookingPropertyDescription() {\r\n  const navigate = useNavigate()\r\n  const dispatch = useAppDispatch()\r\n  const createHotel = useAppSelector((state) => state.Hotel.createHotel)\r\n  console.log(\"createHotel:   \", createHotel);\r\n  \r\n  // Initialize state with values from Redux store\r\n  const [star, setStar] = useState(createHotel?.star || 1)\r\n  const [description, setDescription] = useState(createHotel?.description || \"\")\r\n  const [images, setImages] = useState(createHotel?.images || [])\r\n  const [localImages, setLocalImages] = useState([]) // For local file preview\r\n  \r\n  const [dragActive, setDragActive] = useState(false)\r\n  const [validationErrors, setValidationErrors] = useState([])\r\n  const [isUploading, setIsUploading] = useState(false)\r\n\r\n  // Validate form\r\n  const validateForm = () => {\r\n    const errors = []\r\n\r\n    if (!description.trim()) {\r\n      errors.push(\"Vui lòng nhập mô tả về khách sạn\")\r\n    } else if (description.trim().length < 50) {\r\n      errors.push(\"Mô tả khách sạn phải có ít nhất 50 ký tự\")\r\n    }\r\n\r\n    const totalImages = images.length + localImages.length\r\n    if (totalImages < 5) {\r\n      errors.push(`Vui lòng upload đủ 5 hình ảnh (hiện tại: ${totalImages}/5)`)\r\n    }\r\n\r\n    return errors\r\n  }\r\n\r\n  // Validate whenever form data changes\r\n  useEffect(() => {\r\n    const errors = validateForm()\r\n    setValidationErrors(errors)\r\n  }, [star, description, images, localImages])\r\n\r\n  const handleImageChange = (event) => {\r\n    const files = Array.from(event.target.files || [])\r\n    handleFiles(files)\r\n  }\r\n\r\n  const handleFiles = (files) => {\r\n    // Validate file types\r\n    const validTypes = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/webp\"]\r\n    const invalidFiles = files.filter((file) => !validTypes.includes(file.type))\r\n\r\n    if (invalidFiles.length > 0) {\r\n      showToast.error(\"Chỉ chấp nhận file ảnh định dạng JPG, PNG, WEBP\")\r\n      return\r\n    }\r\n\r\n    // Validate file sizes (max 5MB each)\r\n    const oversizedFiles = files.filter((file) => file.size > 5 * 1024 * 1024)\r\n    if (oversizedFiles.length > 0) {\r\n      showToast.error(\"Kích thước file không được vượt quá 5MB\")\r\n      return\r\n    }\r\n\r\n    // Check total number of images\r\n    const totalImages = images.length + localImages.length + files.length\r\n    if (totalImages > 5) {\r\n      showToast.warning(\"Chỉ được upload tối đa 5 hình ảnh\")\r\n      return\r\n    }\r\n\r\n    // Create preview objects for local files\r\n    const filesWithPreview = files.map(file => ({\r\n      file: file,\r\n      preview: URL.createObjectURL(file),\r\n      name: file.name,\r\n      size: file.size,\r\n      isLocal: true\r\n    }))\r\n\r\n    setLocalImages(prev => [...prev, ...filesWithPreview])\r\n  }\r\n\r\n  const removeLocalImage = (index) => {\r\n    const imageToRemove = localImages[index]\r\n    if (imageToRemove && imageToRemove.preview) {\r\n      URL.revokeObjectURL(imageToRemove.preview)\r\n    }\r\n    setLocalImages(localImages.filter((_, i) => i !== index))\r\n  }\r\n\r\n  const removeUploadedImage = async (index) => {\r\n    const imageToRemove = images[index]\r\n    \r\n    try {\r\n      // Call API to delete from Cloudinary\r\n      const response = await Factories.deleteHotelImages([imageToRemove.public_ID])\r\n      \r\n      if (response.data && !response.data.error) {\r\n        // Remove from local state\r\n        setImages(images.filter((_, i) => i !== index))\r\n        showToast.success(\"Đã xóa ảnh thành công!\")\r\n      } else {\r\n        throw new Error(response.data?.message || \"Không thể xóa ảnh\")\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting image:', error)\r\n      showToast.error(\"Có lỗi xảy ra khi xóa ảnh: \" + error.message)\r\n    }\r\n  }\r\n\r\n  const uploadImages = async () => {\r\n    if (localImages.length === 0) return []\r\n\r\n    try {\r\n      setIsUploading(true)\r\n      \r\n      const formData = new FormData()\r\n      localImages.forEach((imgObj) => {\r\n        formData.append('images', imgObj.file)\r\n      })\r\n\r\n      const response = await Factories.uploadHotelImages(formData)\r\n      \r\n      if (response.data && !response.data.error) {\r\n        // Cleanup local previews\r\n        localImages.forEach(img => {\r\n          if (img.preview) {\r\n            URL.revokeObjectURL(img.preview)\r\n          }\r\n        })\r\n    \r\n        showToast.success(\"Upload ảnh thành công!\")\r\n        return response.data.data.images\r\n      } else {\r\n        throw new Error(response.data?.message || \"Upload failed\")\r\n      }\r\n    } catch (error) {\r\n      console.error('Error uploading images:', error)\r\n      showToast.error(\"Có lỗi xảy ra khi upload ảnh: \" + error.message)\r\n      return []\r\n    } finally {\r\n      setIsUploading(false)\r\n    }\r\n  }\r\n\r\n  const handleDrag = (e) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    if (e.type === \"dragenter\" || e.type === \"dragover\") {\r\n      setDragActive(true)\r\n    } else if (e.type === \"dragleave\") {\r\n      setDragActive(false)\r\n    }\r\n  }\r\n\r\n  const handleDrop = (e) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    setDragActive(false)\r\n\r\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\r\n      const files = Array.from(e.dataTransfer.files)\r\n      handleFiles(files)\r\n    }\r\n  }\r\n\r\n  const handleContinue = async () => {\r\n    const errors = validateForm()\r\n\r\n    if (errors.length > 0) {\r\n      showToast.error(\"Vui lòng kiểm tra lại thông tin đã nhập\")\r\n      return\r\n    }\r\n\r\n    try {\r\n      let uploadedImages = []\r\n      \r\n      // Upload new images if any\r\n      if (localImages.length > 0) {\r\n        uploadedImages = await uploadImages()\r\n        if (uploadedImages.length === 0) {\r\n          return // Upload failed\r\n        }\r\n      }\r\n\r\n      // Combine existing and new images\r\n      const allImages = [...images, ...uploadedImages]\r\n      \r\n      // Clear local images after successful upload\r\n      setLocalImages([])\r\n\r\n      // Dispatch action to save data\r\n      dispatch({\r\n        type: HotelActions.SAVE_HOTEL_DESCRIPTION_CREATE,\r\n        payload: {\r\n          star,\r\n          description: description.trim(),\r\n          images: allImages,\r\n          checkCreateHotel: true,\r\n        },\r\n      })\r\n\r\n      showToast.success(\"Đã lưu thông tin mô tả khách sạn thành công!\")\r\n      navigate(Routers.BookingPropertyChecklist)\r\n      \r\n    } catch (error) {\r\n      console.error('Error in handleContinue:', error)\r\n      showToast.error(\"Có lỗi xảy ra: \" + error.message)\r\n    }\r\n  }\r\n\r\n  const handleBack = () => {\r\n    // Save current data before going back\r\n    dispatch({\r\n      type: HotelActions.SAVE_HOTEL_DESCRIPTION_CREATE,\r\n      payload: {\r\n        star,\r\n        description: description.trim(),\r\n        images: images, // Only save uploaded images, not local previews\r\n      },\r\n    })\r\n\r\n    // Cleanup local images\r\n    localImages.forEach(img => {\r\n      if (img.preview) {\r\n        URL.revokeObjectURL(img.preview)\r\n      }\r\n    })\r\n\r\n    navigate(Routers.BookingPropertyCheckInOut)\r\n  }\r\n\r\n  // Cleanup URLs when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      localImages.forEach(img => {\r\n        if (img.preview) {\r\n          URL.revokeObjectURL(img.preview)\r\n        }\r\n      })\r\n    }\r\n  }, [])\r\n\r\n  const isFormValid = validationErrors.length === 0\r\n  const totalImages = images.length + localImages.length\r\n\r\n  return (\r\n    <div className=\"booking-app\">\r\n      <ToastProvider/>\r\n\r\n      {/* Navigation Bar */}\r\n      <Navbar style={{ backgroundColor: \"#003580\" }}>\r\n        <Container>\r\n          <Navbar.Brand href=\"#home\" className=\"text-white fw-bold\">\r\n            <b style={{ fontSize: 30 }}>\r\n              UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n            </b>\r\n          </Navbar.Brand>\r\n        </Container>\r\n      </Navbar>\r\n\r\n      {/* Progress Bar */}\r\n      <Container className=\"mt-4\">\r\n        <div className=\"progress-section\">\r\n          <div className=\"progress-label mb-2\">\r\n            <h5>Thông tin cơ bản</h5>\r\n          </div>\r\n          <ProgressBar style={{ height: \"20px\" }}>\r\n            <ProgressBar variant=\"primary\" now={20} key={1} />\r\n            <ProgressBar variant=\"primary\" now={20} key={2} />\r\n            <ProgressBar variant=\"primary\" now={20} key={3} />\r\n            <ProgressBar variant=\"primary\" now={20} key={4} />\r\n            <ProgressBar variant=\"primary\" now={20} key={5} />\r\n          </ProgressBar>\r\n        </div>\r\n      </Container>\r\n\r\n      {/* Main Content */}\r\n      <Container className=\"main-content py-4\">\r\n        <Row>\r\n          <Col md={7}>\r\n            <div className=\"mb-4\">\r\n              <h1 className=\"main-heading\">Tiêu chuẩn và mô tả về khách sạn</h1>\r\n            </div>\r\n\r\n            {/* Validation Errors */}\r\n            {validationErrors.length > 0 && (\r\n              <Alert variant=\"danger\" className=\"mb-4\">\r\n                <Alert.Heading>Vui lòng kiểm tra lại:</Alert.Heading>\r\n                <ul className=\"mb-0\">\r\n                  {validationErrors.map((error, index) => (\r\n                    <li key={index}>{error}</li>\r\n                  ))}\r\n                </ul>\r\n              </Alert>\r\n            )}\r\n\r\n            {/* Loading Indicator */}\r\n            {isUploading && (\r\n              <Alert variant=\"info\" className=\"mb-4\">\r\n                <div className=\"d-flex align-items-center\">\r\n                  <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                  Đang upload ảnh...\r\n                </div>\r\n              </Alert>\r\n            )}\r\n\r\n            {/* Description Form */}\r\n            <div className=\"facility-form-card\">\r\n              <Form>\r\n                {/* Star Rating */}\r\n                <Row className=\"mb-4\">\r\n                  <Col md={6}>\r\n                    <Form.Group>\r\n                      <Form.Label className=\"fw-bold\">\r\n                        <Star className=\"me-2\" size={18} />\r\n                        Tiêu chuẩn khách sạn <span className=\"text-danger\">*</span>\r\n                      </Form.Label>\r\n                      <Form.Select\r\n                        className=\"form-input\"\r\n                        value={star}\r\n                        onChange={(e) => setStar(Number(e.target.value))}\r\n                      >\r\n                        {starOptions.map((option) => (\r\n                          <option key={option.value} value={option.value}>\r\n                            {option.label}\r\n                          </option>\r\n                        ))}\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </Col>\r\n                  <Col md={6} className=\"d-flex align-items-end\">\r\n                    <div className=\"star-display\">\r\n                      {[...Array(5)].map((_, index) => (\r\n                        <Star\r\n                          key={index}\r\n                          size={24}\r\n                          className={index < star ? \"star-filled\" : \"star-empty\"}\r\n                          fill={index < star ? \"#ffc107\" : \"none\"}\r\n                          color={index < star ? \"#ffc107\" : \"#dee2e6\"}\r\n                        />\r\n                      ))}\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                {/* Description */}\r\n                <Row className=\"mb-4\">\r\n                  <Col md={12}>\r\n                    <Form.Group>\r\n                      <Form.Label className=\"fw-bold\">\r\n                        Mô tả về khách sạn <span className=\"text-danger\">*</span>\r\n                      </Form.Label>\r\n                      <Form.Control\r\n                        as=\"textarea\"\r\n                        rows={4}\r\n                        placeholder=\"Nhập mô tả chi tiết về khách sạn của bạn... (tối thiểu 50 ký tự)\"\r\n                        className=\"form-input\"\r\n                        value={description}\r\n                        onChange={(e) => setDescription(e.target.value)}\r\n                        isInvalid={validationErrors.some((error) => error.includes(\"mô tả\"))}\r\n                      />\r\n                      <Form.Text className=\"text-muted\">{description.length}/50 ký tự tối thiểu</Form.Text>\r\n                    </Form.Group>\r\n                  </Col>\r\n                </Row>\r\n\r\n                {/* Image Upload */}\r\n                <Row className=\"mb-4\">\r\n                  <Col md={12}>\r\n                    <Form.Group>\r\n                      <Form.Label className=\"fw-bold\">\r\n                        Hình ảnh khách sạn <span className=\"text-danger\">*</span>\r\n                        <span className=\"text-muted ms-2\">({totalImages}/5 ảnh)</span>\r\n                      </Form.Label>\r\n\r\n                      {/* Upload Area */}\r\n                      <div\r\n                        className={`upload-area ${dragActive ? \"drag-active\" : \"\"} ${\r\n                          totalImages >= 5 ? \"disabled\" : \"\"\r\n                        }`}\r\n                        onDragEnter={handleDrag}\r\n                        onDragLeave={handleDrag}\r\n                        onDragOver={handleDrag}\r\n                        onDrop={handleDrop}\r\n                      >\r\n                        <div className=\"upload-content\">\r\n                          <Upload size={48} className=\"upload-icon\" />\r\n                          <h5>Kéo thả ảnh vào đây hoặc</h5>\r\n                          <Form.Control\r\n                            type=\"file\"\r\n                            multiple\r\n                            accept=\"image/*\"\r\n                            onChange={handleImageChange}\r\n                            disabled={totalImages >= 5 || isUploading}\r\n                            className=\"d-none\"\r\n                            id=\"image-upload\"\r\n                          />\r\n                          <Button\r\n                            as=\"label\"\r\n                            htmlFor=\"image-upload\"\r\n                            variant=\"outline-primary\"\r\n                            disabled={totalImages >= 5 || isUploading}\r\n                          >\r\n                            {isUploading ? (\r\n                              <>\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                Đang upload...\r\n                              </>\r\n                            ) : (\r\n                              \"Chọn ảnh\"\r\n                            )}\r\n                          </Button>\r\n                          <p className=\"upload-note\">Chấp nhận JPG, PNG, WEBP. Tối đa 5MB mỗi ảnh.</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Image Preview */}\r\n                      {(images.length > 0 || localImages.length > 0) && (\r\n                        <div className=\"image-preview mt-3\">\r\n                          <Row>\r\n                            {/* Uploaded Images */}\r\n                            {images.map((img, index) => (\r\n                              <Col md={4} key={`uploaded-${index}`} className=\"mb-3\">\r\n                                <div className=\"preview-container\">\r\n                                  <img\r\n                                    src={img.url || \"/placeholder.svg\"}\r\n                                    alt={`Uploaded ${index + 1}`}\r\n                                    className=\"preview-image\"\r\n                                  />\r\n                                  <Button\r\n                                    variant=\"danger\"\r\n                                    size=\"sm\"\r\n                                    className=\"remove-button\"\r\n                                    onClick={() => removeUploadedImage(index)}\r\n                                    title=\"Xóa ảnh đã upload\"\r\n                                  >\r\n                                    <X size={16} />\r\n                                  </Button>\r\n                                  <div className=\"image-info\">\r\n                                    <small>Đã upload</small>\r\n                                    <br />\r\n                                    <small className=\"text-success\">✓ Lưu trên cloud</small>\r\n                                  </div>\r\n                                </div>\r\n                              </Col>\r\n                            ))}\r\n                            \r\n                            {/* Local Preview Images */}\r\n                            {localImages.map((imgObj, index) => (\r\n                              <Col md={4} key={`local-${index}`} className=\"mb-3\">\r\n                                <div className=\"preview-container\">\r\n                                  <img\r\n                                    src={imgObj.preview}\r\n                                    alt={`Preview ${index + 1}`}\r\n                                    className=\"preview-image\"\r\n                                  />\r\n                                  <Button\r\n                                    variant=\"danger\"\r\n                                    size=\"sm\"\r\n                                    className=\"remove-button\"\r\n                                    onClick={() => removeLocalImage(index)}\r\n                                    title=\"Xóa ảnh chưa upload\"\r\n                                  >\r\n                                    <X size={16} />\r\n                                  </Button>\r\n                                  <div className=\"image-info\">\r\n                                    <small>{imgObj.name}</small>\r\n                                    <br />\r\n                                    <small className=\"text-warning\">Chưa upload</small>\r\n                                  </div>\r\n                                </div>\r\n                              </Col>\r\n                            ))}\r\n                          </Row>\r\n                        </div>\r\n                      )}\r\n                    </Form.Group>\r\n                  </Col>\r\n                </Row>\r\n\r\n                {/* Form Summary */}\r\n                {isFormValid && (\r\n                  <Alert variant=\"success\" className=\"mt-4\">\r\n                    <div className=\"form-summary\">\r\n                      <h6>Tóm tắt thông tin:</h6>\r\n                      <p className=\"mb-1\">\r\n                        <strong>Tiêu chuẩn:</strong> {star} sao\r\n                      </p>\r\n                      <p className=\"mb-1\">\r\n                        <strong>Mô tả:</strong> {description.length} ký tự\r\n                      </p>\r\n                      <p className=\"mb-0\">\r\n                        <strong>Hình ảnh:</strong> {totalImages}/5 ảnh \r\n                        {localImages.length > 0 && (\r\n                          <span className=\"text-warning ms-2\">({localImages.length} ảnh chưa upload)</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                  </Alert>\r\n                )}\r\n              </Form>\r\n            </div>\r\n\r\n            <div className=\"navigation-buttons mt-4\">\r\n              <Button \r\n                variant=\"outline-primary\" \r\n                className=\"back-button\" \r\n                onClick={handleBack} \r\n                title=\"Quay lại\"\r\n                disabled={isUploading}\r\n              >\r\n                <ArrowLeft size={20} />\r\n              </Button>\r\n              <Button \r\n                variant=\"primary\" \r\n                className=\"continue-button\" \r\n                onClick={handleContinue} \r\n                disabled={!isFormValid || isUploading}\r\n              >\r\n                {isUploading ? (\r\n                  <>\r\n                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                    Đang xử lý...\r\n                  </>\r\n                ) : (\r\n                  \"Tiếp tục\"\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n\r\n          <Col md={5}>\r\n            <div className=\"info-cards\">\r\n              {/* Info Card */}\r\n              <Card className=\"info-card mb-4\">\r\n                <Card.Body>\r\n                  <div className=\"d-flex align-items-start\">\r\n                    <div className=\"info-icon lightbulb\">\r\n                      <span role=\"img\" aria-label=\"lightbulb\">\r\n                        💡\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"info-content\">\r\n                      <h5 className=\"info-title\">Những tiêu chuẩn của khách sạn của mình?</h5>\r\n                      <p className=\"info-text mt-3\">\r\n                        Quý vị có thể dễ dàng tùy chỉnh các quy tắc chung này sau và các quy tắc chung bổ sung có thể\r\n                        được cài đặt trong trang Chính sách trên extranet sau khi hoàn tất đăng ký.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n\r\n              {/* Tips Card */}\r\n              <Card className=\"info-card\">\r\n                <Card.Body>\r\n                  <div className=\"d-flex align-items-start\">\r\n                    <div className=\"info-icon tips\">\r\n                      <span role=\"img\" aria-label=\"tips\">\r\n                        📝\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"info-content\">\r\n                      <h5 className=\"info-title\">Mẹo upload ảnh hiệu quả</h5>\r\n                      <ul className=\"info-list mt-3\">\r\n                        <li>Ảnh có thể preview trước khi upload</li>\r\n                        <li>Ảnh màu xanh đã lưu trên cloud an toàn</li>\r\n                        <li>Ảnh màu vàng chưa upload, cần tiếp tục để lưu</li>\r\n                        <li>Có thể xóa ảnh bất kỳ lúc nào</li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n\r\n      {/* Existing styles remain the same */}\r\n      <style jsx>{`\r\n        .booking-app {\r\n          min-height: 100vh;\r\n          background-color: #f8f9fa;\r\n        }\r\n\r\n        .progress-section {\r\n          margin: 0 auto;\r\n        }\r\n\r\n        .progress-label {\r\n          font-size: 14px;\r\n          color: #333;\r\n        }\r\n\r\n        .main-content {\r\n          max-width: 1200px;\r\n          margin: 0 auto;\r\n        }\r\n\r\n        .main-heading {\r\n          font-size: 28px;\r\n          font-weight: bold;\r\n          color: #333;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .facility-form-card {\r\n          background-color: #fff;\r\n          border-radius: 8px;\r\n          padding: 24px;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n          border: 1px solid #e9ecef;\r\n        }\r\n\r\n        .form-input {\r\n          border: 1px solid #ced4da;\r\n          border-radius: 6px;\r\n          font-size: 16px;\r\n          transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\r\n        }\r\n\r\n        .form-input:focus {\r\n          border-color: #0071c2;\r\n          box-shadow: 0 0 0 0.2rem rgba(0, 113, 194, 0.25);\r\n        }\r\n\r\n        .form-input.is-invalid {\r\n          border-color: #dc3545;\r\n        }\r\n\r\n        .star-display {\r\n          display: flex;\r\n          gap: 4px;\r\n          align-items: center;\r\n        }\r\n\r\n        .star-filled {\r\n          color: #ffc107;\r\n        }\r\n\r\n        .star-empty {\r\n          color: #dee2e6;\r\n        }\r\n\r\n        .upload-area {\r\n          border: 2px dashed #ced4da;\r\n          border-radius: 8px;\r\n          padding: 40px 20px;\r\n          text-align: center;\r\n          background-color: #f8f9fa;\r\n          transition: all 0.3s ease;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .upload-area:hover:not(.disabled) {\r\n          border-color: #0071c2;\r\n          background-color: #f0f8ff;\r\n        }\r\n\r\n        .upload-area.drag-active {\r\n          border-color: #0071c2;\r\n          background-color: #e3f2fd;\r\n        }\r\n\r\n        .upload-area.disabled {\r\n          opacity: 0.6;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .upload-content {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          gap: 12px;\r\n        }\r\n\r\n        .upload-icon {\r\n          color: #6c757d;\r\n        }\r\n\r\n        .upload-note {\r\n          font-size: 12px;\r\n          color: #6c757d;\r\n          margin: 0;\r\n        }\r\n\r\n        .preview-container {\r\n          position: relative;\r\n          border-radius: 8px;\r\n          overflow: hidden;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n        }\r\n\r\n        .preview-image {\r\n          width: 100%;\r\n          height: 200px;\r\n          object-fit: cover;\r\n          display: block;\r\n        }\r\n\r\n        .remove-button {\r\n          position: absolute;\r\n          top: 8px;\r\n          right: 8px;\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          padding: 0;\r\n        }\r\n\r\n        .image-info {\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          right: 0;\r\n          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\r\n          color: white;\r\n          padding: 8px;\r\n          font-size: 12px;\r\n        }\r\n\r\n        .form-summary h6 {\r\n          color: #155724;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .navigation-buttons {\r\n          display: flex;\r\n          gap: 12px;\r\n        }\r\n\r\n        .back-button {\r\n          width: 50px;\r\n          height: 45px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-color: #0071c2;\r\n          color: #0071c2;\r\n          border-radius: 6px;\r\n        }\r\n\r\n        .back-button:hover {\r\n          background-color: #0071c2;\r\n          color: white;\r\n        }\r\n\r\n        .continue-button {\r\n          flex: 1;\r\n          height: 45px;\r\n          background-color: #0071c2;\r\n          border: none;\r\n          font-weight: 600;\r\n          border-radius: 6px;\r\n          transition: background-color 0.15s ease-in-out;\r\n        }\r\n\r\n        .continue-button:hover:not(:disabled) {\r\n          background-color: #005999;\r\n        }\r\n\r\n        .continue-button:disabled {\r\n          background-color: #6c757d;\r\n          border-color: #6c757d;\r\n          opacity: 0.6;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .info-card {\r\n          background-color: #fff;\r\n          border-radius: 8px;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n          border: 1px solid #e9ecef;\r\n          transition: transform 0.2s ease-in-out;\r\n        }\r\n\r\n        .info-card:hover {\r\n          transform: translateY(-2px);\r\n        }\r\n\r\n        .info-icon {\r\n          width: 40px;\r\n          height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 50%;\r\n          font-size: 20px;\r\n          background-color: #f8f9fa;\r\n          margin-right: 16px;\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .lightbulb {\r\n          background-color: #fff3cd;\r\n          color: #856404;\r\n        }\r\n\r\n        .tips {\r\n          background-color: #d1ecf1;\r\n          color: #0c5460;\r\n        }\r\n\r\n        .info-content {\r\n          flex: 1;\r\n        }\r\n\r\n        .info-title {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          margin-bottom: 0;\r\n          color: #333;\r\n        }\r\n\r\n        .info-list {\r\n          padding-left: 20px;\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .info-list li {\r\n          margin-bottom: 8px;\r\n          color: #666;\r\n        }\r\n\r\n        .info-text {\r\n          font-size: 14px;\r\n          color: #666;\r\n          margin-bottom: 0;\r\n          line-height: 1.6;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .main-heading {\r\n            font-size: 24px;\r\n          }\r\n          \r\n          .facility-form-card {\r\n            padding: 16px;\r\n          }\r\n          \r\n          .upload-area {\r\n            padding: 20px 16px;\r\n          }\r\n          \r\n          .navigation-buttons {\r\n            flex-direction: column;\r\n          }\r\n          \r\n          .back-button {\r\n            width: 100%;\r\n            order: 2;\r\n          }\r\n          \r\n          .continue-button {\r\n            order: 1;\r\n            margin-bottom: 12px;\r\n          }\r\n        }\r\n      `}</style>\r\n    </div>\r\n  )\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC9G,OAAO,sCAAsC;AAC7C,SAASC,SAAS,EAAEC,MAAM,EAAEC,CAAC,EAAEC,IAAI,QAAQ,cAAc;AACzD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,SAAS,MAAM,wBAAwB,CAAC,CAAC;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,WAAW,GAAG,CAClB;EAAEC,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC5B;EAAED,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC5B;EAAED,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC5B;EAAED,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC5B;EAAED,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAQ,CAAC,CAC7B;AAED,eAAe,SAASC,0BAA0BA,CAAA,EAAG;EAAAC,EAAA;EACnD,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGjB,cAAc,CAAC,CAAC;EACjC,MAAMkB,WAAW,GAAGjB,cAAc,CAAEkB,KAAK,IAAKA,KAAK,CAACC,KAAK,CAACF,WAAW,CAAC;EACtEG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,WAAW,CAAC;;EAE3C;EACA,MAAM,CAACK,IAAI,EAAEC,OAAO,CAAC,GAAGzC,QAAQ,CAAC,CAAAmC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,IAAI,KAAI,CAAC,CAAC;EACxD,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,CAAAmC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEO,WAAW,KAAI,EAAE,CAAC;EAC9E,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,CAAAmC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAES,MAAM,KAAI,EAAE,CAAC;EAC/D,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC,EAAC;;EAEnD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMsD,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,EAAE;IAEjB,IAAI,CAACb,WAAW,CAACc,IAAI,CAAC,CAAC,EAAE;MACvBD,MAAM,CAACE,IAAI,CAAC,kCAAkC,CAAC;IACjD,CAAC,MAAM,IAAIf,WAAW,CAACc,IAAI,CAAC,CAAC,CAACE,MAAM,GAAG,EAAE,EAAE;MACzCH,MAAM,CAACE,IAAI,CAAC,0CAA0C,CAAC;IACzD;IAEA,MAAME,WAAW,GAAGf,MAAM,CAACc,MAAM,GAAGZ,WAAW,CAACY,MAAM;IACtD,IAAIC,WAAW,GAAG,CAAC,EAAE;MACnBJ,MAAM,CAACE,IAAI,CAAC,4CAA4CE,WAAW,KAAK,CAAC;IAC3E;IAEA,OAAOJ,MAAM;EACf,CAAC;;EAED;EACAtD,SAAS,CAAC,MAAM;IACd,MAAMsD,MAAM,GAAGD,YAAY,CAAC,CAAC;IAC7BH,mBAAmB,CAACI,MAAM,CAAC;EAC7B,CAAC,EAAE,CAACf,IAAI,EAAEE,WAAW,EAAEE,MAAM,EAAEE,WAAW,CAAC,CAAC;EAE5C,MAAMc,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACH,KAAK,IAAI,EAAE,CAAC;IAClDI,WAAW,CAACJ,KAAK,CAAC;EACpB,CAAC;EAED,MAAMI,WAAW,GAAIJ,KAAK,IAAK;IAC7B;IACA,MAAMK,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IACzE,MAAMC,YAAY,GAAGN,KAAK,CAACO,MAAM,CAAEC,IAAI,IAAK,CAACH,UAAU,CAACI,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC;IAE5E,IAAIJ,YAAY,CAACV,MAAM,GAAG,CAAC,EAAE;MAC3BvC,SAAS,CAACsD,KAAK,CAAC,iDAAiD,CAAC;MAClE;IACF;;IAEA;IACA,MAAMC,cAAc,GAAGZ,KAAK,CAACO,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1E,IAAID,cAAc,CAAChB,MAAM,GAAG,CAAC,EAAE;MAC7BvC,SAAS,CAACsD,KAAK,CAAC,yCAAyC,CAAC;MAC1D;IACF;;IAEA;IACA,MAAMd,WAAW,GAAGf,MAAM,CAACc,MAAM,GAAGZ,WAAW,CAACY,MAAM,GAAGI,KAAK,CAACJ,MAAM;IACrE,IAAIC,WAAW,GAAG,CAAC,EAAE;MACnBxC,SAAS,CAACyD,OAAO,CAAC,mCAAmC,CAAC;MACtD;IACF;;IAEA;IACA,MAAMC,gBAAgB,GAAGf,KAAK,CAACgB,GAAG,CAACR,IAAI,KAAK;MAC1CA,IAAI,EAAEA,IAAI;MACVS,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACX,IAAI,CAAC;MAClCY,IAAI,EAAEZ,IAAI,CAACY,IAAI;MACfP,IAAI,EAAEL,IAAI,CAACK,IAAI;MACfQ,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;IAEHpC,cAAc,CAACqC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGP,gBAAgB,CAAC,CAAC;EACxD,CAAC;EAED,MAAMQ,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,aAAa,GAAGzC,WAAW,CAACwC,KAAK,CAAC;IACxC,IAAIC,aAAa,IAAIA,aAAa,CAACR,OAAO,EAAE;MAC1CC,GAAG,CAACQ,eAAe,CAACD,aAAa,CAACR,OAAO,CAAC;IAC5C;IACAhC,cAAc,CAACD,WAAW,CAACuB,MAAM,CAAC,CAACoB,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMK,mBAAmB,GAAG,MAAOL,KAAK,IAAK;IAC3C,MAAMC,aAAa,GAAG3C,MAAM,CAAC0C,KAAK,CAAC;IAEnC,IAAI;MACF;MACA,MAAMM,QAAQ,GAAG,MAAMrE,SAAS,CAACsE,iBAAiB,CAAC,CAACN,aAAa,CAACO,SAAS,CAAC,CAAC;MAE7E,IAAIF,QAAQ,CAACG,IAAI,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACtB,KAAK,EAAE;QACzC;QACA5B,SAAS,CAACD,MAAM,CAACyB,MAAM,CAAC,CAACoB,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC,CAAC;QAC/CnE,SAAS,CAAC6E,OAAO,CAAC,wBAAwB,CAAC;MAC7C,CAAC,MAAM;QAAA,IAAAC,cAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,cAAA,GAAAL,QAAQ,CAACG,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAeE,OAAO,KAAI,mBAAmB,CAAC;MAChE;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CtD,SAAS,CAACsD,KAAK,CAAC,6BAA6B,GAAGA,KAAK,CAAC0B,OAAO,CAAC;IAChE;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAItD,WAAW,CAACY,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEvC,IAAI;MACFL,cAAc,CAAC,IAAI,CAAC;MAEpB,MAAMgD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BxD,WAAW,CAACyD,OAAO,CAAEC,MAAM,IAAK;QAC9BH,QAAQ,CAACI,MAAM,CAAC,QAAQ,EAAED,MAAM,CAAClC,IAAI,CAAC;MACxC,CAAC,CAAC;MAEF,MAAMsB,QAAQ,GAAG,MAAMrE,SAAS,CAACmF,iBAAiB,CAACL,QAAQ,CAAC;MAE5D,IAAIT,QAAQ,CAACG,IAAI,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACtB,KAAK,EAAE;QACzC;QACA3B,WAAW,CAACyD,OAAO,CAACI,GAAG,IAAI;UACzB,IAAIA,GAAG,CAAC5B,OAAO,EAAE;YACfC,GAAG,CAACQ,eAAe,CAACmB,GAAG,CAAC5B,OAAO,CAAC;UAClC;QACF,CAAC,CAAC;QAEF5D,SAAS,CAAC6E,OAAO,CAAC,wBAAwB,CAAC;QAC3C,OAAOJ,QAAQ,CAACG,IAAI,CAACA,IAAI,CAACnD,MAAM;MAClC,CAAC,MAAM;QAAA,IAAAgE,eAAA;QACL,MAAM,IAAIV,KAAK,CAAC,EAAAU,eAAA,GAAAhB,QAAQ,CAACG,IAAI,cAAAa,eAAA,uBAAbA,eAAA,CAAeT,OAAO,KAAI,eAAe,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CtD,SAAS,CAACsD,KAAK,CAAC,gCAAgC,GAAGA,KAAK,CAAC0B,OAAO,CAAC;MACjE,OAAO,EAAE;IACX,CAAC,SAAS;MACR9C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMwD,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAACtC,IAAI,KAAK,WAAW,IAAIsC,CAAC,CAACtC,IAAI,KAAK,UAAU,EAAE;MACnDvB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6D,CAAC,CAACtC,IAAI,KAAK,WAAW,EAAE;MACjCvB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMgE,UAAU,GAAIH,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB/D,aAAa,CAAC,KAAK,CAAC;IAEpB,IAAI6D,CAAC,CAACI,YAAY,CAACpD,KAAK,IAAIgD,CAAC,CAACI,YAAY,CAACpD,KAAK,CAAC,CAAC,CAAC,EAAE;MACnD,MAAMA,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC8C,CAAC,CAACI,YAAY,CAACpD,KAAK,CAAC;MAC9CI,WAAW,CAACJ,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMqD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAM5D,MAAM,GAAGD,YAAY,CAAC,CAAC;IAE7B,IAAIC,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;MACrBvC,SAAS,CAACsD,KAAK,CAAC,yCAAyC,CAAC;MAC1D;IACF;IAEA,IAAI;MACF,IAAI2C,cAAc,GAAG,EAAE;;MAEvB;MACA,IAAItE,WAAW,CAACY,MAAM,GAAG,CAAC,EAAE;QAC1B0D,cAAc,GAAG,MAAMhB,YAAY,CAAC,CAAC;QACrC,IAAIgB,cAAc,CAAC1D,MAAM,KAAK,CAAC,EAAE;UAC/B,OAAM,CAAC;QACT;MACF;;MAEA;MACA,MAAM2D,SAAS,GAAG,CAAC,GAAGzE,MAAM,EAAE,GAAGwE,cAAc,CAAC;;MAEhD;MACArE,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAb,QAAQ,CAAC;QACPsC,IAAI,EAAEnD,YAAY,CAACiG,6BAA6B;QAChDC,OAAO,EAAE;UACP/E,IAAI;UACJE,WAAW,EAAEA,WAAW,CAACc,IAAI,CAAC,CAAC;UAC/BZ,MAAM,EAAEyE,SAAS;UACjBG,gBAAgB,EAAE;QACpB;MACF,CAAC,CAAC;MAEFrG,SAAS,CAAC6E,OAAO,CAAC,8CAA8C,CAAC;MACjE/D,QAAQ,CAACX,OAAO,CAACmG,wBAAwB,CAAC;IAE5C,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDtD,SAAS,CAACsD,KAAK,CAAC,iBAAiB,GAAGA,KAAK,CAAC0B,OAAO,CAAC;IACpD;EACF,CAAC;EAED,MAAMuB,UAAU,GAAGA,CAAA,KAAM;IACvB;IACAxF,QAAQ,CAAC;MACPsC,IAAI,EAAEnD,YAAY,CAACiG,6BAA6B;MAChDC,OAAO,EAAE;QACP/E,IAAI;QACJE,WAAW,EAAEA,WAAW,CAACc,IAAI,CAAC,CAAC;QAC/BZ,MAAM,EAAEA,MAAM,CAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACAE,WAAW,CAACyD,OAAO,CAACI,GAAG,IAAI;MACzB,IAAIA,GAAG,CAAC5B,OAAO,EAAE;QACfC,GAAG,CAACQ,eAAe,CAACmB,GAAG,CAAC5B,OAAO,CAAC;MAClC;IACF,CAAC,CAAC;IAEF9C,QAAQ,CAACX,OAAO,CAACqG,yBAAyB,CAAC;EAC7C,CAAC;;EAED;EACA1H,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX6C,WAAW,CAACyD,OAAO,CAACI,GAAG,IAAI;QACzB,IAAIA,GAAG,CAAC5B,OAAO,EAAE;UACfC,GAAG,CAACQ,eAAe,CAACmB,GAAG,CAAC5B,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6C,WAAW,GAAG1E,gBAAgB,CAACQ,MAAM,KAAK,CAAC;EACjD,MAAMC,WAAW,GAAGf,MAAM,CAACc,MAAM,GAAGZ,WAAW,CAACY,MAAM;EAEtD,oBACEjC,OAAA;IAAKoG,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BrG,OAAA,CAACL,aAAa;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eAGhBzG,OAAA,CAACvB,MAAM;MAACiI,KAAK,EAAE;QAAEC,eAAe,EAAE;MAAU,CAAE;MAAAN,QAAA,eAC5CrG,OAAA,CAACtB,SAAS;QAAA2H,QAAA,eACRrG,OAAA,CAACvB,MAAM,CAACmI,KAAK;UAACC,IAAI,EAAC,OAAO;UAACT,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACvDrG,OAAA;YAAG0G,KAAK,EAAE;cAAEI,QAAQ,EAAE;YAAG,CAAE;YAAAT,QAAA,GAAC,IACxB,eAAArG,OAAA;cAAM0G,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAV,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGTzG,OAAA,CAACtB,SAAS;MAAC0H,SAAS,EAAC,MAAM;MAAAC,QAAA,eACzBrG,OAAA;QAAKoG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BrG,OAAA;UAAKoG,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCrG,OAAA;YAAAqG,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNzG,OAAA,CAAClB,WAAW;UAAC4H,KAAK,EAAE;YAAEM,MAAM,EAAE;UAAO,CAAE;UAAAX,QAAA,gBACrCrG,OAAA,CAAClB,WAAW;YAACmI,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzG,OAAA,CAAClB,WAAW;YAACmI,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzG,OAAA,CAAClB,WAAW;YAACmI,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzG,OAAA,CAAClB,WAAW;YAACmI,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzG,OAAA,CAAClB,WAAW;YAACmI,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZzG,OAAA,CAACtB,SAAS;MAAC0H,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACtCrG,OAAA,CAACjB,GAAG;QAAAsH,QAAA,gBACFrG,OAAA,CAAChB,GAAG;UAACmI,EAAE,EAAE,CAAE;UAAAd,QAAA,gBACTrG,OAAA;YAAKoG,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBrG,OAAA;cAAIoG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EAGLhF,gBAAgB,CAACQ,MAAM,GAAG,CAAC,iBAC1BjC,OAAA,CAACf,KAAK;YAACgI,OAAO,EAAC,QAAQ;YAACb,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACtCrG,OAAA,CAACf,KAAK,CAACmI,OAAO;cAAAf,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACrDzG,OAAA;cAAIoG,SAAS,EAAC,MAAM;cAAAC,QAAA,EACjB5E,gBAAgB,CAAC4B,GAAG,CAAC,CAACL,KAAK,EAAEa,KAAK,kBACjC7D,OAAA;gBAAAqG,QAAA,EAAiBrD;cAAK,GAAba,KAAK;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR,EAGA9E,WAAW,iBACV3B,OAAA,CAACf,KAAK;YAACgI,OAAO,EAAC,MAAM;YAACb,SAAS,EAAC,MAAM;YAAAC,QAAA,eACpCrG,OAAA;cAAKoG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCrG,OAAA,CAACd,OAAO;gBAACmI,SAAS,EAAC,QAAQ;gBAACnE,IAAI,EAAC,IAAI;gBAACkD,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAGDzG,OAAA;YAAKoG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCrG,OAAA,CAACpB,IAAI;cAAAyH,QAAA,gBAEHrG,OAAA,CAACjB,GAAG;gBAACqH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBrG,OAAA,CAAChB,GAAG;kBAACmI,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACTrG,OAAA,CAACpB,IAAI,CAAC0I,KAAK;oBAAAjB,QAAA,gBACTrG,OAAA,CAACpB,IAAI,CAAC2I,KAAK;sBAACnB,SAAS,EAAC,SAAS;sBAAAC,QAAA,gBAC7BrG,OAAA,CAACV,IAAI;wBAAC8G,SAAS,EAAC,MAAM;wBAAClD,IAAI,EAAE;sBAAG;wBAAAoD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,yCACd,eAAAzG,OAAA;wBAAMoG,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACbzG,OAAA,CAACpB,IAAI,CAAC4I,MAAM;sBACVpB,SAAS,EAAC,YAAY;sBACtBhG,KAAK,EAAEW,IAAK;sBACZ0G,QAAQ,EAAGpC,CAAC,IAAKrE,OAAO,CAAC0G,MAAM,CAACrC,CAAC,CAAC7C,MAAM,CAACpC,KAAK,CAAC,CAAE;sBAAAiG,QAAA,EAEhDlG,WAAW,CAACkD,GAAG,CAAEsE,MAAM,iBACtB3H,OAAA;wBAA2BI,KAAK,EAAEuH,MAAM,CAACvH,KAAM;wBAAAiG,QAAA,EAC5CsB,MAAM,CAACtH;sBAAK,GADFsH,MAAM,CAACvH,KAAK;wBAAAkG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEjB,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNzG,OAAA,CAAChB,GAAG;kBAACmI,EAAE,EAAE,CAAE;kBAACf,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eAC5CrG,OAAA;oBAAKoG,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC1B,CAAC,GAAG/D,KAAK,CAAC,CAAC,CAAC,CAAC,CAACe,GAAG,CAAC,CAACW,CAAC,EAAEH,KAAK,kBAC1B7D,OAAA,CAACV,IAAI;sBAEH4D,IAAI,EAAE,EAAG;sBACTkD,SAAS,EAAEvC,KAAK,GAAG9C,IAAI,GAAG,aAAa,GAAG,YAAa;sBACvD6G,IAAI,EAAE/D,KAAK,GAAG9C,IAAI,GAAG,SAAS,GAAG,MAAO;sBACxCgG,KAAK,EAAElD,KAAK,GAAG9C,IAAI,GAAG,SAAS,GAAG;oBAAU,GAJvC8C,KAAK;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKX,CACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzG,OAAA,CAACjB,GAAG;gBAACqH,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBrG,OAAA,CAAChB,GAAG;kBAACmI,EAAE,EAAE,EAAG;kBAAAd,QAAA,eACVrG,OAAA,CAACpB,IAAI,CAAC0I,KAAK;oBAAAjB,QAAA,gBACTrG,OAAA,CAACpB,IAAI,CAAC2I,KAAK;sBAACnB,SAAS,EAAC,SAAS;sBAAAC,QAAA,GAAC,0CACX,eAAArG,OAAA;wBAAMoG,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACbzG,OAAA,CAACpB,IAAI,CAACiJ,OAAO;sBACXC,EAAE,EAAC,UAAU;sBACbC,IAAI,EAAE,CAAE;sBACRC,WAAW,EAAC,6HAAkE;sBAC9E5B,SAAS,EAAC,YAAY;sBACtBhG,KAAK,EAAEa,WAAY;sBACnBwG,QAAQ,EAAGpC,CAAC,IAAKnE,cAAc,CAACmE,CAAC,CAAC7C,MAAM,CAACpC,KAAK,CAAE;sBAChD6H,SAAS,EAAExG,gBAAgB,CAACyG,IAAI,CAAElF,KAAK,IAAKA,KAAK,CAACF,QAAQ,CAAC,OAAO,CAAC;oBAAE;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACFzG,OAAA,CAACpB,IAAI,CAACuJ,IAAI;sBAAC/B,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAEpF,WAAW,CAACgB,MAAM,EAAC,uCAAmB;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzG,OAAA,CAACjB,GAAG;gBAACqH,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBrG,OAAA,CAAChB,GAAG;kBAACmI,EAAE,EAAE,EAAG;kBAAAd,QAAA,eACVrG,OAAA,CAACpB,IAAI,CAAC0I,KAAK;oBAAAjB,QAAA,gBACTrG,OAAA,CAACpB,IAAI,CAAC2I,KAAK;sBAACnB,SAAS,EAAC,SAAS;sBAAAC,QAAA,GAAC,qCACX,eAAArG,OAAA;wBAAMoG,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzDzG,OAAA;wBAAMoG,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,GAAC,GAAC,EAACnE,WAAW,EAAC,cAAO;sBAAA;wBAAAoE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eAGbzG,OAAA;sBACEoG,SAAS,EAAE,eAAe7E,UAAU,GAAG,aAAa,GAAG,EAAE,IACvDW,WAAW,IAAI,CAAC,GAAG,UAAU,GAAG,EAAE,EACjC;sBACHkG,WAAW,EAAEhD,UAAW;sBACxBiD,WAAW,EAAEjD,UAAW;sBACxBkD,UAAU,EAAElD,UAAW;sBACvBmD,MAAM,EAAE/C,UAAW;sBAAAa,QAAA,eAEnBrG,OAAA;wBAAKoG,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7BrG,OAAA,CAACZ,MAAM;0BAAC8D,IAAI,EAAE,EAAG;0BAACkD,SAAS,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5CzG,OAAA;0BAAAqG,QAAA,EAAI;wBAAwB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACjCzG,OAAA,CAACpB,IAAI,CAACiJ,OAAO;0BACX9E,IAAI,EAAC,MAAM;0BACXyF,QAAQ;0BACRC,MAAM,EAAC,SAAS;0BAChBhB,QAAQ,EAAEtF,iBAAkB;0BAC5BuG,QAAQ,EAAExG,WAAW,IAAI,CAAC,IAAIP,WAAY;0BAC1CyE,SAAS,EAAC,QAAQ;0BAClBuC,EAAE,EAAC;wBAAc;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB,CAAC,eACFzG,OAAA,CAACrB,MAAM;0BACLmJ,EAAE,EAAC,OAAO;0BACVc,OAAO,EAAC,cAAc;0BACtB3B,OAAO,EAAC,iBAAiB;0BACzByB,QAAQ,EAAExG,WAAW,IAAI,CAAC,IAAIP,WAAY;0BAAA0E,QAAA,EAEzC1E,WAAW,gBACV3B,OAAA,CAAAE,SAAA;4BAAAmG,QAAA,gBACErG,OAAA,CAACd,OAAO;8BAACmI,SAAS,EAAC,QAAQ;8BAACnE,IAAI,EAAC,IAAI;8BAACkD,SAAS,EAAC;4BAAM;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,uBAE3D;0BAAA,eAAE,CAAC,GAEH;wBACD;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACK,CAAC,eACTzG,OAAA;0BAAGoG,SAAS,EAAC,aAAa;0BAAAC,QAAA,EAAC;wBAA6C;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAGL,CAACtF,MAAM,CAACc,MAAM,GAAG,CAAC,IAAIZ,WAAW,CAACY,MAAM,GAAG,CAAC,kBAC3CjC,OAAA;sBAAKoG,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,eACjCrG,OAAA,CAACjB,GAAG;wBAAAsH,QAAA,GAEDlF,MAAM,CAACkC,GAAG,CAAC,CAAC6B,GAAG,EAAErB,KAAK,kBACrB7D,OAAA,CAAChB,GAAG;0BAACmI,EAAE,EAAE,CAAE;0BAA2Bf,SAAS,EAAC,MAAM;0BAAAC,QAAA,eACpDrG,OAAA;4BAAKoG,SAAS,EAAC,mBAAmB;4BAAAC,QAAA,gBAChCrG,OAAA;8BACE6I,GAAG,EAAE3D,GAAG,CAAC4D,GAAG,IAAI,kBAAmB;8BACnCC,GAAG,EAAE,YAAYlF,KAAK,GAAG,CAAC,EAAG;8BAC7BuC,SAAS,EAAC;4BAAe;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACFzG,OAAA,CAACrB,MAAM;8BACLsI,OAAO,EAAC,QAAQ;8BAChB/D,IAAI,EAAC,IAAI;8BACTkD,SAAS,EAAC,eAAe;8BACzB4C,OAAO,EAAEA,CAAA,KAAM9E,mBAAmB,CAACL,KAAK,CAAE;8BAC1CoF,KAAK,EAAC,mCAAmB;8BAAA5C,QAAA,eAEzBrG,OAAA,CAACX,CAAC;gCAAC6D,IAAI,EAAE;8BAAG;gCAAAoD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACTzG,OAAA;8BAAKoG,SAAS,EAAC,YAAY;8BAAAC,QAAA,gBACzBrG,OAAA;gCAAAqG,QAAA,EAAO;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eACxBzG,OAAA;gCAAAsG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eACNzG,OAAA;gCAAOoG,SAAS,EAAC,cAAc;gCAAAC,QAAA,EAAC;8BAAgB;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GArBS,YAAY5C,KAAK,EAAE;0BAAAyC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAsB/B,CACN,CAAC,EAGDpF,WAAW,CAACgC,GAAG,CAAC,CAAC0B,MAAM,EAAElB,KAAK,kBAC7B7D,OAAA,CAAChB,GAAG;0BAACmI,EAAE,EAAE,CAAE;0BAAwBf,SAAS,EAAC,MAAM;0BAAAC,QAAA,eACjDrG,OAAA;4BAAKoG,SAAS,EAAC,mBAAmB;4BAAAC,QAAA,gBAChCrG,OAAA;8BACE6I,GAAG,EAAE9D,MAAM,CAACzB,OAAQ;8BACpByF,GAAG,EAAE,WAAWlF,KAAK,GAAG,CAAC,EAAG;8BAC5BuC,SAAS,EAAC;4BAAe;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACFzG,OAAA,CAACrB,MAAM;8BACLsI,OAAO,EAAC,QAAQ;8BAChB/D,IAAI,EAAC,IAAI;8BACTkD,SAAS,EAAC,eAAe;8BACzB4C,OAAO,EAAEA,CAAA,KAAMpF,gBAAgB,CAACC,KAAK,CAAE;8BACvCoF,KAAK,EAAC,kCAAqB;8BAAA5C,QAAA,eAE3BrG,OAAA,CAACX,CAAC;gCAAC6D,IAAI,EAAE;8BAAG;gCAAAoD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACTzG,OAAA;8BAAKoG,SAAS,EAAC,YAAY;8BAAAC,QAAA,gBACzBrG,OAAA;gCAAAqG,QAAA,EAAQtB,MAAM,CAACtB;8BAAI;gCAAA6C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eAC5BzG,OAAA;gCAAAsG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eACNzG,OAAA;gCAAOoG,SAAS,EAAC,cAAc;gCAAAC,QAAA,EAAC;8BAAW;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GArBS,SAAS5C,KAAK,EAAE;0BAAAyC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAsB5B,CACN,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLN,WAAW,iBACVnG,OAAA,CAACf,KAAK;gBAACgI,OAAO,EAAC,SAAS;gBAACb,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACvCrG,OAAA;kBAAKoG,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BrG,OAAA;oBAAAqG,QAAA,EAAI;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3BzG,OAAA;oBAAGoG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACjBrG,OAAA;sBAAAqG,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1F,IAAI,EAAC,MACrC;kBAAA;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJzG,OAAA;oBAAGoG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACjBrG,OAAA;sBAAAqG,QAAA,EAAQ;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACxF,WAAW,CAACgB,MAAM,EAAC,gBAC9C;kBAAA;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJzG,OAAA;oBAAGoG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACjBrG,OAAA;sBAAAqG,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvE,WAAW,EAAC,aACxC,EAACb,WAAW,CAACY,MAAM,GAAG,CAAC,iBACrBjC,OAAA;sBAAMoG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,GAAC,GAAC,EAAChF,WAAW,CAACY,MAAM,EAAC,6BAAiB;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACjF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENzG,OAAA;YAAKoG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCrG,OAAA,CAACrB,MAAM;cACLsI,OAAO,EAAC,iBAAiB;cACzBb,SAAS,EAAC,aAAa;cACvB4C,OAAO,EAAE/C,UAAW;cACpBgD,KAAK,EAAC,eAAU;cAChBP,QAAQ,EAAE/G,WAAY;cAAA0E,QAAA,eAEtBrG,OAAA,CAACb,SAAS;gBAAC+D,IAAI,EAAE;cAAG;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTzG,OAAA,CAACrB,MAAM;cACLsI,OAAO,EAAC,SAAS;cACjBb,SAAS,EAAC,iBAAiB;cAC3B4C,OAAO,EAAEtD,cAAe;cACxBgD,QAAQ,EAAE,CAACvC,WAAW,IAAIxE,WAAY;cAAA0E,QAAA,EAErC1E,WAAW,gBACV3B,OAAA,CAAAE,SAAA;gBAAAmG,QAAA,gBACErG,OAAA,CAACd,OAAO;kBAACmI,SAAS,EAAC,QAAQ;kBAACnE,IAAI,EAAC,IAAI;kBAACkD,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,8BAE3D;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzG,OAAA,CAAChB,GAAG;UAACmI,EAAE,EAAE,CAAE;UAAAd,QAAA,eACTrG,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEzBrG,OAAA,CAACnB,IAAI;cAACuH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC9BrG,OAAA,CAACnB,IAAI,CAACqK,IAAI;gBAAA7C,QAAA,eACRrG,OAAA;kBAAKoG,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCrG,OAAA;oBAAKoG,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClCrG,OAAA;sBAAMmJ,IAAI,EAAC,KAAK;sBAAC,cAAW,WAAW;sBAAA9C,QAAA,EAAC;oBAExC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNzG,OAAA;oBAAKoG,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BrG,OAAA;sBAAIoG,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAwC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxEzG,OAAA;sBAAGoG,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,EAAC;oBAG9B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGPzG,OAAA,CAACnB,IAAI;cAACuH,SAAS,EAAC,WAAW;cAAAC,QAAA,eACzBrG,OAAA,CAACnB,IAAI,CAACqK,IAAI;gBAAA7C,QAAA,eACRrG,OAAA;kBAAKoG,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCrG,OAAA;oBAAKoG,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,eAC7BrG,OAAA;sBAAMmJ,IAAI,EAAC,KAAK;sBAAC,cAAW,MAAM;sBAAA9C,QAAA,EAAC;oBAEnC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNzG,OAAA;oBAAKoG,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BrG,OAAA;sBAAIoG,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvDzG,OAAA;sBAAIoG,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC5BrG,OAAA;wBAAAqG,QAAA,EAAI;sBAAmC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5CzG,OAAA;wBAAAqG,QAAA,EAAI;sBAAsC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/CzG,OAAA;wBAAAqG,QAAA,EAAI;sBAA6C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtDzG,OAAA;wBAAAqG,QAAA,EAAI;sBAA6B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZzG,OAAA;MAAOoJ,GAAG;MAAA/C,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAClG,EAAA,CAj2BuBD,0BAA0B;EAAA,QAC/Bf,WAAW,EACXC,cAAc,EACXC,cAAc;AAAA;AAAA4J,EAAA,GAHZ/I,0BAA0B;AAAA,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}