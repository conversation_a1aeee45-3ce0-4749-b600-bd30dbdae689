const Reservation = require("../../models/reservation");
const Hotel = require("../../models/hotel");
const Room = require("../../models/room");
const Feedback = require("../../models/feedback");
const MonthlyPayment = require("../../models/monthlyPayment");
const User = require("../../models/user");
const ReportedFeedback = require("../../models/reportedFeedback");
const asyncHandler = require("../../middlewares/asyncHandler");

// Admin Dashboard Metrics
const getDashboardMetricsAdmin = asyncHandler(async (req, res) => {
  const { period = "month" } = req.query;
  
  // Calculate date range based on period
  const now = new Date();
  let startDate;
  
  switch (period) {
    case "day":
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      break;
    case "week":
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case "month":
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case "year":
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
  }

  try {
    // Get total hotels count
    const totalHotels = await Hotel.countDocuments();
    
    // Get active hotels count (approved by admin and active by owner)
    const activeHotels = await Hotel.countDocuments({
      adminStatus: "APPROVED",
      ownerStatus: "ACTIVE"
    });
    
    // Get pending approvals count
    const pendingApprovals = await Hotel.countDocuments({
      adminStatus: "PENDING"
    });
    
    // Get total customers count
    const totalCustomers = await User.countDocuments({
      role: "CUSTOMER"
    });
    
    // Get total hotel owners count
    const totalOwners = await User.countDocuments({
      role: "OWNER"
    });
    
    // Get total reservations in period
    const totalReservations = await Reservation.countDocuments({
      createdAt: { $gte: startDate, $lte: now }
    });
    
    // Get completed reservations in period
    const completedReservations = await Reservation.find({
      status: "COMPLETED",
      createdAt: { $gte: startDate, $lte: now }
    });
    
    // Calculate total revenue from completed reservations
    const totalRevenue = completedReservations.reduce((sum, reservation) => {
      return sum + (reservation.finalPrice || reservation.totalPrice || 0);
    }, 0);
    
    // Get pending reports count
    const pendingReports = await ReportedFeedback.countDocuments({
      status: "PENDING"
    });
    
    // Get recent hotel approvals
    const recentApprovals = await Hotel.find({
      adminStatus: "PENDING"
    })
    .populate('owner', 'name email')
    .sort({ requestDate: -1 })
    .limit(5)
    .select('name owner location requestDate adminStatus');
    
    // Get recent reports
    const recentReports = await ReportedFeedback.find()
    .populate('user', 'name email')
    .populate('feedback', 'content')
    .populate({
      path: 'feedback',
      populate: {
        path: 'hotel',
        select: 'name'
      }
    })
    .sort({ createdAt: -1 })
    .limit(5);

    // Get revenue data for chart (last 12 months)
    const revenueData = [];
    const labels = [];
    
    for (let i = 11; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
      
      const monthlyReservations = await Reservation.find({
        status: "COMPLETED",
        createdAt: { $gte: monthStart, $lte: monthEnd }
      });
      
      const monthlyRevenue = monthlyReservations.reduce((sum, reservation) => {
        return sum + (reservation.finalPrice || reservation.totalPrice || 0);
      }, 0);
      
      revenueData.push(monthlyRevenue);
      labels.push(monthStart.toLocaleDateString('vi-VN', { month: 'short' }));
    }

    // Get hotel distribution by location
    const hotelsByLocation = await Hotel.aggregate([
      {
        $match: { adminStatus: "APPROVED" }
      },
      {
        $group: {
          _id: "$location",
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 5
      }
    ]);

    // Get hotels by category (star rating)
    const hotelsByCategory = await Hotel.aggregate([
      {
        $match: { adminStatus: "APPROVED" }
      },
      {
        $group: {
          _id: "$category",
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Format recent approvals
    const formattedApprovals = recentApprovals.map(hotel => ({
      id: hotel._id,
      hotelName: hotel.name,
      owner: hotel.owner?.name || 'N/A',
      location: hotel.location,
      submittedDate: hotel.requestDate?.toLocaleDateString('vi-VN') || 'N/A',
      status: hotel.adminStatus === 'PENDING' ? 'Đang chờ' : 'Đã xử lý'
    }));

    // Format recent reports
    const formattedReports = recentReports.map(report => ({
      id: report._id,
      customerName: report.user?.name || 'N/A',
      hotelName: report.feedback?.hotel?.name || 'N/A',
      reportType: report.reason || 'N/A',
      submittedDate: report.createdAt?.toLocaleDateString('vi-VN') || 'N/A',
      status: report.status === 'PENDING' ? 'Chưa xử lý' : 'Đã xử lý',
      severity: 'Trung bình' // You can add severity logic based on your business rules
    }));

    res.json({
      success: true,
      data: {
        // Overview stats
        totalHotels,
        activeHotels,
        pendingApprovals,
        totalCustomers,
        totalOwners,
        totalReservations,
        totalRevenue,
        pendingReports,
        
        // Chart data
        revenueData: {
          labels,
          datasets: [{
            label: 'Doanh thu thực tế',
            data: revenueData,
            borderColor: '#4361ee',
            backgroundColor: 'rgba(67, 97, 238, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        
        // Distribution data
        hotelDistributionData: {
          labels: hotelsByLocation.map(item => item._id || 'Khác'),
          datasets: [{
            data: hotelsByLocation.map(item => item.count),
            backgroundColor: [
              '#4361ee',
              '#3a0ca3', 
              '#4cc9f0',
              '#f72585',
              '#7209b7'
            ]
          }]
        },
        
        hotelCategoryData: {
          labels: hotelsByCategory.map(item => item._id || 'Khác'),
          datasets: [{
            data: hotelsByCategory.map(item => item.count),
            backgroundColor: [
              '#4cc9f0',
              '#4361ee',
              '#3a0ca3',
              '#7209b7',
              '#f72585'
            ]
          }]
        },
        
        // Recent activities
        recentApprovals: formattedApprovals,
        recentReports: formattedReports
      }
    });
    
  } catch (error) {
    console.error('Error fetching admin dashboard metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy dữ liệu dashboard',
      error: error.message
    });
  }
});

exports.getDashboardMetricsAdmin = getDashboardMetricsAdmin;
