{"ast": null, "code": "const HotelActions = {\n  CREATE_HOTEL: \"CREATE_HOTEL\",\n  CREATE_HOTEL_SUCCESS: \"CREATE_HOTEL_SUCCESS\",\n  FETCH_FAVORITE_HOTELS: \"FETCH_FAVORITE_HOTELS\",\n  FETCH_FAVORITE_HOTELS_SUCCESS: \"FETCH_FAVORITE_HOTELS_SUCCESS\",\n  FETCH_ALL_HOTEL: \"FETCH_ALL_HOTEL\",\n  FETCH_All_HOTEL_SUCCESS: \" FETCH_All_HOTEL_SUCCESS\",\n  FETCH_TOP3_HOTEL: \"FETCH_TOP3_HOTEL\",\n  FETCH_TOP3_HOTEL_SUCCESS: \"FETCH_TOP3_HOTEL_SUCCESS\",\n  FETCH_OWNER_HOTEL: \"FETCH_OWNER_HOTEL\",\n  FETCH_OWNER_HOTEL_SUCCESS: \"FETCH_OWNER_HOTEL_SUCCESS\",\n  UPDATE_HOTEL: \"UPDATE_HOTEL\",\n  UPDATE_HOTEL_SUCCESS: \"UPDATE_HOTEL_SUCCESS\",\n  UPDATE_HOTEL_SERVICE_STATUS: \"UPDATE_HOTEL_SERVICE_STATUS\",\n  UPDATE_HOTEL_SERVICE_STATUS_SUCCESS: \"UPDATE_HOTEL_SERVICE_STATUS_SUCCESS\",\n  CREATE_HOTEL_SERVICE: \"CREATE_HOTEL_SERVICE\",\n  CREATE_HOTEL_SERVICE_SUCCESS: \"CREATE_HOTEL_SERVICE_SUCCESS\",\n  SAVE_HOTEL_NAME_CREATE: \"SAVE_HOTEL_NAME_CREATE\",\n  SAVE_HOTEL_ADDRESS_CREATE: \"SAVE_HOTEL_ADDRESS_CREATE\",\n  SAVE_HOTEL_FACILITIES_CREATE: \"SAVE_HOTEL_FACILITIES_CREATE\",\n  SAVE_HOTEL_CHECKTIME_CREATE: \"SAVE_HOTEL_CHECKTIME_CREATE\",\n  SAVE_HOTEL_DESCRIPTION_CREATE: \"SAVE_HOTEL_DESCRIPTION_CREATE\",\n  EDIT_HOTEL_DESCRIPTION_CREATE: \"EDIT_HOTEL_DESCRIPTION_CREATE\",\n  CLEAR_HOTEL_CREATE: \"CLEAR_HOTEL_CREATE\",\n  ROOM_CREATE: \"ROOM_CREATE\",\n  SAVE_HOTEL_DOCUMENTS_CREATE: \"SAVE_HOTEL_DOCUMENTS_CREATE\",\n  // Service creation actions\n  SAVE_SERVICE_CREATE: \"SAVE_SERVICE_CREATE\",\n  EDIT_SERVICE_CREATE: \"EDIT_SERVICE_CREATE\",\n  DELETE_SERVICE_CREATE: \"DELETE_SERVICE_CREATE\",\n  CLEAR_SERVICE_CREATE: \"CLEAR_SERVICE_CREATE\"\n};\nexport default HotelActions;", "map": {"version": 3, "names": ["HotelActions", "CREATE_HOTEL", "CREATE_HOTEL_SUCCESS", "FETCH_FAVORITE_HOTELS", "FETCH_FAVORITE_HOTELS_SUCCESS", "FETCH_ALL_HOTEL", "FETCH_All_HOTEL_SUCCESS", "FETCH_TOP3_HOTEL", "FETCH_TOP3_HOTEL_SUCCESS", "FETCH_OWNER_HOTEL", "FETCH_OWNER_HOTEL_SUCCESS", "UPDATE_HOTEL", "UPDATE_HOTEL_SUCCESS", "UPDATE_HOTEL_SERVICE_STATUS", "UPDATE_HOTEL_SERVICE_STATUS_SUCCESS", "CREATE_HOTEL_SERVICE", "CREATE_HOTEL_SERVICE_SUCCESS", "SAVE_HOTEL_NAME_CREATE", "SAVE_HOTEL_ADDRESS_CREATE", "SAVE_HOTEL_FACILITIES_CREATE", "SAVE_HOTEL_CHECKTIME_CREATE", "SAVE_HOTEL_DESCRIPTION_CREATE", "EDIT_HOTEL_DESCRIPTION_CREATE", "CLEAR_HOTEL_CREATE", "ROOM_CREATE", "SAVE_HOTEL_DOCUMENTS_CREATE", "SAVE_SERVICE_CREATE", "EDIT_SERVICE_CREATE", "DELETE_SERVICE_CREATE", "CLEAR_SERVICE_CREATE"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/hotel/actions.js"], "sourcesContent": ["const HotelActions = {\r\n  CREATE_HOTEL:\"CREATE_HOTEL\",\r\n  CREATE_HOTEL_SUCCESS: \"CREATE_HOTEL_SUCCESS\",\r\n  FETCH_FAVORITE_HOTELS: \"FETCH_FAVORITE_HOTELS\",\r\n  FETCH_FAVORITE_HOTELS_SUCCESS: \"FETCH_FAVORITE_HOTELS_SUCCESS\",\r\n\r\n  FETCH_ALL_HOTEL: \"FETCH_ALL_HOTEL\",\r\n  FETCH_All_HOTEL_SUCCESS: \" FETCH_All_HOTEL_SUCCESS\",\r\n\r\n  FETCH_TOP3_HOTEL: \"FETCH_TOP3_HOTEL\",\r\n  FETCH_TOP3_HOTEL_SUCCESS: \"FETCH_TOP3_HOTEL_SUCCESS\",\r\n  \r\n  FETCH_OWNER_HOTEL:\"FETCH_OWNER_HOTEL\",\r\n  FETCH_OWNER_HOTEL_SUCCESS:\"FETCH_OWNER_HOTEL_SUCCESS\",\r\n  UPDATE_HOTEL: \"UPDATE_HOTEL\",\r\n  UPDATE_HOTEL_SUCCESS: \"UPDATE_HOTEL_SUCCESS\",\r\n  UPDATE_HOTEL_SERVICE_STATUS: \"UPDATE_HOTEL_SERVICE_STATUS\",\r\n  UPDATE_HOTEL_SERVICE_STATUS_SUCCESS: \"UPDATE_HOTEL_SERVICE_STATUS_SUCCESS\",  \r\n  CREATE_HOTEL_SERVICE: \"CREATE_HOTEL_SERVICE\",\r\n  CREATE_HOTEL_SERVICE_SUCCESS: \"CREATE_HOTEL_SERVICE_SUCCESS\",\r\n\r\n  SAVE_HOTEL_NAME_CREATE: \"SAVE_HOTEL_NAME_CREATE\", \r\n  SAVE_HOTEL_ADDRESS_CREATE: \"SAVE_HOTEL_ADDRESS_CREATE\",\r\n  SAVE_HOTEL_FACILITIES_CREATE: \"SAVE_HOTEL_FACILITIES_CREATE\",\r\n  SAVE_HOTEL_CHECKTIME_CREATE: \"SAVE_HOTEL_CHECKTIME_CREATE\",\r\n  SAVE_HOTEL_DESCRIPTION_CREATE: \"SAVE_HOTEL_DESCRIPTION_CREATE\",\r\n  EDIT_HOTEL_DESCRIPTION_CREATE: \"EDIT_HOTEL_DESCRIPTION_CREATE\",\r\n  CLEAR_HOTEL_CREATE: \"CLEAR_HOTEL_CREATE\",\r\n  ROOM_CREATE: \"ROOM_CREATE\",\r\n  SAVE_HOTEL_DOCUMENTS_CREATE: \"SAVE_HOTEL_DOCUMENTS_CREATE\",\r\n  \r\n  // Service creation actions\r\n  SAVE_SERVICE_CREATE: \"SAVE_SERVICE_CREATE\",\r\n  EDIT_SERVICE_CREATE: \"EDIT_SERVICE_CREATE\",\r\n  DELETE_SERVICE_CREATE: \"DELETE_SERVICE_CREATE\",\r\n  CLEAR_SERVICE_CREATE: \"CLEAR_SERVICE_CREATE\",\r\n};\r\nexport default HotelActions;\r\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACnBC,YAAY,EAAC,cAAc;EAC3BC,oBAAoB,EAAE,sBAAsB;EAC5CC,qBAAqB,EAAE,uBAAuB;EAC9CC,6BAA6B,EAAE,+BAA+B;EAE9DC,eAAe,EAAE,iBAAiB;EAClCC,uBAAuB,EAAE,0BAA0B;EAEnDC,gBAAgB,EAAE,kBAAkB;EACpCC,wBAAwB,EAAE,0BAA0B;EAEpDC,iBAAiB,EAAC,mBAAmB;EACrCC,yBAAyB,EAAC,2BAA2B;EACrDC,YAAY,EAAE,cAAc;EAC5BC,oBAAoB,EAAE,sBAAsB;EAC5CC,2BAA2B,EAAE,6BAA6B;EAC1DC,mCAAmC,EAAE,qCAAqC;EAC1EC,oBAAoB,EAAE,sBAAsB;EAC5CC,4BAA4B,EAAE,8BAA8B;EAE5DC,sBAAsB,EAAE,wBAAwB;EAChDC,yBAAyB,EAAE,2BAA2B;EACtDC,4BAA4B,EAAE,8BAA8B;EAC5DC,2BAA2B,EAAE,6BAA6B;EAC1DC,6BAA6B,EAAE,+BAA+B;EAC9DC,6BAA6B,EAAE,+BAA+B;EAC9DC,kBAAkB,EAAE,oBAAoB;EACxCC,WAAW,EAAE,aAAa;EAC1BC,2BAA2B,EAAE,6BAA6B;EAE1D;EACAC,mBAAmB,EAAE,qBAAqB;EAC1CC,mBAAmB,EAAE,qBAAqB;EAC1CC,qBAAqB,EAAE,uBAAuB;EAC9CC,oBAAoB,EAAE;AACxB,CAAC;AACD,eAAe7B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}