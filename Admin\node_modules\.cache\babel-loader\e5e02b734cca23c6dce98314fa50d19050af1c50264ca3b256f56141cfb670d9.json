{"ast": null, "code": "import { call, put, takeEvery } from 'redux-saga/effects';\nimport AdminDashboardActions from './actions';\nimport AdminDashboardFactories from './factories';\n\n// Fetch admin dashboard metrics\nfunction* fetchAdminDashboardMetrics() {\n  yield takeEvery(AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS, function* (action) {\n    const {\n      onSuccess,\n      onFailed,\n      params\n    } = action.payload || {};\n    try {\n      var _response$data;\n      const response = yield call(AdminDashboardFactories.fetchAdminDashboardMetrics, params);\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200 && response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        var _response$data2;\n        const data = (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.data;\n        yield put({\n          type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS,\n          payload: data\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(data);\n      } else {\n        var _response$data3;\n        const errorMessage = (response === null || response === void 0 ? void 0 : (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message) || \"Không thể lấy dữ liệu dashboard.\";\n        yield put({\n          type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n          payload: errorMessage\n        });\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(errorMessage);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Lỗi server\";\n      yield put({\n        type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n        payload: errorMessage\n      });\n      onFailed === null || onFailed === void 0 ? void 0 : onFailed(errorMessage);\n    }\n  });\n}\nexport default function* AdminDashboardSaga() {\n  yield fetchAdminDashboardMetrics();\n}\n_c = AdminDashboardSaga;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboardSaga\");", "map": {"version": 3, "names": ["call", "put", "takeEvery", "AdminDashboardActions", "AdminDashboardFactories", "fetchAdminDashboardMetrics", "FETCH_ADMIN_DASHBOARD_METRICS", "action", "onSuccess", "onFailed", "params", "payload", "_response$data", "response", "status", "data", "success", "_response$data2", "type", "FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS", "_response$data3", "errorMessage", "message", "FETCH_ADMIN_DASHBOARD_METRICS_FAILURE", "error", "_error$response", "_error$response$data", "AdminDashboardSaga", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/redux/adminDashboard/saga.js"], "sourcesContent": ["import { call, put, takeEvery } from 'redux-saga/effects';\nimport AdminDashboardActions from './actions';\nimport AdminDashboardFactories from './factories';\n\n// Fetch admin dashboard metrics\nfunction* fetchAdminDashboardMetrics() {\n  yield takeEvery(AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS, function* (action) {\n    const { onSuccess, onFailed, params } = action.payload || {};\n\n    try {\n      const response = yield call(AdminDashboardFactories.fetchAdminDashboardMetrics, params);\n\n      if (response?.status === 200 && response?.data?.success) {\n        const data = response.data?.data;\n\n        yield put({\n          type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS,\n          payload: data,\n        });\n\n        onSuccess?.(data);\n      } else {\n        const errorMessage = response?.data?.message || \"Không thể lấy dữ liệu dashboard.\";\n        yield put({\n          type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n          payload: errorMessage,\n        });\n        onFailed?.(errorMessage);\n      }\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || \"Lỗi server\";\n      yield put({\n        type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n        payload: errorMessage,\n      });\n      onFailed?.(errorMessage);\n    }\n  });\n}\n\nexport default function* AdminDashboardSaga() {\n  yield fetchAdminDashboardMetrics();\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,oBAAoB;AACzD,OAAOC,qBAAqB,MAAM,WAAW;AAC7C,OAAOC,uBAAuB,MAAM,aAAa;;AAEjD;AACA,UAAUC,0BAA0BA,CAAA,EAAG;EACrC,MAAMH,SAAS,CAACC,qBAAqB,CAACG,6BAA6B,EAAE,WAAWC,MAAM,EAAE;IACtF,MAAM;MAAEC,SAAS;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAGH,MAAM,CAACI,OAAO,IAAI,CAAC,CAAC;IAE5D,IAAI;MAAA,IAAAC,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAMb,IAAI,CAACI,uBAAuB,CAACC,0BAA0B,EAAEK,MAAM,CAAC;MAEvF,IAAI,CAAAG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,MAAM,MAAK,GAAG,IAAID,QAAQ,aAARA,QAAQ,gBAAAD,cAAA,GAARC,QAAQ,CAAEE,IAAI,cAAAH,cAAA,eAAdA,cAAA,CAAgBI,OAAO,EAAE;QAAA,IAAAC,eAAA;QACvD,MAAMF,IAAI,IAAAE,eAAA,GAAGJ,QAAQ,CAACE,IAAI,cAAAE,eAAA,uBAAbA,eAAA,CAAeF,IAAI;QAEhC,MAAMd,GAAG,CAAC;UACRiB,IAAI,EAAEf,qBAAqB,CAACgB,qCAAqC;UACjER,OAAO,EAAEI;QACX,CAAC,CAAC;QAEFP,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGO,IAAI,CAAC;MACnB,CAAC,MAAM;QAAA,IAAAK,eAAA;QACL,MAAMC,YAAY,GAAG,CAAAR,QAAQ,aAARA,QAAQ,wBAAAO,eAAA,GAARP,QAAQ,CAAEE,IAAI,cAAAK,eAAA,uBAAdA,eAAA,CAAgBE,OAAO,KAAI,kCAAkC;QAClF,MAAMrB,GAAG,CAAC;UACRiB,IAAI,EAAEf,qBAAqB,CAACoB,qCAAqC;UACjEZ,OAAO,EAAEU;QACX,CAAC,CAAC;QACFZ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGY,YAAY,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACd,MAAML,YAAY,GAAG,EAAAI,eAAA,GAAAD,KAAK,CAACX,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBJ,OAAO,KAAI,YAAY;MAClE,MAAMrB,GAAG,CAAC;QACRiB,IAAI,EAAEf,qBAAqB,CAACoB,qCAAqC;QACjEZ,OAAO,EAAEU;MACX,CAAC,CAAC;MACFZ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGY,YAAY,CAAC;IAC1B;EACF,CAAC,CAAC;AACJ;AAEA,eAAe,UAAUM,kBAAkBA,CAAA,EAAG;EAC5C,MAAMtB,0BAA0B,CAAC,CAAC;AACpC;AAACuB,EAAA,GAFwBD,kBAAkB;AAAA,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}