{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Admin\\\\src\\\\pages\\\\dashboard\\\\DashboardPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\nimport { FaFilePdf } from 'react-icons/fa';\nimport pdfMake from '../../utils/fonts';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement);\nconst DashboardPage = () => {\n  _s();\n  var _dashboardData$revenu, _dashboardData$revenu2, _dashboardData$hotelD, _dashboardData$hotelD2, _dashboardData$hotelC, _dashboardData$hotelC2;\n  const dispatch = useDispatch();\n  const {\n    data: dashboardData,\n    loading,\n    error\n  } = useSelector(state => state.AdminDashboard);\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n  const [exportLoading, setExportLoading] = useState(false);\n\n  // Fetch dashboard data on component mount and when period changes\n  useEffect(() => {\n    dispatch({\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\n      payload: {\n        params: {\n          period: selectedPeriod\n        },\n        onSuccess: data => {\n          console.log('Dashboard data loaded successfully:', data);\n        },\n        onFailed: error => {\n          console.error('Failed to load dashboard data:', error);\n        }\n      }\n    });\n  }, [dispatch, selectedPeriod]);\n\n  // Handle period change\n  const handlePeriodChange = period => {\n    setSelectedPeriod(period);\n  };\n\n  // Export dashboard report as PDF\n  const exportDashboardPDF = () => {\n    if (!dashboardData) {\n      alert(\"Không có dữ liệu để xuất báo cáo\");\n      return;\n    }\n    setExportLoading(true);\n    try {\n      var _dashboardData$hotels, _dashboardData$hotels2;\n      const currentDate = new Date().toLocaleDateString('vi-VN');\n      const periodText = selectedPeriod === 'month' ? 'Tháng' : 'Năm';\n\n      // Prepare hotel distribution data\n      const hotelDistributionData = ((_dashboardData$hotels = dashboardData.hotelsByLocation) === null || _dashboardData$hotels === void 0 ? void 0 : _dashboardData$hotels.map((item, index) => {\n        var _item$count;\n        return [(index + 1).toString(), item._id || 'N/A', ((_item$count = item.count) === null || _item$count === void 0 ? void 0 : _item$count.toString()) || '0', `${(item.count / dashboardData.totalHotels * 100).toFixed(1)}%`];\n      })) || [];\n\n      // Prepare hotel classification data\n      const hotelClassificationData = ((_dashboardData$hotels2 = dashboardData.hotelsByRating) === null || _dashboardData$hotels2 === void 0 ? void 0 : _dashboardData$hotels2.map((item, index) => {\n        var _item$count2;\n        return [(index + 1).toString(), `${item._id} sao`, ((_item$count2 = item.count) === null || _item$count2 === void 0 ? void 0 : _item$count2.toString()) || '0', `${(item.count / dashboardData.totalHotels * 100).toFixed(1)}%`];\n      })) || [];\n      const docDefinition = {\n        content: [\n        // Header\n        {\n          text: \"UROOM ADMIN DASHBOARD\",\n          style: \"header\",\n          alignment: \"center\",\n          margin: [0, 0, 0, 20]\n        }, {\n          text: `BÁO CÁO THỐNG KÊ - ${periodText.toUpperCase()}`,\n          style: \"subheader\",\n          alignment: \"center\",\n          margin: [0, 0, 0, 20]\n        }, {\n          text: `Ngày xuất báo cáo: ${currentDate}`,\n          alignment: \"right\",\n          margin: [0, 0, 0, 30]\n        },\n        // Summary Statistics\n        {\n          text: \"I. THỐNG KÊ TỔNG QUAN\",\n          style: \"sectionHeader\",\n          margin: [0, 0, 0, 15]\n        }, {\n          table: {\n            widths: [\"50%\", \"50%\"],\n            body: [[\"Tổng số khách sạn\", (dashboardData.totalHotels || 0).toString()], [\"Tổng số người dùng\", (dashboardData.totalUsers || 0).toString()], [\"Tổng số chủ khách sạn\", (dashboardData.totalHotelOwners || 0).toString()], [\"Doanh thu \" + periodText.toLowerCase(), `${(dashboardData.totalRevenue || 0).toLocaleString('vi-VN')} VND`]]\n          },\n          margin: [0, 0, 0, 20]\n        },\n        // Hotel Distribution by Region\n        {\n          text: \"II. PHÂN BỐ KHÁCH SẠN THEO KHU VỰC\",\n          style: \"sectionHeader\",\n          margin: [0, 0, 0, 15]\n        }, {\n          table: {\n            widths: [\"10%\", \"40%\", \"25%\", \"25%\"],\n            body: [[\"STT\", \"Khu vực\", \"Số lượng\", \"Tỷ lệ\"], ...hotelDistributionData]\n          },\n          margin: [0, 0, 0, 20]\n        },\n        // Hotel Classification by Rating\n        {\n          text: \"III. PHÂN LOẠI KHÁCH SẠN THEO ĐÁNH GIÁ\",\n          style: \"sectionHeader\",\n          margin: [0, 0, 0, 15]\n        }, {\n          table: {\n            widths: [\"10%\", \"40%\", \"25%\", \"25%\"],\n            body: [[\"STT\", \"Phân loại\", \"Số lượng\", \"Tỷ lệ\"], ...hotelClassificationData]\n          },\n          margin: [0, 0, 0, 20]\n        },\n        // Footer\n        {\n          text: \"Báo cáo được tạo tự động bởi hệ thống UROOM Admin Dashboard\",\n          style: \"footer\",\n          alignment: \"center\",\n          margin: [0, 30, 0, 0]\n        }],\n        styles: {\n          header: {\n            fontSize: 20,\n            bold: true,\n            color: \"#212B49\"\n          },\n          subheader: {\n            fontSize: 16,\n            bold: true,\n            color: \"#212B49\"\n          },\n          sectionHeader: {\n            fontSize: 14,\n            bold: true,\n            color: \"#212B49\"\n          },\n          footer: {\n            fontSize: 10,\n            italics: true,\n            color: \"#666666\"\n          }\n        },\n        defaultStyle: {\n          font: \"Roboto\",\n          fallbackFonts: ['Times-Roman']\n        }\n      };\n\n      // Generate PDF\n      pdfMake.createPdf(docDefinition).download(`dashboard-report-${selectedPeriod}-${new Date().getTime()}.pdf`);\n      alert(\"Báo cáo đã được xuất thành công!\");\n    } catch (error) {\n      console.error(\"Error exporting dashboard report:\", error);\n      alert(\"Lỗi khi xuất báo cáo: \" + (error.message || \"Lỗi không xác định\"));\n    } finally {\n      setExportLoading(false);\n    }\n  };\n\n  // Chart empty state component\n  const ChartEmptyState = ({\n    icon,\n    message\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex align-items-center justify-content-center h-100 text-muted\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `bi ${icon} fs-1 d-block mb-2`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n\n  // Format revenue for display\n  const formatRevenue = revenue => {\n    if (revenue >= 1000000) {\n      return (revenue / 1000000).toFixed(1) + 'M';\n    } else if (revenue >= 1000) {\n      return (revenue / 1000).toFixed(1) + 'K';\n    }\n    return (revenue === null || revenue === void 0 ? void 0 : revenue.toLocaleString()) || '0';\n  };\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          height: '400px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"alert-heading\",\n          children: \"L\\u1ED7i!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-danger\",\n          onClick: () => handlePeriodChange(selectedPeriod),\n          children: \"Th\\u1EED l\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"T\\u1ED5ng quan h\\u1EC7 th\\u1ED1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-filter\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select\",\n            value: selectedPeriod,\n            onChange: e => handlePeriodChange(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"month\",\n              children: \"Th\\xE1ng n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"year\",\n              children: \"N\\u0103m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-secondary me-2\",\n          onClick: () => handlePeriodChange(selectedPeriod),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-arrow-clockwise\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), \" L\\xE0m m\\u1EDBi\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: exportDashboardPDF,\n          disabled: exportLoading || loading,\n          children: exportLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), \"\\u0110ang xu\\u1EA5t...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), \"Xu\\u1EA5t PDF\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon hotels\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-building\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.activeHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xE1ch s\\u1EA1n ho\\u1EA1t \\u0111\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon active\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-check-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalCustomers || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon customers\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-people\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalOwners || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u1EE7 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon owners\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-person-badge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Doanh thu h\\u1EC7 th\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"btn-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Ng\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Tu\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-primary\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"N\\u0103m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-body\",\n        children: ((_dashboardData$revenu = dashboardData.revenueData) === null || _dashboardData$revenu === void 0 ? void 0 : (_dashboardData$revenu2 = _dashboardData$revenu.labels) === null || _dashboardData$revenu2 === void 0 ? void 0 : _dashboardData$revenu2.length) > 0 ? /*#__PURE__*/_jsxDEV(Line, {\n          data: dashboardData.revenueData,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                position: \"top\"\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: false,\n                grid: {\n                  drawBorder: false\n                },\n                ticks: {\n                  callback: value => formatRevenue(value)\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n          icon: \"bi-graph-up\",\n          message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u doanh thu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n b\\u1ED1 kh\\xE1ch s\\u1EA1n theo khu v\\u1EF1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelD = dashboardData.hotelDistributionData) === null || _dashboardData$hotelD === void 0 ? void 0 : (_dashboardData$hotelD2 = _dashboardData$hotelD.labels) === null || _dashboardData$hotelD2 === void 0 ? void 0 : _dashboardData$hotelD2.length) > 0 ? /*#__PURE__*/_jsxDEV(Doughnut, {\n            data: dashboardData.hotelDistributionData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\",\n                  labels: {\n                    generateLabels: function (chart) {\n                      const data = chart.data;\n                      if (data.labels.length && data.datasets.length) {\n                        const dataset = data.datasets[0];\n                        const total = dataset.data.reduce((sum, value) => sum + value, 0);\n                        return data.labels.map((label, i) => {\n                          var _dataset$borderColor;\n                          const value = dataset.data[i];\n                          const percentage = (value / total * 100).toFixed(1);\n                          return {\n                            text: `${label}: ${value} (${percentage}%)`,\n                            fillStyle: dataset.backgroundColor[i],\n                            strokeStyle: ((_dataset$borderColor = dataset.borderColor) === null || _dataset$borderColor === void 0 ? void 0 : _dataset$borderColor[i]) || '#fff',\n                            lineWidth: 2,\n                            hidden: false,\n                            index: i\n                          };\n                        });\n                      }\n                      return [];\n                    }\n                  }\n                },\n                tooltip: {\n                  callbacks: {\n                    label: function (context) {\n                      const label = context.label || '';\n                      const value = context.parsed;\n                      const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\n                      const percentage = (value / total * 100).toFixed(1);\n                      return `${label}: ${value} khách sạn (${percentage}%)`;\n                    }\n                  }\n                }\n              },\n              cutout: \"70%\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n b\\u1ED1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelC = dashboardData.hotelCategoryData) === null || _dashboardData$hotelC === void 0 ? void 0 : (_dashboardData$hotelC2 = _dashboardData$hotelC.labels) === null || _dashboardData$hotelC2 === void 0 ? void 0 : _dashboardData$hotelC2.length) > 0 ? /*#__PURE__*/_jsxDEV(Pie, {\n            data: dashboardData.hotelCategoryData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\",\n                  labels: {\n                    generateLabels: function (chart) {\n                      const data = chart.data;\n                      if (data.labels.length && data.datasets.length) {\n                        const dataset = data.datasets[0];\n                        const total = dataset.data.reduce((sum, value) => sum + value, 0);\n                        return data.labels.map((label, i) => {\n                          var _dataset$borderColor2;\n                          const value = dataset.data[i];\n                          const percentage = (value / total * 100).toFixed(1);\n                          return {\n                            text: `${label}: ${value} (${percentage}%)`,\n                            fillStyle: dataset.backgroundColor[i],\n                            strokeStyle: ((_dataset$borderColor2 = dataset.borderColor) === null || _dataset$borderColor2 === void 0 ? void 0 : _dataset$borderColor2[i]) || '#fff',\n                            lineWidth: 2,\n                            hidden: false,\n                            index: i\n                          };\n                        });\n                      }\n                      return [];\n                    }\n                  }\n                },\n                tooltip: {\n                  callbacks: {\n                    label: function (context) {\n                      const label = context.label || '';\n                      const value = context.parsed;\n                      const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\n                      const percentage = (value / total * 100).toFixed(1);\n                      return `${label}: ${value} khách sạn (${percentage}%)`;\n                    }\n                  }\n                }\n              },\n              onHover: (_, activeElements, chart) => {\n                if (activeElements.length > 0) {\n                  const dataIndex = activeElements[0].index;\n                  const dataset = chart.data.datasets[0];\n                  const total = dataset.data.reduce((sum, val) => sum + val, 0);\n                  const value = dataset.data[dataIndex];\n                  const percentage = (value / total * 100).toFixed(1);\n                  const label = chart.data.labels[dataIndex];\n\n                  // Update center text\n                  chart.options.plugins.centerText = {\n                    display: true,\n                    text: `${percentage}%`,\n                    subtext: label,\n                    value: value\n                  };\n                  chart.update('none');\n                } else {\n                  // Reset center text\n                  chart.options.plugins.centerText = {\n                    display: true,\n                    text: 'Click',\n                    subtext: 'để xem chi tiết',\n                    value: ''\n                  };\n                  chart.update('none');\n                }\n              }\n            },\n            plugins: [{\n              id: 'centerTextPie',\n              beforeDraw: chart => {\n                const {\n                  ctx,\n                  width,\n                  height\n                } = chart;\n                const centerText = chart.options.plugins.centerText || {\n                  display: true,\n                  text: 'Click',\n                  subtext: 'để xem chi tiết',\n                  value: ''\n                };\n                if (centerText.display) {\n                  ctx.save();\n                  ctx.textAlign = 'center';\n                  ctx.textBaseline = 'middle';\n                  const centerX = width / 2;\n                  const centerY = height / 2;\n\n                  // Main percentage text\n                  ctx.font = 'bold 20px Arial';\n                  ctx.fillStyle = '#333';\n                  ctx.fillText(centerText.text, centerX, centerY - 8);\n\n                  // Subtext (label)\n                  ctx.font = '12px Arial';\n                  ctx.fillStyle = '#666';\n                  ctx.fillText(centerText.subtext, centerX, centerY + 12);\n\n                  // Value\n                  if (centerText.value) {\n                    ctx.font = '10px Arial';\n                    ctx.fillStyle = '#999';\n                    ctx.fillText(`${centerText.value} khách sạn`, centerX, centerY + 28);\n                  }\n                  ctx.restore();\n                }\n              }\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart-fill\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n lo\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detailed-analysis mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container mb-4 card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-geo-alt me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch chi ti\\u1EBFt theo khu v\\u1EF1c\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Khu v\\u1EF1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.locationBreakdown || []).length > 0 ? (dashboardData.locationBreakdown || []).map((location, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: location.region\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: location.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: location.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 615,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: location.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${location.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 622,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [location.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: location.activePercentage >= 80 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 29\n                    }, this) : location.activePercentage >= 60 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 637,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-geo fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch khu v\\u1EF1c\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container mb-4 card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-star me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch theo ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ph\\xE2n lo\\u1EA1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\xE1nh gi\\xE1 TB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EA5t l\\u01B0\\u1EE3ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.categoryBreakdown || []).length > 0 ? (dashboardData.categoryBreakdown || []).map((category, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: category.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: category.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 686,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: category.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: category.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: `bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`,\n                        style: {\n                          fontSize: '12px'\n                        }\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 697,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"ms-1\",\n                        children: [\"(\", category.avgRating, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${category.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 709,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 708,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [category.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 714,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 707,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: category.avgRating >= 4.5 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"Xu\\u1EA5t s\\u1EAFc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 4.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-info\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 721,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 3.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"7\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-star fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch ph\\xE2n lo\\u1EA1i\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"8fLNYqaW1K1wenMEpqG3xopMfko=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "Line", "Bar", "Pie", "Doughnut", "useDispatch", "useSelector", "AdminDashboardActions", "FaFilePdf", "pdfMake", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "DashboardPage", "_s", "_dashboardData$revenu", "_dashboardData$revenu2", "_dashboardData$hotelD", "_dashboardData$hotelD2", "_dashboardData$hotelC", "_dashboardData$hotelC2", "dispatch", "data", "dashboardData", "loading", "error", "state", "AdminDashboard", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "exportLoading", "setExportLoading", "type", "FETCH_ADMIN_DASHBOARD_METRICS", "payload", "params", "period", "onSuccess", "console", "log", "onFailed", "handlePeriodChange", "exportDashboardPDF", "alert", "_dashboardData$hotels", "_dashboardData$hotels2", "currentDate", "Date", "toLocaleDateString", "periodText", "hotelDistributionData", "hotelsByLocation", "map", "item", "index", "_item$count", "toString", "_id", "count", "totalHotels", "toFixed", "hotelClassificationData", "hotelsByRating", "_item$count2", "docDefinition", "content", "text", "style", "alignment", "margin", "toUpperCase", "table", "widths", "body", "totalUsers", "totalHotelOwners", "toLowerCase", "totalRevenue", "toLocaleString", "styles", "header", "fontSize", "bold", "color", "subheader", "section<PERSON><PERSON><PERSON>", "footer", "italics", "defaultStyle", "font", "fallbackFonts", "createPdf", "download", "getTime", "message", "ChartEmptyState", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatRevenue", "revenue", "height", "role", "onClick", "value", "onChange", "e", "target", "disabled", "activeHotels", "totalCustomers", "totalOwners", "revenueData", "labels", "length", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "scales", "y", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "x", "display", "generateLabels", "chart", "datasets", "dataset", "total", "reduce", "sum", "label", "i", "_dataset$borderColor", "percentage", "fillStyle", "backgroundColor", "strokeStyle", "borderColor", "lineWidth", "hidden", "tooltip", "callbacks", "context", "parsed", "val", "cutout", "hotelCategoryData", "_dataset$borderColor2", "onHover", "_", "activeElements", "dataIndex", "centerText", "subtext", "update", "id", "beforeDraw", "ctx", "width", "save", "textAlign", "textBaseline", "centerX", "centerY", "fillText", "restore", "locationBreakdown", "location", "region", "active", "pending", "activePercentage", "colSpan", "categoryBreakdown", "category", "Array", "Math", "floor", "avgRating", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/pages/dashboard/DashboardPage.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { <PERSON>, Bar, Pie, Doughnut } from \"react-chartjs-2\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\r\nimport { FaFilePdf } from 'react-icons/fa';\r\nimport pdfMake from '../../utils/fonts';\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  ArcElement,\r\n} from 'chart.js';\r\n\r\n// Register Chart.js components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  ArcElement\r\n);\r\n\r\nconst DashboardPage = () => {\r\n  const dispatch = useDispatch();\r\n  const { data: dashboardData, loading, error } = useSelector(state => state.AdminDashboard);\r\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n\r\n  // Fetch dashboard data on component mount and when period changes\r\n  useEffect(() => {\r\n    dispatch({\r\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\r\n      payload: {\r\n        params: { period: selectedPeriod },\r\n        onSuccess: (data) => {\r\n          console.log('Dashboard data loaded successfully:', data);\r\n        },\r\n        onFailed: (error) => {\r\n          console.error('Failed to load dashboard data:', error);\r\n        }\r\n      }\r\n    });\r\n  }, [dispatch, selectedPeriod]);\r\n\r\n  // Handle period change\r\n  const handlePeriodChange = (period) => {\r\n    setSelectedPeriod(period);\r\n  };\r\n\r\n  // Export dashboard report as PDF\r\n  const exportDashboardPDF = () => {\r\n    if (!dashboardData) {\r\n      alert(\"Không có dữ liệu để xuất báo cáo\");\r\n      return;\r\n    }\r\n\r\n    setExportLoading(true);\r\n\r\n    try {\r\n      const currentDate = new Date().toLocaleDateString('vi-VN');\r\n      const periodText = selectedPeriod === 'month' ? 'Tháng' : 'Năm';\r\n\r\n      // Prepare hotel distribution data\r\n      const hotelDistributionData = dashboardData.hotelsByLocation?.map((item, index) => [\r\n        (index + 1).toString(),\r\n        item._id || 'N/A',\r\n        item.count?.toString() || '0',\r\n        `${((item.count / dashboardData.totalHotels) * 100).toFixed(1)}%`\r\n      ]) || [];\r\n\r\n      // Prepare hotel classification data\r\n      const hotelClassificationData = dashboardData.hotelsByRating?.map((item, index) => [\r\n        (index + 1).toString(),\r\n        `${item._id} sao`,\r\n        item.count?.toString() || '0',\r\n        `${((item.count / dashboardData.totalHotels) * 100).toFixed(1)}%`\r\n      ]) || [];\r\n\r\n      const docDefinition = {\r\n        content: [\r\n          // Header\r\n          {\r\n            text: \"UROOM ADMIN DASHBOARD\",\r\n            style: \"header\",\r\n            alignment: \"center\",\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n          {\r\n            text: `BÁO CÁO THỐNG KÊ - ${periodText.toUpperCase()}`,\r\n            style: \"subheader\",\r\n            alignment: \"center\",\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n          {\r\n            text: `Ngày xuất báo cáo: ${currentDate}`,\r\n            alignment: \"right\",\r\n            margin: [0, 0, 0, 30],\r\n          },\r\n\r\n          // Summary Statistics\r\n          {\r\n            text: \"I. THỐNG KÊ TỔNG QUAN\",\r\n            style: \"sectionHeader\",\r\n            margin: [0, 0, 0, 15],\r\n          },\r\n          {\r\n            table: {\r\n              widths: [\"50%\", \"50%\"],\r\n              body: [\r\n                [\"Tổng số khách sạn\", (dashboardData.totalHotels || 0).toString()],\r\n                [\"Tổng số người dùng\", (dashboardData.totalUsers || 0).toString()],\r\n                [\"Tổng số chủ khách sạn\", (dashboardData.totalHotelOwners || 0).toString()],\r\n                [\"Doanh thu \" + periodText.toLowerCase(), `${(dashboardData.totalRevenue || 0).toLocaleString('vi-VN')} VND`],\r\n              ],\r\n            },\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n\r\n          // Hotel Distribution by Region\r\n          {\r\n            text: \"II. PHÂN BỐ KHÁCH SẠN THEO KHU VỰC\",\r\n            style: \"sectionHeader\",\r\n            margin: [0, 0, 0, 15],\r\n          },\r\n          {\r\n            table: {\r\n              widths: [\"10%\", \"40%\", \"25%\", \"25%\"],\r\n              body: [\r\n                [\"STT\", \"Khu vực\", \"Số lượng\", \"Tỷ lệ\"],\r\n                ...hotelDistributionData,\r\n              ],\r\n            },\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n\r\n          // Hotel Classification by Rating\r\n          {\r\n            text: \"III. PHÂN LOẠI KHÁCH SẠN THEO ĐÁNH GIÁ\",\r\n            style: \"sectionHeader\",\r\n            margin: [0, 0, 0, 15],\r\n          },\r\n          {\r\n            table: {\r\n              widths: [\"10%\", \"40%\", \"25%\", \"25%\"],\r\n              body: [\r\n                [\"STT\", \"Phân loại\", \"Số lượng\", \"Tỷ lệ\"],\r\n                ...hotelClassificationData,\r\n              ],\r\n            },\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n\r\n          // Footer\r\n          {\r\n            text: \"Báo cáo được tạo tự động bởi hệ thống UROOM Admin Dashboard\",\r\n            style: \"footer\",\r\n            alignment: \"center\",\r\n            margin: [0, 30, 0, 0],\r\n          },\r\n        ],\r\n        styles: {\r\n          header: {\r\n            fontSize: 20,\r\n            bold: true,\r\n            color: \"#212B49\",\r\n          },\r\n          subheader: {\r\n            fontSize: 16,\r\n            bold: true,\r\n            color: \"#212B49\",\r\n          },\r\n          sectionHeader: {\r\n            fontSize: 14,\r\n            bold: true,\r\n            color: \"#212B49\",\r\n          },\r\n          footer: {\r\n            fontSize: 10,\r\n            italics: true,\r\n            color: \"#666666\",\r\n          },\r\n        },\r\n        defaultStyle: {\r\n          font: \"Roboto\",\r\n          fallbackFonts: ['Times-Roman']\r\n        },\r\n      };\r\n\r\n      // Generate PDF\r\n      pdfMake.createPdf(docDefinition).download(`dashboard-report-${selectedPeriod}-${new Date().getTime()}.pdf`);\r\n      alert(\"Báo cáo đã được xuất thành công!\");\r\n    } catch (error) {\r\n      console.error(\"Error exporting dashboard report:\", error);\r\n      alert(\"Lỗi khi xuất báo cáo: \" + (error.message || \"Lỗi không xác định\"));\r\n    } finally {\r\n      setExportLoading(false);\r\n    }\r\n  };\r\n\r\n  // Chart empty state component\r\n  const ChartEmptyState = ({ icon, message }) => (\r\n    <div className=\"d-flex align-items-center justify-content-center h-100 text-muted\">\r\n      <div className=\"text-center\">\r\n        <i className={`bi ${icon} fs-1 d-block mb-2`}></i>\r\n        <p>{message}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Format revenue for display\r\n  const formatRevenue = (revenue) => {\r\n    if (revenue >= 1000000) {\r\n      return (revenue / 1000000).toFixed(1) + 'M';\r\n    } else if (revenue >= 1000) {\r\n      return (revenue / 1000).toFixed(1) + 'K';\r\n    }\r\n    return revenue?.toLocaleString() || '0';\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n          <div className=\"spinner-border text-primary\" role=\"status\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"alert alert-danger\" role=\"alert\">\r\n          <h4 className=\"alert-heading\">Lỗi!</h4>\r\n          <p>{error}</p>\r\n          <button\r\n            className=\"btn btn-outline-danger\"\r\n            onClick={() => handlePeriodChange(selectedPeriod)}\r\n          >\r\n            Thử lại\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"dashboard-content\">\r\n      <div className=\"page-header\">\r\n        <h1>Tổng quan hệ thống</h1>\r\n        <div className=\"page-actions\">\r\n          <div className=\"date-filter\">\r\n            <select\r\n              className=\"form-select\"\r\n              value={selectedPeriod}\r\n              onChange={(e) => handlePeriodChange(e.target.value)}\r\n            >\r\n              <option value=\"month\">Tháng này</option>\r\n              <option value=\"year\">Năm nay</option>\r\n            </select>\r\n          </div>\r\n          <button\r\n            className=\"btn btn-outline-secondary me-2\"\r\n            onClick={() => handlePeriodChange(selectedPeriod)}\r\n            disabled={loading}\r\n          >\r\n            <i className=\"bi bi-arrow-clockwise\"></i> Làm mới\r\n          </button>\r\n          <button\r\n            className=\"btn btn-primary\"\r\n            onClick={exportDashboardPDF}\r\n            disabled={exportLoading || loading}\r\n          >\r\n            {exportLoading ? (\r\n              <>\r\n                <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\r\n                  <span className=\"visually-hidden\">Loading...</span>\r\n                </div>\r\n                Đang xuất...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaFilePdf className=\"me-2\" />\r\n                Xuất PDF\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"stats-cards\">\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalHotels || 0}</h3>\r\n            <p>Tổng số khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon hotels\">\r\n            <i className=\"bi bi-building\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.activeHotels || 0}</h3>\r\n            <p>Khách sạn hoạt động</p>\r\n          </div>\r\n          <div className=\"stat-card-icon active\">\r\n            <i className=\"bi bi-check-circle\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalCustomers || 0}</h3>\r\n            <p>Tổng số khách hàng</p>\r\n          </div>\r\n          <div className=\"stat-card-icon customers\">\r\n            <i className=\"bi bi-people\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalOwners || 0}</h3>\r\n            <p>Chủ khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon owners\">\r\n            <i className=\"bi bi-person-badge\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Revenue Chart */}\r\n      <div className=\"chart-container\">\r\n        <div className=\"chart-header\">\r\n          <h2>Doanh thu hệ thống</h2>\r\n          <div className=\"chart-actions\">\r\n            <div className=\"btn-group\">\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Ngày</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Tuần</button>\r\n              <button className=\"btn btn-sm btn-primary\">Tháng</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Năm</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-body\">\r\n          {dashboardData.revenueData?.labels?.length > 0 ? (\r\n            <Line\r\n              data={dashboardData.revenueData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"top\",\r\n                  },\r\n                },\r\n                scales: {\r\n                  y: {\r\n                    beginAtZero: false,\r\n                    grid: {\r\n                      drawBorder: false,\r\n                    },\r\n                    ticks: {\r\n                      callback: (value) => formatRevenue(value),\r\n                    },\r\n                  },\r\n                  x: {\r\n                    grid: {\r\n                      display: false,\r\n                    },\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n          ) : (\r\n            <ChartEmptyState icon=\"bi-graph-up\" message=\"Chưa có dữ liệu doanh thu\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Distribution Charts */}\r\n      <div className=\"charts-row\">\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân bố khách sạn theo khu vực</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelDistributionData?.labels?.length > 0 ? (\r\n              <Doughnut\r\n                data={dashboardData.hotelDistributionData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                      labels: {\r\n                        generateLabels: function(chart) {\r\n                          const data = chart.data;\r\n                          if (data.labels.length && data.datasets.length) {\r\n                            const dataset = data.datasets[0];\r\n                            const total = dataset.data.reduce((sum, value) => sum + value, 0);\r\n                            return data.labels.map((label, i) => {\r\n                              const value = dataset.data[i];\r\n                              const percentage = ((value / total) * 100).toFixed(1);\r\n                              return {\r\n                                text: `${label}: ${value} (${percentage}%)`,\r\n                                fillStyle: dataset.backgroundColor[i],\r\n                                strokeStyle: dataset.borderColor?.[i] || '#fff',\r\n                                lineWidth: 2,\r\n                                hidden: false,\r\n                                index: i\r\n                              };\r\n                            });\r\n                          }\r\n                          return [];\r\n                        }\r\n                      }\r\n                    },\r\n                    tooltip: {\r\n                      callbacks: {\r\n                        label: function(context) {\r\n                          const label = context.label || '';\r\n                          const value = context.parsed;\r\n                          const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\r\n                          const percentage = ((value / total) * 100).toFixed(1);\r\n                          return `${label}: ${value} khách sạn (${percentage}%)`;\r\n                        }\r\n                      }\r\n                    }\r\n                  },\r\n                  cutout: \"70%\",\r\n                }}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart\" message=\"Chưa có dữ liệu phân bố\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân loại khách sạn</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelCategoryData?.labels?.length > 0 ? (\r\n              <Pie\r\n                data={dashboardData.hotelCategoryData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                      labels: {\r\n                        generateLabels: function(chart) {\r\n                          const data = chart.data;\r\n                          if (data.labels.length && data.datasets.length) {\r\n                            const dataset = data.datasets[0];\r\n                            const total = dataset.data.reduce((sum, value) => sum + value, 0);\r\n                            return data.labels.map((label, i) => {\r\n                              const value = dataset.data[i];\r\n                              const percentage = ((value / total) * 100).toFixed(1);\r\n                              return {\r\n                                text: `${label}: ${value} (${percentage}%)`,\r\n                                fillStyle: dataset.backgroundColor[i],\r\n                                strokeStyle: dataset.borderColor?.[i] || '#fff',\r\n                                lineWidth: 2,\r\n                                hidden: false,\r\n                                index: i\r\n                              };\r\n                            });\r\n                          }\r\n                          return [];\r\n                        }\r\n                      }\r\n                    },\r\n                    tooltip: {\r\n                      callbacks: {\r\n                        label: function(context) {\r\n                          const label = context.label || '';\r\n                          const value = context.parsed;\r\n                          const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\r\n                          const percentage = ((value / total) * 100).toFixed(1);\r\n                          return `${label}: ${value} khách sạn (${percentage}%)`;\r\n                        }\r\n                      }\r\n                    }\r\n                  },\r\n                  onHover: (_, activeElements, chart) => {\r\n                    if (activeElements.length > 0) {\r\n                      const dataIndex = activeElements[0].index;\r\n                      const dataset = chart.data.datasets[0];\r\n                      const total = dataset.data.reduce((sum, val) => sum + val, 0);\r\n                      const value = dataset.data[dataIndex];\r\n                      const percentage = ((value / total) * 100).toFixed(1);\r\n                      const label = chart.data.labels[dataIndex];\r\n\r\n                      // Update center text\r\n                      chart.options.plugins.centerText = {\r\n                        display: true,\r\n                        text: `${percentage}%`,\r\n                        subtext: label,\r\n                        value: value\r\n                      };\r\n                      chart.update('none');\r\n                    } else {\r\n                      // Reset center text\r\n                      chart.options.plugins.centerText = {\r\n                        display: true,\r\n                        text: 'Click',\r\n                        subtext: 'để xem chi tiết',\r\n                        value: ''\r\n                      };\r\n                      chart.update('none');\r\n                    }\r\n                  },\r\n                }}\r\n                plugins={[{\r\n                  id: 'centerTextPie',\r\n                  beforeDraw: (chart) => {\r\n                    const { ctx, width, height } = chart;\r\n                    const centerText = chart.options.plugins.centerText || {\r\n                      display: true,\r\n                      text: 'Click',\r\n                      subtext: 'để xem chi tiết',\r\n                      value: ''\r\n                    };\r\n\r\n                    if (centerText.display) {\r\n                      ctx.save();\r\n                      ctx.textAlign = 'center';\r\n                      ctx.textBaseline = 'middle';\r\n\r\n                      const centerX = width / 2;\r\n                      const centerY = height / 2;\r\n\r\n                      // Main percentage text\r\n                      ctx.font = 'bold 20px Arial';\r\n                      ctx.fillStyle = '#333';\r\n                      ctx.fillText(centerText.text, centerX, centerY - 8);\r\n\r\n                      // Subtext (label)\r\n                      ctx.font = '12px Arial';\r\n                      ctx.fillStyle = '#666';\r\n                      ctx.fillText(centerText.subtext, centerX, centerY + 12);\r\n\r\n                      // Value\r\n                      if (centerText.value) {\r\n                        ctx.font = '10px Arial';\r\n                        ctx.fillStyle = '#999';\r\n                        ctx.fillText(`${centerText.value} khách sạn`, centerX, centerY + 28);\r\n                      }\r\n\r\n                      ctx.restore();\r\n                    }\r\n                  }\r\n                }]}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart-fill\" message=\"Chưa có dữ liệu phân loại\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detailed Analysis */}\r\n      <div className=\"detailed-analysis mt-4\">\r\n        {/* Location Breakdown */}\r\n        <div className=\"analysis-container mb-4 card\">\r\n          <div className=\"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\">\r\n            <h2 className=\"mb-0\">\r\n              <i className=\"bi bi-geo-alt me-2\"></i>\r\n              Phân tích chi tiết theo khu vực\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body card-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Khu vực</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Trạng thái</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.locationBreakdown || []).length > 0 ? (\r\n                    (dashboardData.locationBreakdown || []).map((location, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{location.region}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{location.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{location.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{location.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${location.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{location.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {location.activePercentage >= 80 ? (\r\n                            <span className=\"badge bg-success\">Tốt</span>\r\n                          ) : location.activePercentage >= 60 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"6\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-geo fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích khu vực\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category Breakdown */}\r\n        <div className=\"analysis-container mb-4 card\">\r\n          <div className=\"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\">\r\n            <h2 className=\"mb-0\">\r\n              <i className=\"bi bi-star me-2\"></i>\r\n              Phân tích theo phân loại khách sạn\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body card-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Phân loại</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Đánh giá TB</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Chất lượng</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.categoryBreakdown || []).length > 0 ? (\r\n                    (dashboardData.categoryBreakdown || []).map((category, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{category.category}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{category.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{category.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{category.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            {[...Array(5)].map((_, i) => (\r\n                              <i\r\n                                key={i}\r\n                                className={`bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`}\r\n                                style={{ fontSize: '12px' }}\r\n                              ></i>\r\n                            ))}\r\n                            <small className=\"ms-1\">({category.avgRating})</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${category.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{category.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {category.avgRating >= 4.5 ? (\r\n                            <span className=\"badge bg-success\">Xuất sắc</span>\r\n                          ) : category.avgRating >= 4.0 ? (\r\n                            <span className=\"badge bg-info\">Tốt</span>\r\n                          ) : category.avgRating >= 3.0 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"7\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-star fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích phân loại\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAZ,OAAO,CAACa,QAAQ,CACdZ,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAC1B,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B,IAAI,EAAEC,aAAa;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAG/B,WAAW,CAACgC,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;EAC1F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,OAAO,CAAC;EAC7D,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACdiC,QAAQ,CAAC;MACPW,IAAI,EAAErC,qBAAqB,CAACsC,6BAA6B;MACzDC,OAAO,EAAE;QACPC,MAAM,EAAE;UAAEC,MAAM,EAAER;QAAe,CAAC;QAClCS,SAAS,EAAGf,IAAI,IAAK;UACnBgB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEjB,IAAI,CAAC;QAC1D,CAAC;QACDkB,QAAQ,EAAGf,KAAK,IAAK;UACnBa,OAAO,CAACb,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,QAAQ,EAAEO,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMa,kBAAkB,GAAIL,MAAM,IAAK;IACrCP,iBAAiB,CAACO,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACnB,aAAa,EAAE;MAClBoB,KAAK,CAAC,kCAAkC,CAAC;MACzC;IACF;IAEAZ,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MAAA,IAAAa,qBAAA,EAAAC,sBAAA;MACF,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;MAC1D,MAAMC,UAAU,GAAGrB,cAAc,KAAK,OAAO,GAAG,OAAO,GAAG,KAAK;;MAE/D;MACA,MAAMsB,qBAAqB,GAAG,EAAAN,qBAAA,GAAArB,aAAa,CAAC4B,gBAAgB,cAAAP,qBAAA,uBAA9BA,qBAAA,CAAgCQ,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;QAAA,IAAAC,WAAA;QAAA,OAAK,CACjF,CAACD,KAAK,GAAG,CAAC,EAAEE,QAAQ,CAAC,CAAC,EACtBH,IAAI,CAACI,GAAG,IAAI,KAAK,EACjB,EAAAF,WAAA,GAAAF,IAAI,CAACK,KAAK,cAAAH,WAAA,uBAAVA,WAAA,CAAYC,QAAQ,CAAC,CAAC,KAAI,GAAG,EAC7B,GAAG,CAAEH,IAAI,CAACK,KAAK,GAAGnC,aAAa,CAACoC,WAAW,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAClE;MAAA,EAAC,KAAI,EAAE;;MAER;MACA,MAAMC,uBAAuB,GAAG,EAAAhB,sBAAA,GAAAtB,aAAa,CAACuC,cAAc,cAAAjB,sBAAA,uBAA5BA,sBAAA,CAA8BO,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;QAAA,IAAAS,YAAA;QAAA,OAAK,CACjF,CAACT,KAAK,GAAG,CAAC,EAAEE,QAAQ,CAAC,CAAC,EACtB,GAAGH,IAAI,CAACI,GAAG,MAAM,EACjB,EAAAM,YAAA,GAAAV,IAAI,CAACK,KAAK,cAAAK,YAAA,uBAAVA,YAAA,CAAYP,QAAQ,CAAC,CAAC,KAAI,GAAG,EAC7B,GAAG,CAAEH,IAAI,CAACK,KAAK,GAAGnC,aAAa,CAACoC,WAAW,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAClE;MAAA,EAAC,KAAI,EAAE;MAER,MAAMI,aAAa,GAAG;QACpBC,OAAO,EAAE;QACP;QACA;UACEC,IAAI,EAAE,uBAAuB;UAC7BC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEH,IAAI,EAAE,sBAAsBjB,UAAU,CAACqB,WAAW,CAAC,CAAC,EAAE;UACtDH,KAAK,EAAE,WAAW;UAClBC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEH,IAAI,EAAE,sBAAsBpB,WAAW,EAAE;UACzCsB,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,uBAAuB;UAC7BC,KAAK,EAAE,eAAe;UACtBE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEE,KAAK,EAAE;YACLC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACtBC,IAAI,EAAE,CACJ,CAAC,mBAAmB,EAAE,CAAClD,aAAa,CAACoC,WAAW,IAAI,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAClE,CAAC,oBAAoB,EAAE,CAACjC,aAAa,CAACmD,UAAU,IAAI,CAAC,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAClE,CAAC,uBAAuB,EAAE,CAACjC,aAAa,CAACoD,gBAAgB,IAAI,CAAC,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAC3E,CAAC,YAAY,GAAGP,UAAU,CAAC2B,WAAW,CAAC,CAAC,EAAE,GAAG,CAACrD,aAAa,CAACsD,YAAY,IAAI,CAAC,EAAEC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC;UAEjH,CAAC;UACDT,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,oCAAoC;UAC1CC,KAAK,EAAE,eAAe;UACtBE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEE,KAAK,EAAE;YACLC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACpCC,IAAI,EAAE,CACJ,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,EACvC,GAAGvB,qBAAqB;UAE5B,CAAC;UACDmB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,wCAAwC;UAC9CC,KAAK,EAAE,eAAe;UACtBE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEE,KAAK,EAAE;YACLC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACpCC,IAAI,EAAE,CACJ,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,CAAC,EACzC,GAAGZ,uBAAuB;UAE9B,CAAC;UACDQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,6DAA6D;UACnEC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACtB,CAAC,CACF;QACDU,MAAM,EAAE;UACNC,MAAM,EAAE;YACNC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE;UACT,CAAC;UACDC,SAAS,EAAE;YACTH,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE;UACT,CAAC;UACDE,aAAa,EAAE;YACbJ,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE;UACT,CAAC;UACDG,MAAM,EAAE;YACNL,QAAQ,EAAE,EAAE;YACZM,OAAO,EAAE,IAAI;YACbJ,KAAK,EAAE;UACT;QACF,CAAC;QACDK,YAAY,EAAE;UACZC,IAAI,EAAE,QAAQ;UACdC,aAAa,EAAE,CAAC,aAAa;QAC/B;MACF,CAAC;;MAED;MACA7F,OAAO,CAAC8F,SAAS,CAAC3B,aAAa,CAAC,CAAC4B,QAAQ,CAAC,oBAAoBhE,cAAc,IAAI,IAAImB,IAAI,CAAC,CAAC,CAAC8C,OAAO,CAAC,CAAC,MAAM,CAAC;MAC3GlD,KAAK,CAAC,kCAAkC,CAAC;IAC3C,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDkB,KAAK,CAAC,wBAAwB,IAAIlB,KAAK,CAACqE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3E,CAAC,SAAS;MACR/D,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMgE,eAAe,GAAGA,CAAC;IAAEC,IAAI;IAAEF;EAAQ,CAAC,kBACxCrF,OAAA;IAAKwF,SAAS,EAAC,mEAAmE;IAAAC,QAAA,eAChFzF,OAAA;MAAKwF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzF,OAAA;QAAGwF,SAAS,EAAE,MAAMD,IAAI;MAAqB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClD7F,OAAA;QAAAyF,QAAA,EAAIJ;MAAO;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMC,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAIA,OAAO,IAAI,OAAO,EAAE;MACtB,OAAO,CAACA,OAAO,GAAG,OAAO,EAAE5C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC7C,CAAC,MAAM,IAAI4C,OAAO,IAAI,IAAI,EAAE;MAC1B,OAAO,CAACA,OAAO,GAAG,IAAI,EAAE5C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC1C;IACA,OAAO,CAAA4C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE1B,cAAc,CAAC,CAAC,KAAI,GAAG;EACzC,CAAC;;EAED;EACA,IAAItD,OAAO,EAAE;IACX,oBACEf,OAAA;MAAKwF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCzF,OAAA;QAAKwF,SAAS,EAAC,kDAAkD;QAAC9B,KAAK,EAAE;UAAEsC,MAAM,EAAE;QAAQ,CAAE;QAAAP,QAAA,eAC3FzF,OAAA;UAAKwF,SAAS,EAAC,6BAA6B;UAACS,IAAI,EAAC,QAAQ;UAAAR,QAAA,eACxDzF,OAAA;YAAMwF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI7E,KAAK,EAAE;IACT,oBACEhB,OAAA;MAAKwF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCzF,OAAA;QAAKwF,SAAS,EAAC,oBAAoB;QAACS,IAAI,EAAC,OAAO;QAAAR,QAAA,gBAC9CzF,OAAA;UAAIwF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvC7F,OAAA;UAAAyF,QAAA,EAAIzE;QAAK;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd7F,OAAA;UACEwF,SAAS,EAAC,wBAAwB;UAClCU,OAAO,EAAEA,CAAA,KAAMlE,kBAAkB,CAACb,cAAc,CAAE;UAAAsE,QAAA,EACnD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAKA,oBACE7F,OAAA;IAAKwF,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCzF,OAAA;MAAKwF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzF,OAAA;QAAAyF,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B7F,OAAA;QAAKwF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzF,OAAA;UAAKwF,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BzF,OAAA;YACEwF,SAAS,EAAC,aAAa;YACvBW,KAAK,EAAEhF,cAAe;YACtBiF,QAAQ,EAAGC,CAAC,IAAKrE,kBAAkB,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAV,QAAA,gBAEpDzF,OAAA;cAAQmG,KAAK,EAAC,OAAO;cAAAV,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7F,OAAA;cAAQmG,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN7F,OAAA;UACEwF,SAAS,EAAC,gCAAgC;UAC1CU,OAAO,EAAEA,CAAA,KAAMlE,kBAAkB,CAACb,cAAc,CAAE;UAClDoF,QAAQ,EAAExF,OAAQ;UAAA0E,QAAA,gBAElBzF,OAAA;YAAGwF,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAC3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7F,OAAA;UACEwF,SAAS,EAAC,iBAAiB;UAC3BU,OAAO,EAAEjE,kBAAmB;UAC5BsE,QAAQ,EAAElF,aAAa,IAAIN,OAAQ;UAAA0E,QAAA,EAElCpE,aAAa,gBACZrB,OAAA,CAAAE,SAAA;YAAAuF,QAAA,gBACEzF,OAAA;cAAKwF,SAAS,EAAC,uCAAuC;cAACS,IAAI,EAAC,QAAQ;cAAAR,QAAA,eAClEzF,OAAA;gBAAMwF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,0BAER;UAAA,eAAE,CAAC,gBAEH7F,OAAA,CAAAE,SAAA;YAAAuF,QAAA,gBACEzF,OAAA,CAACb,SAAS;cAACqG,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAEhC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzF,OAAA;QAAKwF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAAyF,QAAA,EAAK3E,aAAa,CAACoC,WAAW,IAAI;UAAC;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzC7F,OAAA;YAAAyF,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCzF,OAAA;YAAGwF,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7F,OAAA;QAAKwF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAAyF,QAAA,EAAK3E,aAAa,CAAC0F,YAAY,IAAI;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C7F,OAAA;YAAAyF,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCzF,OAAA;YAAGwF,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7F,OAAA;QAAKwF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAAyF,QAAA,EAAK3E,aAAa,CAAC2F,cAAc,IAAI;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5C7F,OAAA;YAAAyF,QAAA,EAAG;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCzF,OAAA;YAAGwF,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7F,OAAA;QAAKwF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAAyF,QAAA,EAAK3E,aAAa,CAAC4F,WAAW,IAAI;UAAC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzC7F,OAAA;YAAAyF,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCzF,OAAA;YAAGwF,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BzF,OAAA;QAAKwF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzF,OAAA;UAAAyF,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B7F,OAAA;UAAKwF,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzF,OAAA;YAAKwF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzF,OAAA;cAAQwF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClE7F,OAAA;cAAQwF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClE7F,OAAA;cAAQwF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzD7F,OAAA;cAAQwF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7F,OAAA;QAAKwF,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxB,EAAAnF,qBAAA,GAAAQ,aAAa,CAAC6F,WAAW,cAAArG,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BsG,MAAM,cAAArG,sBAAA,uBAAjCA,sBAAA,CAAmCsG,MAAM,IAAG,CAAC,gBAC5C7G,OAAA,CAACpB,IAAI;UACHiC,IAAI,EAAEC,aAAa,CAAC6F,WAAY;UAChCG,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACPC,MAAM,EAAE;gBACNC,QAAQ,EAAE;cACZ;YACF,CAAC;YACDC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,KAAK;gBAClBC,IAAI,EAAE;kBACJC,UAAU,EAAE;gBACd,CAAC;gBACDC,KAAK,EAAE;kBACLC,QAAQ,EAAGvB,KAAK,IAAKL,aAAa,CAACK,KAAK;gBAC1C;cACF,CAAC;cACDwB,CAAC,EAAE;gBACDJ,IAAI,EAAE;kBACJK,OAAO,EAAE;gBACX;cACF;YACF;UACF;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF7F,OAAA,CAACsF,eAAe;UAACC,IAAI,EAAC,aAAa;UAACF,OAAO,EAAC;QAA2B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC1E;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBzF,OAAA;QAAKwF,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCzF,OAAA;UAAKwF,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BzF,OAAA;YAAAyF,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAAjF,qBAAA,GAAAM,aAAa,CAAC2B,qBAAqB,cAAAjC,qBAAA,wBAAAC,sBAAA,GAAnCD,qBAAA,CAAqCoG,MAAM,cAAAnG,sBAAA,uBAA3CA,sBAAA,CAA6CoG,MAAM,IAAG,CAAC,gBACtD7G,OAAA,CAACjB,QAAQ;YACP8B,IAAI,EAAEC,aAAa,CAAC2B,qBAAsB;YAC1CqE,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE,QAAQ;kBAClBP,MAAM,EAAE;oBACNiB,cAAc,EAAE,SAAAA,CAASC,KAAK,EAAE;sBAC9B,MAAMjH,IAAI,GAAGiH,KAAK,CAACjH,IAAI;sBACvB,IAAIA,IAAI,CAAC+F,MAAM,CAACC,MAAM,IAAIhG,IAAI,CAACkH,QAAQ,CAAClB,MAAM,EAAE;wBAC9C,MAAMmB,OAAO,GAAGnH,IAAI,CAACkH,QAAQ,CAAC,CAAC,CAAC;wBAChC,MAAME,KAAK,GAAGD,OAAO,CAACnH,IAAI,CAACqH,MAAM,CAAC,CAACC,GAAG,EAAEhC,KAAK,KAAKgC,GAAG,GAAGhC,KAAK,EAAE,CAAC,CAAC;wBACjE,OAAOtF,IAAI,CAAC+F,MAAM,CAACjE,GAAG,CAAC,CAACyF,KAAK,EAAEC,CAAC,KAAK;0BAAA,IAAAC,oBAAA;0BACnC,MAAMnC,KAAK,GAAG6B,OAAO,CAACnH,IAAI,CAACwH,CAAC,CAAC;0BAC7B,MAAME,UAAU,GAAG,CAAEpC,KAAK,GAAG8B,KAAK,GAAI,GAAG,EAAE9E,OAAO,CAAC,CAAC,CAAC;0BACrD,OAAO;4BACLM,IAAI,EAAE,GAAG2E,KAAK,KAAKjC,KAAK,KAAKoC,UAAU,IAAI;4BAC3CC,SAAS,EAAER,OAAO,CAACS,eAAe,CAACJ,CAAC,CAAC;4BACrCK,WAAW,EAAE,EAAAJ,oBAAA,GAAAN,OAAO,CAACW,WAAW,cAAAL,oBAAA,uBAAnBA,oBAAA,CAAsBD,CAAC,CAAC,KAAI,MAAM;4BAC/CO,SAAS,EAAE,CAAC;4BACZC,MAAM,EAAE,KAAK;4BACbhG,KAAK,EAAEwF;0BACT,CAAC;wBACH,CAAC,CAAC;sBACJ;sBACA,OAAO,EAAE;oBACX;kBACF;gBACF,CAAC;gBACDS,OAAO,EAAE;kBACPC,SAAS,EAAE;oBACTX,KAAK,EAAE,SAAAA,CAASY,OAAO,EAAE;sBACvB,MAAMZ,KAAK,GAAGY,OAAO,CAACZ,KAAK,IAAI,EAAE;sBACjC,MAAMjC,KAAK,GAAG6C,OAAO,CAACC,MAAM;sBAC5B,MAAMhB,KAAK,GAAGe,OAAO,CAAChB,OAAO,CAACnH,IAAI,CAACqH,MAAM,CAAC,CAACC,GAAG,EAAEe,GAAG,KAAKf,GAAG,GAAGe,GAAG,EAAE,CAAC,CAAC;sBACrE,MAAMX,UAAU,GAAG,CAAEpC,KAAK,GAAG8B,KAAK,GAAI,GAAG,EAAE9E,OAAO,CAAC,CAAC,CAAC;sBACrD,OAAO,GAAGiF,KAAK,KAAKjC,KAAK,eAAeoC,UAAU,IAAI;oBACxD;kBACF;gBACF;cACF,CAAC;cACDY,MAAM,EAAE;YACV;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF7F,OAAA,CAACsF,eAAe;YAACC,IAAI,EAAC,cAAc;YAACF,OAAO,EAAC;UAAyB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACzE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7F,OAAA;QAAKwF,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCzF,OAAA;UAAKwF,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BzF,OAAA;YAAAyF,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAA/E,qBAAA,GAAAI,aAAa,CAACsI,iBAAiB,cAAA1I,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCkG,MAAM,cAAAjG,sBAAA,uBAAvCA,sBAAA,CAAyCkG,MAAM,IAAG,CAAC,gBAClD7G,OAAA,CAAClB,GAAG;YACF+B,IAAI,EAAEC,aAAa,CAACsI,iBAAkB;YACtCtC,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE,QAAQ;kBAClBP,MAAM,EAAE;oBACNiB,cAAc,EAAE,SAAAA,CAASC,KAAK,EAAE;sBAC9B,MAAMjH,IAAI,GAAGiH,KAAK,CAACjH,IAAI;sBACvB,IAAIA,IAAI,CAAC+F,MAAM,CAACC,MAAM,IAAIhG,IAAI,CAACkH,QAAQ,CAAClB,MAAM,EAAE;wBAC9C,MAAMmB,OAAO,GAAGnH,IAAI,CAACkH,QAAQ,CAAC,CAAC,CAAC;wBAChC,MAAME,KAAK,GAAGD,OAAO,CAACnH,IAAI,CAACqH,MAAM,CAAC,CAACC,GAAG,EAAEhC,KAAK,KAAKgC,GAAG,GAAGhC,KAAK,EAAE,CAAC,CAAC;wBACjE,OAAOtF,IAAI,CAAC+F,MAAM,CAACjE,GAAG,CAAC,CAACyF,KAAK,EAAEC,CAAC,KAAK;0BAAA,IAAAgB,qBAAA;0BACnC,MAAMlD,KAAK,GAAG6B,OAAO,CAACnH,IAAI,CAACwH,CAAC,CAAC;0BAC7B,MAAME,UAAU,GAAG,CAAEpC,KAAK,GAAG8B,KAAK,GAAI,GAAG,EAAE9E,OAAO,CAAC,CAAC,CAAC;0BACrD,OAAO;4BACLM,IAAI,EAAE,GAAG2E,KAAK,KAAKjC,KAAK,KAAKoC,UAAU,IAAI;4BAC3CC,SAAS,EAAER,OAAO,CAACS,eAAe,CAACJ,CAAC,CAAC;4BACrCK,WAAW,EAAE,EAAAW,qBAAA,GAAArB,OAAO,CAACW,WAAW,cAAAU,qBAAA,uBAAnBA,qBAAA,CAAsBhB,CAAC,CAAC,KAAI,MAAM;4BAC/CO,SAAS,EAAE,CAAC;4BACZC,MAAM,EAAE,KAAK;4BACbhG,KAAK,EAAEwF;0BACT,CAAC;wBACH,CAAC,CAAC;sBACJ;sBACA,OAAO,EAAE;oBACX;kBACF;gBACF,CAAC;gBACDS,OAAO,EAAE;kBACPC,SAAS,EAAE;oBACTX,KAAK,EAAE,SAAAA,CAASY,OAAO,EAAE;sBACvB,MAAMZ,KAAK,GAAGY,OAAO,CAACZ,KAAK,IAAI,EAAE;sBACjC,MAAMjC,KAAK,GAAG6C,OAAO,CAACC,MAAM;sBAC5B,MAAMhB,KAAK,GAAGe,OAAO,CAAChB,OAAO,CAACnH,IAAI,CAACqH,MAAM,CAAC,CAACC,GAAG,EAAEe,GAAG,KAAKf,GAAG,GAAGe,GAAG,EAAE,CAAC,CAAC;sBACrE,MAAMX,UAAU,GAAG,CAAEpC,KAAK,GAAG8B,KAAK,GAAI,GAAG,EAAE9E,OAAO,CAAC,CAAC,CAAC;sBACrD,OAAO,GAAGiF,KAAK,KAAKjC,KAAK,eAAeoC,UAAU,IAAI;oBACxD;kBACF;gBACF;cACF,CAAC;cACDe,OAAO,EAAEA,CAACC,CAAC,EAAEC,cAAc,EAAE1B,KAAK,KAAK;gBACrC,IAAI0B,cAAc,CAAC3C,MAAM,GAAG,CAAC,EAAE;kBAC7B,MAAM4C,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC,CAAC3G,KAAK;kBACzC,MAAMmF,OAAO,GAAGF,KAAK,CAACjH,IAAI,CAACkH,QAAQ,CAAC,CAAC,CAAC;kBACtC,MAAME,KAAK,GAAGD,OAAO,CAACnH,IAAI,CAACqH,MAAM,CAAC,CAACC,GAAG,EAAEe,GAAG,KAAKf,GAAG,GAAGe,GAAG,EAAE,CAAC,CAAC;kBAC7D,MAAM/C,KAAK,GAAG6B,OAAO,CAACnH,IAAI,CAAC4I,SAAS,CAAC;kBACrC,MAAMlB,UAAU,GAAG,CAAEpC,KAAK,GAAG8B,KAAK,GAAI,GAAG,EAAE9E,OAAO,CAAC,CAAC,CAAC;kBACrD,MAAMiF,KAAK,GAAGN,KAAK,CAACjH,IAAI,CAAC+F,MAAM,CAAC6C,SAAS,CAAC;;kBAE1C;kBACA3B,KAAK,CAAChB,OAAO,CAACG,OAAO,CAACyC,UAAU,GAAG;oBACjC9B,OAAO,EAAE,IAAI;oBACbnE,IAAI,EAAE,GAAG8E,UAAU,GAAG;oBACtBoB,OAAO,EAAEvB,KAAK;oBACdjC,KAAK,EAAEA;kBACT,CAAC;kBACD2B,KAAK,CAAC8B,MAAM,CAAC,MAAM,CAAC;gBACtB,CAAC,MAAM;kBACL;kBACA9B,KAAK,CAAChB,OAAO,CAACG,OAAO,CAACyC,UAAU,GAAG;oBACjC9B,OAAO,EAAE,IAAI;oBACbnE,IAAI,EAAE,OAAO;oBACbkG,OAAO,EAAE,iBAAiB;oBAC1BxD,KAAK,EAAE;kBACT,CAAC;kBACD2B,KAAK,CAAC8B,MAAM,CAAC,MAAM,CAAC;gBACtB;cACF;YACF,CAAE;YACF3C,OAAO,EAAE,CAAC;cACR4C,EAAE,EAAE,eAAe;cACnBC,UAAU,EAAGhC,KAAK,IAAK;gBACrB,MAAM;kBAAEiC,GAAG;kBAAEC,KAAK;kBAAEhE;gBAAO,CAAC,GAAG8B,KAAK;gBACpC,MAAM4B,UAAU,GAAG5B,KAAK,CAAChB,OAAO,CAACG,OAAO,CAACyC,UAAU,IAAI;kBACrD9B,OAAO,EAAE,IAAI;kBACbnE,IAAI,EAAE,OAAO;kBACbkG,OAAO,EAAE,iBAAiB;kBAC1BxD,KAAK,EAAE;gBACT,CAAC;gBAED,IAAIuD,UAAU,CAAC9B,OAAO,EAAE;kBACtBmC,GAAG,CAACE,IAAI,CAAC,CAAC;kBACVF,GAAG,CAACG,SAAS,GAAG,QAAQ;kBACxBH,GAAG,CAACI,YAAY,GAAG,QAAQ;kBAE3B,MAAMC,OAAO,GAAGJ,KAAK,GAAG,CAAC;kBACzB,MAAMK,OAAO,GAAGrE,MAAM,GAAG,CAAC;;kBAE1B;kBACA+D,GAAG,CAAC/E,IAAI,GAAG,iBAAiB;kBAC5B+E,GAAG,CAACvB,SAAS,GAAG,MAAM;kBACtBuB,GAAG,CAACO,QAAQ,CAACZ,UAAU,CAACjG,IAAI,EAAE2G,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC;;kBAEnD;kBACAN,GAAG,CAAC/E,IAAI,GAAG,YAAY;kBACvB+E,GAAG,CAACvB,SAAS,GAAG,MAAM;kBACtBuB,GAAG,CAACO,QAAQ,CAACZ,UAAU,CAACC,OAAO,EAAES,OAAO,EAAEC,OAAO,GAAG,EAAE,CAAC;;kBAEvD;kBACA,IAAIX,UAAU,CAACvD,KAAK,EAAE;oBACpB4D,GAAG,CAAC/E,IAAI,GAAG,YAAY;oBACvB+E,GAAG,CAACvB,SAAS,GAAG,MAAM;oBACtBuB,GAAG,CAACO,QAAQ,CAAC,GAAGZ,UAAU,CAACvD,KAAK,YAAY,EAAEiE,OAAO,EAAEC,OAAO,GAAG,EAAE,CAAC;kBACtE;kBAEAN,GAAG,CAACQ,OAAO,CAAC,CAAC;gBACf;cACF;YACF,CAAC;UAAE;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEF7F,OAAA,CAACsF,eAAe;YAACC,IAAI,EAAC,mBAAmB;YAACF,OAAO,EAAC;UAA2B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAChF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCzF,OAAA;QAAKwF,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CzF,OAAA;UAAKwF,SAAS,EAAC,qFAAqF;UAAAC,QAAA,eAClGzF,OAAA;YAAIwF,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBzF,OAAA;cAAGwF,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mDAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCzF,OAAA;YAAKwF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BzF,OAAA;cAAOwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCzF,OAAA;gBAAAyF,QAAA,eACEzF,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAAyF,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR7F,OAAA;gBAAAyF,QAAA,EACG,CAAC3E,aAAa,CAAC0J,iBAAiB,IAAI,EAAE,EAAE3D,MAAM,GAAG,CAAC,GACjD,CAAC/F,aAAa,CAAC0J,iBAAiB,IAAI,EAAE,EAAE7H,GAAG,CAAC,CAAC8H,QAAQ,EAAE5H,KAAK,kBAC1D7C,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAAyF,QAAA,EAASgF,QAAQ,CAACC;oBAAM;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgF,QAAQ,CAACxC;oBAAK;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgF,QAAQ,CAACE;oBAAM;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgF,QAAQ,CAACG;oBAAO;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAKwF,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCzF,OAAA;wBAAKwF,SAAS,EAAC,eAAe;wBAAC9B,KAAK,EAAE;0BAAEsG,KAAK,EAAE,MAAM;0BAAEhE,MAAM,EAAE;wBAAM,CAAE;wBAAAP,QAAA,eACrEzF,OAAA;0BACEwF,SAAS,EAAC,yBAAyB;0BACnC9B,KAAK,EAAE;4BAAEsG,KAAK,EAAE,GAAGS,QAAQ,CAACI,gBAAgB;0BAAI;wBAAE;0BAAAnF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACN7F,OAAA;wBAAAyF,QAAA,GAAQgF,QAAQ,CAACI,gBAAgB,EAAC,GAAC;sBAAA;wBAAAnF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,EACGgF,QAAQ,CAACI,gBAAgB,IAAI,EAAE,gBAC9B7K,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAC3C4E,QAAQ,CAACI,gBAAgB,IAAI,EAAE,gBACjC7K,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpD7F,OAAA;sBAAMwF,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAhCEhD,KAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACL,CAAC,gBAEF7F,OAAA;kBAAAyF,QAAA,eACEzF,OAAA;oBAAI8K,OAAO,EAAC,GAAG;oBAACtF,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrDzF,OAAA;sBAAGwF,SAAS,EAAC;oBAA6B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,kEAEjD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7F,OAAA;QAAKwF,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CzF,OAAA;UAAKwF,SAAS,EAAC,qFAAqF;UAAAC,QAAA,eAClGzF,OAAA;YAAIwF,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBzF,OAAA;cAAGwF,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,4DAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCzF,OAAA;YAAKwF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BzF,OAAA;cAAOwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCzF,OAAA;gBAAAyF,QAAA,eACEzF,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAAyF,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxB7F,OAAA;oBAAAyF,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR7F,OAAA;gBAAAyF,QAAA,EACG,CAAC3E,aAAa,CAACiK,iBAAiB,IAAI,EAAE,EAAElE,MAAM,GAAG,CAAC,GACjD,CAAC/F,aAAa,CAACiK,iBAAiB,IAAI,EAAE,EAAEpI,GAAG,CAAC,CAACqI,QAAQ,EAAEnI,KAAK,kBAC1D7C,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAAyF,QAAA,EAASuF,QAAQ,CAACA;oBAAQ;sBAAAtF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEuF,QAAQ,CAAC/C;oBAAK;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEuF,QAAQ,CAACL;oBAAM;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEuF,QAAQ,CAACJ;oBAAO;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAKwF,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACvC,CAAC,GAAGwF,KAAK,CAAC,CAAC,CAAC,CAAC,CAACtI,GAAG,CAAC,CAAC4G,CAAC,EAAElB,CAAC,kBACtBrI,OAAA;wBAEEwF,SAAS,EAAE,MAAM6C,CAAC,GAAG6C,IAAI,CAACC,KAAK,CAACH,QAAQ,CAACI,SAAS,IAAI,CAAC,CAAC,GAAG,cAAc,GAAG,SAAS,oBAAqB;wBAC1G1H,KAAK,EAAE;0BAAEc,QAAQ,EAAE;wBAAO;sBAAE,GAFvB6D,CAAC;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGJ,CACL,CAAC,eACF7F,OAAA;wBAAOwF,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,GAAC,EAACuF,QAAQ,CAACI,SAAS,EAAC,GAAC;sBAAA;wBAAA1F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBAAKwF,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCzF,OAAA;wBAAKwF,SAAS,EAAC,eAAe;wBAAC9B,KAAK,EAAE;0BAAEsG,KAAK,EAAE,MAAM;0BAAEhE,MAAM,EAAE;wBAAM,CAAE;wBAAAP,QAAA,eACrEzF,OAAA;0BACEwF,SAAS,EAAC,yBAAyB;0BACnC9B,KAAK,EAAE;4BAAEsG,KAAK,EAAE,GAAGgB,QAAQ,CAACH,gBAAgB;0BAAI;wBAAE;0BAAAnF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACN7F,OAAA;wBAAAyF,QAAA,GAAQuF,QAAQ,CAACH,gBAAgB,EAAC,GAAC;sBAAA;wBAAAnF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL7F,OAAA;oBAAAyF,QAAA,EACGuF,QAAQ,CAACI,SAAS,IAAI,GAAG,gBACxBpL,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAChDmF,QAAQ,CAACI,SAAS,IAAI,GAAG,gBAC3BpL,OAAA;sBAAMwF,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GACxCmF,QAAQ,CAACI,SAAS,IAAI,GAAG,gBAC3BpL,OAAA;sBAAMwF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpD7F,OAAA;sBAAMwF,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA9CEhD,KAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+CV,CACL,CAAC,gBAEF7F,OAAA;kBAAAyF,QAAA,eACEzF,OAAA;oBAAI8K,OAAO,EAAC,GAAG;oBAACtF,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrDzF,OAAA;sBAAGwF,SAAS,EAAC;oBAA8B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uEAElD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxF,EAAA,CA7sBID,aAAa;EAAA,QACApB,WAAW,EACoBC,WAAW;AAAA;AAAAoM,EAAA,GAFvDjL,aAAa;AA+sBnB,eAAeA,aAAa;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}