{"ast": null, "code": "const BankInfoActions = {\n  SAVE_BANK_INFO: \"SAVE_BANK_INFO\",\n  UPDATE_BANK_INFO: \"UPDATE_BANK_INFO\",\n  DELETE_BANK_INFO: \"DELETE_BANK_INFO\",\n  SET_SHOW_FORM: \"SET_SHOW_FORM\"\n};\nexport default BankInfoActions;", "map": {"version": 3, "names": ["BankInfoActions", "SAVE_BANK_INFO", "UPDATE_BANK_INFO", "DELETE_BANK_INFO", "SET_SHOW_FORM"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/bankInfo/actions.js"], "sourcesContent": ["const BankInfoActions = {\r\n  SAVE_BANK_INFO: \"SAVE_BANK_INFO\",\r\n  UPDATE_BANK_INFO: \"UPDATE_BANK_INFO\", \r\n  DELETE_BANK_INFO: \"DELETE_BANK_INFO\",\r\n  SET_SHOW_FORM: \"SET_SHOW_FORM\",\r\n};\r\n\r\nexport default BankInfoActions;"], "mappings": "AAAA,MAAMA,eAAe,GAAG;EACtBC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,aAAa,EAAE;AACjB,CAAC;AAED,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}