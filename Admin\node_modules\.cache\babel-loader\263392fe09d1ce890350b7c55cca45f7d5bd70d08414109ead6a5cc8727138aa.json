{"ast": null, "code": "import { combineReducers } from 'redux';\nimport AuthReducer from './auth/reducer';\nimport SocketReducer from './socket/socketSlice';\nimport FeedbackReducer from './feedback/reducer';\nimport ReportedFeedbackReducer from \"./reportedFeedback/reducer\";\nimport messageReducer from './message/reducer';\nimport PromotionReducer from './promotion/reducer';\nimport AdminDashboardReducer from './adminDashboard/reducer';\nconst rootReducer = combineReducers({\n  Auth: AuthReducer,\n  Socket: SocketReducer,\n  Feedback: FeedbackReducer,\n  ReportedFeedback: ReportedFeedbackReducer,\n  Message: messageReducer,\n  Promotion: PromotionReducer,\n  AdminDashboard: AdminDashboardReducer\n});\nexport default rootReducer;", "map": {"version": 3, "names": ["combineReducers", "AuthReducer", "SocketReducer", "FeedbackReducer", "ReportedFeedbackReducer", "messageReducer", "PromotionReducer", "AdminDashboardReducer", "rootReducer", "<PERSON><PERSON>", "Socket", "<PERSON><PERSON><PERSON>", "ReportedFeedback", "Message", "Promotion", "AdminDashboard"], "sources": ["E:/WDP301_UROOM/Admin/src/redux/root-reducer.js"], "sourcesContent": ["import { combineReducers } from 'redux';\r\nimport AuthReducer from './auth/reducer';\r\nimport SocketReducer from './socket/socketSlice';\r\nimport FeedbackReducer from './feedback/reducer';\r\nimport ReportedFeedbackReducer from \"./reportedFeedback/reducer\";\r\nimport messageReducer from './message/reducer';\r\nimport PromotionReducer from './promotion/reducer';\r\nimport AdminDashboardReducer from './adminDashboard/reducer';\r\n\r\nconst rootReducer = combineReducers({\r\n    Auth: AuthReducer,\r\n    Socket: SocketReducer,\r\n    Feedback:FeedbackReducer,\r\n    ReportedFeedback: ReportedFeedbackReducer,\r\n    Message: messageReducer,\r\n    Promotion: PromotionReducer,\r\n    AdminDashboard: AdminDashboardReducer,\r\n});\r\n\r\nexport default rootReducer;"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,uBAAuB,MAAM,4BAA4B;AAChE,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,qBAAqB,MAAM,0BAA0B;AAE5D,MAAMC,WAAW,GAAGR,eAAe,CAAC;EAChCS,IAAI,EAAER,WAAW;EACjBS,MAAM,EAAER,aAAa;EACrBS,QAAQ,EAACR,eAAe;EACxBS,gBAAgB,EAAER,uBAAuB;EACzCS,OAAO,EAAER,cAAc;EACvBS,SAAS,EAAER,gBAAgB;EAC3BS,cAAc,EAAER;AACpB,CAAC,CAAC;AAEF,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}