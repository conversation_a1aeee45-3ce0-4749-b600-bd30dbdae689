const mongoose = require('mongoose');
const User = require('./src/models/user');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function createAdminToken() {
  try {
    await mongoose.connect(process.env.MONGODB_URI_DEVELOPMENT);
    
    // Find or create admin user
    let admin = await User.findOne({ role: 'ADMIN' });
    
    if (!admin) {
      admin = new User({
        name: 'Admin Test',
        email: '<EMAIL>',
        password: '12345678',
        role: 'ADMIN',
        isVerified: true
      });
      await admin.save();
      console.log('Created admin user');
    }
    
    // Generate token
    const token = jwt.sign(
      { userId: admin._id, role: admin.role },
      process.env.SECRET_KEY,
      { expiresIn: '24h' }
    );
    
    console.log('Admin token:', token);
    process.exit(0);
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
}

createAdminToken();
