{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api\";\nconst DashboardFactories = {\n  fetchDashboardMetrics: params => api.get(ApiConstants.DASHBOARD_METRICS, {\n    params\n  })\n};\nexport default DashboardFactories;", "map": {"version": 3, "names": ["ApiConstants", "api", "DashboardFactories", "fetchDashboardMetrics", "params", "get", "DASHBOARD_METRICS"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/dashboard/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api\";\r\n\r\nconst DashboardFactories = {\r\n  fetchDashboardMetrics: (params) => api.get(ApiConstants.DASHBOARD_METRICS, { params }),\r\n};\r\n\r\nexport default DashboardFactories; "], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,gBAAgB;AAEhC,MAAMC,kBAAkB,GAAG;EACzBC,qBAAqB,EAAGC,MAAM,IAAKH,GAAG,CAACI,GAAG,CAACL,YAAY,CAACM,iBAAiB,EAAE;IAAEF;EAAO,CAAC;AACvF,CAAC;AAED,eAAeF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}