{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\Chat.jsx\",\n  _s = $RefreshSig$();\nimport { FaPaperPlane } from \"react-icons/fa\";\nimport { IoArrowBack } from \"react-icons/io5\";\nimport { useState, useEffect, useRef } from \"react\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport \"bootstrap-icons/font/bootstrap-icons.css\";\nimport { useAppSelector } from \"@redux/store\";\nimport { useDispatch } from \"react-redux\";\nimport MessageActions from \"@redux/message/actions\";\nimport Utils from \"@utils/Utils\";\nimport { initializeSocket } from \"@redux/socket/socketSlice\";\nimport moment from \"moment-timezone\";\nimport \"@css/hotelHost/ChatPage.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Chat() {\n  _s();\n  var _selectedUser$image, _selectedUser$ownedHo, _selectedUser$ownedHo2;\n  const location = useLocation();\n  const navigate = useNavigate();\n  useEffect(() => {\n    var _location$state;\n    if (location !== null && location !== void 0 && (_location$state = location.state) !== null && _location$state !== void 0 && _location$state.receiver) {\n      setSelectedUser(location.state.receiver);\n\n      // Xóa dữ liệu trong location.state (tránh truyền lại receiver)\n      navigate(location.pathname, {\n        replace: true\n      });\n    }\n  }, [location, navigate]);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const dispatch = useDispatch();\n  const [users, setUsers] = useState([]);\n  const [selectedUser, setSelectedUser] = useState();\n  const [showSidebar, setShowSidebar] = useState(true);\n  const [userMessages, setUserMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState(\"\");\n  const [isReadLast, setIsReadLast] = useState(false);\n  // Ref cho container tin nhắn để scroll xuống cuối\n  const messagesEndRef = useRef(null);\n  const fetchAllUser = () => {\n    dispatch({\n      type: MessageActions.FETCH_ALL_USERS_BY_USERID,\n      payload: {\n        onSuccess: users => {\n          setUsers(users);\n          setSelectedUser(prevSelectedUser => {\n            var _users$;\n            if (!prevSelectedUser && users.length >= 1) {\n              return users[0];\n            } else if ((prevSelectedUser === null || prevSelectedUser === void 0 ? void 0 : prevSelectedUser._id) === ((_users$ = users[0]) === null || _users$ === void 0 ? void 0 : _users$._id)) {\n              return users[0];\n            }\n            return prevSelectedUser;\n          });\n        },\n        onFailed: msg => console.error(\"Không thể tải danh sách người dùng:\", msg),\n        onError: err => console.error(\"Lỗi máy chủ:\", err)\n      }\n    });\n  };\n  const fetchHistoryChat = () => {\n    if (selectedUser !== null && selectedUser !== void 0 && selectedUser._id) {\n      dispatch({\n        type: MessageActions.FETCH_HISTORY_MESSAGE,\n        payload: {\n          receiverId: selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id,\n          onSuccess: messages => {\n            setUserMessages(messages);\n            setIsReadLast(messages[messages.length - 1].isRead);\n          },\n          onFailed: msg => console.error(\"Không thể tải lịch sử tin nhắn:\", msg),\n          onError: err => console.error(\"Lỗi máy chủ:\", err)\n        }\n      });\n    }\n  };\n  const Socket = useAppSelector(state => state.Socket.socket);\n  useEffect(() => {\n    fetchAllUser();\n  }, []);\n  useEffect(() => {\n    fetchHistoryChat();\n  }, [selectedUser]);\n  useEffect(() => {\n    scrollToBottom();\n  }, [userMessages, selectedUser]);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n\n  // Join room khi selectedUser thay đổi\n  useEffect(() => {\n    console.log(\"Socket ABC:\", Socket === null || Socket === void 0 ? void 0 : Socket.id);\n    if (!Socket) return;\n    if ((Auth === null || Auth === void 0 ? void 0 : Auth._id) === -1) return;\n    if (!(selectedUser !== null && selectedUser !== void 0 && selectedUser._id)) return;\n    Socket.emit(\"join-room\", {\n      userId: Auth._id,\n      partnerId: selectedUser._id\n    });\n  }, [Socket, Auth === null || Auth === void 0 ? void 0 : Auth._id, selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id]);\n\n  // Nhận message và markAsRead\n  useEffect(() => {\n    if (!Socket || !(Auth !== null && Auth !== void 0 && Auth._id)) return;\n    const handleReceiveMessage = msg => {\n      if (Auth._id === msg.receiverId && msg.senderId === (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id)) {\n        setUserMessages(prev => [...prev, msg]);\n      }\n      fetchAllUser();\n    };\n    const handleMarkAsRead = msg => {\n      if ((selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id) == msg.senderId) {\n        setUserMessages(prevMessages => prevMessages.map(message => {\n          if (message.senderId === msg.receiverId && message.receiverId === msg.senderId && !message.isRead) {\n            return {\n              ...message,\n              isRead: true\n            };\n          }\n          return message;\n        }));\n      }\n    };\n    Socket.on(\"receive-message\", handleReceiveMessage);\n    Socket.on(\"receive-markAsRead\", handleMarkAsRead);\n    return () => {\n      Socket.off(\"receive-message\", handleReceiveMessage);\n      Socket.off(\"receive-markAsRead\", handleMarkAsRead);\n    };\n  }, [Socket, Auth === null || Auth === void 0 ? void 0 : Auth._id, selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id]);\n\n  // Gửi tin nhắn\n  const sendMessage = e => {\n    e.preventDefault();\n    if (newMessage.trim() === \"\") return;\n    const msgData = {\n      senderId: Auth._id,\n      receiverId: selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id,\n      message: newMessage,\n      timestamp: Date.now() // timestamp phía client (tuỳ bạn)\n    };\n    Socket.emit(\"send-message\", msgData);\n\n    // Thêm tin nhắn vào giao diện tạm thời\n    setUserMessages(prev => [...prev, msgData]);\n    setNewMessage(\"\");\n    fetchAllUser();\n  };\n\n  // Chọn người dùng\n  const selectUser = user => {\n    setSelectedUser(user);\n    setShowSidebar(false);\n    setUsers(prevUsers => prevUsers.map(u => u.id === user.id ? {\n      ...u,\n      unread: 0\n    } : u));\n  };\n\n  // Tìm kiếm người dùng\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const filteredUsers = users.filter(user => {\n    var _user$name;\n    return user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes(searchTerm === null || searchTerm === void 0 ? void 0 : searchTerm.toLowerCase());\n  });\n\n  // Toggle sidebar for mobile view\n  const toggleSidebar = () => {\n    setShowSidebar(!showSidebar);\n  };\n\n  // Check if we're on mobile\n  const isMobile = () => {\n    return window.innerWidth <= 768;\n  };\n\n  // Set initial sidebar state based on screen size\n  useEffect(() => {\n    const handleResize = () => {\n      if (isMobile()) {\n        setShowSidebar(true);\n      } else {\n        setShowSidebar(true);\n      }\n    };\n\n    // Set initial state\n    handleResize();\n\n    // Add event listener\n    window.addEventListener(\"resize\", handleResize);\n\n    // Clean up\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [(showSidebar || !isMobile()) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.sidebar,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.sidebarHeader,\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          style: styles.sidebarTitle,\n          children: \"H\\u1ED9p Tr\\xF2 Chuy\\u1EC7n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.search,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            style: styles.searchIcon,\n            className: \"bi bi-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            style: styles.searchInput,\n            type: \"text\",\n            placeholder: \"T\\xECm ki\\u1EBFm...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.hotelList,\n        children: filteredUsers.map(user => {\n          var _user$image, _user$ownedHotels$0$h, _user$ownedHotels$;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...styles.hotelItem,\n              ...((selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id) === (user === null || user === void 0 ? void 0 : user._id) ? styles.hotelItemActive : {})\n            },\n            onClick: () => {\n              selectUser(user);\n              if (user) {\n                Socket.emit(\"markAsRead\", {\n                  senderId: user._id,\n                  receiverId: Auth._id\n                });\n                fetchAllUser();\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.hotelAvatar,\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                style: styles.hotelAvatarImg,\n                src: (user === null || user === void 0 ? void 0 : (_user$image = user.image) === null || _user$image === void 0 ? void 0 : _user$image.url) || \"https://i.pinimg.com/736x/8f/1c/a2/8f1ca2029e2efceebd22fa05cca423d7.jpg\",\n                alt: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  ...styles.onlineIndicator,\n                  ...(user.status !== \"online\" ? styles.onlineIndicatorOnline : styles.onlineIndicatorOffline)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.hotelInfo,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.hotelName,\n                children: (user === null || user === void 0 ? void 0 : user.role) === \"OWNER\" ? (_user$ownedHotels$0$h = user === null || user === void 0 ? void 0 : (_user$ownedHotels$ = user.ownedHotels[0]) === null || _user$ownedHotels$ === void 0 ? void 0 : _user$ownedHotels$.hotelName) !== null && _user$ownedHotels$0$h !== void 0 ? _user$ownedHotels$0$h : user === null || user === void 0 ? void 0 : user.name : user === null || user === void 0 ? void 0 : user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  ...styles.hotelLastMessage,\n                  fontWeight: !user.lastMessageIsRead && !user.isLastMessageFromMe ? \"bold\" : \"normal\"\n                },\n                children: [user.isLastMessageFromMe && \"Bạn: \", user.lastMessage]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.hotelMeta,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.hotelTime,\n                children: Utils.getFormattedMessageTime(user === null || user === void 0 ? void 0 : user.lastMessageAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), !user.lastMessageIsRead && !user.isLastMessageFromMe && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.hotelUnread,\n                children: user.lastMessageIsRead\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, user._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.main,\n      children: [selectedUser ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.header,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.headerInfo,\n          children: [isMobile() && /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.backButton,\n            onClick: toggleSidebar,\n            children: /*#__PURE__*/_jsxDEV(IoArrowBack, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.avatar,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              style: styles.avatarImg,\n              src: (selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$image = selectedUser.image) === null || _selectedUser$image === void 0 ? void 0 : _selectedUser$image.url) || \"https://i.pinimg.com/736x/8f/1c/a2/8f1ca2029e2efceebd22fa05cca423d7.jpg\",\n              alt: selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.onlineIndicator,\n                ...((selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.status) !== \"online\" ? styles.onlineIndicatorOnline : styles.onlineIndicatorOffline)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              style: {\n                margin: 0\n              },\n              children: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.role) === \"OWNER\" ? (_selectedUser$ownedHo = selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$ownedHo2 = selectedUser.ownedHotels[0]) === null || _selectedUser$ownedHo2 === void 0 ? void 0 : _selectedUser$ownedHo2.hotelName) !== null && _selectedUser$ownedHo !== void 0 ? _selectedUser$ownedHo : selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.name : selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              style: {\n                color: \"#6c757d\"\n              },\n              children: \"Ho\\u1EA1t \\u0111\\u1ED9ng c\\xE1ch \\u0111\\xE2y v\\xE0i ph\\xFAt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.headerActions,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.headerActionsButton,\n            title: \"G\\u1ECDi \\u0111i\\u1EC7n\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-telephone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.headerActionsButton,\n            title: \"Th\\xF4ng tin\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-info-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.headerActionsButton,\n            title: \"T\\xF9y ch\\u1ECDn kh\\xE1c\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-three-dots-vertical\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.messages,\n        children: [userMessages && userMessages.length > 0 ? userMessages.map((message, index) => {\n          const currentTime = moment(message.timestamp);\n          const prevTime = index > 0 ? moment(userMessages[index - 1].timestamp) : null;\n          const shouldShowDivider = index === 0 || prevTime && currentTime.diff(prevTime, \"minutes\") >= 60;\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [shouldShowDivider && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.dateDivider,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: styles.dateDividerSpan,\n                children: Utils.getFormattedMessageTime(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.message,\n                ...(message.senderId === Auth._id ? styles.messageCustomer : styles.messageHotel)\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  ...styles.messageContent,\n                  ...(message.senderId === Auth._id ? styles.messageContentCustomer : styles.messageContentHotel)\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"message-container\",\n                  style: {\n                    position: \"relative\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      whiteSpace: \"pre-line\",\n                      cursor: \"default\"\n                    },\n                    className: \"message-bubble\",\n                    children: message.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: message.senderId == Auth._id ? \"hover-left\" : \"hover-right\",\n                    children: Utils.getFormattedMessageTime(message.timestamp)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 21\n              }, this)\n            }, message._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this), index === userMessages.length - 1 && message.senderId == Auth._id && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.messageStatus,\n              children: message.isRead ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\"\\u0110\\xE3 xem\", \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-check-all\",\n                  style: styles.messageStatusIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\"\\u0110\\xE3 g\\u1EEDi\", \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-check\",\n                  style: styles.messageStatusIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true);\n        }) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.emptyState,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            style: styles.emptyStateIcon,\n            className: \"bi bi-chat-dots\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.emptyStateTitle,\n            children: \"Ch\\u01B0a c\\xF3 tin nh\\u1EAFn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.emptyStateText,\n            children: \"B\\u1EAFt \\u0111\\u1EA7u cu\\u1ED9c tr\\xF2 chuy\\u1EC7n b\\u1EB1ng c\\xE1ch g\\u1EEDi tin nh\\u1EAFn \\u0111\\u1EA7u ti\\xEAn.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.input,\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          style: styles.inputForm,\n          onSubmit: sendMessage,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.inputField,\n            children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n              style: styles.inputFieldTextarea,\n              placeholder: \"Nh\\u1EADp tin nh\\u1EAFn...\",\n              value: newMessage,\n              onChange: e => {\n                if (selectedUser) {\n                  Socket.emit(\"markAsRead\", {\n                    senderId: selectedUser._id,\n                    receiverId: Auth._id\n                  });\n                  fetchAllUser();\n                }\n                setNewMessage(e.target.value);\n              },\n              onClick: () => {\n                if (selectedUser) {\n                  Socket.emit(\"markAsRead\", {\n                    senderId: selectedUser._id,\n                    receiverId: Auth._id\n                  });\n                  fetchAllUser();\n                }\n              },\n              rows: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.inputActions,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: styles.inputActionsButton,\n              type: \"button\",\n              title: \"\\u0110\\xEDnh k\\xE8m file\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-paperclip\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: styles.inputActionsButton,\n              type: \"button\",\n              title: \"G\\u1EEDi h\\xECnh \\u1EA3nh\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: styles.inputActionsButton,\n              type: \"button\",\n              title: \"G\\u1EEDi v\\u1ECB tr\\xED\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-geo-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.sendButton,\n            type: \"submit\",\n            disabled: selectedUser === undefined,\n            children: /*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n}\n\n// Styles object\n_s(Chat, \"y//AT1yHuabwySsE1td6NEXtpIg=\", false, function () {\n  return [useLocation, useNavigate, useAppSelector, useDispatch, useAppSelector];\n});\n_c = Chat;\nconst styles = {\n  body: {\n    margin: 0,\n    fontFamily: \"'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif\",\n    backgroundColor: \"#f8f9fa\",\n    overflowX: \"hidden\"\n  },\n  container: {\n    display: \"flex\",\n    height: \"94vh\",\n    backgroundColor: \"#fff\"\n  },\n  sidebar: {\n    width: \"320px\",\n    borderRight: \"1px solid #e9ecef\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    backgroundColor: \"#fff\"\n  },\n  sidebarHeader: {\n    padding: \"15px\",\n    borderBottom: \"1px solid #e9ecef\"\n  },\n  sidebarTitle: {\n    fontSize: \"1.2rem\",\n    fontWeight: 600,\n    marginBottom: \"15px\"\n  },\n  search: {\n    position: \"relative\"\n  },\n  searchInput: {\n    width: \"100%\",\n    padding: \"10px 15px 10px 40px\",\n    border: \"1px solid #e9ecef\",\n    borderRadius: \"50px\",\n    backgroundColor: \"#f8f9fa\"\n  },\n  searchInputFocus: {\n    outline: \"none\",\n    borderColor: \"#0d6efd\"\n  },\n  searchIcon: {\n    position: \"absolute\",\n    left: \"15px\",\n    top: \"50%\",\n    transform: \"translateY(-50%)\",\n    color: \"#6c757d\"\n  },\n  hotelList: {\n    flex: 1,\n    overflowY: \"auto\",\n    padding: \"10px 0\"\n  },\n  hotelItem: {\n    padding: \"12px 15px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    borderBottom: \"1px solid #f8f9fa\",\n    cursor: \"pointer\",\n    transition: \"background-color 0.2s\",\n    position: \"relative\"\n  },\n  hotelItemHover: {\n    backgroundColor: \"#f8f9fa\"\n  },\n  hotelItemActive: {\n    backgroundColor: \"#e9f5ff\",\n    borderLeft: \"3px solid #0d6efd\"\n  },\n  hotelAvatar: {\n    width: \"50px\",\n    height: \"50px\",\n    borderRadius: \"50%\",\n    marginRight: \"15px\",\n    position: \"relative\"\n  },\n  hotelAvatarImg: {\n    width: \"100%\",\n    height: \"100%\",\n    borderRadius: \"50%\",\n    objectFit: \"cover\"\n  },\n  onlineIndicator: {\n    width: \"12px\",\n    height: \"12px\",\n    borderRadius: \"50%\",\n    border: \"2px solid #fff\",\n    position: \"absolute\",\n    bottom: 0,\n    right: 0\n  },\n  onlineIndicatorOnline: {\n    backgroundColor: \"#20c997\"\n  },\n  onlineIndicatorOffline: {\n    backgroundColor: \"#6c757d\"\n  },\n  hotelInfo: {\n    flex: 1,\n    minWidth: 0\n  },\n  hotelName: {\n    fontWeight: 600,\n    marginBottom: \"3px\",\n    whiteSpace: \"nowrap\",\n    overflow: \"hidden\",\n    textOverflow: \"ellipsis\"\n  },\n  hotelLastMessage: {\n    fontSize: \"0.85rem\",\n    color: \"#6c757d\",\n    whiteSpace: \"nowrap\",\n    overflow: \"hidden\",\n    textOverflow: \"ellipsis\"\n  },\n  hotelMeta: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"flex-end\",\n    marginLeft: \"10px\"\n  },\n  hotelTime: {\n    fontSize: \"0.75rem\",\n    color: \"#6c757d\",\n    marginBottom: \"5px\"\n  },\n  hotelUnread: {\n    backgroundColor: \"#0d6efd\",\n    color: \"#fff\",\n    fontSize: \"0.7rem\",\n    fontWeight: 600,\n    width: \"20px\",\n    height: \"20px\",\n    borderRadius: \"50%\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  main: {\n    flex: 1,\n    display: \"flex\",\n    flexDirection: \"column\"\n  },\n  header: {\n    padding: \"15px 20px\",\n    borderBottom: \"1px solid #e9ecef\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"space-between\",\n    backgroundColor: \"#fff\"\n  },\n  headerInfo: {\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  avatar: {\n    width: \"50px\",\n    height: \"50px\",\n    borderRadius: \"50%\",\n    marginRight: \"15px\",\n    position: \"relative\"\n  },\n  avatarImg: {\n    width: \"100%\",\n    height: \"100%\",\n    borderRadius: \"50%\",\n    objectFit: \"cover\"\n  },\n  headerActions: {\n    display: \"flex\",\n    gap: \"15px\"\n  },\n  headerActionsButton: {\n    background: \"none\",\n    border: \"none\",\n    color: \"#6c757d\",\n    fontSize: \"1.2rem\",\n    cursor: \"pointer\",\n    transition: \"color 0.2s\"\n  },\n  headerActionsButtonHover: {\n    color: \"#0d6efd\"\n  },\n  messages: {\n    flex: 1,\n    padding: \"20px\",\n    overflowY: \"auto\",\n    backgroundColor: \"#f8f9fa\"\n  },\n  dateDivider: {\n    textAlign: \"center\",\n    margin: \"20px 0\",\n    position: \"relative\"\n  },\n  dateDividerSpan: {\n    backgroundColor: \"#f8f9fa\",\n    padding: \"0 10px\",\n    fontSize: \"0.8rem\",\n    color: \"#6c757d\",\n    position: \"relative\",\n    zIndex: 1\n  },\n  dateDividerBefore: {\n    content: \"''\",\n    position: \"absolute\",\n    top: \"50%\",\n    left: 0,\n    right: 0,\n    height: \"1px\",\n    backgroundColor: \"#e9ecef\",\n    zIndex: 0\n  },\n  message: {\n    display: \"flex\",\n    marginBottom: \"8px\"\n  },\n  messageCustomer: {\n    justifyContent: \"flex-end\"\n  },\n  messageHotel: {\n    justifyContent: \"flex-start\"\n  },\n  messageContent: {\n    maxWidth: \"70%\",\n    padding: \"12px 15px\",\n    borderRadius: \"18px\",\n    position: \"relative\"\n  },\n  messageContentCustomer: {\n    backgroundColor: \"#0d6efd\",\n    color: \"#fff\",\n    borderBottomRightRadius: \"4px\"\n  },\n  messageContentHotel: {\n    backgroundColor: \"#e9ecef\",\n    color: \"#212529\",\n    borderBottomLeftRadius: \"4px\"\n  },\n  messageTime: {\n    fontSize: \"0.7rem\",\n    marginTop: \"5px\",\n    textAlign: \"right\",\n    opacity: 0.8\n  },\n  messageStatus: {\n    display: \"flex\",\n    justifyContent: \"flex-end\",\n    fontSize: \"0.7rem\",\n    marginTop: \"2px\",\n    color: \"#6c757d\"\n  },\n  messageStatusIcon: {\n    fontSize: \"0.8rem\",\n    marginLeft: \"3px\"\n  },\n  input: {\n    padding: \"15px 20px\",\n    borderTop: \"1px solid #e9ecef\",\n    backgroundColor: \"#fff\"\n  },\n  inputForm: {\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  inputActions: {\n    display: \"flex\",\n    gap: \"10px\",\n    marginRight: \"15px\"\n  },\n  inputActionsButton: {\n    background: \"none\",\n    border: \"none\",\n    color: \"#6c757d\",\n    fontSize: \"1.2rem\",\n    cursor: \"pointer\",\n    transition: \"color 0.2s\"\n  },\n  inputActionsButtonHover: {\n    color: \"#0d6efd\"\n  },\n  inputField: {\n    flex: 1,\n    position: \"relative\"\n  },\n  inputFieldTextarea: {\n    width: \"100%\",\n    padding: \"12px 15px\",\n    border: \"1px solid #e9ecef\",\n    borderRadius: \"24px\",\n    resize: \"none\",\n    maxHeight: \"100px\",\n    backgroundColor: \"#f8f9fa\"\n  },\n  inputFieldTextareaFocus: {\n    outline: \"none\",\n    borderColor: \"#0d6efd\"\n  },\n  sendButton: {\n    marginLeft: \"15px\",\n    width: \"45px\",\n    height: \"45px\",\n    borderRadius: \"50%\",\n    backgroundColor: \"#0d6efd\",\n    color: \"#fff\",\n    border: \"none\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    cursor: \"pointer\",\n    transition: \"background-color 0.2s\"\n  },\n  sendButtonHover: {\n    backgroundColor: \"#0b5ed7\"\n  },\n  sendButtonIcon: {\n    fontSize: \"1.2rem\"\n  },\n  emptyState: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    height: \"100%\",\n    padding: \"20px\",\n    textAlign: \"center\",\n    color: \"#6c757d\"\n  },\n  emptyStateIcon: {\n    fontSize: \"4rem\",\n    marginBottom: \"20px\",\n    color: \"#e9ecef\"\n  },\n  emptyStateTitle: {\n    fontSize: \"1.5rem\",\n    marginBottom: \"10px\"\n  },\n  emptyStateText: {\n    maxWidth: \"400px\"\n  },\n  backButton: {\n    background: \"none\",\n    border: \"none\",\n    color: \"#6c757d\",\n    fontSize: \"1.5rem\",\n    cursor: \"pointer\",\n    marginRight: \"10px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  }\n  // Media queries would be handled in CSS or with conditional styling in React\n};\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["FaPaperPlane", "IoArrowBack", "useState", "useEffect", "useRef", "useLocation", "useNavigate", "useAppSelector", "useDispatch", "MessageActions", "Utils", "initializeSocket", "moment", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Cha<PERSON>", "_s", "_selectedUser$image", "_selectedUser$ownedHo", "_selectedUser$ownedHo2", "location", "navigate", "_location$state", "state", "receiver", "setSelectedUser", "pathname", "replace", "<PERSON><PERSON>", "dispatch", "users", "setUsers", "selected<PERSON>ser", "showSidebar", "setShowSidebar", "userMessages", "setUserMessages", "newMessage", "setNewMessage", "isReadLast", "setIsReadLast", "messagesEndRef", "fetchAllUser", "type", "FETCH_ALL_USERS_BY_USERID", "payload", "onSuccess", "prevSelectedUser", "_users$", "length", "_id", "onFailed", "msg", "console", "error", "onError", "err", "fetchHistoryChat", "FETCH_HISTORY_MESSAGE", "receiverId", "messages", "isRead", "Socket", "socket", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "log", "id", "emit", "userId", "partnerId", "handleReceiveMessage", "senderId", "prev", "handleMarkAsRead", "prevMessages", "map", "message", "on", "off", "sendMessage", "e", "preventDefault", "trim", "msgData", "timestamp", "Date", "now", "selectUser", "user", "prevUsers", "u", "unread", "searchTerm", "setSearchTerm", "filteredUsers", "filter", "_user$name", "name", "toLowerCase", "includes", "toggleSidebar", "isMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "style", "styles", "container", "children", "sidebar", "sidebarHeader", "sidebarTitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "search", "searchIcon", "className", "searchInput", "placeholder", "value", "onChange", "target", "hotelList", "_user$image", "_user$ownedHotels$0$h", "_user$ownedHotels$", "hotelItem", "hotelItemActive", "onClick", "hotelAvatar", "hotelAvatarImg", "src", "image", "url", "alt", "onlineIndicator", "status", "onlineIndicatorOnline", "onlineIndicatorOffline", "hotelInfo", "hotelName", "role", "ownedHotels", "hotelLastMessage", "fontWeight", "lastMessageIsRead", "isLastMessageFromMe", "lastMessage", "hotelMeta", "hotelTime", "getFormattedMessageTime", "lastMessageAt", "hotelUnread", "main", "header", "headerInfo", "backButton", "avatar", "avatarImg", "margin", "color", "headerActions", "headerActionsButton", "title", "index", "currentTime", "prevTime", "shouldShowDivider", "diff", "dateDivider", "dateDividerSpan", "messageCustomer", "messageHotel", "messageContent", "messageContentCustomer", "messageContentHotel", "position", "whiteSpace", "cursor", "messageStatus", "messageStatusIcon", "emptyState", "emptyStateIcon", "emptyStateTitle", "emptyStateText", "ref", "input", "inputForm", "onSubmit", "inputField", "inputFieldTextarea", "rows", "inputActions", "inputActionsButton", "sendButton", "disabled", "undefined", "_c", "body", "fontFamily", "backgroundColor", "overflowX", "display", "height", "width", "borderRight", "flexDirection", "padding", "borderBottom", "fontSize", "marginBottom", "border", "borderRadius", "searchInputFocus", "outline", "borderColor", "left", "top", "transform", "flex", "overflowY", "alignItems", "transition", "hotelItemHover", "borderLeft", "marginRight", "objectFit", "bottom", "right", "min<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "marginLeft", "justifyContent", "gap", "background", "headerActionsButtonHover", "textAlign", "zIndex", "dateDividerBefore", "content", "max<PERSON><PERSON><PERSON>", "borderBottomRightRadius", "borderBottomLeftRadius", "messageTime", "marginTop", "opacity", "borderTop", "inputActionsButtonHover", "resize", "maxHeight", "inputFieldTextareaFocus", "sendButtonHover", "sendButtonIcon", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/Chat.jsx"], "sourcesContent": ["import { FaPaperPlane } from \"react-icons/fa\";\r\nimport { IoArrowBack } from \"react-icons/io5\";\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { useLocation, useNavigate } from \"react-router-dom\";\r\nimport \"bootstrap-icons/font/bootstrap-icons.css\";\r\nimport { useAppSelector } from \"@redux/store\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport MessageActions from \"@redux/message/actions\";\r\nimport Utils from \"@utils/Utils\";\r\nimport { initializeSocket } from \"@redux/socket/socketSlice\";\r\nimport moment from \"moment-timezone\";\r\nimport \"@css/hotelHost/ChatPage.css\";\r\n\r\nfunction Chat() {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  useEffect(() => {\r\n    if (location?.state?.receiver) {\r\n      setSelectedUser(location.state.receiver);\r\n\r\n      // Xóa dữ liệu trong location.state (tr<PERSON>h t<PERSON>yền lại receiver)\r\n      navigate(location.pathname, { replace: true });\r\n    }\r\n  }, [location, navigate]);\r\n\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const dispatch = useDispatch();\r\n  const [users, setUsers] = useState([]);\r\n  const [selectedUser, setSelectedUser] = useState();\r\n  const [showSidebar, setShowSidebar] = useState(true);\r\n  const [userMessages, setUserMessages] = useState([]);\r\n  const [newMessage, setNewMessage] = useState(\"\");\r\n  const [isReadLast, setIsReadLast] = useState(false);\r\n  // Ref cho container tin nhắn để scroll xuống cuối\r\n  const messagesEndRef = useRef(null);\r\n\r\n  const fetchAllUser = () => {\r\n    dispatch({\r\n      type: MessageActions.FETCH_ALL_USERS_BY_USERID,\r\n      payload: {\r\n        onSuccess: (users) => {\r\n          setUsers(users);\r\n          setSelectedUser((prevSelectedUser) => {\r\n            if (!prevSelectedUser && users.length >= 1) {\r\n              return users[0];\r\n            } else if (prevSelectedUser?._id === users[0]?._id) {\r\n              return users[0];\r\n            }\r\n            return prevSelectedUser;\r\n          });\r\n        },\r\n        onFailed: (msg) => console.error(\"Không thể tải danh sách người dùng:\", msg),\r\n        onError: (err) => console.error(\"Lỗi máy chủ:\", err),\r\n      },\r\n    });\r\n  };\r\n\r\n  const fetchHistoryChat = () => {\r\n    if (selectedUser?._id) {\r\n      dispatch({\r\n        type: MessageActions.FETCH_HISTORY_MESSAGE,\r\n        payload: {\r\n          receiverId: selectedUser?._id,\r\n          onSuccess: (messages) => {\r\n            setUserMessages(messages);\r\n            setIsReadLast(messages[messages.length - 1].isRead);\r\n          },\r\n          onFailed: (msg) => console.error(\"Không thể tải lịch sử tin nhắn:\", msg),\r\n          onError: (err) => console.error(\"Lỗi máy chủ:\", err),\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n  const Socket = useAppSelector((state) => state.Socket.socket);\r\n\r\n  useEffect(() => {\r\n    fetchAllUser();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchHistoryChat();\r\n  }, [selectedUser]);\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [userMessages, selectedUser]);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  // Join room khi selectedUser thay đổi\r\n  useEffect(() => {\r\n    console.log(\"Socket ABC:\", Socket?.id);\r\n    if (!Socket) return;\r\n    if (Auth?._id === -1) return;\r\n    if (!selectedUser?._id) return;\r\n    \r\n    Socket.emit(\"join-room\", {\r\n      userId: Auth._id,\r\n      partnerId: selectedUser._id,\r\n    });\r\n  }, [Socket, Auth?._id, selectedUser?._id]);\r\n\r\n  // Nhận message và markAsRead\r\n  useEffect(() => {\r\n    if (!Socket || !Auth?._id) return;\r\n\r\n    const handleReceiveMessage = (msg) => {\r\n      if (Auth._id === msg.receiverId && msg.senderId === selectedUser?._id) {\r\n        setUserMessages((prev) => [...prev, msg]);\r\n      }\r\n      fetchAllUser();\r\n    };\r\n\r\n    const handleMarkAsRead = (msg) => {\r\n      if (selectedUser?._id == msg.senderId) {\r\n        setUserMessages((prevMessages) =>\r\n          prevMessages.map((message) => {\r\n            if (\r\n              message.senderId === msg.receiverId &&\r\n              message.receiverId === msg.senderId &&\r\n              !message.isRead\r\n            ) {\r\n              return { ...message, isRead: true };\r\n            }\r\n            return message;\r\n          })\r\n        );\r\n      }\r\n    };\r\n\r\n    Socket.on(\"receive-message\", handleReceiveMessage);\r\n    Socket.on(\"receive-markAsRead\", handleMarkAsRead);\r\n\r\n    return () => {\r\n      Socket.off(\"receive-message\", handleReceiveMessage);\r\n      Socket.off(\"receive-markAsRead\", handleMarkAsRead);\r\n    };\r\n  }, [Socket, Auth?._id, selectedUser?._id]);\r\n\r\n  // Gửi tin nhắn\r\n  const sendMessage = (e) => {\r\n    e.preventDefault();\r\n    if (newMessage.trim() === \"\") return;\r\n\r\n    const msgData = {\r\n      senderId: Auth._id,\r\n      receiverId: selectedUser?._id,\r\n      message: newMessage,\r\n      timestamp: Date.now(), // timestamp phía client (tuỳ bạn)\r\n    };\r\n\r\n    Socket.emit(\"send-message\", msgData);\r\n\r\n    // Thêm tin nhắn vào giao diện tạm thời\r\n    setUserMessages((prev) => [...prev, msgData]);\r\n    setNewMessage(\"\");\r\n    fetchAllUser();\r\n  };\r\n\r\n  // Chọn người dùng\r\n  const selectUser = (user) => {\r\n    setSelectedUser(user);\r\n    setShowSidebar(false);\r\n    setUsers((prevUsers) =>\r\n      prevUsers.map((u) => (u.id === user.id ? { ...u, unread: 0 } : u))\r\n    );\r\n  };\r\n\r\n  // Tìm kiếm người dùng\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const filteredUsers = users.filter((user) =>\r\n    user?.name?.toLowerCase().includes(searchTerm?.toLowerCase())\r\n  );\r\n\r\n  // Toggle sidebar for mobile view\r\n  const toggleSidebar = () => {\r\n    setShowSidebar(!showSidebar);\r\n  };\r\n\r\n  // Check if we're on mobile\r\n  const isMobile = () => {\r\n    return window.innerWidth <= 768;\r\n  };\r\n\r\n  // Set initial sidebar state based on screen size\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (isMobile()) {\r\n        setShowSidebar(true);\r\n      } else {\r\n        setShowSidebar(true);\r\n      }\r\n    };\r\n\r\n    // Set initial state\r\n    handleResize();\r\n\r\n    // Add event listener\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    // Clean up\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  return (\r\n    <div style={styles.container}>\r\n      {/* Sidebar - Danh sách người dùng */}\r\n      {(showSidebar || !isMobile()) && (\r\n        <div style={styles.sidebar}>\r\n          <div style={styles.sidebarHeader}>\r\n            <h5 style={styles.sidebarTitle}>Hộp Trò Chuyện</h5>\r\n            <div style={styles.search}>\r\n              <i style={styles.searchIcon} className=\"bi bi-search\"></i>\r\n              <input\r\n                style={styles.searchInput}\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n          <div style={styles.hotelList}>\r\n            {filteredUsers.map((user) => {\r\n              return (\r\n                <div\r\n                  key={user._id}\r\n                  style={{\r\n                    ...styles.hotelItem,\r\n                    ...(selectedUser?._id === user?._id\r\n                      ? styles.hotelItemActive\r\n                      : {}),\r\n                  }}\r\n                  onClick={() => {\r\n                    selectUser(user);\r\n                    if (user) {\r\n                      Socket.emit(\"markAsRead\", {\r\n                        senderId: user._id,\r\n                        receiverId: Auth._id,\r\n                      });\r\n                      fetchAllUser();\r\n                    }\r\n                  }}\r\n                >\r\n                  <div style={styles.hotelAvatar}>\r\n                    <img\r\n                      style={styles.hotelAvatarImg}\r\n                      src={\r\n                        user?.image?.url ||\r\n                        \"https://i.pinimg.com/736x/8f/1c/a2/8f1ca2029e2efceebd22fa05cca423d7.jpg\"\r\n                      }\r\n                      alt={user.name}\r\n                    />\r\n                    <div\r\n                      style={{\r\n                        ...styles.onlineIndicator,\r\n                        ...(user.status !== \"online\"\r\n                          ? styles.onlineIndicatorOnline\r\n                          : styles.onlineIndicatorOffline),\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                  <div style={styles.hotelInfo}>\r\n                    <div style={styles.hotelName}>\r\n                      {user?.role === \"OWNER\"\r\n                        ? user?.ownedHotels[0]?.hotelName ?? user?.name\r\n                        : user?.name}\r\n                    </div>\r\n                    <div\r\n                      style={{\r\n                        ...styles.hotelLastMessage,\r\n                        fontWeight:\r\n                          !user.lastMessageIsRead && !user.isLastMessageFromMe\r\n                            ? \"bold\"\r\n                            : \"normal\",\r\n                      }}\r\n                    >\r\n                      {user.isLastMessageFromMe && \"Bạn: \"}\r\n                      {user.lastMessage}\r\n                    </div>\r\n                  </div>\r\n                  <div style={styles.hotelMeta}>\r\n                    <div style={styles.hotelTime}>\r\n                      {Utils.getFormattedMessageTime(user?.lastMessageAt)}\r\n                    </div>\r\n                    {!user.lastMessageIsRead && !user.isLastMessageFromMe && (\r\n                      <div style={styles.hotelUnread}>\r\n                        {user.lastMessageIsRead}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Chat Area */}\r\n      <div style={styles.main}>\r\n        {/* Header - Thông tin người dùng */}\r\n        {selectedUser ? (\r\n          <div style={styles.header}>\r\n            <div style={styles.headerInfo}>\r\n              {isMobile() && (\r\n                <button style={styles.backButton} onClick={toggleSidebar}>\r\n                  <IoArrowBack />\r\n                </button>\r\n              )}\r\n              <div style={styles.avatar}>\r\n                <img\r\n                  style={styles.avatarImg}\r\n                  src={\r\n                    selectedUser?.image?.url ||\r\n                    \"https://i.pinimg.com/736x/8f/1c/a2/8f1ca2029e2efceebd22fa05cca423d7.jpg\"\r\n                  }\r\n                  alt={selectedUser?.name}\r\n                />\r\n                <div\r\n                  style={{\r\n                    ...styles.onlineIndicator,\r\n                    ...(selectedUser?.status !== \"online\"\r\n                      ? styles.onlineIndicatorOnline\r\n                      : styles.onlineIndicatorOffline),\r\n                  }}\r\n                ></div>\r\n              </div>\r\n              <div>\r\n                <h5 style={{ margin: 0 }}>\r\n                  {selectedUser?.role === \"OWNER\"\r\n                    ? selectedUser?.ownedHotels[0]?.hotelName ??\r\n                      selectedUser?.name\r\n                    : selectedUser?.name}\r\n                </h5>\r\n                <small style={{ color: \"#6c757d\" }}>\r\n                  Hoạt động cách đây vài phút\r\n                </small>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.headerActions}>\r\n              <button style={styles.headerActionsButton} title=\"Gọi điện\">\r\n                <i className=\"bi bi-telephone\"></i>\r\n              </button>\r\n              <button style={styles.headerActionsButton} title=\"Thông tin\">\r\n                <i className=\"bi bi-info-circle\"></i>\r\n              </button>\r\n              <button style={styles.headerActionsButton} title=\"Tùy chọn khác\">\r\n                <i className=\"bi bi-three-dots-vertical\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <></>\r\n        )}\r\n        {/* Khu vực tin nhắn */}\r\n        <div style={styles.messages}>\r\n          {userMessages && userMessages.length > 0 ? (\r\n            userMessages.map((message, index) => {\r\n              const currentTime = moment(message.timestamp);\r\n              const prevTime =\r\n                index > 0 ? moment(userMessages[index - 1].timestamp) : null;\r\n              const shouldShowDivider =\r\n                index === 0 ||\r\n                (prevTime && currentTime.diff(prevTime, \"minutes\") >= 60);\r\n              return (\r\n                <>\r\n                  {shouldShowDivider && (\r\n                    <div style={styles.dateDivider}>\r\n                      <span style={styles.dateDividerSpan}>\r\n                        {Utils.getFormattedMessageTime(message.timestamp)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div\r\n                    key={message._id}\r\n                    style={{\r\n                      ...styles.message,\r\n                      ...(message.senderId === Auth._id\r\n                        ? styles.messageCustomer\r\n                        : styles.messageHotel),\r\n                    }}\r\n                  >\r\n                    <div\r\n                      style={{\r\n                        ...styles.messageContent,\r\n                        ...(message.senderId === Auth._id\r\n                          ? styles.messageContentCustomer\r\n                          : styles.messageContentHotel),\r\n                      }}\r\n                    >\r\n                      <div\r\n                        className=\"message-container\"\r\n                        style={{\r\n                          position: \"relative\",\r\n                        }}\r\n                      >\r\n                        <div\r\n                          style={{\r\n                            whiteSpace: \"pre-line\",\r\n                            cursor: \"default\",\r\n                          }}\r\n                          className=\"message-bubble\"\r\n                        >\r\n                          {message.message}\r\n                        </div>\r\n\r\n                        <span\r\n                          className={\r\n                            message.senderId == Auth._id\r\n                              ? \"hover-left\"\r\n                              : \"hover-right\"\r\n                          }\r\n                        >\r\n                          {Utils.getFormattedMessageTime(message.timestamp)}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Message status (only for the last message from current user) */}\r\n                  {index === userMessages.length - 1 &&\r\n                    message.senderId == Auth._id && (\r\n                      <div style={styles.messageStatus}>\r\n                        {message.isRead ? (\r\n                          <>\r\n                            Đã xem{\" \"}\r\n                            <i\r\n                              className=\"bi bi-check-all\"\r\n                              style={styles.messageStatusIcon}\r\n                            ></i>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            Đã gửi{\" \"}\r\n                            <i\r\n                              className=\"bi bi-check\"\r\n                              style={styles.messageStatusIcon}\r\n                            ></i>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                </>\r\n              );\r\n            })\r\n          ) : (\r\n            <div style={styles.emptyState}>\r\n              <i style={styles.emptyStateIcon} className=\"bi bi-chat-dots\"></i>\r\n              <h3 style={styles.emptyStateTitle}>Chưa có tin nhắn</h3>\r\n              <p style={styles.emptyStateText}>\r\n                Bắt đầu cuộc trò chuyện bằng cách gửi tin nhắn đầu tiên.\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          <div ref={messagesEndRef} />\r\n        </div>\r\n\r\n        {/* Khu vực nhập tin nhắn */}\r\n        <div style={styles.input}>\r\n          <form style={styles.inputForm} onSubmit={sendMessage}>\r\n            <div style={styles.inputField}>\r\n              <textarea\r\n                style={styles.inputFieldTextarea}\r\n                placeholder=\"Nhập tin nhắn...\"\r\n                value={newMessage}\r\n                onChange={(e) => {\r\n                  if (selectedUser) {\r\n                    Socket.emit(\"markAsRead\", {\r\n                      senderId: selectedUser._id,\r\n                      receiverId: Auth._id,\r\n                    });\r\n                    fetchAllUser();\r\n                  }\r\n                  setNewMessage(e.target.value);\r\n                }}\r\n                onClick={() => {\r\n                  if (selectedUser) {\r\n                    Socket.emit(\"markAsRead\", {\r\n                      senderId: selectedUser._id,\r\n                      receiverId: Auth._id,\r\n                    });\r\n                    fetchAllUser();\r\n                  }\r\n                }}\r\n                rows=\"1\"\r\n              ></textarea>\r\n            </div>\r\n            <div style={styles.inputActions}>\r\n              <button\r\n                style={styles.inputActionsButton}\r\n                type=\"button\"\r\n                title=\"Đính kèm file\"\r\n              >\r\n                <i className=\"bi bi-paperclip\"></i>\r\n              </button>\r\n              <button\r\n                style={styles.inputActionsButton}\r\n                type=\"button\"\r\n                title=\"Gửi hình ảnh\"\r\n              >\r\n                <i className=\"bi bi-image\"></i>\r\n              </button>\r\n              <button\r\n                style={styles.inputActionsButton}\r\n                type=\"button\"\r\n                title=\"Gửi vị trí\"\r\n              >\r\n                <i className=\"bi bi-geo-alt\"></i>\r\n              </button>\r\n            </div>\r\n            <button\r\n              style={styles.sendButton}\r\n              type=\"submit\"\r\n              disabled={selectedUser === undefined}\r\n            >\r\n              <FaPaperPlane />\r\n            </button>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Styles object\r\nconst styles = {\r\n  body: {\r\n    margin: 0,\r\n    fontFamily:\r\n      \"'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif\",\r\n    backgroundColor: \"#f8f9fa\",\r\n    overflowX: \"hidden\",\r\n  },\r\n  container: {\r\n    display: \"flex\",\r\n    height: \"94vh\",\r\n    backgroundColor: \"#fff\",\r\n  },\r\n  sidebar: {\r\n    width: \"320px\",\r\n    borderRight: \"1px solid #e9ecef\",\r\n    display: \"flex\",\r\n    flexDirection: \"column\",\r\n    backgroundColor: \"#fff\",\r\n  },\r\n  sidebarHeader: {\r\n    padding: \"15px\",\r\n    borderBottom: \"1px solid #e9ecef\",\r\n  },\r\n  sidebarTitle: {\r\n    fontSize: \"1.2rem\",\r\n    fontWeight: 600,\r\n    marginBottom: \"15px\",\r\n  },\r\n  search: {\r\n    position: \"relative\",\r\n  },\r\n  searchInput: {\r\n    width: \"100%\",\r\n    padding: \"10px 15px 10px 40px\",\r\n    border: \"1px solid #e9ecef\",\r\n    borderRadius: \"50px\",\r\n    backgroundColor: \"#f8f9fa\",\r\n  },\r\n  searchInputFocus: {\r\n    outline: \"none\",\r\n    borderColor: \"#0d6efd\",\r\n  },\r\n  searchIcon: {\r\n    position: \"absolute\",\r\n    left: \"15px\",\r\n    top: \"50%\",\r\n    transform: \"translateY(-50%)\",\r\n    color: \"#6c757d\",\r\n  },\r\n  hotelList: {\r\n    flex: 1,\r\n    overflowY: \"auto\",\r\n    padding: \"10px 0\",\r\n  },\r\n  hotelItem: {\r\n    padding: \"12px 15px\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    borderBottom: \"1px solid #f8f9fa\",\r\n    cursor: \"pointer\",\r\n    transition: \"background-color 0.2s\",\r\n    position: \"relative\",\r\n  },\r\n  hotelItemHover: {\r\n    backgroundColor: \"#f8f9fa\",\r\n  },\r\n  hotelItemActive: {\r\n    backgroundColor: \"#e9f5ff\",\r\n    borderLeft: \"3px solid #0d6efd\",\r\n  },\r\n  hotelAvatar: {\r\n    width: \"50px\",\r\n    height: \"50px\",\r\n    borderRadius: \"50%\",\r\n    marginRight: \"15px\",\r\n    position: \"relative\",\r\n  },\r\n  hotelAvatarImg: {\r\n    width: \"100%\",\r\n    height: \"100%\",\r\n    borderRadius: \"50%\",\r\n    objectFit: \"cover\",\r\n  },\r\n  onlineIndicator: {\r\n    width: \"12px\",\r\n    height: \"12px\",\r\n    borderRadius: \"50%\",\r\n    border: \"2px solid #fff\",\r\n    position: \"absolute\",\r\n    bottom: 0,\r\n    right: 0,\r\n  },\r\n  onlineIndicatorOnline: {\r\n    backgroundColor: \"#20c997\",\r\n  },\r\n  onlineIndicatorOffline: {\r\n    backgroundColor: \"#6c757d\",\r\n  },\r\n  hotelInfo: {\r\n    flex: 1,\r\n    minWidth: 0,\r\n  },\r\n  hotelName: {\r\n    fontWeight: 600,\r\n    marginBottom: \"3px\",\r\n    whiteSpace: \"nowrap\",\r\n    overflow: \"hidden\",\r\n    textOverflow: \"ellipsis\",\r\n  },\r\n  hotelLastMessage: {\r\n    fontSize: \"0.85rem\",\r\n    color: \"#6c757d\",\r\n    whiteSpace: \"nowrap\",\r\n    overflow: \"hidden\",\r\n    textOverflow: \"ellipsis\",\r\n  },\r\n  hotelMeta: {\r\n    display: \"flex\",\r\n    flexDirection: \"column\",\r\n    alignItems: \"flex-end\",\r\n    marginLeft: \"10px\",\r\n  },\r\n  hotelTime: {\r\n    fontSize: \"0.75rem\",\r\n    color: \"#6c757d\",\r\n    marginBottom: \"5px\",\r\n  },\r\n  hotelUnread: {\r\n    backgroundColor: \"#0d6efd\",\r\n    color: \"#fff\",\r\n    fontSize: \"0.7rem\",\r\n    fontWeight: 600,\r\n    width: \"20px\",\r\n    height: \"20px\",\r\n    borderRadius: \"50%\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n  },\r\n  main: {\r\n    flex: 1,\r\n    display: \"flex\",\r\n    flexDirection: \"column\",\r\n  },\r\n  header: {\r\n    padding: \"15px 20px\",\r\n    borderBottom: \"1px solid #e9ecef\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"space-between\",\r\n    backgroundColor: \"#fff\",\r\n  },\r\n  headerInfo: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n  },\r\n  avatar: {\r\n    width: \"50px\",\r\n    height: \"50px\",\r\n    borderRadius: \"50%\",\r\n    marginRight: \"15px\",\r\n    position: \"relative\",\r\n  },\r\n  avatarImg: {\r\n    width: \"100%\",\r\n    height: \"100%\",\r\n    borderRadius: \"50%\",\r\n    objectFit: \"cover\",\r\n  },\r\n  headerActions: {\r\n    display: \"flex\",\r\n    gap: \"15px\",\r\n  },\r\n  headerActionsButton: {\r\n    background: \"none\",\r\n    border: \"none\",\r\n    color: \"#6c757d\",\r\n    fontSize: \"1.2rem\",\r\n    cursor: \"pointer\",\r\n    transition: \"color 0.2s\",\r\n  },\r\n  headerActionsButtonHover: {\r\n    color: \"#0d6efd\",\r\n  },\r\n  messages: {\r\n    flex: 1,\r\n    padding: \"20px\",\r\n    overflowY: \"auto\",\r\n    backgroundColor: \"#f8f9fa\",\r\n  },\r\n  dateDivider: {\r\n    textAlign: \"center\",\r\n    margin: \"20px 0\",\r\n    position: \"relative\",\r\n  },\r\n  dateDividerSpan: {\r\n    backgroundColor: \"#f8f9fa\",\r\n    padding: \"0 10px\",\r\n    fontSize: \"0.8rem\",\r\n    color: \"#6c757d\",\r\n    position: \"relative\",\r\n    zIndex: 1,\r\n  },\r\n  dateDividerBefore: {\r\n    content: \"''\",\r\n    position: \"absolute\",\r\n    top: \"50%\",\r\n    left: 0,\r\n    right: 0,\r\n    height: \"1px\",\r\n    backgroundColor: \"#e9ecef\",\r\n    zIndex: 0,\r\n  },\r\n  message: {\r\n    display: \"flex\",\r\n    marginBottom: \"8px\",\r\n  },\r\n  messageCustomer: {\r\n    justifyContent: \"flex-end\",\r\n  },\r\n  messageHotel: {\r\n    justifyContent: \"flex-start\",\r\n  },\r\n  messageContent: {\r\n    maxWidth: \"70%\",\r\n    padding: \"12px 15px\",\r\n    borderRadius: \"18px\",\r\n    position: \"relative\",\r\n  },\r\n  messageContentCustomer: {\r\n    backgroundColor: \"#0d6efd\",\r\n    color: \"#fff\",\r\n    borderBottomRightRadius: \"4px\",\r\n  },\r\n  messageContentHotel: {\r\n    backgroundColor: \"#e9ecef\",\r\n    color: \"#212529\",\r\n    borderBottomLeftRadius: \"4px\",\r\n  },\r\n  messageTime: {\r\n    fontSize: \"0.7rem\",\r\n    marginTop: \"5px\",\r\n    textAlign: \"right\",\r\n    opacity: 0.8,\r\n  },\r\n  messageStatus: {\r\n    display: \"flex\",\r\n    justifyContent: \"flex-end\",\r\n    fontSize: \"0.7rem\",\r\n    marginTop: \"2px\",\r\n    color: \"#6c757d\",\r\n  },\r\n  messageStatusIcon: {\r\n    fontSize: \"0.8rem\",\r\n    marginLeft: \"3px\",\r\n  },\r\n  input: {\r\n    padding: \"15px 20px\",\r\n    borderTop: \"1px solid #e9ecef\",\r\n    backgroundColor: \"#fff\",\r\n  },\r\n  inputForm: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n  },\r\n  inputActions: {\r\n    display: \"flex\",\r\n    gap: \"10px\",\r\n    marginRight: \"15px\",\r\n  },\r\n  inputActionsButton: {\r\n    background: \"none\",\r\n    border: \"none\",\r\n    color: \"#6c757d\",\r\n    fontSize: \"1.2rem\",\r\n    cursor: \"pointer\",\r\n    transition: \"color 0.2s\",\r\n  },\r\n  inputActionsButtonHover: {\r\n    color: \"#0d6efd\",\r\n  },\r\n  inputField: {\r\n    flex: 1,\r\n    position: \"relative\",\r\n  },\r\n  inputFieldTextarea: {\r\n    width: \"100%\",\r\n    padding: \"12px 15px\",\r\n    border: \"1px solid #e9ecef\",\r\n    borderRadius: \"24px\",\r\n    resize: \"none\",\r\n    maxHeight: \"100px\",\r\n    backgroundColor: \"#f8f9fa\",\r\n  },\r\n  inputFieldTextareaFocus: {\r\n    outline: \"none\",\r\n    borderColor: \"#0d6efd\",\r\n  },\r\n  sendButton: {\r\n    marginLeft: \"15px\",\r\n    width: \"45px\",\r\n    height: \"45px\",\r\n    borderRadius: \"50%\",\r\n    backgroundColor: \"#0d6efd\",\r\n    color: \"#fff\",\r\n    border: \"none\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    cursor: \"pointer\",\r\n    transition: \"background-color 0.2s\",\r\n  },\r\n  sendButtonHover: {\r\n    backgroundColor: \"#0b5ed7\",\r\n  },\r\n  sendButtonIcon: {\r\n    fontSize: \"1.2rem\",\r\n  },\r\n  emptyState: {\r\n    display: \"flex\",\r\n    flexDirection: \"column\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    height: \"100%\",\r\n    padding: \"20px\",\r\n    textAlign: \"center\",\r\n    color: \"#6c757d\",\r\n  },\r\n  emptyStateIcon: {\r\n    fontSize: \"4rem\",\r\n    marginBottom: \"20px\",\r\n    color: \"#e9ecef\",\r\n  },\r\n  emptyStateTitle: {\r\n    fontSize: \"1.5rem\",\r\n    marginBottom: \"10px\",\r\n  },\r\n  emptyStateText: {\r\n    maxWidth: \"400px\",\r\n  },\r\n  backButton: {\r\n    background: \"none\",\r\n    border: \"none\",\r\n    color: \"#6c757d\",\r\n    fontSize: \"1.5rem\",\r\n    cursor: \"pointer\",\r\n    marginRight: \"10px\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n  },\r\n  // Media queries would be handled in CSS or with conditional styling in React\r\n};\r\n\r\nexport default Chat;\r\n"], "mappings": ";;AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAO,0CAA0C;AACjD,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,KAAK,MAAM,cAAc;AAChC,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACd,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9BH,SAAS,CAAC,MAAM;IAAA,IAAAqB,eAAA;IACd,IAAIF,QAAQ,aAARA,QAAQ,gBAAAE,eAAA,GAARF,QAAQ,CAAEG,KAAK,cAAAD,eAAA,eAAfA,eAAA,CAAiBE,QAAQ,EAAE;MAC7BC,eAAe,CAACL,QAAQ,CAACG,KAAK,CAACC,QAAQ,CAAC;;MAExC;MACAH,QAAQ,CAACD,QAAQ,CAACM,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IAChD;EACF,CAAC,EAAE,CAACP,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExB,MAAMO,IAAI,GAAGvB,cAAc,CAAEkB,KAAK,IAAKA,KAAK,CAACK,IAAI,CAACA,IAAI,CAAC;EACvD,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,YAAY,EAAEP,eAAe,CAAC,GAAGzB,QAAQ,CAAC,CAAC;EAClD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnD;EACA,MAAMyC,cAAc,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzBb,QAAQ,CAAC;MACPc,IAAI,EAAEpC,cAAc,CAACqC,yBAAyB;MAC9CC,OAAO,EAAE;QACPC,SAAS,EAAGhB,KAAK,IAAK;UACpBC,QAAQ,CAACD,KAAK,CAAC;UACfL,eAAe,CAAEsB,gBAAgB,IAAK;YAAA,IAAAC,OAAA;YACpC,IAAI,CAACD,gBAAgB,IAAIjB,KAAK,CAACmB,MAAM,IAAI,CAAC,EAAE;cAC1C,OAAOnB,KAAK,CAAC,CAAC,CAAC;YACjB,CAAC,MAAM,IAAI,CAAAiB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEG,GAAG,QAAAF,OAAA,GAAKlB,KAAK,CAAC,CAAC,CAAC,cAAAkB,OAAA,uBAARA,OAAA,CAAUE,GAAG,GAAE;cAClD,OAAOpB,KAAK,CAAC,CAAC,CAAC;YACjB;YACA,OAAOiB,gBAAgB;UACzB,CAAC,CAAC;QACJ,CAAC;QACDI,QAAQ,EAAGC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEF,GAAG,CAAC;QAC5EG,OAAO,EAAGC,GAAG,IAAKH,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEE,GAAG;MACrD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIzB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEkB,GAAG,EAAE;MACrBrB,QAAQ,CAAC;QACPc,IAAI,EAAEpC,cAAc,CAACmD,qBAAqB;QAC1Cb,OAAO,EAAE;UACPc,UAAU,EAAE3B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,GAAG;UAC7BJ,SAAS,EAAGc,QAAQ,IAAK;YACvBxB,eAAe,CAACwB,QAAQ,CAAC;YACzBpB,aAAa,CAACoB,QAAQ,CAACA,QAAQ,CAACX,MAAM,GAAG,CAAC,CAAC,CAACY,MAAM,CAAC;UACrD,CAAC;UACDV,QAAQ,EAAGC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEF,GAAG,CAAC;UACxEG,OAAO,EAAGC,GAAG,IAAKH,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEE,GAAG;QACrD;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAGD,MAAMM,MAAM,GAAGzD,cAAc,CAAEkB,KAAK,IAAKA,KAAK,CAACuC,MAAM,CAACC,MAAM,CAAC;EAE7D9D,SAAS,CAAC,MAAM;IACdyC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAENzC,SAAS,CAAC,MAAM;IACdwD,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACzB,YAAY,CAAC,CAAC;EAElB/B,SAAS,CAAC,MAAM;IACd+D,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC7B,YAAY,EAAEH,YAAY,CAAC,CAAC;EAEhC,MAAMgC,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAxB,cAAc,CAACyB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;;EAED;EACAnE,SAAS,CAAC,MAAM;IACdoD,OAAO,CAACgB,GAAG,CAAC,aAAa,EAAEP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,EAAE,CAAC;IACtC,IAAI,CAACR,MAAM,EAAE;IACb,IAAI,CAAAlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,GAAG,MAAK,CAAC,CAAC,EAAE;IACtB,IAAI,EAAClB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEkB,GAAG,GAAE;IAExBY,MAAM,CAACS,IAAI,CAAC,WAAW,EAAE;MACvBC,MAAM,EAAE5C,IAAI,CAACsB,GAAG;MAChBuB,SAAS,EAAEzC,YAAY,CAACkB;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACY,MAAM,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,GAAG,EAAElB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,GAAG,CAAC,CAAC;;EAE1C;EACAjD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6D,MAAM,IAAI,EAAClC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsB,GAAG,GAAE;IAE3B,MAAMwB,oBAAoB,GAAItB,GAAG,IAAK;MACpC,IAAIxB,IAAI,CAACsB,GAAG,KAAKE,GAAG,CAACO,UAAU,IAAIP,GAAG,CAACuB,QAAQ,MAAK3C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,GAAG,GAAE;QACrEd,eAAe,CAAEwC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAExB,GAAG,CAAC,CAAC;MAC3C;MACAV,YAAY,CAAC,CAAC;IAChB,CAAC;IAED,MAAMmC,gBAAgB,GAAIzB,GAAG,IAAK;MAChC,IAAI,CAAApB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,GAAG,KAAIE,GAAG,CAACuB,QAAQ,EAAE;QACrCvC,eAAe,CAAE0C,YAAY,IAC3BA,YAAY,CAACC,GAAG,CAAEC,OAAO,IAAK;UAC5B,IACEA,OAAO,CAACL,QAAQ,KAAKvB,GAAG,CAACO,UAAU,IACnCqB,OAAO,CAACrB,UAAU,KAAKP,GAAG,CAACuB,QAAQ,IACnC,CAACK,OAAO,CAACnB,MAAM,EACf;YACA,OAAO;cAAE,GAAGmB,OAAO;cAAEnB,MAAM,EAAE;YAAK,CAAC;UACrC;UACA,OAAOmB,OAAO;QAChB,CAAC,CACH,CAAC;MACH;IACF,CAAC;IAEDlB,MAAM,CAACmB,EAAE,CAAC,iBAAiB,EAAEP,oBAAoB,CAAC;IAClDZ,MAAM,CAACmB,EAAE,CAAC,oBAAoB,EAAEJ,gBAAgB,CAAC;IAEjD,OAAO,MAAM;MACXf,MAAM,CAACoB,GAAG,CAAC,iBAAiB,EAAER,oBAAoB,CAAC;MACnDZ,MAAM,CAACoB,GAAG,CAAC,oBAAoB,EAAEL,gBAAgB,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACf,MAAM,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,GAAG,EAAElB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,GAAG,CAAC,CAAC;;EAE1C;EACA,MAAMiC,WAAW,GAAIC,CAAC,IAAK;IACzBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIhD,UAAU,CAACiD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAE9B,MAAMC,OAAO,GAAG;MACdZ,QAAQ,EAAE/C,IAAI,CAACsB,GAAG;MAClBS,UAAU,EAAE3B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,GAAG;MAC7B8B,OAAO,EAAE3C,UAAU;MACnBmD,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE;IACzB,CAAC;IAED5B,MAAM,CAACS,IAAI,CAAC,cAAc,EAAEgB,OAAO,CAAC;;IAEpC;IACAnD,eAAe,CAAEwC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEW,OAAO,CAAC,CAAC;IAC7CjD,aAAa,CAAC,EAAE,CAAC;IACjBI,YAAY,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMiD,UAAU,GAAIC,IAAI,IAAK;IAC3BnE,eAAe,CAACmE,IAAI,CAAC;IACrB1D,cAAc,CAAC,KAAK,CAAC;IACrBH,QAAQ,CAAE8D,SAAS,IACjBA,SAAS,CAACd,GAAG,CAAEe,CAAC,IAAMA,CAAC,CAACxB,EAAE,KAAKsB,IAAI,CAACtB,EAAE,GAAG;MAAE,GAAGwB,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,GAAGD,CAAE,CACnE,CAAC;EACH,CAAC;;EAED;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMkG,aAAa,GAAGpE,KAAK,CAACqE,MAAM,CAAEP,IAAI;IAAA,IAAAQ,UAAA;IAAA,OACtCR,IAAI,aAAJA,IAAI,wBAAAQ,UAAA,GAAJR,IAAI,CAAES,IAAI,cAAAD,UAAA,uBAAVA,UAAA,CAAYE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEM,WAAW,CAAC,CAAC,CAAC;EAAA,CAC/D,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BtE,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMwE,QAAQ,GAAGA,CAAA,KAAM;IACrB,OAAOC,MAAM,CAACC,UAAU,IAAI,GAAG;EACjC,CAAC;;EAED;EACA1G,SAAS,CAAC,MAAM;IACd,MAAM2G,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIH,QAAQ,CAAC,CAAC,EAAE;QACdvE,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACLA,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC;;IAED;IACA0E,YAAY,CAAC,CAAC;;IAEd;IACAF,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;;IAE/C;IACA,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhG,OAAA;IAAKmG,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAE1B,CAACjF,WAAW,IAAI,CAACwE,QAAQ,CAAC,CAAC,kBAC1B7F,OAAA;MAAKmG,KAAK,EAAEC,MAAM,CAACG,OAAQ;MAAAD,QAAA,gBACzBtG,OAAA;QAAKmG,KAAK,EAAEC,MAAM,CAACI,aAAc;QAAAF,QAAA,gBAC/BtG,OAAA;UAAImG,KAAK,EAAEC,MAAM,CAACK,YAAa;UAAAH,QAAA,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnD7G,OAAA;UAAKmG,KAAK,EAAEC,MAAM,CAACU,MAAO;UAAAR,QAAA,gBACxBtG,OAAA;YAAGmG,KAAK,EAAEC,MAAM,CAACW,UAAW;YAACC,SAAS,EAAC;UAAc;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1D7G,OAAA;YACEmG,KAAK,EAAEC,MAAM,CAACa,WAAY;YAC1BlF,IAAI,EAAC,MAAM;YACXmF,WAAW,EAAC,qBAAa;YACzBC,KAAK,EAAE/B,UAAW;YAClBgC,QAAQ,EAAG5C,CAAC,IAAKa,aAAa,CAACb,CAAC,CAAC6C,MAAM,CAACF,KAAK;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7G,OAAA;QAAKmG,KAAK,EAAEC,MAAM,CAACkB,SAAU;QAAAhB,QAAA,EAC1BhB,aAAa,CAACnB,GAAG,CAAEa,IAAI,IAAK;UAAA,IAAAuC,WAAA,EAAAC,qBAAA,EAAAC,kBAAA;UAC3B,oBACEzH,OAAA;YAEEmG,KAAK,EAAE;cACL,GAAGC,MAAM,CAACsB,SAAS;cACnB,IAAI,CAAAtG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,GAAG,OAAK0C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE1C,GAAG,IAC/B8D,MAAM,CAACuB,eAAe,GACtB,CAAC,CAAC;YACR,CAAE;YACFC,OAAO,EAAEA,CAAA,KAAM;cACb7C,UAAU,CAACC,IAAI,CAAC;cAChB,IAAIA,IAAI,EAAE;gBACR9B,MAAM,CAACS,IAAI,CAAC,YAAY,EAAE;kBACxBI,QAAQ,EAAEiB,IAAI,CAAC1C,GAAG;kBAClBS,UAAU,EAAE/B,IAAI,CAACsB;gBACnB,CAAC,CAAC;gBACFR,YAAY,CAAC,CAAC;cAChB;YACF,CAAE;YAAAwE,QAAA,gBAEFtG,OAAA;cAAKmG,KAAK,EAAEC,MAAM,CAACyB,WAAY;cAAAvB,QAAA,gBAC7BtG,OAAA;gBACEmG,KAAK,EAAEC,MAAM,CAAC0B,cAAe;gBAC7BC,GAAG,EACD,CAAA/C,IAAI,aAAJA,IAAI,wBAAAuC,WAAA,GAAJvC,IAAI,CAAEgD,KAAK,cAAAT,WAAA,uBAAXA,WAAA,CAAaU,GAAG,KAChB,yEACD;gBACDC,GAAG,EAAElD,IAAI,CAACS;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACF7G,OAAA;gBACEmG,KAAK,EAAE;kBACL,GAAGC,MAAM,CAAC+B,eAAe;kBACzB,IAAInD,IAAI,CAACoD,MAAM,KAAK,QAAQ,GACxBhC,MAAM,CAACiC,qBAAqB,GAC5BjC,MAAM,CAACkC,sBAAsB;gBACnC;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7G,OAAA;cAAKmG,KAAK,EAAEC,MAAM,CAACmC,SAAU;cAAAjC,QAAA,gBAC3BtG,OAAA;gBAAKmG,KAAK,EAAEC,MAAM,CAACoC,SAAU;gBAAAlC,QAAA,EAC1B,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,IAAI,MAAK,OAAO,IAAAjB,qBAAA,GACnBxC,IAAI,aAAJA,IAAI,wBAAAyC,kBAAA,GAAJzC,IAAI,CAAE0D,WAAW,CAAC,CAAC,CAAC,cAAAjB,kBAAA,uBAApBA,kBAAA,CAAsBe,SAAS,cAAAhB,qBAAA,cAAAA,qBAAA,GAAIxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,GAC7CT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACN7G,OAAA;gBACEmG,KAAK,EAAE;kBACL,GAAGC,MAAM,CAACuC,gBAAgB;kBAC1BC,UAAU,EACR,CAAC5D,IAAI,CAAC6D,iBAAiB,IAAI,CAAC7D,IAAI,CAAC8D,mBAAmB,GAChD,MAAM,GACN;gBACR,CAAE;gBAAAxC,QAAA,GAEDtB,IAAI,CAAC8D,mBAAmB,IAAI,OAAO,EACnC9D,IAAI,CAAC+D,WAAW;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7G,OAAA;cAAKmG,KAAK,EAAEC,MAAM,CAAC4C,SAAU;cAAA1C,QAAA,gBAC3BtG,OAAA;gBAAKmG,KAAK,EAAEC,MAAM,CAAC6C,SAAU;gBAAA3C,QAAA,EAC1B1G,KAAK,CAACsJ,uBAAuB,CAAClE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,aAAa;cAAC;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACL,CAAC7B,IAAI,CAAC6D,iBAAiB,IAAI,CAAC7D,IAAI,CAAC8D,mBAAmB,iBACnD9I,OAAA;gBAAKmG,KAAK,EAAEC,MAAM,CAACgD,WAAY;gBAAA9C,QAAA,EAC5BtB,IAAI,CAAC6D;cAAiB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAhED7B,IAAI,CAAC1C,GAAG;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiEV,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7G,OAAA;MAAKmG,KAAK,EAAEC,MAAM,CAACiD,IAAK;MAAA/C,QAAA,GAErBlF,YAAY,gBACXpB,OAAA;QAAKmG,KAAK,EAAEC,MAAM,CAACkD,MAAO;QAAAhD,QAAA,gBACxBtG,OAAA;UAAKmG,KAAK,EAAEC,MAAM,CAACmD,UAAW;UAAAjD,QAAA,GAC3BT,QAAQ,CAAC,CAAC,iBACT7F,OAAA;YAAQmG,KAAK,EAAEC,MAAM,CAACoD,UAAW;YAAC5B,OAAO,EAAEhC,aAAc;YAAAU,QAAA,eACvDtG,OAAA,CAACb,WAAW;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACT,eACD7G,OAAA;YAAKmG,KAAK,EAAEC,MAAM,CAACqD,MAAO;YAAAnD,QAAA,gBACxBtG,OAAA;cACEmG,KAAK,EAAEC,MAAM,CAACsD,SAAU;cACxB3B,GAAG,EACD,CAAA3G,YAAY,aAAZA,YAAY,wBAAAf,mBAAA,GAAZe,YAAY,CAAE4G,KAAK,cAAA3H,mBAAA,uBAAnBA,mBAAA,CAAqB4H,GAAG,KACxB,yEACD;cACDC,GAAG,EAAE9G,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqE;YAAK;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACF7G,OAAA;cACEmG,KAAK,EAAE;gBACL,GAAGC,MAAM,CAAC+B,eAAe;gBACzB,IAAI,CAAA/G,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgH,MAAM,MAAK,QAAQ,GACjChC,MAAM,CAACiC,qBAAqB,GAC5BjC,MAAM,CAACkC,sBAAsB;cACnC;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7G,OAAA;YAAAsG,QAAA,gBACEtG,OAAA;cAAImG,KAAK,EAAE;gBAAEwD,MAAM,EAAE;cAAE,CAAE;cAAArD,QAAA,EACtB,CAAAlF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqH,IAAI,MAAK,OAAO,IAAAnI,qBAAA,GAC3Bc,YAAY,aAAZA,YAAY,wBAAAb,sBAAA,GAAZa,YAAY,CAAEsH,WAAW,CAAC,CAAC,CAAC,cAAAnI,sBAAA,uBAA5BA,sBAAA,CAA8BiI,SAAS,cAAAlI,qBAAA,cAAAA,qBAAA,GACvCc,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqE,IAAI,GAClBrE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqE;YAAI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACL7G,OAAA;cAAOmG,KAAK,EAAE;gBAAEyD,KAAK,EAAE;cAAU,CAAE;cAAAtD,QAAA,EAAC;YAEpC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7G,OAAA;UAAKmG,KAAK,EAAEC,MAAM,CAACyD,aAAc;UAAAvD,QAAA,gBAC/BtG,OAAA;YAAQmG,KAAK,EAAEC,MAAM,CAAC0D,mBAAoB;YAACC,KAAK,EAAC,yBAAU;YAAAzD,QAAA,eACzDtG,OAAA;cAAGgH,SAAS,EAAC;YAAiB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACT7G,OAAA;YAAQmG,KAAK,EAAEC,MAAM,CAAC0D,mBAAoB;YAACC,KAAK,EAAC,cAAW;YAAAzD,QAAA,eAC1DtG,OAAA;cAAGgH,SAAS,EAAC;YAAmB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACT7G,OAAA;YAAQmG,KAAK,EAAEC,MAAM,CAAC0D,mBAAoB;YAACC,KAAK,EAAC,0BAAe;YAAAzD,QAAA,eAC9DtG,OAAA;cAAGgH,SAAS,EAAC;YAA2B;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN7G,OAAA,CAAAE,SAAA,mBAAI,CACL,eAEDF,OAAA;QAAKmG,KAAK,EAAEC,MAAM,CAACpD,QAAS;QAAAsD,QAAA,GACzB/E,YAAY,IAAIA,YAAY,CAACc,MAAM,GAAG,CAAC,GACtCd,YAAY,CAAC4C,GAAG,CAAC,CAACC,OAAO,EAAE4F,KAAK,KAAK;UACnC,MAAMC,WAAW,GAAGnK,MAAM,CAACsE,OAAO,CAACQ,SAAS,CAAC;UAC7C,MAAMsF,QAAQ,GACZF,KAAK,GAAG,CAAC,GAAGlK,MAAM,CAACyB,YAAY,CAACyI,KAAK,GAAG,CAAC,CAAC,CAACpF,SAAS,CAAC,GAAG,IAAI;UAC9D,MAAMuF,iBAAiB,GACrBH,KAAK,KAAK,CAAC,IACVE,QAAQ,IAAID,WAAW,CAACG,IAAI,CAACF,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAG;UAC3D,oBACElK,OAAA,CAAAE,SAAA;YAAAoG,QAAA,GACG6D,iBAAiB,iBAChBnK,OAAA;cAAKmG,KAAK,EAAEC,MAAM,CAACiE,WAAY;cAAA/D,QAAA,eAC7BtG,OAAA;gBAAMmG,KAAK,EAAEC,MAAM,CAACkE,eAAgB;gBAAAhE,QAAA,EACjC1G,KAAK,CAACsJ,uBAAuB,CAAC9E,OAAO,CAACQ,SAAS;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAED7G,OAAA;cAEEmG,KAAK,EAAE;gBACL,GAAGC,MAAM,CAAChC,OAAO;gBACjB,IAAIA,OAAO,CAACL,QAAQ,KAAK/C,IAAI,CAACsB,GAAG,GAC7B8D,MAAM,CAACmE,eAAe,GACtBnE,MAAM,CAACoE,YAAY;cACzB,CAAE;cAAAlE,QAAA,eAEFtG,OAAA;gBACEmG,KAAK,EAAE;kBACL,GAAGC,MAAM,CAACqE,cAAc;kBACxB,IAAIrG,OAAO,CAACL,QAAQ,KAAK/C,IAAI,CAACsB,GAAG,GAC7B8D,MAAM,CAACsE,sBAAsB,GAC7BtE,MAAM,CAACuE,mBAAmB;gBAChC,CAAE;gBAAArE,QAAA,eAEFtG,OAAA;kBACEgH,SAAS,EAAC,mBAAmB;kBAC7Bb,KAAK,EAAE;oBACLyE,QAAQ,EAAE;kBACZ,CAAE;kBAAAtE,QAAA,gBAEFtG,OAAA;oBACEmG,KAAK,EAAE;sBACL0E,UAAU,EAAE,UAAU;sBACtBC,MAAM,EAAE;oBACV,CAAE;oBACF9D,SAAS,EAAC,gBAAgB;oBAAAV,QAAA,EAEzBlC,OAAO,CAACA;kBAAO;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eAEN7G,OAAA;oBACEgH,SAAS,EACP5C,OAAO,CAACL,QAAQ,IAAI/C,IAAI,CAACsB,GAAG,GACxB,YAAY,GACZ,aACL;oBAAAgE,QAAA,EAEA1G,KAAK,CAACsJ,uBAAuB,CAAC9E,OAAO,CAACQ,SAAS;kBAAC;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1CDzC,OAAO,CAAC9B,GAAG;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Cb,CAAC,EAGLmD,KAAK,KAAKzI,YAAY,CAACc,MAAM,GAAG,CAAC,IAChC+B,OAAO,CAACL,QAAQ,IAAI/C,IAAI,CAACsB,GAAG,iBAC1BtC,OAAA;cAAKmG,KAAK,EAAEC,MAAM,CAAC2E,aAAc;cAAAzE,QAAA,EAC9BlC,OAAO,CAACnB,MAAM,gBACbjD,OAAA,CAAAE,SAAA;gBAAAoG,QAAA,GAAE,gBACM,EAAC,GAAG,eACVtG,OAAA;kBACEgH,SAAS,EAAC,iBAAiB;kBAC3Bb,KAAK,EAAEC,MAAM,CAAC4E;gBAAkB;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA,eACL,CAAC,gBAEH7G,OAAA,CAAAE,SAAA;gBAAAoG,QAAA,GAAE,qBACM,EAAC,GAAG,eACVtG,OAAA;kBACEgH,SAAS,EAAC,aAAa;kBACvBb,KAAK,EAAEC,MAAM,CAAC4E;gBAAkB;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA,eACL;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA,eACH,CAAC;QAEP,CAAC,CAAC,gBAEF7G,OAAA;UAAKmG,KAAK,EAAEC,MAAM,CAAC6E,UAAW;UAAA3E,QAAA,gBAC5BtG,OAAA;YAAGmG,KAAK,EAAEC,MAAM,CAAC8E,cAAe;YAAClE,SAAS,EAAC;UAAiB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE7G,OAAA;YAAImG,KAAK,EAAEC,MAAM,CAAC+E,eAAgB;YAAA7E,QAAA,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD7G,OAAA;YAAGmG,KAAK,EAAEC,MAAM,CAACgF,cAAe;YAAA9E,QAAA,EAAC;UAEjC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAED7G,OAAA;UAAKqL,GAAG,EAAExJ;QAAe;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGN7G,OAAA;QAAKmG,KAAK,EAAEC,MAAM,CAACkF,KAAM;QAAAhF,QAAA,eACvBtG,OAAA;UAAMmG,KAAK,EAAEC,MAAM,CAACmF,SAAU;UAACC,QAAQ,EAAEjH,WAAY;UAAA+B,QAAA,gBACnDtG,OAAA;YAAKmG,KAAK,EAAEC,MAAM,CAACqF,UAAW;YAAAnF,QAAA,eAC5BtG,OAAA;cACEmG,KAAK,EAAEC,MAAM,CAACsF,kBAAmB;cACjCxE,WAAW,EAAC,4BAAkB;cAC9BC,KAAK,EAAE1F,UAAW;cAClB2F,QAAQ,EAAG5C,CAAC,IAAK;gBACf,IAAIpD,YAAY,EAAE;kBAChB8B,MAAM,CAACS,IAAI,CAAC,YAAY,EAAE;oBACxBI,QAAQ,EAAE3C,YAAY,CAACkB,GAAG;oBAC1BS,UAAU,EAAE/B,IAAI,CAACsB;kBACnB,CAAC,CAAC;kBACFR,YAAY,CAAC,CAAC;gBAChB;gBACAJ,aAAa,CAAC8C,CAAC,CAAC6C,MAAM,CAACF,KAAK,CAAC;cAC/B,CAAE;cACFS,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIxG,YAAY,EAAE;kBAChB8B,MAAM,CAACS,IAAI,CAAC,YAAY,EAAE;oBACxBI,QAAQ,EAAE3C,YAAY,CAACkB,GAAG;oBAC1BS,UAAU,EAAE/B,IAAI,CAACsB;kBACnB,CAAC,CAAC;kBACFR,YAAY,CAAC,CAAC;gBAChB;cACF,CAAE;cACF6J,IAAI,EAAC;YAAG;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN7G,OAAA;YAAKmG,KAAK,EAAEC,MAAM,CAACwF,YAAa;YAAAtF,QAAA,gBAC9BtG,OAAA;cACEmG,KAAK,EAAEC,MAAM,CAACyF,kBAAmB;cACjC9J,IAAI,EAAC,QAAQ;cACbgI,KAAK,EAAC,0BAAe;cAAAzD,QAAA,eAErBtG,OAAA;gBAAGgH,SAAS,EAAC;cAAiB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACT7G,OAAA;cACEmG,KAAK,EAAEC,MAAM,CAACyF,kBAAmB;cACjC9J,IAAI,EAAC,QAAQ;cACbgI,KAAK,EAAC,2BAAc;cAAAzD,QAAA,eAEpBtG,OAAA;gBAAGgH,SAAS,EAAC;cAAa;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACT7G,OAAA;cACEmG,KAAK,EAAEC,MAAM,CAACyF,kBAAmB;cACjC9J,IAAI,EAAC,QAAQ;cACbgI,KAAK,EAAC,yBAAY;cAAAzD,QAAA,eAElBtG,OAAA;gBAAGgH,SAAS,EAAC;cAAe;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7G,OAAA;YACEmG,KAAK,EAAEC,MAAM,CAAC0F,UAAW;YACzB/J,IAAI,EAAC,QAAQ;YACbgK,QAAQ,EAAE3K,YAAY,KAAK4K,SAAU;YAAA1F,QAAA,eAErCtG,OAAA,CAACd,YAAY;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAzG,EAAA,CAtgBSD,IAAI;EAAA,QACMZ,WAAW,EACXC,WAAW,EAUfC,cAAc,EACVC,WAAW,EAiDbD,cAAc;AAAA;AAAAwM,EAAA,GA9DtB9L,IAAI;AAugBb,MAAMiG,MAAM,GAAG;EACb8F,IAAI,EAAE;IACJvC,MAAM,EAAE,CAAC;IACTwC,UAAU,EACR,sGAAsG;IACxGC,eAAe,EAAE,SAAS;IAC1BC,SAAS,EAAE;EACb,CAAC;EACDhG,SAAS,EAAE;IACTiG,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,MAAM;IACdH,eAAe,EAAE;EACnB,CAAC;EACD7F,OAAO,EAAE;IACPiG,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,mBAAmB;IAChCH,OAAO,EAAE,MAAM;IACfI,aAAa,EAAE,QAAQ;IACvBN,eAAe,EAAE;EACnB,CAAC;EACD5F,aAAa,EAAE;IACbmG,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE;EAChB,CAAC;EACDnG,YAAY,EAAE;IACZoG,QAAQ,EAAE,QAAQ;IAClBjE,UAAU,EAAE,GAAG;IACfkE,YAAY,EAAE;EAChB,CAAC;EACDhG,MAAM,EAAE;IACN8D,QAAQ,EAAE;EACZ,CAAC;EACD3D,WAAW,EAAE;IACXuF,KAAK,EAAE,MAAM;IACbG,OAAO,EAAE,qBAAqB;IAC9BI,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,MAAM;IACpBZ,eAAe,EAAE;EACnB,CAAC;EACDa,gBAAgB,EAAE;IAChBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE;EACf,CAAC;EACDpG,UAAU,EAAE;IACV6D,QAAQ,EAAE,UAAU;IACpBwC,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,kBAAkB;IAC7B1D,KAAK,EAAE;EACT,CAAC;EACDtC,SAAS,EAAE;IACTiG,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,MAAM;IACjBb,OAAO,EAAE;EACX,CAAC;EACDjF,SAAS,EAAE;IACTiF,OAAO,EAAE,WAAW;IACpBL,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE,QAAQ;IACpBb,YAAY,EAAE,mBAAmB;IACjC9B,MAAM,EAAE,SAAS;IACjB4C,UAAU,EAAE,uBAAuB;IACnC9C,QAAQ,EAAE;EACZ,CAAC;EACD+C,cAAc,EAAE;IACdvB,eAAe,EAAE;EACnB,CAAC;EACDzE,eAAe,EAAE;IACfyE,eAAe,EAAE,SAAS;IAC1BwB,UAAU,EAAE;EACd,CAAC;EACD/F,WAAW,EAAE;IACX2E,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,YAAY,EAAE,KAAK;IACnBa,WAAW,EAAE,MAAM;IACnBjD,QAAQ,EAAE;EACZ,CAAC;EACD9C,cAAc,EAAE;IACd0E,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,YAAY,EAAE,KAAK;IACnBc,SAAS,EAAE;EACb,CAAC;EACD3F,eAAe,EAAE;IACfqE,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,YAAY,EAAE,KAAK;IACnBD,MAAM,EAAE,gBAAgB;IACxBnC,QAAQ,EAAE,UAAU;IACpBmD,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC;EACD3F,qBAAqB,EAAE;IACrB+D,eAAe,EAAE;EACnB,CAAC;EACD9D,sBAAsB,EAAE;IACtB8D,eAAe,EAAE;EACnB,CAAC;EACD7D,SAAS,EAAE;IACTgF,IAAI,EAAE,CAAC;IACPU,QAAQ,EAAE;EACZ,CAAC;EACDzF,SAAS,EAAE;IACTI,UAAU,EAAE,GAAG;IACfkE,YAAY,EAAE,KAAK;IACnBjC,UAAU,EAAE,QAAQ;IACpBqD,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDxF,gBAAgB,EAAE;IAChBkE,QAAQ,EAAE,SAAS;IACnBjD,KAAK,EAAE,SAAS;IAChBiB,UAAU,EAAE,QAAQ;IACpBqD,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDnF,SAAS,EAAE;IACTsD,OAAO,EAAE,MAAM;IACfI,aAAa,EAAE,QAAQ;IACvBe,UAAU,EAAE,UAAU;IACtBW,UAAU,EAAE;EACd,CAAC;EACDnF,SAAS,EAAE;IACT4D,QAAQ,EAAE,SAAS;IACnBjD,KAAK,EAAE,SAAS;IAChBkD,YAAY,EAAE;EAChB,CAAC;EACD1D,WAAW,EAAE;IACXgD,eAAe,EAAE,SAAS;IAC1BxC,KAAK,EAAE,MAAM;IACbiD,QAAQ,EAAE,QAAQ;IAClBjE,UAAU,EAAE,GAAG;IACf4D,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,YAAY,EAAE,KAAK;IACnBV,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE,QAAQ;IACpBY,cAAc,EAAE;EAClB,CAAC;EACDhF,IAAI,EAAE;IACJkE,IAAI,EAAE,CAAC;IACPjB,OAAO,EAAE,MAAM;IACfI,aAAa,EAAE;EACjB,CAAC;EACDpD,MAAM,EAAE;IACNqD,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,mBAAmB;IACjCN,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE,QAAQ;IACpBY,cAAc,EAAE,eAAe;IAC/BjC,eAAe,EAAE;EACnB,CAAC;EACD7C,UAAU,EAAE;IACV+C,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE;EACd,CAAC;EACDhE,MAAM,EAAE;IACN+C,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,YAAY,EAAE,KAAK;IACnBa,WAAW,EAAE,MAAM;IACnBjD,QAAQ,EAAE;EACZ,CAAC;EACDlB,SAAS,EAAE;IACT8C,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,YAAY,EAAE,KAAK;IACnBc,SAAS,EAAE;EACb,CAAC;EACDjE,aAAa,EAAE;IACbyC,OAAO,EAAE,MAAM;IACfgC,GAAG,EAAE;EACP,CAAC;EACDxE,mBAAmB,EAAE;IACnByE,UAAU,EAAE,MAAM;IAClBxB,MAAM,EAAE,MAAM;IACdnD,KAAK,EAAE,SAAS;IAChBiD,QAAQ,EAAE,QAAQ;IAClB/B,MAAM,EAAE,SAAS;IACjB4C,UAAU,EAAE;EACd,CAAC;EACDc,wBAAwB,EAAE;IACxB5E,KAAK,EAAE;EACT,CAAC;EACD5G,QAAQ,EAAE;IACRuK,IAAI,EAAE,CAAC;IACPZ,OAAO,EAAE,MAAM;IACfa,SAAS,EAAE,MAAM;IACjBpB,eAAe,EAAE;EACnB,CAAC;EACD/B,WAAW,EAAE;IACXoE,SAAS,EAAE,QAAQ;IACnB9E,MAAM,EAAE,QAAQ;IAChBiB,QAAQ,EAAE;EACZ,CAAC;EACDN,eAAe,EAAE;IACf8B,eAAe,EAAE,SAAS;IAC1BO,OAAO,EAAE,QAAQ;IACjBE,QAAQ,EAAE,QAAQ;IAClBjD,KAAK,EAAE,SAAS;IAChBgB,QAAQ,EAAE,UAAU;IACpB8D,MAAM,EAAE;EACV,CAAC;EACDC,iBAAiB,EAAE;IACjBC,OAAO,EAAE,IAAI;IACbhE,QAAQ,EAAE,UAAU;IACpByC,GAAG,EAAE,KAAK;IACVD,IAAI,EAAE,CAAC;IACPY,KAAK,EAAE,CAAC;IACRzB,MAAM,EAAE,KAAK;IACbH,eAAe,EAAE,SAAS;IAC1BsC,MAAM,EAAE;EACV,CAAC;EACDtK,OAAO,EAAE;IACPkI,OAAO,EAAE,MAAM;IACfQ,YAAY,EAAE;EAChB,CAAC;EACDvC,eAAe,EAAE;IACf8D,cAAc,EAAE;EAClB,CAAC;EACD7D,YAAY,EAAE;IACZ6D,cAAc,EAAE;EAClB,CAAC;EACD5D,cAAc,EAAE;IACdoE,QAAQ,EAAE,KAAK;IACflC,OAAO,EAAE,WAAW;IACpBK,YAAY,EAAE,MAAM;IACpBpC,QAAQ,EAAE;EACZ,CAAC;EACDF,sBAAsB,EAAE;IACtB0B,eAAe,EAAE,SAAS;IAC1BxC,KAAK,EAAE,MAAM;IACbkF,uBAAuB,EAAE;EAC3B,CAAC;EACDnE,mBAAmB,EAAE;IACnByB,eAAe,EAAE,SAAS;IAC1BxC,KAAK,EAAE,SAAS;IAChBmF,sBAAsB,EAAE;EAC1B,CAAC;EACDC,WAAW,EAAE;IACXnC,QAAQ,EAAE,QAAQ;IAClBoC,SAAS,EAAE,KAAK;IAChBR,SAAS,EAAE,OAAO;IAClBS,OAAO,EAAE;EACX,CAAC;EACDnE,aAAa,EAAE;IACbuB,OAAO,EAAE,MAAM;IACf+B,cAAc,EAAE,UAAU;IAC1BxB,QAAQ,EAAE,QAAQ;IAClBoC,SAAS,EAAE,KAAK;IAChBrF,KAAK,EAAE;EACT,CAAC;EACDoB,iBAAiB,EAAE;IACjB6B,QAAQ,EAAE,QAAQ;IAClBuB,UAAU,EAAE;EACd,CAAC;EACD9C,KAAK,EAAE;IACLqB,OAAO,EAAE,WAAW;IACpBwC,SAAS,EAAE,mBAAmB;IAC9B/C,eAAe,EAAE;EACnB,CAAC;EACDb,SAAS,EAAE;IACTe,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE;EACd,CAAC;EACD7B,YAAY,EAAE;IACZU,OAAO,EAAE,MAAM;IACfgC,GAAG,EAAE,MAAM;IACXT,WAAW,EAAE;EACf,CAAC;EACDhC,kBAAkB,EAAE;IAClB0C,UAAU,EAAE,MAAM;IAClBxB,MAAM,EAAE,MAAM;IACdnD,KAAK,EAAE,SAAS;IAChBiD,QAAQ,EAAE,QAAQ;IAClB/B,MAAM,EAAE,SAAS;IACjB4C,UAAU,EAAE;EACd,CAAC;EACD0B,uBAAuB,EAAE;IACvBxF,KAAK,EAAE;EACT,CAAC;EACD6B,UAAU,EAAE;IACV8B,IAAI,EAAE,CAAC;IACP3C,QAAQ,EAAE;EACZ,CAAC;EACDc,kBAAkB,EAAE;IAClBc,KAAK,EAAE,MAAM;IACbG,OAAO,EAAE,WAAW;IACpBI,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,MAAM;IACpBqC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,OAAO;IAClBlD,eAAe,EAAE;EACnB,CAAC;EACDmD,uBAAuB,EAAE;IACvBrC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE;EACf,CAAC;EACDrB,UAAU,EAAE;IACVsC,UAAU,EAAE,MAAM;IAClB5B,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,YAAY,EAAE,KAAK;IACnBZ,eAAe,EAAE,SAAS;IAC1BxC,KAAK,EAAE,MAAM;IACbmD,MAAM,EAAE,MAAM;IACdT,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE,QAAQ;IACpBY,cAAc,EAAE,QAAQ;IACxBvD,MAAM,EAAE,SAAS;IACjB4C,UAAU,EAAE;EACd,CAAC;EACD8B,eAAe,EAAE;IACfpD,eAAe,EAAE;EACnB,CAAC;EACDqD,cAAc,EAAE;IACd5C,QAAQ,EAAE;EACZ,CAAC;EACD5B,UAAU,EAAE;IACVqB,OAAO,EAAE,MAAM;IACfI,aAAa,EAAE,QAAQ;IACvBe,UAAU,EAAE,QAAQ;IACpBY,cAAc,EAAE,QAAQ;IACxB9B,MAAM,EAAE,MAAM;IACdI,OAAO,EAAE,MAAM;IACf8B,SAAS,EAAE,QAAQ;IACnB7E,KAAK,EAAE;EACT,CAAC;EACDsB,cAAc,EAAE;IACd2B,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,MAAM;IACpBlD,KAAK,EAAE;EACT,CAAC;EACDuB,eAAe,EAAE;IACf0B,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD1B,cAAc,EAAE;IACdyD,QAAQ,EAAE;EACZ,CAAC;EACDrF,UAAU,EAAE;IACV+E,UAAU,EAAE,MAAM;IAClBxB,MAAM,EAAE,MAAM;IACdnD,KAAK,EAAE,SAAS;IAChBiD,QAAQ,EAAE,QAAQ;IAClB/B,MAAM,EAAE,SAAS;IACjB+C,WAAW,EAAE,MAAM;IACnBvB,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE,QAAQ;IACpBY,cAAc,EAAE;EAClB;EACA;AACF,CAAC;AAED,eAAelO,IAAI;AAAC,IAAA8L,EAAA;AAAAyD,YAAA,CAAAzD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}