{"ast": null, "code": "import { all, call, fork, put, takeEvery } from \"redux-saga/effects\";\nimport HotelServiceActions from \"./actions\";\nimport Factories from \"./factories\";\nfunction* updateHotelService() {\n  yield takeEvery(HotelServiceActions.UPDATE_HOTEL_SERVICE, function* (action) {\n    const {\n      serviceId,\n      updateData,\n      onSuccess,\n      onFailed,\n      onError\n    } = action.payload || {};\n    if (!serviceId) {\n      onFailed === null || onFailed === void 0 ? void 0 : onFailed(\"Service ID không được để trống\");\n      return;\n    }\n    try {\n      const response = yield call(() => Factories.updateHotelService(serviceId, updateData));\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(response.data);\n        yield put({\n          type: HotelServiceActions.UPDATE_HOTEL_SERVICE_SUCCESS,\n          payload: response.data\n        });\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      const status = (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status;\n      const msg = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Có lỗi xảy ra khi cập nhật dịch vụ\";\n      if (status >= 500) {\n        onError === null || onError === void 0 ? void 0 : onError(error);\n      } else {\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(msg);\n      }\n    }\n  });\n}\nexport default function* hotelServiceSaga() {\n  yield all([fork(updateHotelService)]);\n}", "map": {"version": 3, "names": ["all", "call", "fork", "put", "takeEvery", "HotelServiceActions", "Factories", "updateHotelService", "UPDATE_HOTEL_SERVICE", "action", "serviceId", "updateData", "onSuccess", "onFailed", "onError", "payload", "response", "status", "data", "type", "UPDATE_HOTEL_SERVICE_SUCCESS", "error", "_error$response", "_error$response2", "_error$response2$data", "msg", "message", "hotelServiceSaga"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/Hotelservices/saga.js"], "sourcesContent": ["import { all, call, fork, put, takeEvery } from \"redux-saga/effects\";\r\nimport HotelServiceActions from \"./actions\";\r\nimport Factories from \"./factories\"; \r\n\r\nfunction* updateHotelService() {\r\n  yield takeEvery(HotelServiceActions.UPDATE_HOTEL_SERVICE, function* (action) {\r\n    const { serviceId, updateData, onSuccess, onFailed, onError } = action.payload || {};\r\n\r\n    if (!serviceId) {\r\n      onFailed?.(\"Service ID không được để trống\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = yield call(() =>\r\n        Factories.updateHotelService(serviceId, updateData)\r\n      );\r\n\r\n      if (response?.status === 200) {\r\n        onSuccess?.(response.data);\r\n        yield put({\r\n          type: HotelServiceActions.UPDATE_HOTEL_SERVICE_SUCCESS,\r\n          payload: response.data,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      const status = error.response?.status;\r\n      const msg = error.response?.data?.message || \"<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật dịch vụ\";\r\n\r\n      if (status >= 500) {\r\n        onError?.(error);\r\n      } else {\r\n        onFailed?.(msg);\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nexport default function* hotelServiceSaga() {\r\n  yield all([\r\n    fork(updateHotelService),\r\n    \r\n  ]);\r\n}\r\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,oBAAoB;AACpE,OAAOC,mBAAmB,MAAM,WAAW;AAC3C,OAAOC,SAAS,MAAM,aAAa;AAEnC,UAAUC,kBAAkBA,CAAA,EAAG;EAC7B,MAAMH,SAAS,CAACC,mBAAmB,CAACG,oBAAoB,EAAE,WAAWC,MAAM,EAAE;IAC3E,MAAM;MAAEC,SAAS;MAAEC,UAAU;MAAEC,SAAS;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGL,MAAM,CAACM,OAAO,IAAI,CAAC,CAAC;IAEpF,IAAI,CAACL,SAAS,EAAE;MACdG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,gCAAgC,CAAC;MAC5C;IACF;IAEA,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMf,IAAI,CAAC,MAC1BK,SAAS,CAACC,kBAAkB,CAACG,SAAS,EAAEC,UAAU,CACpD,CAAC;MAED,IAAI,CAAAK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,MAAM,MAAK,GAAG,EAAE;QAC5BL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGI,QAAQ,CAACE,IAAI,CAAC;QAC1B,MAAMf,GAAG,CAAC;UACRgB,IAAI,EAAEd,mBAAmB,CAACe,4BAA4B;UACtDL,OAAO,EAAEC,QAAQ,CAACE;QACpB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,MAAMP,MAAM,IAAAK,eAAA,GAAGD,KAAK,CAACL,QAAQ,cAAAM,eAAA,uBAAdA,eAAA,CAAgBL,MAAM;MACrC,MAAMQ,GAAG,GAAG,EAAAF,gBAAA,GAAAF,KAAK,CAACL,QAAQ,cAAAO,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBL,IAAI,cAAAM,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,oCAAoC;MAEjF,IAAIT,MAAM,IAAI,GAAG,EAAE;QACjBH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGO,KAAK,CAAC;MAClB,CAAC,MAAM;QACLR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGY,GAAG,CAAC;MACjB;IACF;EACF,CAAC,CAAC;AACJ;AAEA,eAAe,UAAUE,gBAAgBA,CAAA,EAAG;EAC1C,MAAM3B,GAAG,CAAC,CACRE,IAAI,CAACK,kBAAkB,CAAC,CAEzB,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}