{"ast": null, "code": "const RoomUnitActions = {\n  // Room actions\n  FETCH_ROOMS: 'FETCH_ROOMS',\n  FETCH_ROOMS_SUCCESS: 'FETCH_ROOMS_SUCCESS',\n  FETCH_ROOMS_FAILED: 'FETCH_ROOMS_FAILED',\n  ADD_ROOM: 'ADD_ROOM',\n  ADD_ROOM_SUCCESS: 'ADD_ROOM_SUCCESS',\n  ADD_ROOM_FAILED: 'ADD_ROOM_FAILED',\n  UPDATE_ROOM: 'UPDATE_ROOM',\n  UPDATE_ROOM_SUCCESS: 'UPDATE_ROOM_SUCCESS',\n  UPDATE_ROOM_FAILED: 'UPDATE_ROOM_FAILED',\n  DELETE_ROOM: 'DELETE_ROOM',\n  DELETE_ROOM_SUCCESS: 'DELETE_ROOM_SUCCESS',\n  DELETE_ROOM_FAILED: 'DELETE_ROOM_FAILED',\n  // Booking actions\n  FETCH_BOOKINGS: 'FET<PERSON>_BOOKINGS',\n  FETCH_BOOKINGS_SUCCESS: 'FETCH_BOOKINGS_SUCCESS',\n  FETCH_BOOKINGS_FAILED: 'FETCH_BOOKINGS_FAILED',\n  ADD_BOOKING: 'ADD_BOOKING',\n  ADD_BOOKING_SUCCESS: 'ADD_BOOKING_SUCCESS',\n  ADD_BOOKING_FAILED: 'ADD_BOOKING_FAILED',\n  UPDATE_BOOKING: 'UPDATE_BOOKING',\n  UPDATE_BOOKING_SUCCESS: 'UPDATE_BOOKING_SUCCESS',\n  UPDATE_BOOKING_FAILED: 'UPDATE_BOOKING_FAILED',\n  DELETE_BOOKING: 'DELETE_BOOKING',\n  DELETE_BOOKING_SUCCESS: 'DELETE_BOOKING_SUCCESS',\n  DELETE_BOOKING_FAILED: 'DELETE_BOOKING_FAILED',\n  // Check-in/Check-out actions\n  CHECK_IN: 'CHECK_IN',\n  CHECK_IN_SUCCESS: 'CHECK_IN_SUCCESS',\n  CHECK_IN_FAILED: 'CHECK_IN_FAILED',\n  CHECK_OUT: 'CHECK_OUT',\n  CHECK_OUT_SUCCESS: 'CHECK_OUT_SUCCESS',\n  CHECK_OUT_FAILED: 'CHECK_OUT_FAILED',\n  // Filter actions\n  SET_ROOM_FILTER: 'SET_ROOM_FILTER',\n  SET_DATE_RANGE: 'SET_DATE_RANGE',\n  // UI state actions\n  SET_LOADING: 'SET_LOADING',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR'\n};\nexport default RoomUnitActions;", "map": {"version": 3, "names": ["RoomUnitActions", "FETCH_ROOMS", "FETCH_ROOMS_SUCCESS", "FETCH_ROOMS_FAILED", "ADD_ROOM", "ADD_ROOM_SUCCESS", "ADD_ROOM_FAILED", "UPDATE_ROOM", "UPDATE_ROOM_SUCCESS", "UPDATE_ROOM_FAILED", "DELETE_ROOM", "DELETE_ROOM_SUCCESS", "DELETE_ROOM_FAILED", "FETCH_BOOKINGS", "FETCH_BOOKINGS_SUCCESS", "FETCH_BOOKINGS_FAILED", "ADD_BOOKING", "ADD_BOOKING_SUCCESS", "ADD_BOOKING_FAILED", "UPDATE_BOOKING", "UPDATE_BOOKING_SUCCESS", "UPDATE_BOOKING_FAILED", "DELETE_BOOKING", "DELETE_BOOKING_SUCCESS", "DELETE_BOOKING_FAILED", "CHECK_IN", "CHECK_IN_SUCCESS", "CHECK_IN_FAILED", "CHECK_OUT", "CHECK_OUT_SUCCESS", "CHECK_OUT_FAILED", "SET_ROOM_FILTER", "SET_DATE_RANGE", "SET_LOADING", "SET_ERROR", "CLEAR_ERROR"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/room_unit/action.js"], "sourcesContent": ["const RoomUnitActions = {\r\n  // Room actions\r\n  FETCH_ROOMS: 'FETCH_ROOMS',\r\n  FETCH_ROOMS_SUCCESS: 'FETCH_ROOMS_SUCCESS',\r\n  FETCH_ROOMS_FAILED: 'FETCH_ROOMS_FAILED',\r\n  \r\n  ADD_ROOM: 'ADD_ROOM',\r\n  ADD_ROOM_SUCCESS: 'ADD_ROOM_SUCCESS',\r\n  ADD_ROOM_FAILED: 'ADD_ROOM_FAILED',\r\n  \r\n  UPDATE_ROOM: 'UPDATE_ROOM',\r\n  UPDATE_ROOM_SUCCESS: 'UPDATE_ROOM_SUCCESS',\r\n  UPDATE_ROOM_FAILED: 'UPDATE_ROOM_FAILED',\r\n  \r\n  DELETE_ROOM: 'DELETE_ROOM',\r\n  DELETE_ROOM_SUCCESS: 'DELETE_ROOM_SUCCESS',\r\n  DELETE_ROOM_FAILED: 'DELETE_ROOM_FAILED',\r\n  \r\n  // Booking actions\r\n  FETCH_BOOKINGS: 'FET<PERSON>_BOOKINGS',\r\n  FETCH_BOOKINGS_SUCCESS: 'FETCH_BOOKINGS_SUCCESS',\r\n  FETCH_BOOKINGS_FAILED: 'FETCH_BOOKINGS_FAILED',\r\n  \r\n  ADD_BOOKING: 'ADD_BOOKING',\r\n  ADD_BOOKING_SUCCESS: 'ADD_BOOKING_SUCCESS',\r\n  ADD_BOOKING_FAILED: 'ADD_BOOKING_FAILED',\r\n  \r\n  UPDATE_BOOKING: 'UPDATE_BOOKING',\r\n  UPDATE_BOOKING_SUCCESS: 'UPDATE_BOOKING_SUCCESS',\r\n  UPDATE_BOOKING_FAILED: 'UPDATE_BOOKING_FAILED',\r\n  \r\n  DELETE_BOOKING: 'DELETE_BOOKING',\r\n  DELETE_BOOKING_SUCCESS: 'DELETE_BOOKING_SUCCESS',\r\n  DELETE_BOOKING_FAILED: 'DELETE_BOOKING_FAILED',\r\n  \r\n  // Check-in/Check-out actions\r\n  CHECK_IN: 'CHECK_IN',\r\n  CHECK_IN_SUCCESS: 'CHECK_IN_SUCCESS',\r\n  CHECK_IN_FAILED: 'CHECK_IN_FAILED',\r\n  \r\n  CHECK_OUT: 'CHECK_OUT',\r\n  CHECK_OUT_SUCCESS: 'CHECK_OUT_SUCCESS',\r\n  CHECK_OUT_FAILED: 'CHECK_OUT_FAILED',\r\n  \r\n  // Filter actions\r\n  SET_ROOM_FILTER: 'SET_ROOM_FILTER',\r\n  SET_DATE_RANGE: 'SET_DATE_RANGE',\r\n  \r\n  // UI state actions\r\n  SET_LOADING: 'SET_LOADING',\r\n  SET_ERROR: 'SET_ERROR',\r\n  CLEAR_ERROR: 'CLEAR_ERROR',\r\n};\r\n\r\nexport default RoomUnitActions;"], "mappings": "AAAA,MAAMA,eAAe,GAAG;EACtB;EACAC,WAAW,EAAE,aAAa;EAC1BC,mBAAmB,EAAE,qBAAqB;EAC1CC,kBAAkB,EAAE,oBAAoB;EAExCC,QAAQ,EAAE,UAAU;EACpBC,gBAAgB,EAAE,kBAAkB;EACpCC,eAAe,EAAE,iBAAiB;EAElCC,WAAW,EAAE,aAAa;EAC1BC,mBAAmB,EAAE,qBAAqB;EAC1CC,kBAAkB,EAAE,oBAAoB;EAExCC,WAAW,EAAE,aAAa;EAC1BC,mBAAmB,EAAE,qBAAqB;EAC1CC,kBAAkB,EAAE,oBAAoB;EAExC;EACAC,cAAc,EAAE,gBAAgB;EAChCC,sBAAsB,EAAE,wBAAwB;EAChDC,qBAAqB,EAAE,uBAAuB;EAE9CC,WAAW,EAAE,aAAa;EAC1BC,mBAAmB,EAAE,qBAAqB;EAC1CC,kBAAkB,EAAE,oBAAoB;EAExCC,cAAc,EAAE,gBAAgB;EAChCC,sBAAsB,EAAE,wBAAwB;EAChDC,qBAAqB,EAAE,uBAAuB;EAE9CC,cAAc,EAAE,gBAAgB;EAChCC,sBAAsB,EAAE,wBAAwB;EAChDC,qBAAqB,EAAE,uBAAuB;EAE9C;EACAC,QAAQ,EAAE,UAAU;EACpBC,gBAAgB,EAAE,kBAAkB;EACpCC,eAAe,EAAE,iBAAiB;EAElCC,SAAS,EAAE,WAAW;EACtBC,iBAAiB,EAAE,mBAAmB;EACtCC,gBAAgB,EAAE,kBAAkB;EAEpC;EACAC,eAAe,EAAE,iBAAiB;EAClCC,cAAc,EAAE,gBAAgB;EAEhC;EACAC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE;AACf,CAAC;AAED,eAAenC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}