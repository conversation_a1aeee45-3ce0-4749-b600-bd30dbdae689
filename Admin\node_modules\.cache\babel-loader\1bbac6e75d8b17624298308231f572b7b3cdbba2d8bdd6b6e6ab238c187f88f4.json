{"ast": null, "code": "import { FETCH_ADMIN_DASHBOARD_METRICS, FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS, FETCH_ADMIN_DASHBOARD_METRICS_FAILURE } from \"./actions\";\nconst initialState = {\n  loading: false,\n  error: null,\n  data: {\n    // Overview stats\n    totalHotels: 0,\n    activeHotels: 0,\n    pendingApprovals: 0,\n    totalCustomers: 0,\n    totalOwners: 0,\n    totalReservations: 0,\n    totalRevenue: 0,\n    pendingReports: 0,\n    // Chart data\n    revenueData: {\n      labels: [],\n      datasets: []\n    },\n    // Distribution data\n    hotelDistributionData: {\n      labels: [],\n      datasets: [{\n        data: [],\n        backgroundColor: []\n      }]\n    },\n    hotelCategoryData: {\n      labels: [],\n      datasets: [{\n        data: [],\n        backgroundColor: []\n      }]\n    },\n    // Recent activities\n    recentApprovals: [],\n    recentReports: []\n  }\n};\nconst AdminDashboardReducer = (state = initialState, action) => {\n  switch (action.type) {\n    case FETCH_ADMIN_DASHBOARD_METRICS:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        error: null,\n        data: {\n          ...state.data,\n          ...action.payload\n        }\n      };\n    case FETCH_ADMIN_DASHBOARD_METRICS_FAILURE:\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n    default:\n      return state;\n  }\n};\n_c = AdminDashboardReducer;\nexport default AdminDashboardReducer;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboardReducer\");", "map": {"version": 3, "names": ["FETCH_ADMIN_DASHBOARD_METRICS", "FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS", "FETCH_ADMIN_DASHBOARD_METRICS_FAILURE", "initialState", "loading", "error", "data", "totalHotels", "activeHotels", "pendingApprovals", "totalCustomers", "totalOwners", "totalReservations", "totalRevenue", "pendingReports", "revenueData", "labels", "datasets", "hotelDistributionData", "backgroundColor", "hotelCategoryData", "recentApprovals", "recentReports", "AdminDashboardReducer", "state", "action", "type", "payload", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/redux/adminDashboard/reducer.js"], "sourcesContent": ["import {\n  FETCH_ADMIN_DASHBOARD_METRICS,\n  FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS,\n  FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n} from \"./actions\";\n\nconst initialState = {\n  loading: false,\n  error: null,\n  data: {\n    // Overview stats\n    totalHotels: 0,\n    activeHotels: 0,\n    pendingApprovals: 0,\n    totalCustomers: 0,\n    totalOwners: 0,\n    totalReservations: 0,\n    totalRevenue: 0,\n    pendingReports: 0,\n    \n    // Chart data\n    revenueData: {\n      labels: [],\n      datasets: []\n    },\n    \n    // Distribution data\n    hotelDistributionData: {\n      labels: [],\n      datasets: [{ data: [], backgroundColor: [] }]\n    },\n    \n    hotelCategoryData: {\n      labels: [],\n      datasets: [{ data: [], backgroundColor: [] }]\n    },\n    \n    // Recent activities\n    recentApprovals: [],\n    recentReports: []\n  }\n};\n\nconst AdminDashboardReducer = (state = initialState, action) => {\n  switch (action.type) {\n    case FETCH_ADMIN_DASHBOARD_METRICS:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n\n    case FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        error: null,\n        data: {\n          ...state.data,\n          ...action.payload\n        }\n      };\n\n    case FETCH_ADMIN_DASHBOARD_METRICS_FAILURE:\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n\n    default:\n      return state;\n  }\n};\n\nexport default AdminDashboardReducer;\n"], "mappings": "AAAA,SACEA,6BAA6B,EAC7BC,qCAAqC,EACrCC,qCAAqC,QAChC,WAAW;AAElB,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;IACJ;IACAC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,CAAC;IACdC,iBAAiB,EAAE,CAAC;IACpBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IAEjB;IACAC,WAAW,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE;IACZ,CAAC;IAED;IACAC,qBAAqB,EAAE;MACrBF,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;QAAEX,IAAI,EAAE,EAAE;QAAEa,eAAe,EAAE;MAAG,CAAC;IAC9C,CAAC;IAEDC,iBAAiB,EAAE;MACjBJ,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;QAAEX,IAAI,EAAE,EAAE;QAAEa,eAAe,EAAE;MAAG,CAAC;IAC9C,CAAC;IAED;IACAE,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE;EACjB;AACF,CAAC;AAED,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,GAAGrB,YAAY,EAAEsB,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK1B,6BAA6B;MAChC,OAAO;QACL,GAAGwB,KAAK;QACRpB,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKJ,qCAAqC;MACxC,OAAO;QACL,GAAGuB,KAAK;QACRpB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE;UACJ,GAAGkB,KAAK,CAAClB,IAAI;UACb,GAAGmB,MAAM,CAACE;QACZ;MACF,CAAC;IAEH,KAAKzB,qCAAqC;MACxC,OAAO;QACL,GAAGsB,KAAK;QACRpB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEoB,MAAM,CAACE;MAChB,CAAC;IAEH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;AAACI,EAAA,GA9BIL,qBAAqB;AAgC3B,eAAeA,qBAAqB;AAAC,IAAAK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}