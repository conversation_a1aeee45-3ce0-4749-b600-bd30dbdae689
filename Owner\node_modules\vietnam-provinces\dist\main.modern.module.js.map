{"version": 3, "file": "main.modern.module.js", "sources": ["../src/main.ts"], "sourcesContent": ["import _provinces from './data/provinces.json';\nimport _districts from './data/districts.json';\nimport _wards from './data/wards.json';\n\nimport { Province, District, Ward, ProvinceDetail } from './types';\n\nexport const provinces: Province[] = _provinces;\nexport const districts: District[] = _districts;\nexport const wards: Ward[] = _wards;\n\nexport const getProvinces = (): Province[] => {\n  return provinces;\n};\n\nexport const getDistricts = (province_code?: string): District[] => {\n  return province_code\n    ? districts.filter((d) => d.province_code === province_code)\n    : districts;\n};\n\nexport const getWards = (district_code?: string): Ward[] => {\n  return district_code\n    ? wards.filter((ward) => ward.district_code === district_code)\n    : wards;\n};\n\nexport const getProvincesWithDetail = (code?: string): ProvinceDetail => {\n  const tree: any = {};\n  provinces.forEach((province: Province) => {\n    tree[province.code] = province;\n  });\n\n  districts.forEach((district) => {\n    if (!tree[district.province_code].districts) {\n      tree[district.province_code].districts = {};\n    }\n    tree[district.province_code].districts[district.code] = district;\n  });\n\n  wards.forEach((ward) => {\n    if (!tree[ward.province_code].districts[ward.district_code].wards) {\n      tree[ward.province_code].districts[ward.district_code].wards = {};\n    }\n    tree[ward.province_code].districts[ward.district_code].wards[ward.code] =\n      ward;\n  });\n\n  return code ? tree[code] : tree;\n};\n\nexport { Province, District, Ward, ProvinceDetail };\n"], "names": ["provinces", "districts", "getProvinces", "getDistricts", "province_code", "filter", "d", "getWards", "district_code", "wards", "ward", "getProvincesWithDetail", "code", "tree", "for<PERSON>ach", "province", "district"], "mappings": "IAMsBA,o6FACAC,iumrEAGTC,EAAe,WAC1B,OACDF,GAEYG,EAAe,SAACC,GAC3B,OAAoBA,EAChBH,EAAUI,OAAO,SAACC,GAAD,OAAQA,EAACF,gBAAkBA,IAC5CH,GAGOM,EAAW,SAACC,GACvB,OAAoBA,EAChBC,EAAMJ,OAAO,SAACK,UAAaA,EAACF,gBAAkBA,IAC9CC,GAGOE,EAAyB,SAACC,GACrC,IAAUC,EAAQ,GAoBlB,OAnBAb,EAAUc,QAAQ,SAACC,GACjBF,EAAKE,EAASH,MAAQG,IAGxBd,EAAUa,QAAQ,SAACE,GACZH,EAAKG,EAASZ,eAAeH,YAChCY,EAAKG,EAASZ,eAAeH,UAAY,IAE3CY,EAAKG,EAASZ,eAAeH,UAAUe,EAASJ,MAAQI,IAG1DP,EAAMK,QAAQ,SAACJ,GACRG,EAAKH,EAAKN,eAAeH,UAAUS,EAAKF,eAAeC,QAC1DI,EAAKH,EAAKN,eAAeH,UAAUS,EAAKF,eAAeC,MAAQ,IAEjEI,EAAKH,EAAKN,eAAeH,UAAUS,EAAKF,eAAeC,MAAMC,EAAKE,MAChEF,IAGGE,EAAOC,EAAKD,GAAQC"}