{"ast": null, "code": "import RoomActions from \"./actions\";\nconst initialState = {\n  rooms: [],\n  roomDetail: null,\n  loading: false,\n  error: null,\n  createRoom: {\n    // Basic Info\n    name: \"\",\n    type: \"Single Room\",\n    capacity: 1,\n    quantity: 1,\n    description: \"\",\n    bed: [],\n    facilities: [],\n    images: [],\n    price: 0\n  },\n  editRoom: null,\n  checkCreateRoom: false,\n  // List of rooms being created for hotel creation process\n  createRoomList: []\n};\nconst roomReducer = (state = initialState, action) => {\n  var _state$roomDetail, _state$roomDetail2;\n  switch (action.type) {\n    case RoomActions.FETCH_ROOM_SUCCESS:\n      return {\n        ...state,\n        rooms: action.payload,\n        error: null\n      };\n    case RoomActions.FETCH_ROOMS_SUCCESS:\n      return {\n        ...state,\n        rooms: action.payload,\n        loading: false,\n        error: null\n      };\n    case RoomActions.FETCH_ROOM_DETAIL_SUCCESS:\n      return {\n        ...state,\n        roomDetail: action.payload,\n        loading: false,\n        error: null\n      };\n    case RoomActions.CREATE_ROOM_SUCCESS:\n      return {\n        ...state,\n        rooms: [...state.rooms, action.payload],\n        loading: false,\n        error: null,\n        createRoom: initialState.createRoom,\n        checkCreateRoom: false\n      };\n    case RoomActions.UPDATE_ROOM_SUCCESS:\n      return {\n        ...state,\n        rooms: state.rooms.map(room => room._id === action.payload._id ? action.payload : room),\n        roomDetail: ((_state$roomDetail = state.roomDetail) === null || _state$roomDetail === void 0 ? void 0 : _state$roomDetail._id) === action.payload._id ? action.payload : state.roomDetail,\n        loading: false,\n        error: null\n      };\n    case RoomActions.DELETE_ROOM_SUCCESS:\n      return {\n        ...state,\n        rooms: state.rooms.filter(room => room._id !== action.payload),\n        roomDetail: ((_state$roomDetail2 = state.roomDetail) === null || _state$roomDetail2 === void 0 ? void 0 : _state$roomDetail2._id) === action.payload ? null : state.roomDetail,\n        loading: false,\n        error: null\n      };\n    case RoomActions.SAVE_ROOM_NAME_CREATE:\n      return {\n        ...state,\n        createRoom: {\n          ...state.createRoom,\n          name: action.payload.name\n        }\n      };\n    case RoomActions.SAVE_ROOM_DETAILS_CREATE:\n      return {\n        ...state,\n        createRoom: {\n          ...state.createRoom,\n          type: action.payload.type,\n          capacity: action.payload.capacity,\n          quantity: action.payload.quantity,\n          description: action.payload.description,\n          bed: action.payload.bed,\n          facilities: action.payload.facilities\n        }\n      };\n    case RoomActions.SAVE_ROOM_IMAGES_CREATE:\n      return {\n        ...state,\n        createRoom: {\n          ...state.createRoom,\n          images: action.payload.images\n        }\n      };\n    case RoomActions.SAVE_ROOM_PRICING_CREATE:\n      return {\n        ...state,\n        createRoom: {\n          ...state.createRoom,\n          price: action.payload.price\n        },\n        checkCreateRoom: true\n      };\n    case RoomActions.CLEAR_ROOM_CREATE:\n      return {\n        ...state,\n        createRoom: initialState.createRoom,\n        checkCreateRoom: false\n      };\n    case RoomActions.TOGGLE_ROOM_STATUS:\n      return {\n        ...state,\n        rooms: state.rooms.map(room => room._id === action.payload.roomId ? {\n          ...room,\n          isActive: !room.isActive\n        } : room)\n      };\n    case RoomActions.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload\n      };\n    case RoomActions.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        loading: false\n      };\n    case RoomActions.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n\n    // Edit Room Actions\n    case \"SET_EDIT_ROOM\":\n      return {\n        ...state,\n        editRoom: action.payload\n      };\n    case \"CLEAR_EDIT_ROOM\":\n      return {\n        ...state,\n        editRoom: null\n      };\n\n    // Room Create List Management Actions\n    case RoomActions.SAVE_ROOM_TO_CREATE_LIST:\n      return {\n        ...state,\n        createRoomList: [...state.createRoomList, action.payload]\n      };\n    case RoomActions.EDIT_ROOM_IN_CREATE_LIST:\n      return {\n        ...state,\n        createRoomList: state.createRoomList.map((room, index) => index === action.payload.index ? {\n          ...room,\n          ...action.payload.roomData\n        } : room)\n      };\n    case RoomActions.DELETE_ROOM_FROM_CREATE_LIST:\n      return {\n        ...state,\n        createRoomList: state.createRoomList.filter((_, index) => index !== action.payload.index)\n      };\n    case RoomActions.CLEAR_ROOM_CREATE_LIST:\n      return {\n        ...state,\n        createRoomList: []\n      };\n    default:\n      return state;\n  }\n};\nexport default roomReducer;", "map": {"version": 3, "names": ["RoomActions", "initialState", "rooms", "roomDetail", "loading", "error", "createRoom", "name", "type", "capacity", "quantity", "description", "bed", "facilities", "images", "price", "editRoom", "checkCreateRoom", "createRoomList", "roomReducer", "state", "action", "_state$roomDetail", "_state$roomDetail2", "FETCH_ROOM_SUCCESS", "payload", "FETCH_ROOMS_SUCCESS", "FETCH_ROOM_DETAIL_SUCCESS", "CREATE_ROOM_SUCCESS", "UPDATE_ROOM_SUCCESS", "map", "room", "_id", "DELETE_ROOM_SUCCESS", "filter", "SAVE_ROOM_NAME_CREATE", "SAVE_ROOM_DETAILS_CREATE", "SAVE_ROOM_IMAGES_CREATE", "SAVE_ROOM_PRICING_CREATE", "CLEAR_ROOM_CREATE", "TOGGLE_ROOM_STATUS", "roomId", "isActive", "SET_LOADING", "SET_ERROR", "CLEAR_ERROR", "SAVE_ROOM_TO_CREATE_LIST", "EDIT_ROOM_IN_CREATE_LIST", "index", "roomData", "DELETE_ROOM_FROM_CREATE_LIST", "_", "CLEAR_ROOM_CREATE_LIST"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/room/reducer.js"], "sourcesContent": ["import RoomActions from \"./actions\";\r\n\r\nconst initialState = {\r\n  rooms: [],\r\n  roomDetail: null,\r\n  loading: false,\r\n  error: null,\r\n  createRoom: {\r\n    // Basic Info\r\n    name: \"\",\r\n    type: \"Single Room\",\r\n    capacity: 1,\r\n    quantity: 1,\r\n    description: \"\",\r\n    bed: [],\r\n    facilities: [],\r\n    images: [],\r\n    price: 0,\r\n  },\r\n  editRoom: null,\r\n  checkCreateRoom: false,\r\n  // List of rooms being created for hotel creation process\r\n  createRoomList: [],\r\n};\r\n\r\nconst roomReducer = (state = initialState, action) => {\r\n  switch (action.type) {\r\n    case RoomActions.FETCH_ROOM_SUCCESS:\r\n      return {\r\n        ...state,\r\n        rooms: action.payload,\r\n        error: null, \r\n      };  \r\n    case RoomActions.FETCH_ROOMS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        rooms: action.payload,\r\n        loading: false,\r\n        error: null,\r\n      };\r\n\r\n    case RoomActions.FETCH_ROOM_DETAIL_SUCCESS:\r\n      return {\r\n        ...state,\r\n        roomDetail: action.payload,\r\n        loading: false,\r\n        error: null,\r\n      };\r\n\r\n    case RoomActions.CREATE_ROOM_SUCCESS:\r\n      return {\r\n        ...state,\r\n        rooms: [...state.rooms, action.payload],\r\n        loading: false,\r\n        error: null,\r\n        createRoom: initialState.createRoom,\r\n        checkCreateRoom: false,\r\n      };\r\n\r\n    case RoomActions.UPDATE_ROOM_SUCCESS:\r\n      return {\r\n        ...state,\r\n        rooms: state.rooms.map((room) =>\r\n          room._id === action.payload._id ? action.payload : room\r\n        ),\r\n        roomDetail:\r\n          state.roomDetail?._id === action.payload._id\r\n            ? action.payload\r\n            : state.roomDetail,\r\n        loading: false,\r\n        error: null,\r\n      };\r\n\r\n    case RoomActions.DELETE_ROOM_SUCCESS:\r\n      return {\r\n        ...state,\r\n        rooms: state.rooms.filter((room) => room._id !== action.payload),\r\n        roomDetail:\r\n          state.roomDetail?._id === action.payload ? null : state.roomDetail,\r\n        loading: false,\r\n        error: null,\r\n      };\r\n\r\n    case RoomActions.SAVE_ROOM_NAME_CREATE:\r\n      return {\r\n        ...state,\r\n        createRoom: {\r\n          ...state.createRoom,\r\n          name: action.payload.name,\r\n        },\r\n      };\r\n\r\n    case RoomActions.SAVE_ROOM_DETAILS_CREATE:\r\n      return {\r\n        ...state,\r\n        createRoom: {\r\n          ...state.createRoom,\r\n          type: action.payload.type,\r\n          capacity: action.payload.capacity,\r\n          quantity: action.payload.quantity,\r\n          description: action.payload.description,\r\n          bed: action.payload.bed,\r\n          facilities: action.payload.facilities,\r\n        },\r\n      };\r\n\r\n    case RoomActions.SAVE_ROOM_IMAGES_CREATE:\r\n      return {\r\n        ...state,\r\n        createRoom: {\r\n          ...state.createRoom,\r\n          images: action.payload.images,\r\n        },\r\n      };\r\n\r\n    case RoomActions.SAVE_ROOM_PRICING_CREATE:\r\n      return {\r\n        ...state,\r\n        createRoom: {\r\n          ...state.createRoom,\r\n          price: action.payload.price,\r\n        },\r\n        checkCreateRoom: true,\r\n      };\r\n\r\n    case RoomActions.CLEAR_ROOM_CREATE:\r\n      return {\r\n        ...state,\r\n        createRoom: initialState.createRoom,\r\n        checkCreateRoom: false,\r\n      };\r\n\r\n    case RoomActions.TOGGLE_ROOM_STATUS:\r\n      return {\r\n        ...state,\r\n        rooms: state.rooms.map((room) =>\r\n          room._id === action.payload.roomId\r\n            ? { ...room, isActive: !room.isActive }\r\n            : room\r\n        ),\r\n      };\r\n\r\n    case RoomActions.SET_LOADING:\r\n      return {\r\n        ...state,\r\n        loading: action.payload,\r\n      };\r\n\r\n    case RoomActions.SET_ERROR:\r\n      return {\r\n        ...state,\r\n        error: action.payload,\r\n        loading: false,\r\n      };\r\n\r\n    case RoomActions.CLEAR_ERROR:\r\n      return {\r\n        ...state,\r\n        error: null,\r\n      };\r\n\r\n    // Edit Room Actions\r\n    case \"SET_EDIT_ROOM\":\r\n      return {\r\n        ...state,\r\n        editRoom: action.payload,\r\n      };\r\n\r\n    case \"CLEAR_EDIT_ROOM\":\r\n      return {\r\n        ...state,\r\n        editRoom: null,\r\n      };\r\n\r\n    // Room Create List Management Actions\r\n    case RoomActions.SAVE_ROOM_TO_CREATE_LIST:\r\n      return {\r\n        ...state,\r\n        createRoomList: [...state.createRoomList, action.payload],\r\n      };\r\n\r\n    case RoomActions.EDIT_ROOM_IN_CREATE_LIST:\r\n      return {\r\n        ...state,\r\n        createRoomList: state.createRoomList.map((room, index) =>\r\n          index === action.payload.index \r\n            ? { ...room, ...action.payload.roomData } \r\n            : room\r\n        ),\r\n      };\r\n\r\n    case RoomActions.DELETE_ROOM_FROM_CREATE_LIST:\r\n      return {\r\n        ...state,\r\n        createRoomList: state.createRoomList.filter((_, index) => \r\n          index !== action.payload.index\r\n        ),\r\n      };\r\n\r\n    case RoomActions.CLEAR_ROOM_CREATE_LIST:\r\n      return {\r\n        ...state,\r\n        createRoomList: [],\r\n      };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default roomReducer;\r\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,WAAW;AAEnC,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;IACV;IACAC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,aAAa;IACnBC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,EAAE;IACPC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,IAAI;EACdC,eAAe,EAAE,KAAK;EACtB;EACAC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,KAAK,GAAGnB,YAAY,EAAEoB,MAAM,KAAK;EAAA,IAAAC,iBAAA,EAAAC,kBAAA;EACpD,QAAQF,MAAM,CAACb,IAAI;IACjB,KAAKR,WAAW,CAACwB,kBAAkB;MACjC,OAAO;QACL,GAAGJ,KAAK;QACRlB,KAAK,EAAEmB,MAAM,CAACI,OAAO;QACrBpB,KAAK,EAAE;MACT,CAAC;IACH,KAAKL,WAAW,CAAC0B,mBAAmB;MAClC,OAAO;QACL,GAAGN,KAAK;QACRlB,KAAK,EAAEmB,MAAM,CAACI,OAAO;QACrBrB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKL,WAAW,CAAC2B,yBAAyB;MACxC,OAAO;QACL,GAAGP,KAAK;QACRjB,UAAU,EAAEkB,MAAM,CAACI,OAAO;QAC1BrB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKL,WAAW,CAAC4B,mBAAmB;MAClC,OAAO;QACL,GAAGR,KAAK;QACRlB,KAAK,EAAE,CAAC,GAAGkB,KAAK,CAAClB,KAAK,EAAEmB,MAAM,CAACI,OAAO,CAAC;QACvCrB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAEL,YAAY,CAACK,UAAU;QACnCW,eAAe,EAAE;MACnB,CAAC;IAEH,KAAKjB,WAAW,CAAC6B,mBAAmB;MAClC,OAAO;QACL,GAAGT,KAAK;QACRlB,KAAK,EAAEkB,KAAK,CAAClB,KAAK,CAAC4B,GAAG,CAAEC,IAAI,IAC1BA,IAAI,CAACC,GAAG,KAAKX,MAAM,CAACI,OAAO,CAACO,GAAG,GAAGX,MAAM,CAACI,OAAO,GAAGM,IACrD,CAAC;QACD5B,UAAU,EACR,EAAAmB,iBAAA,GAAAF,KAAK,CAACjB,UAAU,cAAAmB,iBAAA,uBAAhBA,iBAAA,CAAkBU,GAAG,MAAKX,MAAM,CAACI,OAAO,CAACO,GAAG,GACxCX,MAAM,CAACI,OAAO,GACdL,KAAK,CAACjB,UAAU;QACtBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKL,WAAW,CAACiC,mBAAmB;MAClC,OAAO;QACL,GAAGb,KAAK;QACRlB,KAAK,EAAEkB,KAAK,CAAClB,KAAK,CAACgC,MAAM,CAAEH,IAAI,IAAKA,IAAI,CAACC,GAAG,KAAKX,MAAM,CAACI,OAAO,CAAC;QAChEtB,UAAU,EACR,EAAAoB,kBAAA,GAAAH,KAAK,CAACjB,UAAU,cAAAoB,kBAAA,uBAAhBA,kBAAA,CAAkBS,GAAG,MAAKX,MAAM,CAACI,OAAO,GAAG,IAAI,GAAGL,KAAK,CAACjB,UAAU;QACpEC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKL,WAAW,CAACmC,qBAAqB;MACpC,OAAO;QACL,GAAGf,KAAK;QACRd,UAAU,EAAE;UACV,GAAGc,KAAK,CAACd,UAAU;UACnBC,IAAI,EAAEc,MAAM,CAACI,OAAO,CAAClB;QACvB;MACF,CAAC;IAEH,KAAKP,WAAW,CAACoC,wBAAwB;MACvC,OAAO;QACL,GAAGhB,KAAK;QACRd,UAAU,EAAE;UACV,GAAGc,KAAK,CAACd,UAAU;UACnBE,IAAI,EAAEa,MAAM,CAACI,OAAO,CAACjB,IAAI;UACzBC,QAAQ,EAAEY,MAAM,CAACI,OAAO,CAAChB,QAAQ;UACjCC,QAAQ,EAAEW,MAAM,CAACI,OAAO,CAACf,QAAQ;UACjCC,WAAW,EAAEU,MAAM,CAACI,OAAO,CAACd,WAAW;UACvCC,GAAG,EAAES,MAAM,CAACI,OAAO,CAACb,GAAG;UACvBC,UAAU,EAAEQ,MAAM,CAACI,OAAO,CAACZ;QAC7B;MACF,CAAC;IAEH,KAAKb,WAAW,CAACqC,uBAAuB;MACtC,OAAO;QACL,GAAGjB,KAAK;QACRd,UAAU,EAAE;UACV,GAAGc,KAAK,CAACd,UAAU;UACnBQ,MAAM,EAAEO,MAAM,CAACI,OAAO,CAACX;QACzB;MACF,CAAC;IAEH,KAAKd,WAAW,CAACsC,wBAAwB;MACvC,OAAO;QACL,GAAGlB,KAAK;QACRd,UAAU,EAAE;UACV,GAAGc,KAAK,CAACd,UAAU;UACnBS,KAAK,EAAEM,MAAM,CAACI,OAAO,CAACV;QACxB,CAAC;QACDE,eAAe,EAAE;MACnB,CAAC;IAEH,KAAKjB,WAAW,CAACuC,iBAAiB;MAChC,OAAO;QACL,GAAGnB,KAAK;QACRd,UAAU,EAAEL,YAAY,CAACK,UAAU;QACnCW,eAAe,EAAE;MACnB,CAAC;IAEH,KAAKjB,WAAW,CAACwC,kBAAkB;MACjC,OAAO;QACL,GAAGpB,KAAK;QACRlB,KAAK,EAAEkB,KAAK,CAAClB,KAAK,CAAC4B,GAAG,CAAEC,IAAI,IAC1BA,IAAI,CAACC,GAAG,KAAKX,MAAM,CAACI,OAAO,CAACgB,MAAM,GAC9B;UAAE,GAAGV,IAAI;UAAEW,QAAQ,EAAE,CAACX,IAAI,CAACW;QAAS,CAAC,GACrCX,IACN;MACF,CAAC;IAEH,KAAK/B,WAAW,CAAC2C,WAAW;MAC1B,OAAO;QACL,GAAGvB,KAAK;QACRhB,OAAO,EAAEiB,MAAM,CAACI;MAClB,CAAC;IAEH,KAAKzB,WAAW,CAAC4C,SAAS;MACxB,OAAO;QACL,GAAGxB,KAAK;QACRf,KAAK,EAAEgB,MAAM,CAACI,OAAO;QACrBrB,OAAO,EAAE;MACX,CAAC;IAEH,KAAKJ,WAAW,CAAC6C,WAAW;MAC1B,OAAO;QACL,GAAGzB,KAAK;QACRf,KAAK,EAAE;MACT,CAAC;;IAEH;IACA,KAAK,eAAe;MAClB,OAAO;QACL,GAAGe,KAAK;QACRJ,QAAQ,EAAEK,MAAM,CAACI;MACnB,CAAC;IAEH,KAAK,iBAAiB;MACpB,OAAO;QACL,GAAGL,KAAK;QACRJ,QAAQ,EAAE;MACZ,CAAC;;IAEH;IACA,KAAKhB,WAAW,CAAC8C,wBAAwB;MACvC,OAAO;QACL,GAAG1B,KAAK;QACRF,cAAc,EAAE,CAAC,GAAGE,KAAK,CAACF,cAAc,EAAEG,MAAM,CAACI,OAAO;MAC1D,CAAC;IAEH,KAAKzB,WAAW,CAAC+C,wBAAwB;MACvC,OAAO;QACL,GAAG3B,KAAK;QACRF,cAAc,EAAEE,KAAK,CAACF,cAAc,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEiB,KAAK,KACnDA,KAAK,KAAK3B,MAAM,CAACI,OAAO,CAACuB,KAAK,GAC1B;UAAE,GAAGjB,IAAI;UAAE,GAAGV,MAAM,CAACI,OAAO,CAACwB;QAAS,CAAC,GACvClB,IACN;MACF,CAAC;IAEH,KAAK/B,WAAW,CAACkD,4BAA4B;MAC3C,OAAO;QACL,GAAG9B,KAAK;QACRF,cAAc,EAAEE,KAAK,CAACF,cAAc,CAACgB,MAAM,CAAC,CAACiB,CAAC,EAAEH,KAAK,KACnDA,KAAK,KAAK3B,MAAM,CAACI,OAAO,CAACuB,KAC3B;MACF,CAAC;IAEH,KAAKhD,WAAW,CAACoD,sBAAsB;MACrC,OAAO;QACL,GAAGhC,KAAK;QACRF,cAAc,EAAE;MAClB,CAAC;IAEH;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;AAED,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}