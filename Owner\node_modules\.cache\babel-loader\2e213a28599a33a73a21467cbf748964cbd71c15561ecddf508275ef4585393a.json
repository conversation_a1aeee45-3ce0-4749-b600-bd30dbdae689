{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport { useDispatch, useSelector } from 'react-redux';\nimport createSagaMiddleware from 'redux-saga';\nimport { persistStore, persistReducer } from 'redux-persist';\nimport storage from 'redux-persist/lib/storage';\nimport expireReducer from 'redux-persist-transform-expire';\nimport rootReducer from './root-reducer';\nimport rootSaga from './root-saga';\n\n// Khởi tạo middleware saga\nconst sagaMiddleware = createSagaMiddleware();\n\n// Cấu hình expire cho các reducer có thời gian hết hạn\nconst expireConfig = {\n  expireKey: 'expireAt',\n  expireSeconds: 60 * 60 * 1,\n  // 1 ngày\n  autoExpire: true\n};\n\n// Cấu hình persist tổng\nconst persistConfig = {\n  key: 'root',\n  storage,\n  whitelist: ['Auth', 'Hotel', 'Hotelservices', 'Reservation', 'MonthlyPayment', 'ReportedFeedback', 'Feedback', 'Room', 'RoomUnit', 'BankInfo'],\n  transforms: [expireReducer('Search', expireConfig), expireReducer('Hotel', expireConfig), expireReducer('Hotelservices', expireConfig), expireReducer('Reservation', expireConfig), expireReducer('MonthlyPayment', expireConfig), expireReducer('ReportedFeedback', expireConfig), expireReducer('Feedback', expireConfig)]\n};\n\n// Gộp persist vào reducer\nconst persistedReducer = persistReducer(persistConfig, rootReducer);\n\n// Tạo store\nconst store = configureStore({\n  reducer: persistedReducer,\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: false\n  }).concat(sagaMiddleware)\n});\n\n// Chạy saga\nsagaMiddleware.run(rootSaga);\n\n// Tạo persistor để dùng trong <PersistGate>\nexport const persistor = persistStore(store);\n\n// Custom hooks không type\nexport const useAppDispatch = useDispatch;\nexport const useAppSelector = useSelector;\nexport default store;", "map": {"version": 3, "names": ["configureStore", "useDispatch", "useSelector", "createSagaMiddleware", "persistStore", "persistReducer", "storage", "expireReducer", "rootReducer", "rootSaga", "sagaMiddleware", "expireConfig", "expire<PERSON><PERSON>", "expireSeconds", "autoExpire", "persistConfig", "key", "whitelist", "transforms", "persistedReducer", "store", "reducer", "middleware", "getDefaultMiddleware", "serializableCheck", "concat", "run", "persistor", "useAppDispatch", "useAppSelector"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/store.js"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport createSagaMiddleware from 'redux-saga';\r\nimport { persistStore, persistReducer } from 'redux-persist';\r\nimport storage from 'redux-persist/lib/storage';\r\nimport expireReducer from 'redux-persist-transform-expire';\r\n\r\nimport rootReducer from './root-reducer';\r\nimport rootSaga from './root-saga';\r\n\r\n// Khởi tạo middleware saga\r\nconst sagaMiddleware = createSagaMiddleware();\r\n\r\n// Cấu hình expire cho các reducer có thời gian hết hạn\r\nconst expireConfig = {\r\n  expireKey: 'expireAt',\r\n  expireSeconds: 60*60*1, // 1 ngày\r\n  autoExpire: true,\r\n};\r\n\r\n// Cấu hình persist tổng\r\nconst persistConfig = {\r\n  key: 'root',\r\n  storage,\r\n  whitelist: ['Auth', 'Hotel', 'Hotelservices', 'Reservation', 'MonthlyPayment', 'ReportedFeedback', 'Feedback', 'Room', 'RoomUnit', 'BankInfo'], \r\n  transforms: [\r\n    expireReducer('Search', expireConfig),\r\n    expireReducer('Hotel', expireConfig),\r\n    expireReducer('Hotelservices', expireConfig),\r\n    expireReducer('Reservation', expireConfig),\r\n    expireReducer('MonthlyPayment', expireConfig),\r\n    expireReducer('ReportedFeedback', expireConfig),\r\n    expireReducer('Feedback', expireConfig),\r\n  ],\r\n};\r\n\r\n// Gộp persist vào reducer\r\nconst persistedReducer = persistReducer(persistConfig, rootReducer);\r\n\r\n// Tạo store\r\nconst store = configureStore({\r\n  reducer: persistedReducer,\r\n  middleware: getDefaultMiddleware =>\r\n    getDefaultMiddleware({\r\n      serializableCheck: false,\r\n    }).concat(sagaMiddleware),\r\n});\r\n\r\n// Chạy saga\r\nsagaMiddleware.run(rootSaga);\r\n\r\n// Tạo persistor để dùng trong <PersistGate>\r\nexport const persistor = persistStore(store);\r\n\r\n// Custom hooks không type\r\nexport const useAppDispatch = useDispatch;\r\nexport const useAppSelector = useSelector;\r\n\r\nexport default store;\r\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,oBAAoB,MAAM,YAAY;AAC7C,SAASC,YAAY,EAAEC,cAAc,QAAQ,eAAe;AAC5D,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,aAAa,MAAM,gCAAgC;AAE1D,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,aAAa;;AAElC;AACA,MAAMC,cAAc,GAAGP,oBAAoB,CAAC,CAAC;;AAE7C;AACA,MAAMQ,YAAY,GAAG;EACnBC,SAAS,EAAE,UAAU;EACrBC,aAAa,EAAE,EAAE,GAAC,EAAE,GAAC,CAAC;EAAE;EACxBC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,MAAMC,aAAa,GAAG;EACpBC,GAAG,EAAE,MAAM;EACXV,OAAO;EACPW,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC;EAC9IC,UAAU,EAAE,CACVX,aAAa,CAAC,QAAQ,EAAEI,YAAY,CAAC,EACrCJ,aAAa,CAAC,OAAO,EAAEI,YAAY,CAAC,EACpCJ,aAAa,CAAC,eAAe,EAAEI,YAAY,CAAC,EAC5CJ,aAAa,CAAC,aAAa,EAAEI,YAAY,CAAC,EAC1CJ,aAAa,CAAC,gBAAgB,EAAEI,YAAY,CAAC,EAC7CJ,aAAa,CAAC,kBAAkB,EAAEI,YAAY,CAAC,EAC/CJ,aAAa,CAAC,UAAU,EAAEI,YAAY,CAAC;AAE3C,CAAC;;AAED;AACA,MAAMQ,gBAAgB,GAAGd,cAAc,CAACU,aAAa,EAAEP,WAAW,CAAC;;AAEnE;AACA,MAAMY,KAAK,GAAGpB,cAAc,CAAC;EAC3BqB,OAAO,EAAEF,gBAAgB;EACzBG,UAAU,EAAEC,oBAAoB,IAC9BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;EACrB,CAAC,CAAC,CAACC,MAAM,CAACf,cAAc;AAC5B,CAAC,CAAC;;AAEF;AACAA,cAAc,CAACgB,GAAG,CAACjB,QAAQ,CAAC;;AAE5B;AACA,OAAO,MAAMkB,SAAS,GAAGvB,YAAY,CAACgB,KAAK,CAAC;;AAE5C;AACA,OAAO,MAAMQ,cAAc,GAAG3B,WAAW;AACzC,OAAO,MAAM4B,cAAc,GAAG3B,WAAW;AAEzC,eAAekB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}