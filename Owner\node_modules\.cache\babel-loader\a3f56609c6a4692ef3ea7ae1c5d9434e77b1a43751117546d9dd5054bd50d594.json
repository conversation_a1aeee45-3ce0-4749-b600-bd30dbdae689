{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\information\\\\MyAccountHotelPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Container, Row, Col, Card, Nav } from \"react-bootstrap\";\nimport { FaKey, FaImage } from \"react-icons/fa\";\nimport { IoSettingsSharp } from \"react-icons/io5\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport \"../../../css/hotelHost/MyAccountPage.css\";\nimport ChangePassword from \"./components/ChangePasswordHotel\";\nimport ViewInformation from \"./components/ViewInformationHotel\";\nimport ViewAvatar from \"./components/ViewAvatarHotel\";\nimport { useLocation } from \"react-router-dom\";\nimport { ToastProvider } from \"@components/ToastContainer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MyAccountHotelPage() {\n  _s();\n  var _location$state;\n  const location = useLocation();\n  const {\n    id\n  } = location.state || {};\n  useEffect(() => {\n    if (id) {\n      setIndexActive(id);\n    }\n  }, [(_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.id]);\n  const [indexActive, setIndexActive] = useState(0);\n  const handleMenuClick = index => {\n    setIndexActive(index);\n  };\n  const menuItems = [{\n    name: \"Tài Khoản Của Tôi\",\n    icon: /*#__PURE__*/_jsxDEV(IoSettingsSharp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 40\n    }, this)\n  }, {\n    name: \"Đổi Mật Khẩu\",\n    icon: /*#__PURE__*/_jsxDEV(FaKey, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 35\n    }, this)\n  }, {\n    name: \"Xem Ảnh Đại Diện\",\n    icon: /*#__PURE__*/_jsxDEV(FaImage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 39\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"main-content_1 p-3\",\n    children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {\n      variant: \"tabs\",\n      activeKey: indexActive,\n      className: \"mb-3\",\n      children: menuItems.map((item, index) => /*#__PURE__*/_jsxDEV(Nav.Item, {\n        children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n          eventKey: index,\n          onClick: () => handleMenuClick(index),\n          children: [item.icon, \"\\xA0\", item.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this)\n      }, item.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          style: {\n            backgroundColor: \"rgba(255, 255, 255,0.9)\"\n          },\n          children: [indexActive === 0 && /*#__PURE__*/_jsxDEV(ViewInformation, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 35\n          }, this), indexActive === 1 && /*#__PURE__*/_jsxDEV(ChangePassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 35\n          }, this), indexActive === 2 && /*#__PURE__*/_jsxDEV(ViewAvatar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 35\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(MyAccountHotelPage, \"2DcOz1O0MHC2tutciXFNrePAbJk=\", false, function () {\n  return [useLocation];\n});\n_c = MyAccountHotelPage;\nexport default MyAccountHotelPage;\nvar _c;\n$RefreshReg$(_c, \"MyAccountHotelPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Container", "Row", "Col", "Card", "Nav", "FaKey", "FaImage", "IoSettingsSharp", "ChangePassword", "ViewInformation", "ViewAvatar", "useLocation", "ToastProvider", "jsxDEV", "_jsxDEV", "MyAccountHotelPage", "_s", "_location$state", "location", "id", "state", "setIndexActive", "indexActive", "handleMenuClick", "index", "menuItems", "name", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "variant", "active<PERSON><PERSON>", "map", "item", "<PERSON><PERSON>", "Link", "eventKey", "onClick", "md", "style", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/information/MyAccountHotelPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Container, Row, Col, Card, Nav } from \"react-bootstrap\";\r\nimport { FaKey, FaImage } from \"react-icons/fa\";\r\nimport { IoSettingsSharp } from \"react-icons/io5\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport \"../../../css/hotelHost/MyAccountPage.css\";\r\nimport ChangePassword from \"./components/ChangePasswordHotel\";\r\nimport ViewInformation from \"./components/ViewInformationHotel\";\r\nimport ViewAvatar from \"./components/ViewAvatarHotel\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport { ToastProvider } from \"@components/ToastContainer\";\r\n\r\nfunction MyAccountHotelPage() {\r\n  const location = useLocation();\r\n  const { id } = location.state || {};\r\n  useEffect(() => {\r\n    if (id) {\r\n      setIndexActive(id);\r\n    }\r\n  }, [location.state?.id]);\r\n  const [indexActive, setIndexActive] = useState(0);\r\n  const handleMenuClick = (index) => {\r\n    setIndexActive(index);\r\n  };\r\n  const menuItems = [\r\n    { name: \"Tài <PERSON>n <PERSON>ủa Tôi\", icon: <IoSettingsSharp /> },\r\n    { name: \"Đổi Mật Khẩu\", icon: <FaKey /> },\r\n    { name: \"Xem Ảnh Đại Diện\", icon: <FaImage /> },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"main-content_1 p-3\">\r\n      <ToastProvider/>\r\n      <Nav variant=\"tabs\" activeKey={indexActive} className=\"mb-3\">\r\n        {menuItems.map((item, index) => (\r\n          <Nav.Item key={item.name}>\r\n            <Nav.Link eventKey={index} onClick={() => handleMenuClick(index)}>\r\n              {item.icon}&nbsp;{item.name}\r\n            </Nav.Link>\r\n          </Nav.Item>\r\n        ))}\r\n      </Nav>\r\n      <Row>\r\n        <Col md={12}>\r\n          <Card style={{ backgroundColor: \"rgba(255, 255, 255,0.9)\" }}>\r\n            {indexActive === 0 && <ViewInformation />}\r\n            {indexActive === 1 && <ChangePassword />}\r\n            {indexActive === 2 && <ViewAvatar />}\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default MyAccountHotelPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AAChE,SAASC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAC/C,SAASC,eAAe,QAAQ,iBAAiB;AACjD,OAAO,sCAAsC;AAC7C,OAAO,0CAA0C;AACjD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC5B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAG,CAAC,GAAGD,QAAQ,CAACE,KAAK,IAAI,CAAC,CAAC;EACnCtB,SAAS,CAAC,MAAM;IACd,IAAIqB,EAAE,EAAE;MACNE,cAAc,CAACF,EAAE,CAAC;IACpB;EACF,CAAC,EAAE,EAAAF,eAAA,GAACC,QAAQ,CAACE,KAAK,cAAAH,eAAA,uBAAdA,eAAA,CAAgBE,EAAE,CAAC,CAAC;EACxB,MAAM,CAACG,WAAW,EAAED,cAAc,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAMwB,eAAe,GAAIC,KAAK,IAAK;IACjCH,cAAc,CAACG,KAAK,CAAC;EACvB,CAAC;EACD,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,eAAEb,OAAA,CAACP,eAAe;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACxD;IAAEL,IAAI,EAAE,cAAc;IAAEC,IAAI,eAAEb,OAAA,CAACT,KAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACzC;IAAEL,IAAI,EAAE,kBAAkB;IAAEC,IAAI,eAAEb,OAAA,CAACR,OAAO;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CAChD;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCnB,OAAA,CAACF,aAAa;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eAChBjB,OAAA,CAACV,GAAG;MAAC8B,OAAO,EAAC,MAAM;MAACC,SAAS,EAAEb,WAAY;MAACU,SAAS,EAAC,MAAM;MAAAC,QAAA,EACzDR,SAAS,CAACW,GAAG,CAAC,CAACC,IAAI,EAAEb,KAAK,kBACzBV,OAAA,CAACV,GAAG,CAACkC,IAAI;QAAAL,QAAA,eACPnB,OAAA,CAACV,GAAG,CAACmC,IAAI;UAACC,QAAQ,EAAEhB,KAAM;UAACiB,OAAO,EAAEA,CAAA,KAAMlB,eAAe,CAACC,KAAK,CAAE;UAAAS,QAAA,GAC9DI,IAAI,CAACV,IAAI,EAAC,MAAM,EAACU,IAAI,CAACX,IAAI;QAAA;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC,GAHEM,IAAI,CAACX,IAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAId,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNjB,OAAA,CAACb,GAAG;MAAAgC,QAAA,eACFnB,OAAA,CAACZ,GAAG;QAACwC,EAAE,EAAE,EAAG;QAAAT,QAAA,eACVnB,OAAA,CAACX,IAAI;UAACwC,KAAK,EAAE;YAAEC,eAAe,EAAE;UAA0B,CAAE;UAAAX,QAAA,GACzDX,WAAW,KAAK,CAAC,iBAAIR,OAAA,CAACL,eAAe;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxCT,WAAW,KAAK,CAAC,iBAAIR,OAAA,CAACN,cAAc;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvCT,WAAW,KAAK,CAAC,iBAAIR,OAAA,CAACJ,UAAU;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACf,EAAA,CAzCQD,kBAAkB;EAAA,QACRJ,WAAW;AAAA;AAAAkC,EAAA,GADrB9B,kBAAkB;AA2C3B,eAAeA,kBAAkB;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}