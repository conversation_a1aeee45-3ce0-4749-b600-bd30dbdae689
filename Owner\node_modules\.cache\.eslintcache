[{"E:\\WDP301_UROOM\\Owner\\src\\index.js": "1", "E:\\WDP301_UROOM\\Owner\\src\\App.js": "2", "E:\\WDP301_UROOM\\Owner\\src\\reportWebVitals.js": "3", "E:\\WDP301_UROOM\\Owner\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Owner\\src\\redux\\socket\\socketSlice.js": "5", "E:\\WDP301_UROOM\\Owner\\src\\utils\\Routes.js": "6", "E:\\WDP301_UROOM\\Owner\\src\\redux\\root-reducer.js": "7", "E:\\WDP301_UROOM\\Owner\\src\\redux\\root-saga.js": "8", "E:\\WDP301_UROOM\\Owner\\src\\pages\\BannedPage.jsx": "9", "E:\\WDP301_UROOM\\Owner\\src\\pages\\WaitPendingPage.jsx": "10", "E:\\WDP301_UROOM\\Owner\\src\\pages\\ErrorPage.jsx": "11", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Transaction.jsx": "12", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\RoomAvailabilityCalendar.jsx": "13", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\TransactionDetail.jsx": "14", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\ForgetPasswordHotelPage.jsx": "15", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\RegisterHotelPage.jsx": "16", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\DocumentUpload.jsx": "17", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\VerifyCodeRegisterPage.jsx": "18", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\VerifyCodeHotelPage.jsx": "19", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\ResetPasswordHotelPage.jsx": "20", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\LoginHotelPage.jsx": "21", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Feedback\\ListFeedbackHotelPage.jsx": "22", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Feedback\\ReportedFeedbackHotel.jsx": "23", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\HomeHotel.jsx": "24", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingRegistration.jsx": "25", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyDescription.jsx": "26", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyFacility.jsx": "27", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyCheckInOut.jsx": "28", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\MyAccountHotelPage.jsx": "29", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyName.jsx": "30", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyLocation.jsx": "31", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\CreateRoom.jsx": "32", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\RoomNameForm.jsx": "33", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyChecklist.jsx": "34", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\RoomImageForm.jsx": "35", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\PricingSetupForm.jsx": "36", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\hotel\\HotelManagement.jsx": "37", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\service\\AdditionalServicesPage.jsx": "38", "E:\\WDP301_UROOM\\Owner\\src\\pages\\room\\Room.jsx": "39", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\components\\ViewInformationHotel.jsx": "40", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\AI\\DataAnalysisAI.jsx": "41", "E:\\WDP301_UROOM\\Owner\\src\\utils\\Utils.js": "42", "E:\\WDP301_UROOM\\Owner\\src\\utils\\handleToken.js": "43", "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\saga.js": "44", "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\saga.js": "45", "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\reducer.js": "46", "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\reducer.js": "47", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\reducer.js": "48", "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\saga.js": "49", "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\saga.js": "50", "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\saga.js": "51", "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\reducer.js": "52", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\saga.js": "53", "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\reducer.js": "54", "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\saga.js": "55", "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\reducer.js": "56", "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\reducer.js": "57", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\reducer.js": "58", "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\actions.js": "59", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\saga.js": "60", "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\actions.js": "61", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\actions.js": "62", "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\actions.js": "63", "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\actions.js": "64", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\actions.js": "65", "E:\\WDP301_UROOM\\Owner\\src\\utils\\data.js": "66", "E:\\WDP301_UROOM\\Owner\\src\\components\\ToastContainer.jsx": "67", "E:\\WDP301_UROOM\\Owner\\src\\components\\CustomPagination.jsx": "68", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\components\\ViewAvatarHotel.jsx": "69", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\components\\ChangePasswordHotel.jsx": "70", "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\factories.js": "71", "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\actions.js": "72", "E:\\WDP301_UROOM\\Owner\\src\\components\\ConfirmationModal.jsx": "73", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\hotel\\Hotel.jsx": "74", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\AI\\InSightAiPage.jsx": "75", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Chat.jsx": "76", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\dash_board\\DashBoardPage.jsx": "77", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\revenue\\RevenuePage.jsx": "78", "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\factories.js": "79", "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\factories.js": "80", "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\actions.js": "81", "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\factories.js": "82", "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\factories.js": "83", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\factories.js": "84", "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\factories.js": "85", "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\factories.js": "86", "E:\\WDP301_UROOM\\Owner\\src\\adapter\\ApiConstants.js": "87", "E:\\WDP301_UROOM\\Owner\\src\\libs\\api\\index.js": "88", "E:\\WDP301_UROOM\\Owner\\src\\pages\\room\\RoomListingPage.jsx": "89", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\service\\CreateService.jsx": "90", "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\reducer.js": "91", "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\saga.js": "92", "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\reducer.js": "93", "E:\\WDP301_UROOM\\Owner\\src\\redux\\room_unit\\reducer.js": "94", "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\saga.js": "95", "E:\\WDP301_UROOM\\Owner\\src\\redux\\room_unit\\action.js": "96", "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\actions.js": "97", "E:\\WDP301_UROOM\\Owner\\src\\redux\\bankInfo\\actions.js": "98", "E:\\WDP301_UROOM\\Owner\\src\\redux\\bankInfo\\reducer.js": "99", "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\factories.js": "100", "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\report\\MyReportPage.jsx": "101", "E:\\WDP301_UROOM\\Owner\\src\\pages\\management_booking\\ManagementBooking.jsx": "102", "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\factories.js": "103", "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\actions.js": "104"}, {"size": 833, "mtime": *************, "results": "105", "hashOfConfig": "106"}, {"size": 7643, "mtime": *************, "results": "107", "hashOfConfig": "106"}, {"size": 375, "mtime": *************, "results": "108", "hashOfConfig": "106"}, {"size": 1893, "mtime": *************, "results": "109", "hashOfConfig": "106"}, {"size": 1578, "mtime": *************, "results": "110", "hashOfConfig": "106"}, {"size": 2459, "mtime": *************, "results": "111", "hashOfConfig": "106"}, {"size": 1316, "mtime": *************, "results": "112", "hashOfConfig": "106"}, {"size": 838, "mtime": *************, "results": "113", "hashOfConfig": "106"}, {"size": 1710, "mtime": 1750045440570, "results": "114", "hashOfConfig": "106"}, {"size": 1718, "mtime": 1751502051354, "results": "115", "hashOfConfig": "106"}, {"size": 1067, "mtime": 1750045440571, "results": "116", "hashOfConfig": "106"}, {"size": 31867, "mtime": 1751502051357, "results": "117", "hashOfConfig": "106"}, {"size": 46901, "mtime": 1751502051357, "results": "118", "hashOfConfig": "106"}, {"size": 14999, "mtime": 1751502051358, "results": "119", "hashOfConfig": "106"}, {"size": 3973, "mtime": 1751502051366, "results": "120", "hashOfConfig": "106"}, {"size": 7584, "mtime": 1751502051367, "results": "121", "hashOfConfig": "106"}, {"size": 26426, "mtime": 1751502051366, "results": "122", "hashOfConfig": "106"}, {"size": 8657, "mtime": 1751502051368, "results": "123", "hashOfConfig": "106"}, {"size": 9688, "mtime": 1751502051367, "results": "124", "hashOfConfig": "106"}, {"size": 5924, "mtime": 1751502051367, "results": "125", "hashOfConfig": "106"}, {"size": 10510, "mtime": 1751502051366, "results": "126", "hashOfConfig": "106"}, {"size": 11188, "mtime": 1750045440572, "results": "127", "hashOfConfig": "106"}, {"size": 8939, "mtime": 1750045440573, "results": "128", "hashOfConfig": "106"}, {"size": 10725, "mtime": 1750045440578, "results": "129", "hashOfConfig": "106"}, {"size": 9686, "mtime": 1750045440578, "results": "130", "hashOfConfig": "106"}, {"size": 29543, "mtime": 1751502051359, "results": "131", "hashOfConfig": "106"}, {"size": 11179, "mtime": 1750045440577, "results": "132", "hashOfConfig": "106"}, {"size": 20045, "mtime": 1750045440576, "results": "133", "hashOfConfig": "106"}, {"size": 1975, "mtime": 1751502051364, "results": "134", "hashOfConfig": "106"}, {"size": 12074, "mtime": 1750045440578, "results": "135", "hashOfConfig": "106"}, {"size": 18362, "mtime": 1751502051359, "results": "136", "hashOfConfig": "106"}, {"size": 15717, "mtime": 1751502051360, "results": "137", "hashOfConfig": "106"}, {"size": 11856, "mtime": 1751502051363, "results": "138", "hashOfConfig": "106"}, {"size": 25056, "mtime": 1751502051358, "results": "139", "hashOfConfig": "106"}, {"size": 18409, "mtime": 1751502051362, "results": "140", "hashOfConfig": "106"}, {"size": 10237, "mtime": 1751502051361, "results": "141", "hashOfConfig": "106"}, {"size": 11913, "mtime": 1751502051364, "results": "142", "hashOfConfig": "106"}, {"size": 15778, "mtime": 1751502051369, "results": "143", "hashOfConfig": "106"}, {"size": 30026, "mtime": 1751502051370, "results": "144", "hashOfConfig": "106"}, {"size": 7303, "mtime": 1751502051365, "results": "145", "hashOfConfig": "106"}, {"size": 18747, "mtime": 1751502051355, "results": "146", "hashOfConfig": "106"}, {"size": 2918, "mtime": *************, "results": "147", "hashOfConfig": "106"}, {"size": 551, "mtime": 1750045440599, "results": "148", "hashOfConfig": "106"}, {"size": 8511, "mtime": 1751502051374, "results": "149", "hashOfConfig": "106"}, {"size": 6404, "mtime": 1750045440589, "results": "150", "hashOfConfig": "106"}, {"size": 546, "mtime": 1750045440587, "results": "151", "hashOfConfig": "106"}, {"size": 5530, "mtime": 1751502051373, "results": "152", "hashOfConfig": "106"}, {"size": 917, "mtime": 1750045440595, "results": "153", "hashOfConfig": "106"}, {"size": 1231, "mtime": 1751502051371, "results": "154", "hashOfConfig": "106"}, {"size": 6213, "mtime": 1750045440590, "results": "155", "hashOfConfig": "106"}, {"size": 2085, "mtime": 1750045440593, "results": "156", "hashOfConfig": "106"}, {"size": 1417, "mtime": 1750045440588, "results": "157", "hashOfConfig": "106"}, {"size": 3012, "mtime": 1750045440595, "results": "158", "hashOfConfig": "106"}, {"size": 551, "mtime": 1750045440593, "results": "159", "hashOfConfig": "106"}, {"size": 786, "mtime": 1750045440594, "results": "160", "hashOfConfig": "106"}, {"size": 1723, "mtime": 1750045440589, "results": "161", "hashOfConfig": "106"}, {"size": 490, "mtime": 1750045440594, "results": "162", "hashOfConfig": "106"}, {"size": 679, "mtime": 1750045440596, "results": "163", "hashOfConfig": "106"}, {"size": 200, "mtime": 1750045440593, "results": "164", "hashOfConfig": "106"}, {"size": 886, "mtime": 1750045440596, "results": "165", "hashOfConfig": "106"}, {"size": 861, "mtime": 1750045440588, "results": "166", "hashOfConfig": "106"}, {"size": 239, "mtime": 1750045440596, "results": "167", "hashOfConfig": "106"}, {"size": 708, "mtime": 1750045440589, "results": "168", "hashOfConfig": "106"}, {"size": 1643, "mtime": 1751502051372, "results": "169", "hashOfConfig": "106"}, {"size": 481, "mtime": 1750045440595, "results": "170", "hashOfConfig": "106"}, {"size": 7210, "mtime": 1751502051377, "results": "171", "hashOfConfig": "106"}, {"size": 1493, "mtime": 1750045440539, "results": "172", "hashOfConfig": "106"}, {"size": 1781, "mtime": 1750045440538, "results": "173", "hashOfConfig": "106"}, {"size": 6274, "mtime": 1751502051365, "results": "174", "hashOfConfig": "106"}, {"size": 7637, "mtime": 1751502051365, "results": "175", "hashOfConfig": "106"}, {"size": 2309, "mtime": 1751502051373, "results": "176", "hashOfConfig": "106"}, {"size": 199, "mtime": 1750045440587, "results": "177", "hashOfConfig": "106"}, {"size": 2348, "mtime": 1750045440538, "results": "178", "hashOfConfig": "106"}, {"size": 45658, "mtime": 1751502051364, "results": "179", "hashOfConfig": "106"}, {"size": 7116, "mtime": 1750045440572, "results": "180", "hashOfConfig": "106"}, {"size": 26031, "mtime": 1751502051356, "results": "181", "hashOfConfig": "106"}, {"size": 14441, "mtime": 1751502051363, "results": "182", "hashOfConfig": "106"}, {"size": 30429, "mtime": 1751502051369, "results": "183", "hashOfConfig": "106"}, {"size": 377, "mtime": 1750045440593, "results": "184", "hashOfConfig": "106"}, {"size": 327, "mtime": 1750045440587, "results": "185", "hashOfConfig": "106"}, {"size": 322, "mtime": 1750045440592, "results": "186", "hashOfConfig": "106"}, {"size": 1525, "mtime": 1750045440589, "results": "187", "hashOfConfig": "106"}, {"size": 1111, "mtime": 1751502051371, "results": "188", "hashOfConfig": "106"}, {"size": 491, "mtime": 1750045440595, "results": "189", "hashOfConfig": "106"}, {"size": 301, "mtime": 1750045440596, "results": "190", "hashOfConfig": "106"}, {"size": 264, "mtime": 1750045440594, "results": "191", "hashOfConfig": "106"}, {"size": 1881, "mtime": *************, "results": "192", "hashOfConfig": "106"}, {"size": 2658, "mtime": 1750045440570, "results": "193", "hashOfConfig": "106"}, {"size": 22858, "mtime": 1751502051370, "results": "194", "hashOfConfig": "106"}, {"size": 9323, "mtime": 1751502051369, "results": "195", "hashOfConfig": "106"}, {"size": 5034, "mtime": 1751502051374, "results": "196", "hashOfConfig": "106"}, {"size": 905, "mtime": *************, "results": "197", "hashOfConfig": "106"}, {"size": 1120, "mtime": 1751502051372, "results": "198", "hashOfConfig": "106"}, {"size": 6082, "mtime": *************, "results": "199", "hashOfConfig": "106"}, {"size": 838, "mtime": 1751502051372, "results": "200", "hashOfConfig": "106"}, {"size": 1627, "mtime": *************, "results": "201", "hashOfConfig": "106"}, {"size": 1241, "mtime": 1751502051374, "results": "202", "hashOfConfig": "106"}, {"size": 219, "mtime": 1751502051371, "results": "203", "hashOfConfig": "106"}, {"size": 1234, "mtime": 1751502051371, "results": "204", "hashOfConfig": "106"}, {"size": 2966, "mtime": 1751502051374, "results": "205", "hashOfConfig": "106"}, {"size": 15395, "mtime": 1751502051368, "results": "206", "hashOfConfig": "106"}, {"size": 36118, "mtime": 1751502051370, "results": "207", "hashOfConfig": "106"}, {"size": 255, "mtime": 1751502051372, "results": "208", "hashOfConfig": "106"}, {"size": 609, "mtime": 1751502051372, "results": "209", "hashOfConfig": "106"}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vm3kib", {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WDP301_UROOM\\Owner\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\App.js", ["522", "523"], [], "E:\\WDP301_UROOM\\Owner\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\root-reducer.js", ["524"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\BannedPage.jsx", ["525"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\WaitPendingPage.jsx", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\ErrorPage.jsx", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Transaction.jsx", ["526", "527", "528", "529"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\RoomAvailabilityCalendar.jsx", ["530", "531", "532", "533", "534", "535", "536", "537", "538"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\TransactionDetail.jsx", ["539", "540", "541", "542", "543"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\ForgetPasswordHotelPage.jsx", ["544", "545", "546", "547"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\RegisterHotelPage.jsx", ["548", "549"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\DocumentUpload.jsx", ["550"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\VerifyCodeHotelPage.jsx", ["551"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\ResetPasswordHotelPage.jsx", ["552"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\login_register\\LoginHotelPage.jsx", ["553"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Feedback\\ListFeedbackHotelPage.jsx", ["554"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Feedback\\ReportedFeedbackHotel.jsx", ["555", "556", "557"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\HomeHotel.jsx", ["558", "559", "560", "561", "562", "563"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingRegistration.jsx", ["564"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyDescription.jsx", ["565", "566"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyFacility.jsx", ["567"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyCheckInOut.jsx", ["568"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\MyAccountHotelPage.jsx", ["569", "570"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyName.jsx", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyLocation.jsx", ["571", "572", "573"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\CreateRoom.jsx", ["574", "575", "576", "577", "578", "579"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\RoomNameForm.jsx", ["580"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\BookingPropertyChecklist.jsx", ["581", "582", "583", "584"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\RoomImageForm.jsx", ["585", "586", "587"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\create_hotel\\PricingSetupForm.jsx", ["588"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\hotel\\HotelManagement.jsx", ["589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\service\\AdditionalServicesPage.jsx", ["602", "603"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\room\\Room.jsx", ["604", "605", "606", "607", "608"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\components\\ViewInformationHotel.jsx", ["609", "610"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\AI\\DataAnalysisAI.jsx", ["611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627"], [], "E:\\WDP301_UROOM\\Owner\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\saga.js", ["628", "629", "630", "631", "632"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\reducer.js", ["633", "634", "635", "636"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\saga.js", ["637"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\reducer.js", ["638"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\utils\\data.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Owner\\src\\components\\CustomPagination.jsx", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\components\\ViewAvatarHotel.jsx", ["639", "640", "641"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\information\\components\\ChangePasswordHotel.jsx", ["642", "643", "644"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\hotel\\factories.js", ["645", "646", "647"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\hotel\\Hotel.jsx", ["648", "649", "650", "651", "652", "653", "654", "655", "656"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\AI\\InSightAiPage.jsx", ["657", "658", "659"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\Chat.jsx", ["660", "661", "662", "663", "664", "665", "666", "667"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\dash_board\\DashBoardPage.jsx", ["668", "669", "670", "671"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\revenue\\RevenuePage.jsx", ["672", "673", "674", "675", "676", "677"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\Hotelservices\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\monthlyPayment\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\adapter\\ApiConstants.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\libs\\api\\index.js", ["678"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\room\\RoomListingPage.jsx", ["679", "680", "681", "682", "683"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\service\\CreateService.jsx", ["684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\saga.js", ["697", "698"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\room_unit\\reducer.js", ["699"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\saga.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\room_unit\\action.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\bankInfo\\actions.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\bankInfo\\reducer.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\room\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\hotel_host\\report\\MyReportPage.jsx", ["700"], [], "E:\\WDP301_UROOM\\Owner\\src\\pages\\management_booking\\ManagementBooking.jsx", ["701", "702", "703"], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\factories.js", [], [], "E:\\WDP301_UROOM\\Owner\\src\\redux\\dashboard\\actions.js", [], [], {"ruleId": "704", "severity": 1, "message": "705", "line": 36, "column": 8, "nodeType": "706", "messageId": "707", "endLine": 36, "endColumn": 23}, {"ruleId": "708", "severity": 1, "message": "709", "line": 56, "column": 6, "nodeType": "710", "endLine": 56, "endColumn": 17, "suggestions": "711"}, {"ruleId": "704", "severity": 1, "message": "712", "line": 15, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 15, "endColumn": 14}, {"ruleId": "704", "severity": 1, "message": "713", "line": 1, "column": 17, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 26}, {"ruleId": "704", "severity": 1, "message": "714", "line": 18, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 18, "endColumn": 21}, {"ruleId": "704", "severity": 1, "message": "715", "line": 18, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 18, "endColumn": 34}, {"ruleId": "704", "severity": 1, "message": "716", "line": 24, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 24, "endColumn": 48}, {"ruleId": "704", "severity": 1, "message": "717", "line": 27, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 27, "endColumn": 17}, {"ruleId": "704", "severity": 1, "message": "718", "line": 13, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 13, "endColumn": 10}, {"ruleId": "704", "severity": 1, "message": "719", "line": 33, "column": 5, "nodeType": "706", "messageId": "707", "endLine": 33, "endColumn": 12}, {"ruleId": "704", "severity": 1, "message": "720", "line": 34, "column": 5, "nodeType": "706", "messageId": "707", "endLine": 34, "endColumn": 12}, {"ruleId": "704", "severity": 1, "message": "721", "line": 35, "column": 5, "nodeType": "706", "messageId": "707", "endLine": 35, "endColumn": 10}, {"ruleId": "704", "severity": 1, "message": "722", "line": 48, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 48, "endColumn": 17}, {"ruleId": "704", "severity": 1, "message": "723", "line": 67, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 67, "endColumn": 26}, {"ruleId": "704", "severity": 1, "message": "724", "line": 68, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 68, "endColumn": 27}, {"ruleId": "704", "severity": 1, "message": "725", "line": 69, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 69, "endColumn": 20}, {"ruleId": "704", "severity": 1, "message": "726", "line": 195, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 195, "endColumn": 26}, {"ruleId": "704", "severity": 1, "message": "727", "line": 1, "column": 17, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 25}, {"ruleId": "704", "severity": 1, "message": "713", "line": 1, "column": 27, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 36}, {"ruleId": "704", "severity": 1, "message": "728", "line": 6, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 6, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "729", "line": 15, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 15, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "730", "line": 16, "column": 8, "nodeType": "706", "messageId": "707", "endLine": 16, "endColumn": 13}, {"ruleId": "704", "severity": 1, "message": "731", "line": 4, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 4, "endColumn": 21}, {"ruleId": "704", "severity": 1, "message": "732", "line": 10, "column": 8, "nodeType": "706", "messageId": "707", "endLine": 10, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "733", "line": 15, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 15, "endColumn": 17}, {"ruleId": "704", "severity": 1, "message": "734", "line": 64, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 64, "endColumn": 33}, {"ruleId": "704", "severity": 1, "message": "731", "line": 4, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 4, "endColumn": 40}, {"ruleId": "735", "severity": 1, "message": "736", "line": 211, "column": 17, "nodeType": "737", "endLine": 215, "endColumn": 52}, {"ruleId": "704", "severity": 1, "message": "738", "line": 17, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 17, "endColumn": 20}, {"ruleId": "704", "severity": 1, "message": "739", "line": 26, "column": 17, "nodeType": "706", "messageId": "707", "endLine": 26, "endColumn": 25}, {"ruleId": "704", "severity": 1, "message": "731", "line": 4, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 4, "endColumn": 40}, {"ruleId": "704", "severity": 1, "message": "731", "line": 4, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 4, "endColumn": 40}, {"ruleId": "740", "severity": 1, "message": "741", "line": 142, "column": 46, "nodeType": "742", "messageId": "743", "endLine": 142, "endColumn": 48}, {"ruleId": "704", "severity": 1, "message": "713", "line": 1, "column": 20, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 29}, {"ruleId": "704", "severity": 1, "message": "744", "line": 9, "column": 8, "nodeType": "706", "messageId": "707", "endLine": 9, "endColumn": 23}, {"ruleId": "704", "severity": 1, "message": "745", "line": 21, "column": 19, "nodeType": "706", "messageId": "707", "endLine": 21, "endColumn": 29}, {"ruleId": "740", "severity": 1, "message": "746", "line": 16, "column": 18, "nodeType": "742", "messageId": "743", "endLine": 16, "endColumn": 20}, {"ruleId": "735", "severity": 1, "message": "747", "line": 104, "column": 21, "nodeType": "737", "endLine": 104, "endColumn": 59}, {"ruleId": "735", "severity": 1, "message": "747", "line": 135, "column": 23, "nodeType": "737", "endLine": 135, "endColumn": 81}, {"ruleId": "735", "severity": 1, "message": "747", "line": 166, "column": 23, "nodeType": "737", "endLine": 166, "endColumn": 81}, {"ruleId": "735", "severity": 1, "message": "747", "line": 183, "column": 23, "nodeType": "737", "endLine": 183, "endColumn": 81}, {"ruleId": "735", "severity": 1, "message": "747", "line": 203, "column": 23, "nodeType": "737", "endLine": 203, "endColumn": 81}, {"ruleId": "708", "severity": 1, "message": "748", "line": 24, "column": 5, "nodeType": "710", "endLine": 24, "endColumn": 7, "suggestions": "749"}, {"ruleId": "708", "severity": 1, "message": "750", "line": 60, "column": 6, "nodeType": "710", "endLine": 60, "endColumn": 46, "suggestions": "751"}, {"ruleId": "708", "severity": 1, "message": "752", "line": 262, "column": 6, "nodeType": "710", "endLine": 262, "endColumn": 8, "suggestions": "753"}, {"ruleId": "735", "severity": 1, "message": "736", "line": 143, "column": 31, "nodeType": "737", "endLine": 143, "endColumn": 34}, {"ruleId": "708", "severity": 1, "message": "754", "line": 107, "column": 6, "nodeType": "710", "endLine": 107, "endColumn": 60, "suggestions": "755"}, {"ruleId": "704", "severity": 1, "message": "756", "line": 2, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 2, "endColumn": 19}, {"ruleId": "708", "severity": 1, "message": "757", "line": 20, "column": 6, "nodeType": "710", "endLine": 20, "endColumn": 26, "suggestions": "758"}, {"ruleId": "708", "severity": 1, "message": "759", "line": 57, "column": 6, "nodeType": "710", "endLine": 57, "endColumn": 8, "suggestions": "760"}, {"ruleId": "708", "severity": 1, "message": "761", "line": 80, "column": 6, "nodeType": "710", "endLine": 80, "endColumn": 20, "suggestions": "762"}, {"ruleId": "708", "severity": 1, "message": "763", "line": 97, "column": 6, "nodeType": "710", "endLine": 97, "endColumn": 24, "suggestions": "764"}, {"ruleId": "704", "severity": 1, "message": "728", "line": 8, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 8, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "765", "line": 9, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 9, "endColumn": 13}, {"ruleId": "704", "severity": 1, "message": "766", "line": 12, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 12, "endColumn": 8}, {"ruleId": "704", "severity": 1, "message": "767", "line": 13, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 13, "endColumn": 8}, {"ruleId": "704", "severity": 1, "message": "768", "line": 14, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 14, "endColumn": 10}, {"ruleId": "704", "severity": 1, "message": "769", "line": 23, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 23, "endColumn": 17}, {"ruleId": "704", "severity": 1, "message": "728", "line": 8, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 8, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "770", "line": 39, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 39, "endColumn": 21}, {"ruleId": "704", "severity": 1, "message": "771", "line": 39, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 39, "endColumn": 37}, {"ruleId": "704", "severity": 1, "message": "772", "line": 68, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 68, "endColumn": 18}, {"ruleId": "735", "severity": 1, "message": "736", "line": 257, "column": 15, "nodeType": "737", "endLine": 268, "endColumn": 16}, {"ruleId": "704", "severity": 1, "message": "714", "line": 18, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 18, "endColumn": 21}, {"ruleId": "704", "severity": 1, "message": "773", "line": 26, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 26, "endColumn": 20}, {"ruleId": "704", "severity": 1, "message": "774", "line": 26, "column": 22, "nodeType": "706", "messageId": "707", "endLine": 26, "endColumn": 35}, {"ruleId": "704", "severity": 1, "message": "728", "line": 9, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 9, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "756", "line": 3, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 3, "endColumn": 12}, {"ruleId": "704", "severity": 1, "message": "775", "line": 17, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 17, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "776", "line": 18, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 18, "endColumn": 5}, {"ruleId": "704", "severity": 1, "message": "777", "line": 19, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 19, "endColumn": 10}, {"ruleId": "704", "severity": 1, "message": "778", "line": 20, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 20, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "779", "line": 21, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 21, "endColumn": 6}, {"ruleId": "704", "severity": 1, "message": "780", "line": 22, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 22, "endColumn": 11}, {"ruleId": "704", "severity": 1, "message": "781", "line": 23, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 23, "endColumn": 14}, {"ruleId": "704", "severity": 1, "message": "782", "line": 24, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 24, "endColumn": 14}, {"ruleId": "704", "severity": 1, "message": "738", "line": 35, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 35, "endColumn": 20}, {"ruleId": "704", "severity": 1, "message": "717", "line": 45, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 45, "endColumn": 17}, {"ruleId": "704", "severity": 1, "message": "783", "line": 47, "column": 20, "nodeType": "706", "messageId": "707", "endLine": 47, "endColumn": 31}, {"ruleId": "708", "severity": 1, "message": "784", "line": 74, "column": 6, "nodeType": "710", "endLine": 74, "endColumn": 8, "suggestions": "785"}, {"ruleId": "704", "severity": 1, "message": "720", "line": 15, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 15, "endColumn": 17}, {"ruleId": "708", "severity": 1, "message": "786", "line": 38, "column": 6, "nodeType": "710", "endLine": 38, "endColumn": 8, "suggestions": "787"}, {"ruleId": "704", "severity": 1, "message": "756", "line": 3, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 3, "endColumn": 12}, {"ruleId": "704", "severity": 1, "message": "728", "line": 8, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 8, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "765", "line": 9, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 9, "endColumn": 13}, {"ruleId": "704", "severity": 1, "message": "767", "line": 11, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 11, "endColumn": 8}, {"ruleId": "704", "severity": 1, "message": "730", "line": 15, "column": 8, "nodeType": "706", "messageId": "707", "endLine": 15, "endColumn": 13}, {"ruleId": "704", "severity": 1, "message": "713", "line": 2, "column": 17, "nodeType": "706", "messageId": "707", "endLine": 2, "endColumn": 26}, {"ruleId": "704", "severity": 1, "message": "788", "line": 4, "column": 21, "nodeType": "706", "messageId": "707", "endLine": 4, "endColumn": 34}, {"ruleId": "704", "severity": 1, "message": "789", "line": 30, "column": 8, "nodeType": "706", "messageId": "707", "endLine": 30, "endColumn": 21}, {"ruleId": "704", "severity": 1, "message": "790", "line": 35, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 35, "endColumn": 17}, {"ruleId": "704", "severity": 1, "message": "791", "line": 64, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 64, "endColumn": 15}, {"ruleId": "735", "severity": 1, "message": "747", "line": 92, "column": 15, "nodeType": "737", "endLine": 100, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 106, "column": 15, "nodeType": "737", "endLine": 114, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 120, "column": 15, "nodeType": "737", "endLine": 126, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 132, "column": 15, "nodeType": "737", "endLine": 140, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 146, "column": 15, "nodeType": "737", "endLine": 152, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 158, "column": 15, "nodeType": "737", "endLine": 166, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 172, "column": 15, "nodeType": "737", "endLine": 180, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 186, "column": 15, "nodeType": "737", "endLine": 194, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 200, "column": 15, "nodeType": "737", "endLine": 206, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 212, "column": 15, "nodeType": "737", "endLine": 220, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "747", "line": 226, "column": 15, "nodeType": "737", "endLine": 234, "endColumn": 16}, {"ruleId": "735", "severity": 1, "message": "736", "line": 290, "column": 23, "nodeType": "737", "endLine": 298, "endColumn": 24}, {"ruleId": "740", "severity": 1, "message": "746", "line": 303, "column": 44, "nodeType": "742", "messageId": "743", "endLine": 303, "endColumn": 46}, {"ruleId": "740", "severity": 1, "message": "746", "line": 304, "column": 44, "nodeType": "742", "messageId": "743", "endLine": 304, "endColumn": 46}, {"ruleId": "704", "severity": 1, "message": "792", "line": 44, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 44, "endColumn": 41}, {"ruleId": "704", "severity": 1, "message": "793", "line": 64, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 64, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "792", "line": 112, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 112, "endColumn": 41}, {"ruleId": "704", "severity": 1, "message": "793", "line": 132, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 132, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "794", "line": 133, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 133, "endColumn": 16}, {"ruleId": "704", "severity": 1, "message": "795", "line": 1, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 15}, {"ruleId": "796", "severity": 1, "message": "797", "line": 11, "column": 3, "nodeType": "798", "messageId": "743", "endLine": 11, "endColumn": 8}, {"ruleId": "796", "severity": 1, "message": "799", "line": 30, "column": 5, "nodeType": "798", "messageId": "743", "endLine": 30, "endColumn": 16}, {"ruleId": "796", "severity": 1, "message": "800", "line": 31, "column": 5, "nodeType": "798", "messageId": "743", "endLine": 31, "endColumn": 10}, {"ruleId": "704", "severity": 1, "message": "792", "line": 72, "column": 44, "nodeType": "706", "messageId": "707", "endLine": 72, "endColumn": 51}, {"ruleId": "704", "severity": 1, "message": "732", "line": 1, "column": 8, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "788", "line": 7, "column": 21, "nodeType": "706", "messageId": "707", "endLine": 7, "endColumn": 34}, {"ruleId": "740", "severity": 1, "message": "746", "line": 69, "column": 27, "nodeType": "742", "messageId": "743", "endLine": 69, "endColumn": 29}, {"ruleId": "740", "severity": 1, "message": "746", "line": 147, "column": 29, "nodeType": "742", "messageId": "743", "endLine": 147, "endColumn": 31}, {"ruleId": "704", "severity": 1, "message": "756", "line": 2, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 2, "endColumn": 12}, {"ruleId": "704", "severity": 1, "message": "765", "line": 8, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 8, "endColumn": 13}, {"ruleId": "704", "severity": 1, "message": "788", "line": 13, "column": 21, "nodeType": "706", "messageId": "707", "endLine": 13, "endColumn": 34}, {"ruleId": "740", "severity": 1, "message": "746", "line": 11, "column": 34, "nodeType": "742", "messageId": "743", "endLine": 11, "endColumn": 36}, {"ruleId": "740", "severity": 1, "message": "746", "line": 14, "column": 38, "nodeType": "742", "messageId": "743", "endLine": 14, "endColumn": 40}, {"ruleId": "740", "severity": 1, "message": "746", "line": 17, "column": 34, "nodeType": "742", "messageId": "743", "endLine": 17, "endColumn": 36}, {"ruleId": "704", "severity": 1, "message": "765", "line": 9, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 9, "endColumn": 13}, {"ruleId": "704", "severity": 1, "message": "801", "line": 27, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 27, "endColumn": 16}, {"ruleId": "704", "severity": 1, "message": "802", "line": 27, "column": 18, "nodeType": "706", "messageId": "707", "endLine": 27, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "803", "line": 30, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 30, "endColumn": 18}, {"ruleId": "704", "severity": 1, "message": "804", "line": 30, "column": 20, "nodeType": "706", "messageId": "707", "endLine": 30, "endColumn": 31}, {"ruleId": "704", "severity": 1, "message": "720", "line": 46, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 46, "endColumn": 17}, {"ruleId": "704", "severity": 1, "message": "783", "line": 49, "column": 20, "nodeType": "706", "messageId": "707", "endLine": 49, "endColumn": 31}, {"ruleId": "708", "severity": 1, "message": "784", "line": 58, "column": 6, "nodeType": "710", "endLine": 58, "endColumn": 12, "suggestions": "805"}, {"ruleId": "806", "severity": 1, "message": "807", "line": 1009, "column": 31, "nodeType": "737", "endLine": 1019, "endColumn": 33}, {"ruleId": "704", "severity": 1, "message": "808", "line": 1, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 14}, {"ruleId": "704", "severity": 1, "message": "809", "line": 1, "column": 21, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 24}, {"ruleId": "704", "severity": 1, "message": "810", "line": 1, "column": 26, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 34}, {"ruleId": "704", "severity": 1, "message": "811", "line": 10, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 10, "endColumn": 26}, {"ruleId": "704", "severity": 1, "message": "812", "line": 33, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 33, "endColumn": 20}, {"ruleId": "708", "severity": 1, "message": "813", "line": 80, "column": 6, "nodeType": "710", "endLine": 80, "endColumn": 8, "suggestions": "814"}, {"ruleId": "708", "severity": 1, "message": "815", "line": 84, "column": 6, "nodeType": "710", "endLine": 84, "endColumn": 20, "suggestions": "816"}, {"ruleId": "740", "severity": 1, "message": "741", "line": 119, "column": 29, "nodeType": "742", "messageId": "743", "endLine": 119, "endColumn": 31}, {"ruleId": "708", "severity": 1, "message": "813", "line": 142, "column": 6, "nodeType": "710", "endLine": 142, "endColumn": 44, "suggestions": "817"}, {"ruleId": "740", "severity": 1, "message": "741", "line": 415, "column": 46, "nodeType": "742", "messageId": "743", "endLine": 415, "endColumn": 48}, {"ruleId": "740", "severity": 1, "message": "741", "line": 428, "column": 38, "nodeType": "742", "messageId": "743", "endLine": 428, "endColumn": 40}, {"ruleId": "704", "severity": 1, "message": "818", "line": 1, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "809", "line": 1, "column": 21, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 24}, {"ruleId": "735", "severity": 1, "message": "747", "line": 342, "column": 17, "nodeType": "737", "endLine": 348, "endColumn": 18}, {"ruleId": "735", "severity": 1, "message": "747", "line": 379, "column": 17, "nodeType": "737", "endLine": 385, "endColumn": 18}, {"ruleId": "704", "severity": 1, "message": "818", "line": 1, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "810", "line": 1, "column": 26, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 34}, {"ruleId": "704", "severity": 1, "message": "819", "line": 79, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 79, "endColumn": 24}, {"ruleId": "704", "severity": 1, "message": "820", "line": 80, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 80, "endColumn": 25}, {"ruleId": "704", "severity": 1, "message": "821", "line": 212, "column": 13, "nodeType": "706", "messageId": "707", "endLine": 212, "endColumn": 23}, {"ruleId": "704", "severity": 1, "message": "822", "line": 237, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 237, "endColumn": 28}, {"ruleId": "823", "severity": 1, "message": "824", "line": 37, "column": 1, "nodeType": "825", "endLine": 108, "endColumn": 3}, {"ruleId": "704", "severity": 1, "message": "756", "line": 3, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 3, "endColumn": 12}, {"ruleId": "704", "severity": 1, "message": "826", "line": 9, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 9, "endColumn": 8}, {"ruleId": "708", "severity": 1, "message": "827", "line": 34, "column": 6, "nodeType": "710", "endLine": 34, "endColumn": 23, "suggestions": "828"}, {"ruleId": "704", "severity": 1, "message": "829", "line": 140, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 140, "endColumn": 21}, {"ruleId": "704", "severity": 1, "message": "830", "line": 145, "column": 9, "nodeType": "706", "messageId": "707", "endLine": 145, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "713", "line": 1, "column": 27, "nodeType": "706", "messageId": "707", "endLine": 1, "endColumn": 36}, {"ruleId": "704", "severity": 1, "message": "728", "line": 8, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 8, "endColumn": 7}, {"ruleId": "704", "severity": 1, "message": "765", "line": 9, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 9, "endColumn": 13}, {"ruleId": "704", "severity": 1, "message": "766", "line": 12, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 12, "endColumn": 8}, {"ruleId": "704", "severity": 1, "message": "831", "line": 13, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 13, "endColumn": 8}, {"ruleId": "704", "severity": 1, "message": "767", "line": 14, "column": 3, "nodeType": "706", "messageId": "707", "endLine": 14, "endColumn": 8}, {"ruleId": "704", "severity": 1, "message": "832", "line": 17, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 17, "endColumn": 21}, {"ruleId": "704", "severity": 1, "message": "833", "line": 19, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 19, "endColumn": 24}, {"ruleId": "704", "severity": 1, "message": "745", "line": 42, "column": 19, "nodeType": "706", "messageId": "707", "endLine": 42, "endColumn": 29}, {"ruleId": "704", "severity": 1, "message": "834", "line": 43, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 43, "endColumn": 19}, {"ruleId": "704", "severity": 1, "message": "835", "line": 43, "column": 21, "nodeType": "706", "messageId": "707", "endLine": 43, "endColumn": 33}, {"ruleId": "704", "severity": 1, "message": "836", "line": 44, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 44, "endColumn": 23}, {"ruleId": "704", "severity": 1, "message": "837", "line": 44, "column": 25, "nodeType": "706", "messageId": "707", "endLine": 44, "endColumn": 41}, {"ruleId": "704", "severity": 1, "message": "838", "line": 10, "column": 40, "nodeType": "706", "messageId": "707", "endLine": 10, "endColumn": 48}, {"ruleId": "704", "severity": 1, "message": "792", "line": 10, "column": 50, "nodeType": "706", "messageId": "707", "endLine": 10, "endColumn": 57}, {"ruleId": "796", "severity": 1, "message": "839", "line": 28, "column": 7, "nodeType": "798", "messageId": "743", "endLine": 28, "endColumn": 11}, {"ruleId": "708", "severity": 1, "message": "840", "line": 36, "column": 6, "nodeType": "710", "endLine": 36, "endColumn": 16, "suggestions": "841"}, {"ruleId": "704", "severity": 1, "message": "842", "line": 72, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 72, "endColumn": 29}, {"ruleId": "708", "severity": 1, "message": "784", "line": 106, "column": 6, "nodeType": "710", "endLine": 106, "endColumn": 8, "suggestions": "843"}, {"ruleId": "708", "severity": 1, "message": "827", "line": 158, "column": 6, "nodeType": "710", "endLine": 158, "endColumn": 39, "suggestions": "844"}, "no-unused-vars", "'ViewInformation' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["845"], "'Room' is defined but never used.", "'useEffect' is defined but never used.", "'useDispatch' is defined but never used.", "'useSelector' is defined but never used.", "'trackSynchronousRequestDataAccessInDev' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaPrint' is defined but never used.", "'filters' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'services' is assigned a value but never used.", "'showAddRoomModal' is assigned a value but never used.", "'showEditRoomModal' is assigned a value but never used.", "'roomToEdit' is assigned a value but never used.", "'openEditRoomModal' is assigned a value but never used.", "'useState' is defined but never used.", "'Card' is defined but never used.", "'useParams' is defined but never used.", "'Utils' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'AuthActions' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Routers' is defined but never used.", "'setEmail' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'FeedbackActions' is defined but never used.", "'setLoading' is assigned a value but never used.", "Expected '!==' and instead saw '!='.", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "React Hook useEffect has missing dependencies: 'createHotel.checkCreateHotel' and 'navigate'. Either include them or remove the dependency array.", ["846"], "React Hook useEffect has a missing dependency: 'validateForm'. Either include it or remove the dependency array.", ["847"], "React Hook useEffect has a missing dependency: 'localImages'. Either include it or remove the dependency array.", ["848"], "React Hook useEffect has a missing dependency: 'validateTimes'. Either include it or remove the dependency array.", ["849"], "'Container' is defined but never used.", "React Hook useEffect has a missing dependency: 'id'. Either include it or remove the dependency array.", ["850"], "React Hook useEffect has missing dependencies: 'selectedCity' and 'selectedDistrict'. Either include them or remove the dependency array.", ["851"], "React Hook useEffect has a missing dependency: 'selectedDistrict'. Either include it or remove the dependency array.", ["852"], "React Hook useEffect has a missing dependency: 'selectedWard'. Either include it or remove the dependency array.", ["853"], "'InputGroup' is defined but never used.", "'Modal' is defined but never used.", "'Alert' is defined but never used.", "'Spinner' is defined but never used.", "'location' is assigned a value but never used.", "'currentRoom' is assigned a value but never used.", "'setCurrentRoom' is assigned a value but never used.", "'roomTypes' is assigned a value but never used.", "'imageFiles' is assigned a value but never used.", "'setImageFiles' is assigned a value but never used.", "'Wifi' is defined but never used.", "'Tv' is defined but never used.", "'Droplet' is defined but never used.", "'Wind' is defined but never used.", "'Cup' is defined but never used.", "'Building' is defined but never used.", "'PersonCheck' is defined but never used.", "'ShieldCheck' is defined but never used.", "'setFormData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchHotelInfo'. Either include it or remove the dependency array.", ["854"], "React Hook useEffect has missing dependencies: 'Auth._id' and 'fetchHotelInfo'. Either include them or remove the dependency array.", ["855"], "'ToastProvider' is defined but never used.", "'DashBoardPage' is defined but never used.", "'Manager' is defined but never used.", "'Socket' is assigned a value but never used.", "'onError' is assigned a value but never used.", "'status' is assigned a value but never used.", "'msg' is assigned a value but never used.", "'Hotel' is defined but never used.", "no-dupe-keys", "Duplicate key 'error'.", "ObjectExpression", "Duplicate key 'phoneNumber'.", "Duplicate key 'email'.", "'Upload' is defined but never used.", "'X' is defined but never used.", "'bedCount' is assigned a value but never used.", "'setBedCount' is assigned a value but never used.", ["856"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "'Line' is defined but never used.", "'Pie' is defined but never used.", "'Doughnut' is defined but never used.", "'initializeSocket' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["857"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["858"], ["859"], "'Bar' is defined but never used.", "'currentYear' is assigned a value but never used.", "'currentMonth' is assigned a value but never used.", "'totalCount' is assigned a value but never used.", "'fallbackRevenueData' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Table' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRooms'. Either include it or remove the dependency array.", ["860"], "'getTotalBeds' is assigned a value but never used.", "'renderIcon' is assigned a value but never used.", "'Badge' is defined but never used.", "'useLocation' is defined but never used.", "'useAppSelector' is defined but never used.", "'hotelInfo' is assigned a value but never used.", "'setHotelInfo' is assigned a value but never used.", "'currentOption' is assigned a value but never used.", "'setCurrentOption' is assigned a value but never used.", "'onFailed' is assigned a value but never used.", "Duplicate key 'type'.", "React Hook useEffect has a missing dependency: 'fetchUserReports'. Either include it or remove the dependency array.", ["861"], "'reservationCustomer' is assigned a value but never used.", ["862"], ["863"], {"desc": "864", "fix": "865"}, {"desc": "866", "fix": "867"}, {"desc": "868", "fix": "869"}, {"desc": "870", "fix": "871"}, {"desc": "872", "fix": "873"}, {"desc": "874", "fix": "875"}, {"desc": "876", "fix": "877"}, {"desc": "876", "fix": "878"}, {"desc": "879", "fix": "880"}, {"desc": "881", "fix": "882"}, {"desc": "883", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "881", "fix": "897"}, {"desc": "898", "fix": "899"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "900", "text": "901"}, "Update the dependencies array to be: [createHotel.checkCreateHotel, navigate]", {"range": "902", "text": "903"}, "Update the dependencies array to be: [star, description, images, localImages, validateForm]", {"range": "904", "text": "905"}, "Update the dependencies array to be: [localImages]", {"range": "906", "text": "907"}, "Update the dependencies array to be: [checkInStart, checkInEnd, checkOutStart, checkOutEnd, validateTimes]", {"range": "908", "text": "909"}, "Update the dependencies array to be: [id, location.state.id]", {"range": "910", "text": "911"}, "Update the dependencies array to be: [selectedCity, selectedDistrict]", {"range": "912", "text": "913"}, {"range": "914", "text": "913"}, "Update the dependencies array to be: [selectedDistrict, selectedWard]", {"range": "915", "text": "916"}, "Update the dependencies array to be: [fetchHotelInfo]", {"range": "917", "text": "918"}, "Update the dependencies array to be: [Auth._id, fetchHotelInfo]", {"range": "919", "text": "920"}, "Update the dependencies array to be: [fetchHotelInfo, show]", {"range": "921", "text": "922"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "923", "text": "924"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "925", "text": "926"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "927", "text": "928"}, "Update the dependencies array to be: [fetchRooms, hotelDetail._id]", {"range": "929", "text": "930"}, "Update the dependencies array to be: [Auth._id, fetchUserReports]", {"range": "931", "text": "932"}, {"range": "933", "text": "918"}, "Update the dependencies array to be: [hotelId, dispatch, searchParams, fetchRooms]", {"range": "934", "text": "935"}, [3411, 3422], "[Auth?._id, dispatch]", [621, 623], "[createHotel.checkCreateHotel, navigate]", [2262, 2302], "[star, description, images, localImages, validateForm]", [8021, 8023], "[localImages]", [3393, 3447], "[checkInStart, checkInEnd, checkOutStart, checkOutEnd, validateTimes]", [803, 823], "[id, location.state.id]", [1949, 1951], "[selectedCity, selectedDistrict]", [2651, 2665], [3161, 3179], "[selected<PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON>]", [2106, 2108], "[fetchHotelInfo]", [1534, 1536], "[Auth._id, fetchHotelInfo]", [1847, 1853], "[fetchHotelInfo, show]", [2805, 2807], "[fetchAllUser]", [2864, 2878], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4513, 4551], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [1145, 1162], "[fetchRooms, hotelDetail._id]", [1336, 1346], "[Auth._id, fetchUserReports]", [3249, 3251], [4632, 4665], "[hotelId, dispatch, searchParams, fetchRooms]"]