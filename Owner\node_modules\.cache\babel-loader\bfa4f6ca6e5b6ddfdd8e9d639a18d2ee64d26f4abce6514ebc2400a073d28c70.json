{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"workAsyncStorageInstance\", {\n  enumerable: true,\n  get: function () {\n    return workAsyncStorageInstance;\n  }\n});\nconst _asynclocalstorage = require(\"./async-local-storage\");\nconst workAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();", "map": {"version": 3, "names": ["workAsyncStorageInstance", "_asynclocalstorage", "createAsyncLocalStorage"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\server\\app-render\\work-async-storage-instance.ts"], "sourcesContent": ["import type { WorkAsyncStorage } from './work-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const workAsyncStorageInstance: WorkAsyncStorage =\n  createAsyncLocalStorage()\n"], "mappings": ";;;;;+BAGa;;;WAAAA,wBAAA;;;mCAF2B;AAEjC,MAAMA,wBAAA,GACX,IAAAC,kBAAA,CAAAC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}