{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\revenue\\\\RevenuePage.jsx\",\n  _s = $RefreshSig$();\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\nimport { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport Utils from \"@utils/Utils\";\nimport Factories from \"@redux/room/factories\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReservationActions = {\n  FETCH_RESERVATIONS: \"FETCH_RESERVATIONS\",\n  FETCH_RESERVATIONS_SUCCESS: \"FETCH_RESERVATIONS_SUCCESS\",\n  FETCH_RESERVATIONS_FAILURE: \"FETCH_RESERVATIONS_FAILURE\"\n};\nconst getRecentYears = (num = 5) => {\n  const currentYear = new Date().getFullYear();\n  return Array.from({\n    length: num\n  }, (_, i) => currentYear - i);\n};\nconst RevenuePage = () => {\n  _s();\n  var _realData$monthlyReve, _realData$revenueByRo3, _realData$monthlyReve2, _realData$revenueByRo4;\n  const dispatch = useDispatch();\n  const {\n    reservations\n  } = useSelector(state => state.Reservation);\n  const hotelDetail = useSelector(state => state.Hotel.hotel);\n  const hotelId = hotelDetail === null || hotelDetail === void 0 ? void 0 : hotelDetail._id;\n\n  // Đặt các biến thời gian ở đầu component để mọi nơi đều dùng được\n  const now = new Date();\n  const currentYear = now.getFullYear();\n  const currentMonth = now.getMonth() + 1;\n\n  // State cho filter mới\n  const [periodType, setPeriodType] = useState(\"month\"); // \"month\" | \"year\"\n  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\n\n  // State for real data\n  const [realData, setRealData] = useState(null);\n\n  // State for rooms\n  const [rooms, setRooms] = useState([]);\n\n  // Helper to get month name in Vietnamese\n  const getMonthName = month => {\n    const monthNames = [\"T1\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\", \"T8\", \"T9\", \"T10\", \"T11\", \"T12\"];\n    return monthNames[month - 1] || `T${month}`;\n  };\n\n  // Helper for currency formatting\n  const formatCurrency = amount => {\n    if (typeof amount !== \"number\") return amount;\n    return Utils.formatCurrency(amount);\n  };\n  useEffect(() => {\n    // Fetch reservations data using Redux like Transaction page\n    dispatch({\n      type: ReservationActions.FETCH_RESERVATIONS,\n      payload: {\n        year: selectedYear,\n        sort: \"desc\"\n      }\n    });\n  }, [dispatch, selectedYear]);\n  useEffect(() => {\n    if (hotelId) {\n      Factories.fetchRoomByHotelId(hotelId).then(response => {\n        setRooms(Array.isArray(response === null || response === void 0 ? void 0 : response.rooms) ? response.rooms : []);\n      });\n    }\n  }, [hotelId]);\n\n  // Process reservations data to create monthly stats\n  useEffect(() => {\n    if (reservations && reservations.length > 0) {\n      console.log(\"Processing reservations:\", reservations);\n      const currentYear = new Date().getFullYear();\n      const currentMonth = new Date().getMonth() + 1;\n      const monthlyStats = [];\n\n      // Lọc reservation chỉ của khách sạn owner hiện tại\n      const filteredReservations = hotelId ? reservations.filter(res => {\n        var _res$hotel;\n        const resHotelId = ((_res$hotel = res.hotel) === null || _res$hotel === void 0 ? void 0 : _res$hotel._id) || res.hotel;\n        return (resHotelId === null || resHotelId === void 0 ? void 0 : resHotelId.toString()) === (hotelId === null || hotelId === void 0 ? void 0 : hotelId.toString());\n      }) : [];\n\n      // Group reservations by month\n      for (let month = 1; month <= 12; month++) {\n        // Thay đổi từ currentMonth thành 12\n        const monthReservations = filteredReservations.filter(res => {\n          const createdAt = new Date(res.createdAt);\n          return createdAt.getFullYear() === selectedYear &&\n          // Sử dụng selectedYear\n          createdAt.getMonth() + 1 === month && res.status !== \"NOT PAID\";\n        });\n        const revenue = monthReservations.reduce((sum, res) => {\n          const price = res.finalPrice > 0 ? res.finalPrice : res.totalPrice;\n          return sum + price;\n        }, 0);\n\n        // Tính hoa hồng chỉ cho đơn online\n        const onlineReservations = monthReservations.filter(res => res.status !== \"OFFLINE\");\n        const onlineRevenue = onlineReservations.reduce((sum, res) => {\n          const price = res.finalPrice > 0 ? res.finalPrice : res.totalPrice;\n          return sum + price;\n        }, 0);\n        const commission = Math.floor(onlineRevenue * 0.12);\n        const actualAmountToHost = revenue - commission;\n\n        // Tính toán trạng thái thanh toán dựa trên trạng thái reservation\n        let paymentStatus = null;\n        if (monthReservations.length > 0) {\n          const completedCount = monthReservations.filter(res => res.status === \"COMPLETED\" || res.status === \"CHECKED OUT\").length;\n          const pendingCount = monthReservations.filter(res => res.status === \"PENDING\" || res.status === \"BOOKED\" || res.status === \"CHECKED IN\").length;\n          const notPaidCount = monthReservations.filter(res => res.status === \"NOT PAID\" || res.status === \"CANCELLED\").length;\n          if (notPaidCount > 0 && completedCount === 0) {\n            paymentStatus = \"NOT_PAID\";\n          } else if (completedCount > 0 && pendingCount === 0) {\n            paymentStatus = \"PAID\";\n          } else if (completedCount > 0 && pendingCount > 0) {\n            paymentStatus = \"PARTIAL\";\n          } else if (pendingCount > 0) {\n            paymentStatus = \"PENDING\";\n          }\n        }\n        monthlyStats.push({\n          month,\n          year: selectedYear,\n          // Sử dụng selectedYear\n          revenue,\n          commission,\n          actualAmountToHost,\n          reservationCount: monthReservations.length,\n          monthlyPayment: 0,\n          paymentStatus\n        });\n      }\n\n      // Calculate room type stats với logic mới\n      const roomTypeStats = [];\n      const roomNames = rooms.map(room => room.name);\n      roomNames.forEach(roomName => {\n        var _monthlyStats$find;\n        // Lọc theo periodType và thời gian\n        let relevantReservations = filteredReservations.filter(res => {\n          var _res$rooms;\n          return ((_res$rooms = res.rooms) === null || _res$rooms === void 0 ? void 0 : _res$rooms.some(room => {\n            var _room$room;\n            return ((_room$room = room.room) === null || _room$room === void 0 ? void 0 : _room$room.name) === roomName;\n          })) && res.status !== \"NOT PAID\";\n        });\n\n        // Thêm filter theo thời gian\n        if (periodType === \"month\") {\n          relevantReservations = relevantReservations.filter(res => {\n            const createdAt = new Date(res.createdAt);\n            return createdAt.getFullYear() === selectedYear && createdAt.getMonth() + 1 === selectedMonth;\n          });\n        } else if (periodType === \"year\") {\n          relevantReservations = relevantReservations.filter(res => {\n            const createdAt = new Date(res.createdAt);\n            return createdAt.getFullYear() === selectedYear;\n          });\n        }\n        const roomRevenue = relevantReservations.reduce((sum, res) => {\n          const price = res.finalPrice > 0 ? res.finalPrice : res.totalPrice;\n          return sum + price;\n        }, 0);\n        const quantity = relevantReservations.reduce((sum, res) => {\n          var _res$rooms2;\n          return sum + (((_res$rooms2 = res.rooms) === null || _res$rooms2 === void 0 ? void 0 : _res$rooms2.filter(room => {\n            var _room$room2;\n            return ((_room$room2 = room.room) === null || _room$room2 === void 0 ? void 0 : _room$room2.name) === roomName;\n          }).reduce((qSum, room) => qSum + room.quantity, 0)) || 0);\n        }, 0);\n        const avgPrice = relevantReservations.length > 0 ? roomRevenue / relevantReservations.length : 0;\n\n        // Tính % dựa trên doanh thu của kỳ hiện tại\n        const periodRevenue = periodType === \"month\" ? ((_monthlyStats$find = monthlyStats.find(m => m.month === selectedMonth)) === null || _monthlyStats$find === void 0 ? void 0 : _monthlyStats$find.revenue) || 0 : monthlyStats.reduce((sum, m) => sum + m.revenue, 0);\n        const percent = periodRevenue > 0 ? (roomRevenue / periodRevenue * 100).toFixed(1) : 0;\n        if (roomRevenue > 0 || quantity > 0) {\n          // Chỉ thêm room có dữ liệu\n          roomTypeStats.push({\n            type: roomName,\n            quantity,\n            avgPrice,\n            revenue: roomRevenue,\n            percent\n          });\n        }\n      });\n\n      // Tính toán dữ liệu kênh đặt phòng - sử dụng tất cả reservation (không chỉ tháng hiện tại)\n      const websiteReservations = reservations.filter(res => res.status !== \"OFFLINE\");\n      const offlineReservations = reservations.filter(res => res.status === \"OFFLINE\");\n      const websiteCount = websiteReservations.length;\n      const offlineCount = offlineReservations.length;\n      const totalCount = websiteCount + offlineCount;\n      const bookingChannelData = {\n        labels: [\"Đặt phòng qua website\", \"Đặt phòng offline\"],\n        datasets: [{\n          data: [websiteCount, offlineCount],\n          backgroundColor: [\"#4361ee\", \"#f72585\"],\n          borderWidth: 1\n        }]\n      };\n      setRealData({\n        monthlyRevenueStats: monthlyStats,\n        revenueByRoomType: roomTypeStats,\n        totalRevenue: monthlyStats.reduce((sum, m) => sum + m.revenue, 0),\n        completedRevenue: monthlyStats.reduce((sum, m) => sum + m.revenue, 0),\n        revpar: 0,\n        adr: 0,\n        profit: monthlyStats.reduce((sum, m) => sum + m.actualAmountToHost, 0),\n        bookingChannelData\n      });\n    }\n  }, [reservations, selectedYear, selectedMonth, periodType, rooms, hotelId]); // Thêm dependencies\n\n  // Fallback mock data - chỉ hiển thị từ tháng 1 đến tháng hiện tại\n  const fallbackRevenueData = {\n    labels: Array.from({\n      length: currentMonth\n    }, (_, i) => getMonthName(i + 1)),\n    datasets: [{\n      label: \"Doanh thu thực tế\",\n      data: Array.from({\n        length: currentMonth\n      }, (_, i) => Math.floor(Math.random() * 50000) + 10000),\n      borderColor: \"#4361ee\",\n      backgroundColor: \"rgba(67, 97, 238, 0.1)\",\n      tension: 0.4,\n      fill: true\n    }]\n  };\n\n  // Room type rows - chỉ hiển thị dữ liệu thực tế\n  const roomTypeRows = ((_realData$revenueByRo, _realData$revenueByRo2) => {\n    console.log(\"realData?.revenueByRoomType:\", realData === null || realData === void 0 ? void 0 : realData.revenueByRoomType);\n    console.log(\"realData?.revenueByRoomType?.length:\", realData === null || realData === void 0 ? void 0 : (_realData$revenueByRo = realData.revenueByRoomType) === null || _realData$revenueByRo === void 0 ? void 0 : _realData$revenueByRo.length);\n    if ((realData === null || realData === void 0 ? void 0 : (_realData$revenueByRo2 = realData.revenueByRoomType) === null || _realData$revenueByRo2 === void 0 ? void 0 : _realData$revenueByRo2.length) > 0) {\n      console.log(\"Using real room type data\");\n      return realData.revenueByRoomType.map(row => ({\n        type: row.type,\n        quantity: row.quantity,\n        avgPrice: formatCurrency(row.avgPrice),\n        revenue: formatCurrency(row.revenue),\n        percent: row.percent + \"%\"\n      }));\n    } else {\n      console.log(\"No real room type data available\");\n      return [];\n    }\n  })();\n\n  // Hàm lọc dữ liệu theo periodType, selectedMonth, selectedYear\n  const getFilteredMonthlyStats = () => {\n    if (!(realData !== null && realData !== void 0 && realData.monthlyRevenueStats)) return [];\n    if (periodType === \"month\") {\n      // Khi filter là tháng, chỉ lấy dữ liệu của tháng được chọn (cho bảng/tổng hợp),\n      // nhưng biểu đồ xu hướng sẽ lấy từ T1 đến tháng hiện tại của năm hiện tại\n      return realData.monthlyRevenueStats.filter(item => item.month === selectedMonth && item.year === selectedYear);\n    } else if (periodType === \"year\") {\n      // Khi filter là năm, lấy tất cả tháng của năm đó\n      return realData.monthlyRevenueStats.filter(item => item.year === selectedYear);\n    }\n    return [];\n  };\n\n  // Sử dụng dữ liệu đã lọc cho các thống kê\n  const filteredStats = getFilteredMonthlyStats();\n\n  // Biểu đồ doanh thu (xu hướng doanh thu) luôn là 12 tháng\n  let revenueData;\n  const stats = (realData === null || realData === void 0 ? void 0 : (_realData$monthlyReve = realData.monthlyRevenueStats) === null || _realData$monthlyReve === void 0 ? void 0 : _realData$monthlyReve.filter(item => item.year === selectedYear)) || [];\n  revenueData = {\n    labels: Array.from({\n      length: 12\n    }, (_, i) => getMonthName(i + 1)),\n    datasets: [{\n      label: \"Doanh thu thực tế\",\n      data: Array.from({\n        length: 12\n      }, (_, i) => {\n        const stat = stats.find(s => s.month === i + 1);\n        return stat ? stat.revenue : 0;\n      }),\n      borderColor: \"#4361ee\",\n      backgroundColor: \"rgba(67, 97, 238, 0.1)\",\n      tension: 0.4,\n      fill: true\n    }]\n  };\n\n  // KPI values\n  const totalRevenue = filteredStats.reduce((sum, item) => sum + item.revenue, 0);\n  const totalCommission = filteredStats.reduce((sum, item) => sum + (item.commission || 0), 0);\n  const totalActualAmount = filteredStats.reduce((sum, item) => sum + (item.actualAmountToHost || 0), 0);\n  const totalReservation = filteredStats.reduce((sum, item) => sum + (item.reservationCount || 0), 0);\n  const avgRevenue = filteredStats.length > 0 ? totalRevenue / filteredStats.length : 0;\n\n  // Hàm lọc dữ liệu kênh đặt phòng theo periodType\n  const getFilteredBookingChannelData = () => {\n    if (!reservations || reservations.length === 0) {\n      return {\n        labels: [\"Đặt phòng qua website\", \"Đặt phòng offline\"],\n        datasets: [{\n          data: [0, 0],\n          backgroundColor: [\"#4361ee\", \"#f72585\"],\n          borderWidth: 1\n        }]\n      };\n    }\n    let filteredReservations = [];\n    if (periodType === \"month\") {\n      filteredReservations = reservations.filter(res => {\n        const createdAt = new Date(res.createdAt);\n        return createdAt.getFullYear() === selectedYear && createdAt.getMonth() + 1 === selectedMonth;\n      });\n    } else if (periodType === \"year\") {\n      filteredReservations = reservations.filter(res => {\n        const createdAt = new Date(res.createdAt);\n        return createdAt.getFullYear() === selectedYear;\n      });\n    }\n    const websiteReservations = filteredReservations.filter(res => res.status !== \"OFFLINE\");\n    const offlineReservations = filteredReservations.filter(res => res.status === \"OFFLINE\");\n    const websiteCount = websiteReservations.length;\n    const offlineCount = offlineReservations.length;\n    return {\n      labels: [\"Đặt phòng qua website\", \"Đặt phòng offline\"],\n      datasets: [{\n        data: [websiteCount, offlineCount],\n        backgroundColor: [\"#4361ee\", \"#f72585\"],\n        borderWidth: 1\n      }]\n    };\n  };\n\n  // Booking channel data - sử dụng dữ liệu đã lọc theo periodType\n  const bookingChannelData = getFilteredBookingChannelData();\n  // Kiểm tra dữ liệu kênh đặt phòng có dữ liệu không\n  const hasBookingData = bookingChannelData.datasets[0].data.some(val => val > 0);\n\n  // UI filter mới\n  const years = getRecentYears(5);\n  // months: nếu là năm hiện tại thì chỉ list tới tháng hiện tại, nếu là năm trước thì đủ 12 tháng\n  const months = Array.from({\n    length: selectedYear === currentYear ? currentMonth : 12\n  }, (_, i) => ({\n    value: i + 1,\n    label: `Tháng ${i + 1}`\n  }));\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Ph\\xE2n t\\xEDch doanh thu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Theo d\\xF5i v\\xE0 ph\\xE2n t\\xEDch doanh thu c\\u1EE7a kh\\xE1ch s\\u1EA1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select form-select-sm me-2\",\n          value: periodType,\n          onChange: e => {\n            setPeriodType(e.target.value);\n            // Reset filter value khi đổi loại kỳ\n            if (e.target.value === \"month\") {\n              setSelectedMonth(new Date().getMonth() + 1);\n              setSelectedYear(new Date().getFullYear());\n            } else if (e.target.value === \"year\") {\n              setSelectedYear(new Date().getFullYear());\n            }\n          },\n          style: {\n            width: 120\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"month\",\n            children: \"Th\\xE1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"year\",\n            children: \"N\\u0103m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), periodType === \"month\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select form-select-sm me-2\",\n            value: selectedMonth,\n            onChange: e => setSelectedMonth(Number(e.target.value)),\n            style: {\n              width: 110\n            },\n            children: months.map(m => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: m.value,\n              children: m.label\n            }, m.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select form-select-sm me-2\",\n            value: selectedYear,\n            onChange: e => setSelectedYear(Number(e.target.value)),\n            style: {\n              width: 90\n            },\n            children: years.map(y => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: y,\n              children: y\n            }, y, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), periodType === \"year\" && /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select form-select-sm me-2\",\n          value: selectedYear,\n          onChange: e => setSelectedYear(Number(e.target.value)),\n          style: {\n            width: 90\n          },\n          children: years.map(y => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: y,\n            children: y\n          }, y, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), filteredStats.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-muted\",\n              children: periodType === \"month\" ? \"Doanh thu tháng hiện tại\" : \"Tổng doanh thu năm nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mb-0\",\n              children: formatCurrency(totalRevenue)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [totalReservation, \" \\u0111\\u1EB7t ph\\xF2ng\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-danger\",\n                children: [\"Hoa h\\u1ED3ng: \", formatCurrency(totalCommission)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-success\",\n                children: [\"Th\\u1EF1c nh\\u1EADn: \", formatCurrency(totalActualAmount)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-muted\",\n              children: periodType === \"month\" ? \"Trung bình tháng\" : \"Trung bình năm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mb-0\",\n              children: formatCurrency(avgRevenue)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"S\\u1ED1 k\\u1EF3: \", filteredStats.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-danger\",\n                children: [\"T\\u1ED5ng hoa h\\u1ED3ng: \", formatCurrency(totalCommission)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-success\",\n                children: [\"T\\u1ED5ng th\\u1EF1c nh\\u1EADn: \", formatCurrency(totalActualAmount)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-muted\",\n              children: \"L\\u1EE3i nhu\\u1EADn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mb-0\",\n              children: formatCurrency(totalActualAmount)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: periodType === \"month\" ? `Tháng ${selectedMonth} này` : `Năm ${selectedYear} này`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-muted\",\n              children: \"T\\u1ED5ng \\u0111\\u1EB7t ph\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mb-0\",\n              children: totalReservation\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Trung b\\xECnh: \", filteredStats.length > 0 ? Math.round(totalReservation / filteredStats.length) : 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-warning\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"bi bi-exclamation-triangle me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 11\n      }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u doanh thu cho k\\u1EF3 n\\xE0y. Vui l\\xF2ng ki\\u1EC3m tra l\\u1EA1i sau.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title mb-4\",\n              children: \"Xu h\\u01B0\\u1EDBng doanh thu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              data: revenueData,\n              options: {\n                responsive: true,\n                plugins: {\n                  legend: {\n                    position: \"top\"\n                  },\n                  tooltip: {\n                    callbacks: {\n                      label: function (context) {\n                        return context.dataset.label + ': ' + formatCurrency(context.parsed.y);\n                      }\n                    }\n                  }\n                },\n                scales: {\n                  y: {\n                    beginAtZero: false,\n                    grid: {\n                      drawBorder: false\n                    },\n                    ticks: {\n                      callback: value => formatCurrency(value)\n                    }\n                  },\n                  x: {\n                    grid: {\n                      display: false\n                    }\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title mb-4\",\n              children: [\"K\\xEAnh \\u0111\\u1EB7t ph\\xF2ng (\", periodType === \"month\" ? \"Tháng này\" : \"Năm nay\", \")\", reservations && reservations.length > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-success ms-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-check-circle\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this), \" D\\u1EEF li\\u1EC7u th\\u1EF1c t\\u1EBF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this), (!reservations || reservations.length === 0) && /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-warning ms-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-exclamation-triangle\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this), \" Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"  -container\",\n              children: hasBookingData ? /*#__PURE__*/_jsxDEV(Pie, {\n                data: bookingChannelData,\n                options: {\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      position: \"bottom\"\n                    },\n                    tooltip: {\n                      callbacks: {\n                        label: function (context) {\n                          const total = context.dataset.data.reduce((a, b) => a + b, 0);\n                          const percentage = total > 0 ? (context.parsed / total * 100).toFixed(1) : 0;\n                          return context.label + ': ' + context.parsed + ' (' + percentage + '%)';\n                        }\n                      }\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert alert-info mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-info-circle me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 21\n                }, this), \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u \\u0111\\u1EB7t ph\\xF2ng cho k\\u1EF3 n\\xE0y.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"card-title mb-4\",\n          children: [\"Ph\\xE2n t\\xEDch doanh thu theo t\\xEAn ph\\xF2ng\", periodType === \"month\" ? ` trong tháng ${selectedMonth}/${selectedYear}` : ` trong năm ${selectedYear}`, (realData === null || realData === void 0 ? void 0 : (_realData$revenueByRo3 = realData.revenueByRoomType) === null || _realData$revenueByRo3 === void 0 ? void 0 : _realData$revenueByRo3.length) > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-success ms-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-check-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this), \" D\\u1EEF li\\u1EC7u th\\u1EF1c t\\u1EBF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 15\n          }, this), (!(realData !== null && realData !== void 0 && realData.revenueByRoomType) || realData.revenueByRoomType.length === 0) && /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-warning ms-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-exclamation-triangle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 17\n            }, this), \" Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"table-light\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"T\\xEAn ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"S\\u1ED1 l\\u01B0\\u1EE3ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Gi\\xE1 trung b\\xECnh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Doanh thu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"% T\\u1ED5ng doanh thu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: roomTypeRows.length > 0 ? roomTypeRows.map((row, idx) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: row.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: row.quantity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: row.avgPrice\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: row.revenue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: row.percent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 23\n                }, this)]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 21\n              }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: \"5\",\n                  className: \"text-center text-muted\",\n                  children: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u doanh thu theo t\\xEAn ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), (!(realData !== null && realData !== void 0 && realData.revenueByRoomType) || realData.revenueByRoomType.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-info mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this), \"D\\u1EEF li\\u1EC7u s\\u1EBD \\u0111\\u01B0\\u1EE3c hi\\u1EC3n th\\u1ECB khi c\\xF3 reservation trong h\\u1EC7 th\\u1ED1ng.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }, this), filteredStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"card-title mb-4\",\n          children: [\"Chi ti\\u1EBFt doanh thu (\", periodType === \"month\" ? \"Tháng này\" : \"Năm nay\", \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"table-light\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Th\\xE1ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"N\\u0103m\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Doanh thu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Hoa h\\u1ED3ng (\\u0111\\u01A1n online 12%)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Th\\u1EF1c nh\\u1EADn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"S\\u1ED1 l\\u01B0\\u1EE3ng \\u0111\\u1EB7t ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: filteredStats.map((item, idx) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: getMonthName(item.month)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: item.year\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"fw-bold\",\n                  children: formatCurrency(item.revenue)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"text-danger\",\n                  children: formatCurrency(item.commission || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"text-success\",\n                  children: formatCurrency(item.actualAmountToHost || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: item.reservationCount || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 23\n                }, this)]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 9\n    }, this), realData && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-success\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"bi bi-check-circle me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 11\n      }, this), \"D\\u1EEF li\\u1EC7u th\\u1EF1c t\\u1EBF \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c t\\u1EA3i th\\xE0nh c\\xF4ng t\\u1EEB h\\u1EC7 th\\u1ED1ng.\", /*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-muted ms-2\",\n        children: [\"C\\u1EADp nh\\u1EADt l\\u1EA7n cu\\u1ED1i: \", new Date().toLocaleString('vi-VN')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-muted\",\n        children: [\"S\\u1ED1 th\\xE1ng c\\xF3 d\\u1EEF li\\u1EC7u: \", ((_realData$monthlyReve2 = realData.monthlyRevenueStats) === null || _realData$monthlyReve2 === void 0 ? void 0 : _realData$monthlyReve2.length) || 0, \" | S\\u1ED1 lo\\u1EA1i ph\\xF2ng: \", ((_realData$revenueByRo4 = realData.revenueByRoomType) === null || _realData$revenueByRo4 === void 0 ? void 0 : _realData$revenueByRo4.length) || 0, \" | T\\u1ED5ng reservation: \", totalReservation || 0]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 9\n    }, this), !realData && reservations && reservations.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-warning\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"bi bi-exclamation-triangle me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 11\n      }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u reservation n\\xE0o trong h\\u1EC7 th\\u1ED1ng. Vui l\\xF2ng ki\\u1EC3m tra l\\u1EA1i sau.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(RevenuePage, \"Tf3qQVKBQbfu9tTjDoF6nSo0Kno=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = RevenuePage;\nexport default RevenuePage;\nvar _c;\n$RefreshReg$(_c, \"RevenuePage\");", "map": {"version": 3, "names": ["Line", "Bar", "Pie", "Doughnut", "useEffect", "useState", "useDispatch", "useSelector", "Utils", "Factories", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReservationActions", "FETCH_RESERVATIONS", "FETCH_RESERVATIONS_SUCCESS", "FETCH_RESERVATIONS_FAILURE", "getRecentYears", "num", "currentYear", "Date", "getFullYear", "Array", "from", "length", "_", "i", "RevenuePage", "_s", "_realData$monthlyReve", "_realData$revenueByRo3", "_realData$monthlyReve2", "_realData$revenueByRo4", "dispatch", "reservations", "state", "Reservation", "hotelDetail", "Hotel", "hotel", "hotelId", "_id", "now", "currentMonth", "getMonth", "periodType", "setPeriodType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "selected<PERSON>ear", "setSelectedYear", "realData", "setRealData", "rooms", "setRooms", "getMonthName", "month", "monthNames", "formatCurrency", "amount", "type", "payload", "year", "sort", "fetchRoomByHotelId", "then", "response", "isArray", "console", "log", "monthlyStats", "filteredReservations", "filter", "res", "_res$hotel", "resHotelId", "toString", "monthReservations", "createdAt", "status", "revenue", "reduce", "sum", "price", "finalPrice", "totalPrice", "onlineReservations", "onlineRevenue", "commission", "Math", "floor", "actualAmountToHost", "paymentStatus", "completedCount", "pendingCount", "notPaidCount", "push", "reservationCount", "monthlyPayment", "roomTypeStats", "roomNames", "map", "room", "name", "for<PERSON>ach", "roomName", "_monthlyStats$find", "relevantReservations", "_res$rooms", "some", "_room$room", "roomRevenue", "quantity", "_res$rooms2", "_room$room2", "qSum", "avgPrice", "periodRevenue", "find", "m", "percent", "toFixed", "websiteReservations", "offlineReservations", "websiteCount", "offlineCount", "totalCount", "bookingChannelData", "labels", "datasets", "data", "backgroundColor", "borderWidth", "monthlyRevenueStats", "revenueByRoomType", "totalRevenue", "completedRevenue", "revpar", "adr", "profit", "fallbackRevenueData", "label", "random", "borderColor", "tension", "fill", "roomTypeRows", "_realData$revenueByRo", "_realData$revenueByRo2", "row", "getFilteredMonthlyStats", "item", "filteredStats", "revenueData", "stats", "stat", "s", "totalCommission", "totalActualAmount", "totalReservation", "avgRevenue", "getFilteredBookingChannelData", "hasBookingData", "val", "years", "months", "value", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "style", "width", "Number", "y", "round", "options", "responsive", "plugins", "legend", "position", "tooltip", "callbacks", "context", "dataset", "parsed", "scales", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "x", "display", "maintainAspectRatio", "total", "a", "b", "percentage", "idx", "colSpan", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/revenue/RevenuePage.jsx"], "sourcesContent": ["import { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport Utils from \"@utils/Utils\";\r\nimport Factories from \"@redux/room/factories\";\r\n\r\nconst ReservationActions = {\r\n  FETCH_RESERVATIONS: \"FETCH_RESERVATIONS\",\r\n  FETCH_RESERVATIONS_SUCCESS: \"FETCH_RESERVATIONS_SUCCESS\",\r\n  FETCH_RESERVATIONS_FAILURE: \"FETCH_RESERVATIONS_FAILURE\",\r\n};\r\n\r\nconst getRecentYears = (num = 5) => {\r\n  const currentYear = new Date().getFullYear();\r\n  return Array.from({ length: num }, (_, i) => currentYear - i);\r\n};\r\n\r\nconst RevenuePage = () => {\r\n  const dispatch = useDispatch();\r\n  const { reservations } = useSelector((state) => state.Reservation);\r\n  const hotelDetail = useSelector(state => state.Hotel.hotel);\r\n  const hotelId = hotelDetail?._id;\r\n\r\n  // Đặt các biến thời gian ở đầu component để mọi nơi đều dùng được\r\n  const now = new Date();\r\n  const currentYear = now.getFullYear();\r\n  const currentMonth = now.getMonth() + 1;\r\n\r\n  // State cho filter mới\r\n  const [periodType, setPeriodType] = useState(\"month\"); // \"month\" | \"year\"\r\n  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);\r\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\r\n\r\n  // State for real data\r\n  const [realData, setRealData] = useState(null);\r\n\r\n  // State for rooms\r\n  const [rooms, setRooms] = useState([]);\r\n\r\n  // Helper to get month name in Vietnamese\r\n  const getMonthName = (month) => {\r\n    const monthNames = [\r\n      \"T1\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\",\r\n      \"T7\", \"T8\", \"T9\", \"T10\", \"T11\", \"T12\"\r\n    ];\r\n    return monthNames[month - 1] || `T${month}`;\r\n  };\r\n\r\n  // Helper for currency formatting\r\n  const formatCurrency = (amount) => {\r\n    if (typeof amount !== \"number\") return amount;\r\n    return Utils.formatCurrency(amount);\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Fetch reservations data using Redux like Transaction page\r\n    dispatch({\r\n      type: ReservationActions.FETCH_RESERVATIONS,\r\n      payload: {\r\n        year: selectedYear,\r\n        sort: \"desc\",\r\n      },\r\n    });\r\n  }, [dispatch, selectedYear]);\r\n\r\n  useEffect(() => {\r\n    if (hotelId) {\r\n      Factories.fetchRoomByHotelId(hotelId).then(response => {\r\n        setRooms(Array.isArray(response?.rooms) ? response.rooms : []);\r\n      });\r\n    }\r\n  }, [hotelId]);\r\n\r\n  // Process reservations data to create monthly stats\r\n  useEffect(() => {\r\n    if (reservations && reservations.length > 0) {\r\n      console.log(\"Processing reservations:\", reservations);\r\n      \r\n      const currentYear = new Date().getFullYear();\r\n      const currentMonth = new Date().getMonth() + 1;\r\n      const monthlyStats = [];\r\n\r\n      // Lọc reservation chỉ của khách sạn owner hiện tại\r\n      const filteredReservations = hotelId\r\n        ? reservations.filter(res => {\r\n            const resHotelId = res.hotel?._id || res.hotel;\r\n            return resHotelId?.toString() === hotelId?.toString();\r\n          })\r\n        : [];\r\n\r\n      // Group reservations by month\r\n      for (let month = 1; month <= 12; month++) { // Thay đổi từ currentMonth thành 12\r\n        const monthReservations = filteredReservations.filter(res => {\r\n          const createdAt = new Date(res.createdAt);\r\n          return createdAt.getFullYear() === selectedYear && // Sử dụng selectedYear\r\n                 createdAt.getMonth() + 1 === month &&\r\n                 res.status !== \"NOT PAID\";\r\n        });\r\n\r\n        const revenue = monthReservations.reduce((sum, res) => {\r\n          const price = res.finalPrice > 0 ? res.finalPrice : res.totalPrice;\r\n          return sum + price;\r\n        }, 0);\r\n\r\n        // Tính hoa hồng chỉ cho đơn online\r\n        const onlineReservations = monthReservations.filter(res => res.status !== \"OFFLINE\");\r\n        const onlineRevenue = onlineReservations.reduce((sum, res) => {\r\n          const price = res.finalPrice > 0 ? res.finalPrice : res.totalPrice;\r\n          return sum + price;\r\n        }, 0);\r\n        \r\n        const commission = Math.floor(onlineRevenue * 0.12);\r\n        const actualAmountToHost = revenue - commission;\r\n\r\n        // Tính toán trạng thái thanh toán dựa trên trạng thái reservation\r\n        let paymentStatus = null;\r\n        if (monthReservations.length > 0) {\r\n          const completedCount = monthReservations.filter(res => \r\n            res.status === \"COMPLETED\" || res.status === \"CHECKED OUT\"\r\n          ).length;\r\n          const pendingCount = monthReservations.filter(res => \r\n            res.status === \"PENDING\" || res.status === \"BOOKED\" || res.status === \"CHECKED IN\"\r\n          ).length;\r\n          const notPaidCount = monthReservations.filter(res => \r\n            res.status === \"NOT PAID\" || res.status === \"CANCELLED\"\r\n          ).length;\r\n\r\n          if (notPaidCount > 0 && completedCount === 0) {\r\n            paymentStatus = \"NOT_PAID\";\r\n          } else if (completedCount > 0 && pendingCount === 0) {\r\n            paymentStatus = \"PAID\";\r\n          } else if (completedCount > 0 && pendingCount > 0) {\r\n            paymentStatus = \"PARTIAL\";\r\n          } else if (pendingCount > 0) {\r\n            paymentStatus = \"PENDING\";\r\n          }\r\n        }\r\n\r\n        monthlyStats.push({\r\n          month,\r\n          year: selectedYear, // Sử dụng selectedYear\r\n          revenue,\r\n          commission,\r\n          actualAmountToHost,\r\n          reservationCount: monthReservations.length,\r\n          monthlyPayment: 0,\r\n          paymentStatus\r\n        });\r\n      }\r\n\r\n      // Calculate room type stats với logic mới\r\n      const roomTypeStats = [];\r\n      const roomNames = rooms.map(room => room.name);\r\n\r\n      roomNames.forEach(roomName => {\r\n        // Lọc theo periodType và thời gian\r\n        let relevantReservations = filteredReservations.filter(res => \r\n          res.rooms?.some(room => room.room?.name === roomName) &&\r\n          res.status !== \"NOT PAID\"\r\n        );\r\n\r\n        // Thêm filter theo thời gian\r\n        if (periodType === \"month\") {\r\n          relevantReservations = relevantReservations.filter(res => {\r\n            const createdAt = new Date(res.createdAt);\r\n            return createdAt.getFullYear() === selectedYear && \r\n                   createdAt.getMonth() + 1 === selectedMonth;\r\n          });\r\n        } else if (periodType === \"year\") {\r\n          relevantReservations = relevantReservations.filter(res => {\r\n            const createdAt = new Date(res.createdAt);\r\n            return createdAt.getFullYear() === selectedYear;\r\n          });\r\n        }\r\n\r\n        const roomRevenue = relevantReservations.reduce((sum, res) => {\r\n          const price = res.finalPrice > 0 ? res.finalPrice : res.totalPrice;\r\n          return sum + price;\r\n        }, 0);\r\n\r\n        const quantity = relevantReservations.reduce((sum, res) => \r\n          sum + (res.rooms?.filter(room => room.room?.name === roomName)\r\n            .reduce((qSum, room) => qSum + room.quantity, 0) || 0), 0\r\n        );\r\n\r\n        const avgPrice = relevantReservations.length > 0 ? roomRevenue / relevantReservations.length : 0;\r\n        \r\n        // Tính % dựa trên doanh thu của kỳ hiện tại\r\n        const periodRevenue = periodType === \"month\" \r\n          ? monthlyStats.find(m => m.month === selectedMonth)?.revenue || 0\r\n          : monthlyStats.reduce((sum, m) => sum + m.revenue, 0);\r\n        \r\n        const percent = periodRevenue > 0 ? ((roomRevenue / periodRevenue) * 100).toFixed(1) : 0;\r\n\r\n        if (roomRevenue > 0 || quantity > 0) { // Chỉ thêm room có dữ liệu\r\n          roomTypeStats.push({\r\n            type: roomName,\r\n            quantity,\r\n            avgPrice,\r\n            revenue: roomRevenue,\r\n            percent\r\n          });\r\n        }\r\n      });\r\n\r\n      // Tính toán dữ liệu kênh đặt phòng - sử dụng tất cả reservation (không chỉ tháng hiện tại)\r\n      const websiteReservations = reservations.filter(res => res.status !== \"OFFLINE\");\r\n      const offlineReservations = reservations.filter(res => res.status === \"OFFLINE\");\r\n      \r\n      const websiteCount = websiteReservations.length;\r\n      const offlineCount = offlineReservations.length;\r\n      const totalCount = websiteCount + offlineCount;\r\n\r\n      const bookingChannelData = {\r\n        labels: [\"Đặt phòng qua website\", \"Đặt phòng offline\"],\r\n        datasets: [{\r\n          data: [websiteCount, offlineCount],\r\n          backgroundColor: [\"#4361ee\", \"#f72585\"],\r\n          borderWidth: 1,\r\n        }]\r\n      };\r\n\r\n      setRealData({\r\n        monthlyRevenueStats: monthlyStats,\r\n        revenueByRoomType: roomTypeStats,\r\n        totalRevenue: monthlyStats.reduce((sum, m) => sum + m.revenue, 0),\r\n        completedRevenue: monthlyStats.reduce((sum, m) => sum + m.revenue, 0),\r\n        revpar: 0,\r\n        adr: 0,\r\n        profit: monthlyStats.reduce((sum, m) => sum + m.actualAmountToHost, 0),\r\n        bookingChannelData\r\n      });\r\n    }\r\n  }, [reservations, selectedYear, selectedMonth, periodType, rooms, hotelId]); // Thêm dependencies\r\n\r\n  // Fallback mock data - chỉ hiển thị từ tháng 1 đến tháng hiện tại\r\n  const fallbackRevenueData = {\r\n    labels: Array.from({ length: currentMonth }, (_, i) => getMonthName(i + 1)),\r\n    datasets: [\r\n      {\r\n        label: \"Doanh thu thực tế\",\r\n        data: Array.from({ length: currentMonth }, (_, i) => \r\n          Math.floor(Math.random() * 50000) + 10000\r\n        ),\r\n        borderColor: \"#4361ee\",\r\n        backgroundColor: \"rgba(67, 97, 238, 0.1)\",\r\n        tension: 0.4,\r\n        fill: true,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Room type rows - chỉ hiển thị dữ liệu thực tế\r\n  const roomTypeRows = (() => {\r\n    console.log(\"realData?.revenueByRoomType:\", realData?.revenueByRoomType);\r\n    console.log(\"realData?.revenueByRoomType?.length:\", realData?.revenueByRoomType?.length);\r\n    \r\n    if (realData?.revenueByRoomType?.length > 0) {\r\n      console.log(\"Using real room type data\");\r\n      return realData.revenueByRoomType.map(row => ({\r\n        type: row.type,\r\n        quantity: row.quantity,\r\n        avgPrice: formatCurrency(row.avgPrice),\r\n        revenue: formatCurrency(row.revenue),\r\n        percent: row.percent + \"%\"\r\n      }));\r\n    } else {\r\n      console.log(\"No real room type data available\");\r\n      return [];\r\n    }\r\n  })();\r\n\r\n  // Hàm lọc dữ liệu theo periodType, selectedMonth, selectedYear\r\n  const getFilteredMonthlyStats = () => {\r\n    if (!realData?.monthlyRevenueStats) return [];\r\n    if (periodType === \"month\") {\r\n      // Khi filter là tháng, chỉ lấy dữ liệu của tháng được chọn (cho bảng/tổng hợp),\r\n      // nhưng biểu đồ xu hướng sẽ lấy từ T1 đến tháng hiện tại của năm hiện tại\r\n      return realData.monthlyRevenueStats.filter(\r\n        item => item.month === selectedMonth && item.year === selectedYear\r\n      );\r\n    } else if (periodType === \"year\") {\r\n      // Khi filter là năm, lấy tất cả tháng của năm đó\r\n      return realData.monthlyRevenueStats.filter(\r\n        item => item.year === selectedYear\r\n      );\r\n    }\r\n    return [];\r\n  };\r\n\r\n  // Sử dụng dữ liệu đã lọc cho các thống kê\r\n  const filteredStats = getFilteredMonthlyStats();\r\n\r\n  // Biểu đồ doanh thu (xu hướng doanh thu) luôn là 12 tháng\r\n  let revenueData;\r\n  const stats = realData?.monthlyRevenueStats?.filter(item => item.year === selectedYear) || [];\r\n  revenueData = {\r\n    labels: Array.from({ length: 12 }, (_, i) => getMonthName(i + 1)),\r\n    datasets: [\r\n      {\r\n        label: \"Doanh thu thực tế\",\r\n        data: Array.from({ length: 12 }, (_, i) => {\r\n          const stat = stats.find(s => s.month === i + 1);\r\n          return stat ? stat.revenue : 0;\r\n        }),\r\n        borderColor: \"#4361ee\",\r\n        backgroundColor: \"rgba(67, 97, 238, 0.1)\",\r\n        tension: 0.4,\r\n        fill: true,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // KPI values\r\n  const totalRevenue = filteredStats.reduce((sum, item) => sum + item.revenue, 0);\r\n  const totalCommission = filteredStats.reduce((sum, item) => sum + (item.commission || 0), 0);\r\n  const totalActualAmount = filteredStats.reduce((sum, item) => sum + (item.actualAmountToHost || 0), 0);\r\n  const totalReservation = filteredStats.reduce((sum, item) => sum + (item.reservationCount || 0), 0);\r\n  const avgRevenue = filteredStats.length > 0 ? totalRevenue / filteredStats.length : 0;\r\n\r\n  // Hàm lọc dữ liệu kênh đặt phòng theo periodType\r\n  const getFilteredBookingChannelData = () => {\r\n    if (!reservations || reservations.length === 0) {\r\n      return {\r\n        labels: [\"Đặt phòng qua website\", \"Đặt phòng offline\"],\r\n        datasets: [{\r\n          data: [0, 0],\r\n          backgroundColor: [\"#4361ee\", \"#f72585\"],\r\n          borderWidth: 1,\r\n        }]\r\n      };\r\n    }\r\n    let filteredReservations = [];\r\n    if (periodType === \"month\") {\r\n      filteredReservations = reservations.filter(res => {\r\n        const createdAt = new Date(res.createdAt);\r\n        return createdAt.getFullYear() === selectedYear && createdAt.getMonth() + 1 === selectedMonth;\r\n      });\r\n    } else if (periodType === \"year\") {\r\n      filteredReservations = reservations.filter(res => {\r\n        const createdAt = new Date(res.createdAt);\r\n        return createdAt.getFullYear() === selectedYear;\r\n      });\r\n    }\r\n    const websiteReservations = filteredReservations.filter(res => res.status !== \"OFFLINE\");\r\n    const offlineReservations = filteredReservations.filter(res => res.status === \"OFFLINE\");\r\n    const websiteCount = websiteReservations.length;\r\n    const offlineCount = offlineReservations.length;\r\n    return {\r\n      labels: [\"Đặt phòng qua website\", \"Đặt phòng offline\"],\r\n      datasets: [{\r\n        data: [websiteCount, offlineCount],\r\n        backgroundColor: [\"#4361ee\", \"#f72585\"],\r\n        borderWidth: 1,\r\n      }]\r\n    };\r\n  };\r\n\r\n  // Booking channel data - sử dụng dữ liệu đã lọc theo periodType\r\n  const bookingChannelData = getFilteredBookingChannelData();\r\n  // Kiểm tra dữ liệu kênh đặt phòng có dữ liệu không\r\n  const hasBookingData = bookingChannelData.datasets[0].data.some(val => val > 0);\r\n\r\n  // UI filter mới\r\n  const years = getRecentYears(5);\r\n  // months: nếu là năm hiện tại thì chỉ list tới tháng hiện tại, nếu là năm trước thì đủ 12 tháng\r\n  const months = Array.from({ length: selectedYear === currentYear ? currentMonth : 12 }, (_, i) => ({ value: i + 1, label: `Tháng ${i + 1}` }));\r\n\r\n  return (\r\n    <>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <div>\r\n          <h4>Phân tích doanh thu</h4>\r\n          <p className=\"text-muted\">\r\n            Theo dõi và phân tích doanh thu của khách sạn\r\n          </p>\r\n        </div>\r\n        <div className=\"d-flex align-items-center gap-2\">\r\n          {/* Filter loại kỳ */}\r\n          <select\r\n            className=\"form-select form-select-sm me-2\"\r\n            value={periodType}\r\n            onChange={e => {\r\n              setPeriodType(e.target.value);\r\n              // Reset filter value khi đổi loại kỳ\r\n              if (e.target.value === \"month\") {\r\n                setSelectedMonth(new Date().getMonth() + 1);\r\n                setSelectedYear(new Date().getFullYear());\r\n              } else if (e.target.value === \"year\") {\r\n                setSelectedYear(new Date().getFullYear());\r\n              }\r\n            }}\r\n            style={{ width: 120 }}\r\n          >\r\n            <option value=\"month\">Tháng</option>\r\n            <option value=\"year\">Năm</option>\r\n          </select>\r\n          {/* Filter giá trị kỳ */}\r\n          {periodType === \"month\" && (\r\n            <>\r\n              <select\r\n                className=\"form-select form-select-sm me-2\"\r\n                value={selectedMonth}\r\n                onChange={e => setSelectedMonth(Number(e.target.value))}\r\n                style={{ width: 110 }}\r\n              >\r\n                {months.map(m => (\r\n                  <option key={m.value} value={m.value}>{m.label}</option>\r\n                ))}\r\n              </select>\r\n              <select\r\n                className=\"form-select form-select-sm me-2\"\r\n                value={selectedYear}\r\n                onChange={e => setSelectedYear(Number(e.target.value))}\r\n                style={{ width: 90 }}\r\n              >\r\n                {years.map(y => (\r\n                  <option key={y} value={y}>{y}</option>\r\n                ))}\r\n              </select>\r\n            </>\r\n          )}\r\n          {periodType === \"year\" && (\r\n            <select\r\n              className=\"form-select form-select-sm me-2\"\r\n              value={selectedYear}\r\n              onChange={e => setSelectedYear(Number(e.target.value))}\r\n              style={{ width: 90 }}\r\n            >\r\n              {years.map(y => (\r\n                <option key={y} value={y}>{y}</option>\r\n              ))}\r\n            </select>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Thêm thống kê tổng hợp từ filter */}\r\n      {filteredStats.length > 0 ? (\r\n        <div className=\"row mb-4\">\r\n          <div className=\"col-md-3\">\r\n            <div className=\"card h-100\">\r\n              <div className=\"card-body\">\r\n                <h6 className=\"text-muted\">\r\n                  {periodType === \"month\" ? \"Doanh thu tháng hiện tại\" : \"Tổng doanh thu năm nay\"}\r\n                </h6>\r\n                <h3 className=\"mb-0\">\r\n                  {formatCurrency(totalRevenue)}\r\n                </h3>\r\n                <small className=\"text-muted\">\r\n                  {totalReservation} đặt phòng\r\n                </small>\r\n                <div className=\"mt-2\">\r\n                  <small className=\"text-danger\">\r\n                    Hoa hồng: {formatCurrency(totalCommission)}\r\n                  </small>\r\n                  <br />\r\n                  <small className=\"text-success\">\r\n                    Thực nhận: {formatCurrency(totalActualAmount)}\r\n                  </small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"col-md-3\">\r\n            <div className=\"card h-100\">\r\n              <div className=\"card-body\">\r\n                <h6 className=\"text-muted\">\r\n                  {periodType === \"month\" ? \"Trung bình tháng\" : \"Trung bình năm\"}\r\n                </h6>\r\n                <h3 className=\"mb-0\">\r\n                  {formatCurrency(avgRevenue)}\r\n                </h3>\r\n                <small className=\"text-muted\">\r\n                  Số kỳ: {filteredStats.length}\r\n                </small>\r\n                <div className=\"mt-2\">\r\n                  <small className=\"text-danger\">\r\n                    Tổng hoa hồng: {formatCurrency(totalCommission)}\r\n                  </small>\r\n                  <br />\r\n                  <small className=\"text-success\">\r\n                    Tổng thực nhận: {formatCurrency(totalActualAmount)}\r\n                  </small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"col-md-3\">\r\n            <div className=\"card h-100\">\r\n              <div className=\"card-body\">\r\n                <h6 className=\"text-muted\">Lợi nhuận</h6>\r\n                <h3 className=\"mb-0\">\r\n                  {formatCurrency(totalActualAmount)}\r\n                </h3>\r\n                <small className=\"text-muted\">\r\n                  {periodType === \"month\" ? `Tháng ${selectedMonth} này` : `Năm ${selectedYear} này`}\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"col-md-3\">\r\n            <div className=\"card h-100\">\r\n              <div className=\"card-body\">\r\n                <h6 className=\"text-muted\">Tổng đặt phòng</h6>\r\n                <h3 className=\"mb-0\">\r\n                  {totalReservation}\r\n                </h3>\r\n                <small className=\"text-muted\">\r\n                  Trung bình: {filteredStats.length > 0 ? Math.round(totalReservation / filteredStats.length) : 0}\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"alert alert-warning\">\r\n          <i className=\"bi bi-exclamation-triangle me-2\"></i>\r\n          Chưa có dữ liệu doanh thu cho kỳ này. Vui lòng kiểm tra lại sau.\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"row mb-4\">\r\n        <div className=\"col-md-8\">\r\n          <div className=\"card\">\r\n            <div className=\"card-body\">\r\n              <h5 className=\"card-title mb-4\">Xu hướng doanh thu</h5>\r\n              <Line\r\n                data={revenueData}\r\n                options={{\r\n                  responsive: true,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"top\",\r\n                    },\r\n                    tooltip: {\r\n                      callbacks: {\r\n                        label: function(context) {\r\n                          return context.dataset.label + ': ' + formatCurrency(context.parsed.y);\r\n                        }\r\n                      }\r\n                    }\r\n                  },\r\n                  scales: {\r\n                    y: {\r\n                      beginAtZero: false,\r\n                      grid: {\r\n                        drawBorder: false,\r\n                      },\r\n                      ticks: {\r\n                        callback: (value) => formatCurrency(value),\r\n                      },\r\n                    },\r\n                    x: {\r\n                      grid: {\r\n                        display: false,\r\n                      },\r\n                    },\r\n                  },\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-md-4\">\r\n          <div className=\"card h-100\">\r\n            <div className=\"card-body\">\r\n              <h5 className=\"card-title mb-4\">\r\n                Kênh đặt phòng ({periodType === \"month\" ? \"Tháng này\" : \"Năm nay\"})\r\n                {reservations && reservations.length > 0 && (\r\n                  <small className=\"text-success ms-2\">\r\n                    <i className=\"bi bi-check-circle\"></i> Dữ liệu thực tế\r\n                  </small>\r\n                )}\r\n                {(!reservations || reservations.length === 0) && (\r\n                  <small className=\"text-warning ms-2\">\r\n                    <i className=\"bi bi-exclamation-triangle\"></i> Chưa có dữ liệu\r\n                  </small>\r\n                )}\r\n              </h5>\r\n              <div className=\"  -container\">\r\n                {hasBookingData ? (\r\n                  <Pie\r\n                    data={bookingChannelData}\r\n                    options={{\r\n                      responsive: true,\r\n                      maintainAspectRatio: false,\r\n                      plugins: {\r\n                        legend: {\r\n                          position: \"bottom\",\r\n                        },\r\n                        tooltip: {\r\n                          callbacks: {\r\n                            label: function(context) {\r\n                              const total = context.dataset.data.reduce((a, b) => a + b, 0);\r\n                              const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;\r\n                              return context.label + ': ' + context.parsed + ' (' + percentage + '%)';\r\n                            }\r\n                          }\r\n                        }\r\n                      },\r\n                    }}\r\n                  />\r\n                ) : (\r\n                  <div className=\"alert alert-info mt-3\">\r\n                    <i className=\"bi bi-info-circle me-2\"></i>\r\n                    Không có dữ liệu đặt phòng cho kỳ này.\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"card mb-4\">\r\n        <div className=\"card-body\">\r\n          <h5 className=\"card-title mb-4\">\r\n            Phân tích doanh thu theo tên phòng \r\n            {periodType === \"month\" \r\n              ? ` trong tháng ${selectedMonth}/${selectedYear}` \r\n              : ` trong năm ${selectedYear}`\r\n            }\r\n            {realData?.revenueByRoomType?.length > 0 && (\r\n              <small className=\"text-success ms-2\">\r\n                <i className=\"bi bi-check-circle\"></i> Dữ liệu thực tế\r\n              </small>\r\n            )}\r\n            {(!realData?.revenueByRoomType || realData.revenueByRoomType.length === 0) && (\r\n              <small className=\"text-warning ms-2\">\r\n                <i className=\"bi bi-exclamation-triangle\"></i> Chưa có dữ liệu\r\n              </small>\r\n            )}\r\n          </h5>\r\n          <div className=\"table-responsive\">\r\n            <table className=\"table\">\r\n              <thead className=\"table-light\">\r\n                <tr>\r\n                  <th>Tên phòng</th>\r\n                  <th>Số lượng</th>\r\n                  <th>Giá trung bình</th>\r\n                  <th>Doanh thu</th>\r\n                  <th>% Tổng doanh thu</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {roomTypeRows.length > 0 ? (\r\n                  roomTypeRows.map((row, idx) => (\r\n                    <tr key={idx}>\r\n                      <td>{row.type}</td>\r\n                      <td>{row.quantity}</td>\r\n                      <td>{row.avgPrice}</td>\r\n                      <td>{row.revenue}</td>\r\n                      <td>{row.percent}</td>\r\n                    </tr>\r\n                  ))\r\n                ) : (\r\n                  <tr>\r\n                    <td colSpan=\"5\" className=\"text-center text-muted\">\r\n                      Chưa có dữ liệu doanh thu theo tên phòng\r\n                    </td>\r\n                  </tr>\r\n                )}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          {(!realData?.revenueByRoomType || realData.revenueByRoomType.length === 0) && (\r\n            <div className=\"alert alert-info mt-3\">\r\n              <i className=\"bi bi-info-circle me-2\"></i>\r\n              Dữ liệu sẽ được hiển thị khi có reservation trong hệ thống.\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Thêm bảng doanh thu theo tháng */}\r\n      {filteredStats.length > 0 && (\r\n        <div className=\"card mb-4\">\r\n          <div className=\"card-body\">\r\n            <h5 className=\"card-title mb-4\">\r\n              Chi tiết doanh thu ({periodType === \"month\" ? \"Tháng này\" : \"Năm nay\"})\r\n            </h5>\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table\">\r\n                <thead className=\"table-light\">\r\n                  <tr>\r\n                    <th>Tháng</th>\r\n                    <th>Năm</th>\r\n                    <th>Doanh thu</th>\r\n                    <th>Hoa hồng (đơn online 12%)</th>\r\n                    <th>Thực nhận</th>\r\n                    {/* <th>Thanh toán hàng tháng</th> */}\r\n                    <th>Số lượng đặt phòng</th>\r\n                    {/* <th>Trạng thái thanh toán</th> */}\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {filteredStats.map((item, idx) => (\r\n                    <tr key={idx}>\r\n                      <td>{getMonthName(item.month)}</td>\r\n                      <td>{item.year}</td>\r\n                      <td className=\"fw-bold\">{formatCurrency(item.revenue)}</td>\r\n                      <td className=\"text-danger\">{formatCurrency(item.commission || 0)}</td>\r\n                      <td className=\"text-success\">{formatCurrency(item.actualAmountToHost || 0)}</td>\r\n                      {/* <td>{formatCurrency(item.monthlyPayment)}</td> */}\r\n                      <td>{item.reservationCount || 0}</td>\r\n                      {/* <td>\r\n                        {item.paymentStatus ? (\r\n                          <span className={`badge ${\r\n                            item.paymentStatus === 'PAID' ? 'bg-success' : \r\n                            item.paymentStatus === 'PARTIAL' ? 'bg-info' :\r\n                            item.paymentStatus === 'PENDING' ? 'bg-warning' :\r\n                            item.paymentStatus === 'NOT_PAID' ? 'bg-danger' : 'bg-secondary'\r\n                          }`}>\r\n                            {item.paymentStatus === 'PAID' ? 'Đã thanh toán' : \r\n                             item.paymentStatus === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                             item.paymentStatus === 'PENDING' ? 'Chờ thanh toán' :\r\n                             item.paymentStatus === 'NOT_PAID' ? 'Chưa thanh toán' : 'Không có'}\r\n                          </span>\r\n                        ) : (\r\n                          <span className=\"badge bg-secondary\">Không có</span>\r\n                        )}\r\n                      </td> */}\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {realData && (\r\n        <div className=\"alert alert-success\">\r\n          <i className=\"bi bi-check-circle me-2\"></i>\r\n          Dữ liệu thực tế đã được tải thành công từ hệ thống.\r\n          <small className=\"text-muted ms-2\">\r\n            Cập nhật lần cuối: {new Date().toLocaleString('vi-VN')}\r\n          </small>\r\n          <br />\r\n          <small className=\"text-muted\">\r\n            Số tháng có dữ liệu: {realData.monthlyRevenueStats?.length || 0} | \r\n            Số loại phòng: {realData.revenueByRoomType?.length || 0} |\r\n            Tổng reservation: {totalReservation || 0}\r\n          </small>\r\n        </div>\r\n      )}\r\n      {!realData && reservations && reservations.length === 0 && (\r\n        <div className=\"alert alert-warning\">\r\n          <i className=\"bi bi-exclamation-triangle me-2\"></i>\r\n          Chưa có dữ liệu reservation nào trong hệ thống. Vui lòng kiểm tra lại sau.\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default RevenuePage;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC1D,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,SAAS,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,kBAAkB,GAAG;EACzBC,kBAAkB,EAAE,oBAAoB;EACxCC,0BAA0B,EAAE,4BAA4B;EACxDC,0BAA0B,EAAE;AAC9B,CAAC;AAED,MAAMC,cAAc,GAAGA,CAACC,GAAG,GAAG,CAAC,KAAK;EAClC,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC5C,OAAOC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAEN;EAAI,CAAC,EAAE,CAACO,CAAC,EAAEC,CAAC,KAAKP,WAAW,GAAGO,CAAC,CAAC;AAC/D,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACxB,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAa,CAAC,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACC,WAAW,CAAC;EAClE,MAAMC,WAAW,GAAG/B,WAAW,CAAC6B,KAAK,IAAIA,KAAK,CAACG,KAAK,CAACC,KAAK,CAAC;EAC3D,MAAMC,OAAO,GAAGH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,GAAG;;EAEhC;EACA,MAAMC,GAAG,GAAG,IAAItB,IAAI,CAAC,CAAC;EACtB,MAAMD,WAAW,GAAGuB,GAAG,CAACrB,WAAW,CAAC,CAAC;EACrC,MAAMsB,YAAY,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC;;EAEvC;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,IAAIgB,IAAI,CAAC,CAAC,CAACwB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7E,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,IAAIgB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;;EAE1E;EACA,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAMmD,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,UAAU,GAAG,CACjB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAClC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CACtC;IACD,OAAOA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAI,IAAIA,KAAK,EAAE;EAC7C,CAAC;;EAED;EACA,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE,OAAOA,MAAM;IAC7C,OAAOpD,KAAK,CAACmD,cAAc,CAACC,MAAM,CAAC;EACrC,CAAC;EAEDxD,SAAS,CAAC,MAAM;IACd;IACA8B,QAAQ,CAAC;MACP2B,IAAI,EAAE/C,kBAAkB,CAACC,kBAAkB;MAC3C+C,OAAO,EAAE;QACPC,IAAI,EAAEb,YAAY;QAClBc,IAAI,EAAE;MACR;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9B,QAAQ,EAAEgB,YAAY,CAAC,CAAC;EAE5B9C,SAAS,CAAC,MAAM;IACd,IAAIqC,OAAO,EAAE;MACXhC,SAAS,CAACwD,kBAAkB,CAACxB,OAAO,CAAC,CAACyB,IAAI,CAACC,QAAQ,IAAI;QACrDZ,QAAQ,CAAChC,KAAK,CAAC6C,OAAO,CAACD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEb,KAAK,CAAC,GAAGa,QAAQ,CAACb,KAAK,GAAG,EAAE,CAAC;MAChE,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACb,OAAO,CAAC,CAAC;;EAEb;EACArC,SAAS,CAAC,MAAM;IACd,IAAI+B,YAAY,IAAIA,YAAY,CAACV,MAAM,GAAG,CAAC,EAAE;MAC3C4C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEnC,YAAY,CAAC;MAErD,MAAMf,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMsB,YAAY,GAAG,IAAIvB,IAAI,CAAC,CAAC,CAACwB,QAAQ,CAAC,CAAC,GAAG,CAAC;MAC9C,MAAM0B,YAAY,GAAG,EAAE;;MAEvB;MACA,MAAMC,oBAAoB,GAAG/B,OAAO,GAChCN,YAAY,CAACsC,MAAM,CAACC,GAAG,IAAI;QAAA,IAAAC,UAAA;QACzB,MAAMC,UAAU,GAAG,EAAAD,UAAA,GAAAD,GAAG,CAAClC,KAAK,cAAAmC,UAAA,uBAATA,UAAA,CAAWjC,GAAG,KAAIgC,GAAG,CAAClC,KAAK;QAC9C,OAAO,CAAAoC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,QAAQ,CAAC,CAAC,OAAKpC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,QAAQ,CAAC,CAAC;MACvD,CAAC,CAAC,GACF,EAAE;;MAEN;MACA,KAAK,IAAIpB,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAI,EAAE,EAAEA,KAAK,EAAE,EAAE;QAAE;QAC1C,MAAMqB,iBAAiB,GAAGN,oBAAoB,CAACC,MAAM,CAACC,GAAG,IAAI;UAC3D,MAAMK,SAAS,GAAG,IAAI1D,IAAI,CAACqD,GAAG,CAACK,SAAS,CAAC;UACzC,OAAOA,SAAS,CAACzD,WAAW,CAAC,CAAC,KAAK4B,YAAY;UAAI;UAC5C6B,SAAS,CAAClC,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAKY,KAAK,IAClCiB,GAAG,CAACM,MAAM,KAAK,UAAU;QAClC,CAAC,CAAC;QAEF,MAAMC,OAAO,GAAGH,iBAAiB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAET,GAAG,KAAK;UACrD,MAAMU,KAAK,GAAGV,GAAG,CAACW,UAAU,GAAG,CAAC,GAAGX,GAAG,CAACW,UAAU,GAAGX,GAAG,CAACY,UAAU;UAClE,OAAOH,GAAG,GAAGC,KAAK;QACpB,CAAC,EAAE,CAAC,CAAC;;QAEL;QACA,MAAMG,kBAAkB,GAAGT,iBAAiB,CAACL,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACM,MAAM,KAAK,SAAS,CAAC;QACpF,MAAMQ,aAAa,GAAGD,kBAAkB,CAACL,MAAM,CAAC,CAACC,GAAG,EAAET,GAAG,KAAK;UAC5D,MAAMU,KAAK,GAAGV,GAAG,CAACW,UAAU,GAAG,CAAC,GAAGX,GAAG,CAACW,UAAU,GAAGX,GAAG,CAACY,UAAU;UAClE,OAAOH,GAAG,GAAGC,KAAK;QACpB,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMK,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,GAAG,IAAI,CAAC;QACnD,MAAMI,kBAAkB,GAAGX,OAAO,GAAGQ,UAAU;;QAE/C;QACA,IAAII,aAAa,GAAG,IAAI;QACxB,IAAIf,iBAAiB,CAACrD,MAAM,GAAG,CAAC,EAAE;UAChC,MAAMqE,cAAc,GAAGhB,iBAAiB,CAACL,MAAM,CAACC,GAAG,IACjDA,GAAG,CAACM,MAAM,KAAK,WAAW,IAAIN,GAAG,CAACM,MAAM,KAAK,aAC/C,CAAC,CAACvD,MAAM;UACR,MAAMsE,YAAY,GAAGjB,iBAAiB,CAACL,MAAM,CAACC,GAAG,IAC/CA,GAAG,CAACM,MAAM,KAAK,SAAS,IAAIN,GAAG,CAACM,MAAM,KAAK,QAAQ,IAAIN,GAAG,CAACM,MAAM,KAAK,YACxE,CAAC,CAACvD,MAAM;UACR,MAAMuE,YAAY,GAAGlB,iBAAiB,CAACL,MAAM,CAACC,GAAG,IAC/CA,GAAG,CAACM,MAAM,KAAK,UAAU,IAAIN,GAAG,CAACM,MAAM,KAAK,WAC9C,CAAC,CAACvD,MAAM;UAER,IAAIuE,YAAY,GAAG,CAAC,IAAIF,cAAc,KAAK,CAAC,EAAE;YAC5CD,aAAa,GAAG,UAAU;UAC5B,CAAC,MAAM,IAAIC,cAAc,GAAG,CAAC,IAAIC,YAAY,KAAK,CAAC,EAAE;YACnDF,aAAa,GAAG,MAAM;UACxB,CAAC,MAAM,IAAIC,cAAc,GAAG,CAAC,IAAIC,YAAY,GAAG,CAAC,EAAE;YACjDF,aAAa,GAAG,SAAS;UAC3B,CAAC,MAAM,IAAIE,YAAY,GAAG,CAAC,EAAE;YAC3BF,aAAa,GAAG,SAAS;UAC3B;QACF;QAEAtB,YAAY,CAAC0B,IAAI,CAAC;UAChBxC,KAAK;UACLM,IAAI,EAAEb,YAAY;UAAE;UACpB+B,OAAO;UACPQ,UAAU;UACVG,kBAAkB;UAClBM,gBAAgB,EAAEpB,iBAAiB,CAACrD,MAAM;UAC1C0E,cAAc,EAAE,CAAC;UACjBN;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMO,aAAa,GAAG,EAAE;MACxB,MAAMC,SAAS,GAAG/C,KAAK,CAACgD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;MAE9CH,SAAS,CAACI,OAAO,CAACC,QAAQ,IAAI;QAAA,IAAAC,kBAAA;QAC5B;QACA,IAAIC,oBAAoB,GAAGpC,oBAAoB,CAACC,MAAM,CAACC,GAAG;UAAA,IAAAmC,UAAA;UAAA,OACxD,EAAAA,UAAA,GAAAnC,GAAG,CAACpB,KAAK,cAAAuD,UAAA,uBAATA,UAAA,CAAWC,IAAI,CAACP,IAAI;YAAA,IAAAQ,UAAA;YAAA,OAAI,EAAAA,UAAA,GAAAR,IAAI,CAACA,IAAI,cAAAQ,UAAA,uBAATA,UAAA,CAAWP,IAAI,MAAKE,QAAQ;UAAA,EAAC,KACrDhC,GAAG,CAACM,MAAM,KAAK,UAAU;QAAA,CAC3B,CAAC;;QAED;QACA,IAAIlC,UAAU,KAAK,OAAO,EAAE;UAC1B8D,oBAAoB,GAAGA,oBAAoB,CAACnC,MAAM,CAACC,GAAG,IAAI;YACxD,MAAMK,SAAS,GAAG,IAAI1D,IAAI,CAACqD,GAAG,CAACK,SAAS,CAAC;YACzC,OAAOA,SAAS,CAACzD,WAAW,CAAC,CAAC,KAAK4B,YAAY,IACxC6B,SAAS,CAAClC,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAKG,aAAa;UACnD,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIF,UAAU,KAAK,MAAM,EAAE;UAChC8D,oBAAoB,GAAGA,oBAAoB,CAACnC,MAAM,CAACC,GAAG,IAAI;YACxD,MAAMK,SAAS,GAAG,IAAI1D,IAAI,CAACqD,GAAG,CAACK,SAAS,CAAC;YACzC,OAAOA,SAAS,CAACzD,WAAW,CAAC,CAAC,KAAK4B,YAAY;UACjD,CAAC,CAAC;QACJ;QAEA,MAAM8D,WAAW,GAAGJ,oBAAoB,CAAC1B,MAAM,CAAC,CAACC,GAAG,EAAET,GAAG,KAAK;UAC5D,MAAMU,KAAK,GAAGV,GAAG,CAACW,UAAU,GAAG,CAAC,GAAGX,GAAG,CAACW,UAAU,GAAGX,GAAG,CAACY,UAAU;UAClE,OAAOH,GAAG,GAAGC,KAAK;QACpB,CAAC,EAAE,CAAC,CAAC;QAEL,MAAM6B,QAAQ,GAAGL,oBAAoB,CAAC1B,MAAM,CAAC,CAACC,GAAG,EAAET,GAAG;UAAA,IAAAwC,WAAA;UAAA,OACpD/B,GAAG,IAAI,EAAA+B,WAAA,GAAAxC,GAAG,CAACpB,KAAK,cAAA4D,WAAA,uBAATA,WAAA,CAAWzC,MAAM,CAAC8B,IAAI;YAAA,IAAAY,WAAA;YAAA,OAAI,EAAAA,WAAA,GAAAZ,IAAI,CAACA,IAAI,cAAAY,WAAA,uBAATA,WAAA,CAAWX,IAAI,MAAKE,QAAQ;UAAA,EAAC,CAC3DxB,MAAM,CAAC,CAACkC,IAAI,EAAEb,IAAI,KAAKa,IAAI,GAAGb,IAAI,CAACU,QAAQ,EAAE,CAAC,CAAC,KAAI,CAAC,CAAC;QAAA,GAAE,CAC5D,CAAC;QAED,MAAMI,QAAQ,GAAGT,oBAAoB,CAACnF,MAAM,GAAG,CAAC,GAAGuF,WAAW,GAAGJ,oBAAoB,CAACnF,MAAM,GAAG,CAAC;;QAEhG;QACA,MAAM6F,aAAa,GAAGxE,UAAU,KAAK,OAAO,GACxC,EAAA6D,kBAAA,GAAApC,YAAY,CAACgD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/D,KAAK,KAAKT,aAAa,CAAC,cAAA2D,kBAAA,uBAAjDA,kBAAA,CAAmD1B,OAAO,KAAI,CAAC,GAC/DV,YAAY,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEqC,CAAC,KAAKrC,GAAG,GAAGqC,CAAC,CAACvC,OAAO,EAAE,CAAC,CAAC;QAEvD,MAAMwC,OAAO,GAAGH,aAAa,GAAG,CAAC,GAAG,CAAEN,WAAW,GAAGM,aAAa,GAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QAExF,IAAIV,WAAW,GAAG,CAAC,IAAIC,QAAQ,GAAG,CAAC,EAAE;UAAE;UACrCb,aAAa,CAACH,IAAI,CAAC;YACjBpC,IAAI,EAAE6C,QAAQ;YACdO,QAAQ;YACRI,QAAQ;YACRpC,OAAO,EAAE+B,WAAW;YACpBS;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,mBAAmB,GAAGxF,YAAY,CAACsC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACM,MAAM,KAAK,SAAS,CAAC;MAChF,MAAM4C,mBAAmB,GAAGzF,YAAY,CAACsC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACM,MAAM,KAAK,SAAS,CAAC;MAEhF,MAAM6C,YAAY,GAAGF,mBAAmB,CAAClG,MAAM;MAC/C,MAAMqG,YAAY,GAAGF,mBAAmB,CAACnG,MAAM;MAC/C,MAAMsG,UAAU,GAAGF,YAAY,GAAGC,YAAY;MAE9C,MAAME,kBAAkB,GAAG;QACzBC,MAAM,EAAE,CAAC,uBAAuB,EAAE,mBAAmB,CAAC;QACtDC,QAAQ,EAAE,CAAC;UACTC,IAAI,EAAE,CAACN,YAAY,EAAEC,YAAY,CAAC;UAClCM,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;UACvCC,WAAW,EAAE;QACf,CAAC;MACH,CAAC;MAEDhF,WAAW,CAAC;QACViF,mBAAmB,EAAE/D,YAAY;QACjCgE,iBAAiB,EAAEnC,aAAa;QAChCoC,YAAY,EAAEjE,YAAY,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEqC,CAAC,KAAKrC,GAAG,GAAGqC,CAAC,CAACvC,OAAO,EAAE,CAAC,CAAC;QACjEwD,gBAAgB,EAAElE,YAAY,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEqC,CAAC,KAAKrC,GAAG,GAAGqC,CAAC,CAACvC,OAAO,EAAE,CAAC,CAAC;QACrEyD,MAAM,EAAE,CAAC;QACTC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAErE,YAAY,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEqC,CAAC,KAAKrC,GAAG,GAAGqC,CAAC,CAAC5B,kBAAkB,EAAE,CAAC,CAAC;QACtEoC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC7F,YAAY,EAAEe,YAAY,EAAEF,aAAa,EAAEF,UAAU,EAAEQ,KAAK,EAAEb,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE7E;EACA,MAAMoG,mBAAmB,GAAG;IAC1BZ,MAAM,EAAE1G,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEmB;IAAa,CAAC,EAAE,CAAClB,CAAC,EAAEC,CAAC,KAAK6B,YAAY,CAAC7B,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3EuG,QAAQ,EAAE,CACR;MACEY,KAAK,EAAE,mBAAmB;MAC1BX,IAAI,EAAE5G,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAEmB;MAAa,CAAC,EAAE,CAAClB,CAAC,EAAEC,CAAC,KAC9C+D,IAAI,CAACC,KAAK,CAACD,IAAI,CAACqD,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KACtC,CAAC;MACDC,WAAW,EAAE,SAAS;MACtBZ,eAAe,EAAE,wBAAwB;MACzCa,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,CAAC,CAAAC,qBAAA,EAAAC,sBAAA,KAAM;IAC1BhF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAElB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmF,iBAAiB,CAAC;IACxElE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAElB,QAAQ,aAARA,QAAQ,wBAAAgG,qBAAA,GAARhG,QAAQ,CAAEmF,iBAAiB,cAAAa,qBAAA,uBAA3BA,qBAAA,CAA6B3H,MAAM,CAAC;IAExF,IAAI,CAAA2B,QAAQ,aAARA,QAAQ,wBAAAiG,sBAAA,GAARjG,QAAQ,CAAEmF,iBAAiB,cAAAc,sBAAA,uBAA3BA,sBAAA,CAA6B5H,MAAM,IAAG,CAAC,EAAE;MAC3C4C,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,OAAOlB,QAAQ,CAACmF,iBAAiB,CAACjC,GAAG,CAACgD,GAAG,KAAK;QAC5CzF,IAAI,EAAEyF,GAAG,CAACzF,IAAI;QACdoD,QAAQ,EAAEqC,GAAG,CAACrC,QAAQ;QACtBI,QAAQ,EAAE1D,cAAc,CAAC2F,GAAG,CAACjC,QAAQ,CAAC;QACtCpC,OAAO,EAAEtB,cAAc,CAAC2F,GAAG,CAACrE,OAAO,CAAC;QACpCwC,OAAO,EAAE6B,GAAG,CAAC7B,OAAO,GAAG;MACzB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLpD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C,OAAO,EAAE;IACX;EACF,CAAC,EAAE,CAAC;;EAEJ;EACA,MAAMiF,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,EAACnG,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEkF,mBAAmB,GAAE,OAAO,EAAE;IAC7C,IAAIxF,UAAU,KAAK,OAAO,EAAE;MAC1B;MACA;MACA,OAAOM,QAAQ,CAACkF,mBAAmB,CAAC7D,MAAM,CACxC+E,IAAI,IAAIA,IAAI,CAAC/F,KAAK,KAAKT,aAAa,IAAIwG,IAAI,CAACzF,IAAI,KAAKb,YACxD,CAAC;IACH,CAAC,MAAM,IAAIJ,UAAU,KAAK,MAAM,EAAE;MAChC;MACA,OAAOM,QAAQ,CAACkF,mBAAmB,CAAC7D,MAAM,CACxC+E,IAAI,IAAIA,IAAI,CAACzF,IAAI,KAAKb,YACxB,CAAC;IACH;IACA,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMuG,aAAa,GAAGF,uBAAuB,CAAC,CAAC;;EAE/C;EACA,IAAIG,WAAW;EACf,MAAMC,KAAK,GAAG,CAAAvG,QAAQ,aAARA,QAAQ,wBAAAtB,qBAAA,GAARsB,QAAQ,CAAEkF,mBAAmB,cAAAxG,qBAAA,uBAA7BA,qBAAA,CAA+B2C,MAAM,CAAC+E,IAAI,IAAIA,IAAI,CAACzF,IAAI,KAAKb,YAAY,CAAC,KAAI,EAAE;EAC7FwG,WAAW,GAAG;IACZzB,MAAM,EAAE1G,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK6B,YAAY,CAAC7B,CAAC,GAAG,CAAC,CAAC,CAAC;IACjEuG,QAAQ,EAAE,CACR;MACEY,KAAK,EAAE,mBAAmB;MAC1BX,IAAI,EAAE5G,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK;QACzC,MAAMiI,IAAI,GAAGD,KAAK,CAACpC,IAAI,CAACsC,CAAC,IAAIA,CAAC,CAACpG,KAAK,KAAK9B,CAAC,GAAG,CAAC,CAAC;QAC/C,OAAOiI,IAAI,GAAGA,IAAI,CAAC3E,OAAO,GAAG,CAAC;MAChC,CAAC,CAAC;MACF+D,WAAW,EAAE,SAAS;MACtBZ,eAAe,EAAE,wBAAwB;MACzCa,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAMV,YAAY,GAAGiB,aAAa,CAACvE,MAAM,CAAC,CAACC,GAAG,EAAEqE,IAAI,KAAKrE,GAAG,GAAGqE,IAAI,CAACvE,OAAO,EAAE,CAAC,CAAC;EAC/E,MAAM6E,eAAe,GAAGL,aAAa,CAACvE,MAAM,CAAC,CAACC,GAAG,EAAEqE,IAAI,KAAKrE,GAAG,IAAIqE,IAAI,CAAC/D,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5F,MAAMsE,iBAAiB,GAAGN,aAAa,CAACvE,MAAM,CAAC,CAACC,GAAG,EAAEqE,IAAI,KAAKrE,GAAG,IAAIqE,IAAI,CAAC5D,kBAAkB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACtG,MAAMoE,gBAAgB,GAAGP,aAAa,CAACvE,MAAM,CAAC,CAACC,GAAG,EAAEqE,IAAI,KAAKrE,GAAG,IAAIqE,IAAI,CAACtD,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACnG,MAAM+D,UAAU,GAAGR,aAAa,CAAChI,MAAM,GAAG,CAAC,GAAG+G,YAAY,GAAGiB,aAAa,CAAChI,MAAM,GAAG,CAAC;;EAErF;EACA,MAAMyI,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,IAAI,CAAC/H,YAAY,IAAIA,YAAY,CAACV,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO;QACLwG,MAAM,EAAE,CAAC,uBAAuB,EAAE,mBAAmB,CAAC;QACtDC,QAAQ,EAAE,CAAC;UACTC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACZC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;UACvCC,WAAW,EAAE;QACf,CAAC;MACH,CAAC;IACH;IACA,IAAI7D,oBAAoB,GAAG,EAAE;IAC7B,IAAI1B,UAAU,KAAK,OAAO,EAAE;MAC1B0B,oBAAoB,GAAGrC,YAAY,CAACsC,MAAM,CAACC,GAAG,IAAI;QAChD,MAAMK,SAAS,GAAG,IAAI1D,IAAI,CAACqD,GAAG,CAACK,SAAS,CAAC;QACzC,OAAOA,SAAS,CAACzD,WAAW,CAAC,CAAC,KAAK4B,YAAY,IAAI6B,SAAS,CAAClC,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAKG,aAAa;MAC/F,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIF,UAAU,KAAK,MAAM,EAAE;MAChC0B,oBAAoB,GAAGrC,YAAY,CAACsC,MAAM,CAACC,GAAG,IAAI;QAChD,MAAMK,SAAS,GAAG,IAAI1D,IAAI,CAACqD,GAAG,CAACK,SAAS,CAAC;QACzC,OAAOA,SAAS,CAACzD,WAAW,CAAC,CAAC,KAAK4B,YAAY;MACjD,CAAC,CAAC;IACJ;IACA,MAAMyE,mBAAmB,GAAGnD,oBAAoB,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACM,MAAM,KAAK,SAAS,CAAC;IACxF,MAAM4C,mBAAmB,GAAGpD,oBAAoB,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACM,MAAM,KAAK,SAAS,CAAC;IACxF,MAAM6C,YAAY,GAAGF,mBAAmB,CAAClG,MAAM;IAC/C,MAAMqG,YAAY,GAAGF,mBAAmB,CAACnG,MAAM;IAC/C,OAAO;MACLwG,MAAM,EAAE,CAAC,uBAAuB,EAAE,mBAAmB,CAAC;MACtDC,QAAQ,EAAE,CAAC;QACTC,IAAI,EAAE,CAACN,YAAY,EAAEC,YAAY,CAAC;QAClCM,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;QACvCC,WAAW,EAAE;MACf,CAAC;IACH,CAAC;EACH,CAAC;;EAED;EACA,MAAML,kBAAkB,GAAGkC,6BAA6B,CAAC,CAAC;EAC1D;EACA,MAAMC,cAAc,GAAGnC,kBAAkB,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAACrB,IAAI,CAACsD,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;;EAE/E;EACA,MAAMC,KAAK,GAAGnJ,cAAc,CAAC,CAAC,CAAC;EAC/B;EACA,MAAMoJ,MAAM,GAAG/I,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAEyB,YAAY,KAAK9B,WAAW,GAAGwB,YAAY,GAAG;EAAG,CAAC,EAAE,CAAClB,CAAC,EAAEC,CAAC,MAAM;IAAE4I,KAAK,EAAE5I,CAAC,GAAG,CAAC;IAAEmH,KAAK,EAAE,SAASnH,CAAC,GAAG,CAAC;EAAG,CAAC,CAAC,CAAC;EAE9I,oBACEhB,OAAA,CAAAE,SAAA;IAAA2J,QAAA,gBACE7J,OAAA;MAAK8J,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrE7J,OAAA;QAAA6J,QAAA,gBACE7J,OAAA;UAAA6J,QAAA,EAAI;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BlK,OAAA;UAAG8J,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAE1B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNlK,OAAA;QAAK8J,SAAS,EAAC,iCAAiC;QAAAD,QAAA,gBAE9C7J,OAAA;UACE8J,SAAS,EAAC,iCAAiC;UAC3CF,KAAK,EAAEzH,UAAW;UAClBgI,QAAQ,EAAEC,CAAC,IAAI;YACbhI,aAAa,CAACgI,CAAC,CAACC,MAAM,CAACT,KAAK,CAAC;YAC7B;YACA,IAAIQ,CAAC,CAACC,MAAM,CAACT,KAAK,KAAK,OAAO,EAAE;cAC9BtH,gBAAgB,CAAC,IAAI5B,IAAI,CAAC,CAAC,CAACwB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAC3CM,eAAe,CAAC,IAAI9B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;YAC3C,CAAC,MAAM,IAAIyJ,CAAC,CAACC,MAAM,CAACT,KAAK,KAAK,MAAM,EAAE;cACpCpH,eAAe,CAAC,IAAI9B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;YAC3C;UACF,CAAE;UACF2J,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAEtB7J,OAAA;YAAQ4J,KAAK,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpClK,OAAA;YAAQ4J,KAAK,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,EAER/H,UAAU,KAAK,OAAO,iBACrBnC,OAAA,CAAAE,SAAA;UAAA2J,QAAA,gBACE7J,OAAA;YACE8J,SAAS,EAAC,iCAAiC;YAC3CF,KAAK,EAAEvH,aAAc;YACrB8H,QAAQ,EAAEC,CAAC,IAAI9H,gBAAgB,CAACkI,MAAM,CAACJ,CAAC,CAACC,MAAM,CAACT,KAAK,CAAC,CAAE;YACxDU,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,EAErBF,MAAM,CAAChE,GAAG,CAACkB,CAAC,iBACX7G,OAAA;cAAsB4J,KAAK,EAAE/C,CAAC,CAAC+C,KAAM;cAAAC,QAAA,EAAEhD,CAAC,CAACsB;YAAK,GAAjCtB,CAAC,CAAC+C,KAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTlK,OAAA;YACE8J,SAAS,EAAC,iCAAiC;YAC3CF,KAAK,EAAErH,YAAa;YACpB4H,QAAQ,EAAEC,CAAC,IAAI5H,eAAe,CAACgI,MAAM,CAACJ,CAAC,CAACC,MAAM,CAACT,KAAK,CAAC,CAAE;YACvDU,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAG,CAAE;YAAAV,QAAA,EAEpBH,KAAK,CAAC/D,GAAG,CAAC8E,CAAC,iBACVzK,OAAA;cAAgB4J,KAAK,EAAEa,CAAE;cAAAZ,QAAA,EAAEY;YAAC,GAAfA,CAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuB,CACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA,eACT,CACH,EACA/H,UAAU,KAAK,MAAM,iBACpBnC,OAAA;UACE8J,SAAS,EAAC,iCAAiC;UAC3CF,KAAK,EAAErH,YAAa;UACpB4H,QAAQ,EAAEC,CAAC,IAAI5H,eAAe,CAACgI,MAAM,CAACJ,CAAC,CAACC,MAAM,CAACT,KAAK,CAAC,CAAE;UACvDU,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAG,CAAE;UAAAV,QAAA,EAEpBH,KAAK,CAAC/D,GAAG,CAAC8E,CAAC,iBACVzK,OAAA;YAAgB4J,KAAK,EAAEa,CAAE;YAAAZ,QAAA,EAAEY;UAAC,GAAfA,CAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAuB,CACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpB,aAAa,CAAChI,MAAM,GAAG,CAAC,gBACvBd,OAAA;MAAK8J,SAAS,EAAC,UAAU;MAAAD,QAAA,gBACvB7J,OAAA;QAAK8J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB7J,OAAA;UAAK8J,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB7J,OAAA;YAAK8J,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7J,OAAA;cAAI8J,SAAS,EAAC,YAAY;cAAAD,QAAA,EACvB1H,UAAU,KAAK,OAAO,GAAG,0BAA0B,GAAG;YAAwB;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACLlK,OAAA;cAAI8J,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB7G,cAAc,CAAC6E,YAAY;YAAC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACLlK,OAAA;cAAO8J,SAAS,EAAC,YAAY;cAAAD,QAAA,GAC1BR,gBAAgB,EAAC,yBACpB;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlK,OAAA;cAAK8J,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB7J,OAAA;gBAAO8J,SAAS,EAAC,aAAa;gBAAAD,QAAA,GAAC,iBACnB,EAAC7G,cAAc,CAACmG,eAAe,CAAC;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACRlK,OAAA;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlK,OAAA;gBAAO8J,SAAS,EAAC,cAAc;gBAAAD,QAAA,GAAC,uBACnB,EAAC7G,cAAc,CAACoG,iBAAiB,CAAC;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlK,OAAA;QAAK8J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB7J,OAAA;UAAK8J,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB7J,OAAA;YAAK8J,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7J,OAAA;cAAI8J,SAAS,EAAC,YAAY;cAAAD,QAAA,EACvB1H,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;YAAgB;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACLlK,OAAA;cAAI8J,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB7G,cAAc,CAACsG,UAAU;YAAC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACLlK,OAAA;cAAO8J,SAAS,EAAC,YAAY;cAAAD,QAAA,GAAC,mBACrB,EAACf,aAAa,CAAChI,MAAM;YAAA;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACRlK,OAAA;cAAK8J,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB7J,OAAA;gBAAO8J,SAAS,EAAC,aAAa;gBAAAD,QAAA,GAAC,2BACd,EAAC7G,cAAc,CAACmG,eAAe,CAAC;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACRlK,OAAA;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlK,OAAA;gBAAO8J,SAAS,EAAC,cAAc;gBAAAD,QAAA,GAAC,iCACd,EAAC7G,cAAc,CAACoG,iBAAiB,CAAC;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlK,OAAA;QAAK8J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB7J,OAAA;UAAK8J,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB7J,OAAA;YAAK8J,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7J,OAAA;cAAI8J,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzClK,OAAA;cAAI8J,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB7G,cAAc,CAACoG,iBAAiB;YAAC;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACLlK,OAAA;cAAO8J,SAAS,EAAC,YAAY;cAAAD,QAAA,EAC1B1H,UAAU,KAAK,OAAO,GAAG,SAASE,aAAa,MAAM,GAAG,OAAOE,YAAY;YAAM;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlK,OAAA;QAAK8J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB7J,OAAA;UAAK8J,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB7J,OAAA;YAAK8J,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7J,OAAA;cAAI8J,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ClK,OAAA;cAAI8J,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjBR;YAAgB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACLlK,OAAA;cAAO8J,SAAS,EAAC,YAAY;cAAAD,QAAA,GAAC,iBAChB,EAACf,aAAa,CAAChI,MAAM,GAAG,CAAC,GAAGiE,IAAI,CAAC2F,KAAK,CAACrB,gBAAgB,GAAGP,aAAa,CAAChI,MAAM,CAAC,GAAG,CAAC;YAAA;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENlK,OAAA;MAAK8J,SAAS,EAAC,qBAAqB;MAAAD,QAAA,gBAClC7J,OAAA;QAAG8J,SAAS,EAAC;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,2GAErD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDlK,OAAA;MAAK8J,SAAS,EAAC,UAAU;MAAAD,QAAA,gBACvB7J,OAAA;QAAK8J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB7J,OAAA;UAAK8J,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnB7J,OAAA;YAAK8J,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7J,OAAA;cAAI8J,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDlK,OAAA,CAACX,IAAI;cACHmI,IAAI,EAAEuB,WAAY;cAClB4B,OAAO,EAAE;gBACPC,UAAU,EAAE,IAAI;gBAChBC,OAAO,EAAE;kBACPC,MAAM,EAAE;oBACNC,QAAQ,EAAE;kBACZ,CAAC;kBACDC,OAAO,EAAE;oBACPC,SAAS,EAAE;sBACT9C,KAAK,EAAE,SAAAA,CAAS+C,OAAO,EAAE;wBACvB,OAAOA,OAAO,CAACC,OAAO,CAAChD,KAAK,GAAG,IAAI,GAAGnF,cAAc,CAACkI,OAAO,CAACE,MAAM,CAACX,CAAC,CAAC;sBACxE;oBACF;kBACF;gBACF,CAAC;gBACDY,MAAM,EAAE;kBACNZ,CAAC,EAAE;oBACDa,WAAW,EAAE,KAAK;oBAClBC,IAAI,EAAE;sBACJC,UAAU,EAAE;oBACd,CAAC;oBACDC,KAAK,EAAE;sBACLC,QAAQ,EAAG9B,KAAK,IAAK5G,cAAc,CAAC4G,KAAK;oBAC3C;kBACF,CAAC;kBACD+B,CAAC,EAAE;oBACDJ,IAAI,EAAE;sBACJK,OAAO,EAAE;oBACX;kBACF;gBACF;cACF;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlK,OAAA;QAAK8J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB7J,OAAA;UAAK8J,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB7J,OAAA;YAAK8J,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7J,OAAA;cAAI8J,SAAS,EAAC,iBAAiB;cAAAD,QAAA,GAAC,kCACd,EAAC1H,UAAU,KAAK,OAAO,GAAG,WAAW,GAAG,SAAS,EAAC,GAClE,EAACX,YAAY,IAAIA,YAAY,CAACV,MAAM,GAAG,CAAC,iBACtCd,OAAA;gBAAO8J,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAClC7J,OAAA;kBAAG8J,SAAS,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,wCACxC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACR,EACA,CAAC,CAAC1I,YAAY,IAAIA,YAAY,CAACV,MAAM,KAAK,CAAC,kBAC1Cd,OAAA;gBAAO8J,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAClC7J,OAAA;kBAAG8J,SAAS,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sCAChD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLlK,OAAA;cAAK8J,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC1BL,cAAc,gBACbxJ,OAAA,CAACT,GAAG;gBACFiI,IAAI,EAAEH,kBAAmB;gBACzBsD,OAAO,EAAE;kBACPC,UAAU,EAAE,IAAI;kBAChBiB,mBAAmB,EAAE,KAAK;kBAC1BhB,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNC,QAAQ,EAAE;oBACZ,CAAC;oBACDC,OAAO,EAAE;sBACPC,SAAS,EAAE;wBACT9C,KAAK,EAAE,SAAAA,CAAS+C,OAAO,EAAE;0BACvB,MAAMY,KAAK,GAAGZ,OAAO,CAACC,OAAO,CAAC3D,IAAI,CAACjD,MAAM,CAAC,CAACwH,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;0BAC7D,MAAMC,UAAU,GAAGH,KAAK,GAAG,CAAC,GAAG,CAAEZ,OAAO,CAACE,MAAM,GAAGU,KAAK,GAAI,GAAG,EAAE/E,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;0BAC9E,OAAOmE,OAAO,CAAC/C,KAAK,GAAG,IAAI,GAAG+C,OAAO,CAACE,MAAM,GAAG,IAAI,GAAGa,UAAU,GAAG,IAAI;wBACzE;sBACF;oBACF;kBACF;gBACF;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEFlK,OAAA;gBAAK8J,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACpC7J,OAAA;kBAAG8J,SAAS,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,+EAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlK,OAAA;MAAK8J,SAAS,EAAC,WAAW;MAAAD,QAAA,eACxB7J,OAAA;QAAK8J,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxB7J,OAAA;UAAI8J,SAAS,EAAC,iBAAiB;UAAAD,QAAA,GAAC,gDAE9B,EAAC1H,UAAU,KAAK,OAAO,GACnB,gBAAgBE,aAAa,IAAIE,YAAY,EAAE,GAC/C,cAAcA,YAAY,EAAE,EAE/B,CAAAE,QAAQ,aAARA,QAAQ,wBAAArB,sBAAA,GAARqB,QAAQ,CAAEmF,iBAAiB,cAAAxG,sBAAA,uBAA3BA,sBAAA,CAA6BN,MAAM,IAAG,CAAC,iBACtCd,OAAA;YAAO8J,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClC7J,OAAA;cAAG8J,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,wCACxC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,EACA,CAAC,EAACzH,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEmF,iBAAiB,KAAInF,QAAQ,CAACmF,iBAAiB,CAAC9G,MAAM,KAAK,CAAC,kBACvEd,OAAA;YAAO8J,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClC7J,OAAA;cAAG8J,SAAS,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sCAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACLlK,OAAA;UAAK8J,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC/B7J,OAAA;YAAO8J,SAAS,EAAC,OAAO;YAAAD,QAAA,gBACtB7J,OAAA;cAAO8J,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC5B7J,OAAA;gBAAA6J,QAAA,gBACE7J,OAAA;kBAAA6J,QAAA,EAAI;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvBlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRlK,OAAA;cAAA6J,QAAA,EACGrB,YAAY,CAAC1H,MAAM,GAAG,CAAC,GACtB0H,YAAY,CAAC7C,GAAG,CAAC,CAACgD,GAAG,EAAEuD,GAAG,kBACxBlM,OAAA;gBAAA6J,QAAA,gBACE7J,OAAA;kBAAA6J,QAAA,EAAKlB,GAAG,CAACzF;gBAAI;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnBlK,OAAA;kBAAA6J,QAAA,EAAKlB,GAAG,CAACrC;gBAAQ;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBlK,OAAA;kBAAA6J,QAAA,EAAKlB,GAAG,CAACjC;gBAAQ;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBlK,OAAA;kBAAA6J,QAAA,EAAKlB,GAAG,CAACrE;gBAAO;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtBlK,OAAA;kBAAA6J,QAAA,EAAKlB,GAAG,CAAC7B;gBAAO;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GALfgC,GAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMR,CACL,CAAC,gBAEFlK,OAAA;gBAAA6J,QAAA,eACE7J,OAAA;kBAAImM,OAAO,EAAC,GAAG;kBAACrC,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAEnD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACL,CAAC,EAACzH,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEmF,iBAAiB,KAAInF,QAAQ,CAACmF,iBAAiB,CAAC9G,MAAM,KAAK,CAAC,kBACvEd,OAAA;UAAK8J,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBACpC7J,OAAA;YAAG8J,SAAS,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oHAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpB,aAAa,CAAChI,MAAM,GAAG,CAAC,iBACvBd,OAAA;MAAK8J,SAAS,EAAC,WAAW;MAAAD,QAAA,eACxB7J,OAAA;QAAK8J,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxB7J,OAAA;UAAI8J,SAAS,EAAC,iBAAiB;UAAAD,QAAA,GAAC,2BACV,EAAC1H,UAAU,KAAK,OAAO,GAAG,WAAW,GAAG,SAAS,EAAC,GACxE;QAAA;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlK,OAAA;UAAK8J,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC/B7J,OAAA;YAAO8J,SAAS,EAAC,OAAO;YAAAD,QAAA,gBACtB7J,OAAA;cAAO8J,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC5B7J,OAAA;gBAAA6J,QAAA,gBACE7J,OAAA;kBAAA6J,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACZlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClClK,OAAA;kBAAA6J,QAAA,EAAI;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAElBlK,OAAA;kBAAA6J,QAAA,EAAI;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRlK,OAAA;cAAA6J,QAAA,EACGf,aAAa,CAACnD,GAAG,CAAC,CAACkD,IAAI,EAAEqD,GAAG,kBAC3BlM,OAAA;gBAAA6J,QAAA,gBACE7J,OAAA;kBAAA6J,QAAA,EAAKhH,YAAY,CAACgG,IAAI,CAAC/F,KAAK;gBAAC;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnClK,OAAA;kBAAA6J,QAAA,EAAKhB,IAAI,CAACzF;gBAAI;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBlK,OAAA;kBAAI8J,SAAS,EAAC,SAAS;kBAAAD,QAAA,EAAE7G,cAAc,CAAC6F,IAAI,CAACvE,OAAO;gBAAC;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3DlK,OAAA;kBAAI8J,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAE7G,cAAc,CAAC6F,IAAI,CAAC/D,UAAU,IAAI,CAAC;gBAAC;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvElK,OAAA;kBAAI8J,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAE7G,cAAc,CAAC6F,IAAI,CAAC5D,kBAAkB,IAAI,CAAC;gBAAC;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAEhFlK,OAAA;kBAAA6J,QAAA,EAAKhB,IAAI,CAACtD,gBAAgB,IAAI;gBAAC;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAP9BgC,GAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBR,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAzH,QAAQ,iBACPzC,OAAA;MAAK8J,SAAS,EAAC,qBAAqB;MAAAD,QAAA,gBAClC7J,OAAA;QAAG8J,SAAS,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,4HAE3C,eAAAlK,OAAA;QAAO8J,SAAS,EAAC,iBAAiB;QAAAD,QAAA,GAAC,yCACd,EAAC,IAAInJ,IAAI,CAAC,CAAC,CAAC0L,cAAc,CAAC,OAAO,CAAC;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACRlK,OAAA;QAAA+J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNlK,OAAA;QAAO8J,SAAS,EAAC,YAAY;QAAAD,QAAA,GAAC,4CACP,EAAC,EAAAxI,sBAAA,GAAAoB,QAAQ,CAACkF,mBAAmB,cAAAtG,sBAAA,uBAA5BA,sBAAA,CAA8BP,MAAM,KAAI,CAAC,EAAC,iCACjD,EAAC,EAAAQ,sBAAA,GAAAmB,QAAQ,CAACmF,iBAAiB,cAAAtG,sBAAA,uBAA1BA,sBAAA,CAA4BR,MAAM,KAAI,CAAC,EAAC,4BACtC,EAACuI,gBAAgB,IAAI,CAAC;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACA,CAACzH,QAAQ,IAAIjB,YAAY,IAAIA,YAAY,CAACV,MAAM,KAAK,CAAC,iBACrDd,OAAA;MAAK8J,SAAS,EAAC,qBAAqB;MAAAD,QAAA,gBAClC7J,OAAA;QAAG8J,SAAS,EAAC;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,0HAErD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAChJ,EAAA,CAluBID,WAAW;EAAA,QACEtB,WAAW,EACHC,WAAW,EAChBA,WAAW;AAAA;AAAAyM,EAAA,GAH3BpL,WAAW;AAouBjB,eAAeA,WAAW;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}