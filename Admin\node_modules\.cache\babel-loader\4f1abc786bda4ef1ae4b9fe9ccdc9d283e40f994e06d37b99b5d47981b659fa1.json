{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api\";\nconst AdminDashboardFactories = {\n  fetchAdminDashboardMetrics: params => api.get(ApiConstants.ADMIN_DASHBOARD_METRICS, {\n    params\n  })\n};\nexport default AdminDashboardFactories;", "map": {"version": 3, "names": ["ApiConstants", "api", "AdminDashboardFactories", "fetchAdminDashboardMetrics", "params", "get", "ADMIN_DASHBOARD_METRICS"], "sources": ["E:/WDP301_UROOM/Admin/src/redux/adminDashboard/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api\";\n\nconst AdminDashboardFactories = {\n  fetchAdminDashboardMetrics: (params) => \n    api.get(ApiConstants.ADMIN_DASHBOARD_METRICS, { params }),\n};\n\nexport default AdminDashboardFactories;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,gBAAgB;AAEhC,MAAMC,uBAAuB,GAAG;EAC9BC,0BAA0B,EAAGC,MAAM,IACjCH,GAAG,CAACI,GAAG,CAACL,YAAY,CAACM,uBAAuB,EAAE;IAAEF;EAAO,CAAC;AAC5D,CAAC;AAED,eAAeF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}