{"ast": null, "code": "\"use client\";\n\nvar _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\Transaction.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Row, Col, Table, Form, Button, Card, Alert, Pagination } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport { useNavigate } from \"react-router-dom\";\nimport TransactionDetail from \"./TransactionDetail\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport ReservationActions from \"../../redux/reservation/actions\";\nimport MonthlyPaymentActions from \"@redux/monthlyPayment/actions\";\nimport Utils from \"@utils/Utils\";\nimport { useAppDispatch, useAppSelector } from \"../../redux/store\";\nimport BankInfoActions from \"../../redux/bankInfo/actions\";\nimport { trackSynchronousRequestDataAccessInDev } from \"next/dist/server/app-render/dynamic-rendering\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Transaction = () => {\n  _s();\n  const navigate = useNavigate();\n  const [showModal, setShowModal] = useState(false);\n  const dispatch = useAppDispatch(); // Sử dụng useAppDispatch thay vì useDispatch\n\n  // Sử dụng useAppSelector cho tất cả\n  const {\n    reservations\n  } = useAppSelector(state => state.Reservation);\n  const {\n    list\n  } = useAppSelector(state => state.MonthlyPayment);\n  const {\n    bankInfo,\n    hasBankInfo,\n    showForm\n  } = useAppSelector(state => state.BankInfo);\n  const [selectedAdminYear, setSelectedAdminYear] = useState(new Date().getFullYear());\n  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\n  const [selectedStatus, setSelectedStatus] = useState(\"All\");\n  const [selectedSort, setSelectedSort] = useState(\"desc\");\n  const [detailReservation, setDetailReservation] = useState({});\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const itemsPerPage = 10;\n\n  // Local state cho form\n  const [bankInfoForm, setBankInfoForm] = useState({\n    accountNumber: \"\",\n    accountName: \"\",\n    bankName: \"\",\n    branch: \"\"\n  });\n\n  // Sync form với Redux state khi có bankInfo\n  useEffect(() => {\n    if (bankInfo) {\n      setBankInfoForm(bankInfo);\n    }\n  }, [bankInfo]);\n\n  // Gọi API lấy reservation theo filter, sort, month, year\n  useEffect(() => {\n    dispatch({\n      type: ReservationActions.FETCH_RESERVATIONS,\n      payload: {\n        status: selectedStatus !== \"All\" ? selectedStatus : undefined,\n        month: selectedMonth,\n        year: selectedYear,\n        sort: selectedSort\n      }\n    });\n    // Reset về trang 1 khi filter thay đổi\n    setCurrentPage(1);\n  }, [dispatch, selectedMonth, selectedYear, selectedStatus, selectedSort]);\n  useEffect(() => {\n    dispatch({\n      type: MonthlyPaymentActions.FETCH_MONTHLY_PAYMENTS,\n      payload: {\n        year: selectedAdminYear\n      }\n    });\n  }, [dispatch, selectedAdminYear]);\n  const handleBankInfoChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setBankInfoForm({\n      ...bankInfoForm,\n      [name]: value\n    });\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n\n    // Validate form\n    if (!bankInfoForm.accountNumber || !bankInfoForm.accountName || !bankInfoForm.bankName) {\n      showToast.error('Vui lòng điền đầy đủ thông tin!');\n      return;\n    }\n    console.log('Current bankInfo state:', bankInfo);\n    console.log('Form data:', bankInfoForm);\n    console.log('hasBankInfo:', hasBankInfo);\n    if (hasBankInfo) {\n      dispatch({\n        type: BankInfoActions.UPDATE_BANK_INFO,\n        payload: bankInfoForm\n      });\n      showToast.success('Cập nhật thông tin ngân hàng thành công!');\n    } else {\n      dispatch({\n        type: BankInfoActions.SAVE_BANK_INFO,\n        payload: bankInfoForm\n      });\n      showToast.success('Lưu thông tin ngân hàng thành công!');\n    }\n  };\n  const handleEdit = () => {\n    setBankInfoForm(bankInfo);\n    dispatch({\n      type: BankInfoActions.SET_SHOW_FORM,\n      payload: true\n    });\n  };\n  const handleDelete = () => {\n    if (true) {\n      dispatch({\n        type: BankInfoActions.DELETE_BANK_INFO\n      });\n      setBankInfoForm({\n        accountNumber: \"\",\n        accountName: \"\",\n        bankName: \"\",\n        branch: \"\"\n      });\n      showToast.success('Xoá thông tin ngân hàng thành công!');\n    }\n  };\n  const handleCancel = () => {\n    setBankInfoForm(bankInfo || {\n      accountNumber: \"\",\n      accountName: \"\",\n      bankName: \"\",\n      branch: \"\"\n    });\n    dispatch({\n      type: BankInfoActions.SET_SHOW_FORM,\n      payload: false\n    });\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat(\"vi-VN\", {\n      style: \"currency\",\n      currency: \"VND\"\n    }).format(amount);\n  };\n  const months = [\"Tháng 1\", \"Tháng 2\", \"Tháng 3\", \"Tháng 4\", \"Tháng 5\", \"Tháng 6\", \"Tháng 7\", \"Tháng 8\", \"Tháng 9\", \"Tháng 10\", \"Tháng 11\", \"Tháng 12\"];\n\n  // Tạo danh sách năm từ 2021 đến 2025\n  const years = Array.from({\n    length: 5\n  }, (_, i) => 2021 + i);\n\n  // Logic để hiển thị tháng dựa trên năm được chọn\n  const getAvailableMonths = () => {\n    const currentYear = new Date().getFullYear();\n    const currentMonth = new Date().getMonth(); // 0-11\n\n    if (selectedYear === currentYear) {\n      // Nếu là năm hiện tại (2025), chỉ hiển thị đến tháng hiện tại (tháng 5 = index 4)\n      return months.slice(0, currentMonth + 1);\n    } else {\n      // Nếu là năm khác, hiển thị đủ 12 tháng\n      return months;\n    }\n  };\n\n  // Pagination logic\n  const totalItems = (reservations === null || reservations === void 0 ? void 0 : reservations.length) || 0;\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = startIndex + itemsPerPage;\n  const currentReservations = (reservations === null || reservations === void 0 ? void 0 : reservations.slice(startIndex, endIndex)) || [];\n  const handlePageChange = pageNumber => {\n    setCurrentPage(pageNumber);\n  };\n\n  // Tạo pagination items\n  const renderPaginationItems = () => {\n    const items = [];\n    const maxVisiblePages = 5;\n\n    // Previous button\n    items.push(/*#__PURE__*/_jsxDEV(Pagination.Prev, {\n      disabled: currentPage === 1,\n      onClick: () => handlePageChange(currentPage - 1)\n    }, \"prev\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this));\n\n    // First page\n    if (totalPages > maxVisiblePages && currentPage > 3) {\n      items.push(/*#__PURE__*/_jsxDEV(Pagination.Item, {\n        onClick: () => handlePageChange(1),\n        children: \"1\"\n      }, 1, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this));\n      if (currentPage > 4) {\n        items.push(/*#__PURE__*/_jsxDEV(Pagination.Ellipsis, {}, \"ellipsis1\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 20\n        }, this));\n      }\n    }\n\n    // Visible pages\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let page = startPage; page <= endPage; page++) {\n      items.push(/*#__PURE__*/_jsxDEV(Pagination.Item, {\n        active: page === currentPage,\n        onClick: () => handlePageChange(page),\n        children: page\n      }, page, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this));\n    }\n\n    // Last page\n    if (totalPages > maxVisiblePages && currentPage < totalPages - 2) {\n      if (currentPage < totalPages - 3) {\n        items.push(/*#__PURE__*/_jsxDEV(Pagination.Ellipsis, {}, \"ellipsis2\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 20\n        }, this));\n      }\n      items.push(/*#__PURE__*/_jsxDEV(Pagination.Item, {\n        onClick: () => handlePageChange(totalPages),\n        children: totalPages\n      }, totalPages, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this));\n    }\n\n    // Next button\n    items.push(/*#__PURE__*/_jsxDEV(Pagination.Next, {\n      disabled: currentPage === totalPages,\n      onClick: () => handlePageChange(currentPage + 1)\n    }, \"next\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this));\n    return items;\n  };\n  const totalCustomerPaid = reservations === null || reservations === void 0 ? void 0 : reservations.reduce((sum, r) => sum + r.totalPrice, 0);\n\n  // Separate calculations for online and offline reservations\n  const onlineReservations = (reservations === null || reservations === void 0 ? void 0 : reservations.filter(r => r.status !== \"OFFLINE\")) || [];\n  const offlineReservations = (reservations === null || reservations === void 0 ? void 0 : reservations.filter(r => r.status === \"OFFLINE\")) || [];\n  const onlineTotalPaid = onlineReservations.reduce((sum, r) => sum + r.totalPrice, 0);\n  const offlineTotalPaid = offlineReservations.reduce((sum, r) => sum + r.totalPrice, 0);\n\n  // Commission only calculated for online reservations\n  const totalCommission = Math.floor(onlineTotalPaid * 0.12);\n\n  // Host amount: online reservations (88%) + offline reservations (100%)\n  const totalAmountToHost = Math.floor(onlineTotalPaid * 0.88) + offlineTotalPaid;\n  const completedCount = (reservations === null || reservations === void 0 ? void 0 : reservations.filter(r => r.status === \"COMPLETED\" || r.status === \"CHECKED OUT\").length) || 0;\n  const pendingCount = (reservations === null || reservations === void 0 ? void 0 : reservations.filter(r => r.status === \"PENDING\").length) || 0;\n  const bookedCount = (reservations === null || reservations === void 0 ? void 0 : reservations.filter(r => r.status === \"BOOKED\" || r.status === \"CHECKED IN\").length) || 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"main-content_1\",\n    children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"B\\u1EA3ng \\u0111i\\u1EC1u khi\\u1EC3n thanh to\\xE1n ch\\u1EE7 kh\\xE1ch s\\u1EA1n\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            as: \"h5\",\n            children: \"K\\u1EF3 thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                className: \"align-items-end\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Th\\xE1ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      value: selectedMonth,\n                      onChange: e => {\n                        const newMonth = Number.parseInt(e.target.value);\n                        setSelectedMonth(newMonth);\n                      },\n                      children: getAvailableMonths().map((month, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: index + 1,\n                        children: month\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 339,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"N\\u0103m\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      value: selectedYear,\n                      onChange: e => {\n                        const newYear = Number.parseInt(e.target.value);\n                        setSelectedYear(newYear);\n\n                        // Nếu chuyển sang năm hiện tại và tháng hiện tại > tháng được chọn\n                        const currentYear = new Date().getFullYear();\n                        const currentMonth = new Date().getMonth();\n                        if (newYear === currentYear && selectedMonth > currentMonth) {\n                          setSelectedMonth(currentMonth);\n                        }\n                      },\n                      children: years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: year,\n                        children: year\n                      }, year, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Tr\\u1EA1ng th\\xE1i\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      value: selectedStatus,\n                      onChange: e => setSelectedStatus(e.target.value),\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"All\",\n                        children: \"T\\u1EA5t c\\u1EA3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"COMPLETED\",\n                        children: \"COMPLETED\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"CHECKED OUT\",\n                        children: \"CHECKED OUT\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"CHECKED IN\",\n                        children: \"CHECKED IN\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 384,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"BOOKED\",\n                        children: \"BOOKED\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 385,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"CANCELLED\",\n                        children: \"CANCELLED\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"NOT PAID\",\n                        children: \"NOT PAID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"OFFLINE\",\n                        children: \"OFFLINE\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"L\\u1ECDc theo:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      value: selectedSort,\n                      onChange: e => setSelectedSort(e.target.value),\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"desc\",\n                        children: \"M\\u1EDBi nh\\u1EA5t\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"asc\",\n                        children: \"C\\u0169 nh\\u1EA5t\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 400,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            as: \"h5\",\n            children: \"T\\xF3m t\\u1EAFt thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"T\\u1ED5ng thanh to\\xE1n c\\u1EE7a kh\\xE1ch:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Utils.formatCurrency(totalCustomerPaid)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"T\\u1ED5ng hoa h\\u1ED3ng (Admin):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-danger\",\n                children: Utils.formatCurrency(totalCommission)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"T\\u1ED5ng s\\u1ED1 ti\\u1EC1n cho ch\\u1EE7 kh\\xE1ch s\\u1EA1n:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-success\",\n                children: Utils.formatCurrency(totalAmountToHost)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"H\\xF3a \\u0111\\u01A1n Ho\\xE0n th\\xE0nh / \\u0110ang x\\u1EED l\\xFD / \\u0110ang th\\u1EF1c hi\\u1EC7n:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [completedCount, \"/\", pendingCount, \"/\", bookedCount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        as: \"h5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Danh s\\xE1ch thanh to\\xE1n cho \", getAvailableMonths()[selectedMonth - 1], \" \", \"- \", selectedYear]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-muted\",\n            children: [\"Hi\\u1EC3n th\\u1ECB \", startIndex + 1, \"-\", Math.min(endIndex, totalItems), \" c\\u1EE7a\", \" \", totalItems, \" k\\u1EBFt qu\\u1EA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: [/*#__PURE__*/_jsxDEV(Table, {\n          responsive: true,\n          striped: true,\n          hover: true,\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"#\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Ng\\xE0y \\u0111\\u1EB7t\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Ph\\xF2ng \\u0111\\u1EB7t\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Kh\\xE1ch thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Hoa h\\u1ED3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"S\\u1ED1 ti\\u1EC1n cho ch\\u1EE7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: currentReservations && currentReservations.length > 0 ? currentReservations.map((reservation, index) => {\n              if (reservation.status !== \"OFFLINE\") {\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  onClick: () => {\n                    setShowModal(true);\n                    setDetailReservation(reservation);\n                  },\n                  style: {\n                    cursor: \"pointer\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: startIndex + index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: Utils.getDate(reservation.createdAt, 18)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: reservation.rooms && reservation.rooms.length > 0 && reservation.rooms.map((roomObj, idx) => {\n                        var _roomObj$room;\n                        console.log(\"ABC: \", roomObj);\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(_roomObj$room = roomObj.room) === null || _roomObj$room === void 0 ? void 0 : _roomObj$room.name, \" - \", roomObj.quantity, \" \", \"rooms\"]\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 489,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: Utils.formatCurrency(reservation.totalPrice || 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-danger\",\n                    children: Utils.formatCurrency(Math.floor(reservation.totalPrice * 0.12 || 0))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-success\",\n                    children: Utils.formatCurrency(Math.floor(reservation.totalPrice * 0.88 || 0))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge ${reservation.status === \"COMPLETED\" ? \"bg-secondary\" : reservation.status === \"PENDING\" ? \"bg-warning\" : reservation.status === \"CANCELLED\" || reservation.status === \"NOT PAID\" ? \"bg-danger\" : reservation.status === \"BOOKED\" ? \"bg-success\" : reservation.status === \"CHECKED OUT\" ? \"bg-info\" : \"bg-primary\"}`,\n                      style: {\n                        width: \"100px\",\n                        height: \"30px\",\n                        paddingTop: \"10px\",\n                        paddingBottom: \"10px\"\n                      },\n                      children: reservation.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this)]\n                }, reservation._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  onClick: () => {\n                    setShowModal(true);\n                    setDetailReservation(reservation);\n                  },\n                  style: {\n                    cursor: \"pointer\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: startIndex + index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: Utils.getDate(reservation.createdAt, 18)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: reservation.rooms && reservation.rooms.length > 0 && reservation.rooms.map((roomObj, idx) => {\n                        var _roomObj$room2;\n                        console.log(\"ABC: \", roomObj);\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(_roomObj$room2 = roomObj.room) === null || _roomObj$room2 === void 0 ? void 0 : _roomObj$room2.name, \" - \", roomObj.quantity, \" \", \"rooms\"]\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 557,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: Utils.formatCurrency(reservation.totalPrice || 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-danger\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-success\",\n                    children: Utils.formatCurrency(Math.floor(reservation.totalPrice * 1 || 0))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge ${reservation.status === \"COMPLETED\" ? \"bg-secondary\" : reservation.status === \"PENDING\" ? \"bg-warning\" : reservation.status === \"CANCELLED\" || reservation.status === \"NOT PAID\" ? \"bg-danger\" : reservation.status === \"BOOKED\" ? \"bg-success\" : reservation.status === \"CHECKED OUT\" ? \"bg-info\" : \"bg-primary\"}`,\n                      style: {\n                        width: \"100px\",\n                        height: \"30px\",\n                        paddingTop: \"10px\",\n                        paddingBottom: \"10px\"\n                      },\n                      children: reservation.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 25\n                  }, this)]\n                }, reservation._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 23\n                }, this);\n              }\n            }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"text-center\",\n                children: \"Kh\\xF4ng t\\xECm th\\u1EA5y thanh to\\xE1n n\\xE0o cho k\\u1EF3 n\\xE0y\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tfoot\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"table-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"3\",\n                className: \"text-end\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"T\\u1ED5ng:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Utils.formatCurrency(totalCustomerPaid)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Utils.formatCurrency(totalCommission)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Utils.formatCurrency(totalAmountToHost)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center mt-3\",\n          children: /*#__PURE__*/_jsxDEV(Pagination, {\n            children: renderPaginationItems()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            as: \"h5\",\n            children: \"Thanh to\\xE1n t\\u1EEB Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"N\\u0103m\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: selectedAdminYear,\n                  onChange: e => setSelectedAdminYear(Number.parseInt(e.target.value)),\n                  children: years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: year,\n                    children: year\n                  }, year, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        as: \"h5\",\n        children: \"Th\\xF4ng tin t\\xE0i kho\\u1EA3n ng\\xE2n h\\xE0ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: [!hasBankInfo && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"warning\",\n          children: \"Vui l\\xF2ng th\\xEAm th\\xF4ng tin t\\xE0i kho\\u1EA3n ng\\xE2n h\\xE0ng c\\u1EE7a b\\u1EA1n \\u0111\\u1EC3 nh\\u1EADn thanh to\\xE1n.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this), showForm ? /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"S\\u1ED1 t\\xE0i kho\\u1EA3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"accountNumber\",\n                  value: bankInfoForm.accountNumber,\n                  onChange: handleBankInfoChange,\n                  placeholder: \"Nh\\u1EADp s\\u1ED1 t\\xE0i kho\\u1EA3n c\\u1EE7a b\\u1EA1n\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"T\\xEAn t\\xE0i kho\\u1EA3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"accountName\",\n                  value: bankInfoForm.accountName,\n                  onChange: handleBankInfoChange,\n                  placeholder: \"Nh\\u1EADp t\\xEAn ch\\u1EE7 t\\xE0i kho\\u1EA3n\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"T\\xEAn ng\\xE2n h\\xE0ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"bankName\",\n                  value: bankInfoForm.bankName,\n                  onChange: handleBankInfoChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Ch\\u1ECDn ng\\xE2n h\\xE0ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"MB Bank\",\n                    children: \"MB Bank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Techcombank\",\n                    children: \"Techcombank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Vietcombank\",\n                    children: \"Vietcombank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"BIDV\",\n                    children: \"BIDV\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"HDBank\",\n                    children: \"HDBank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"VPBank\",\n                    children: \"VPBank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 724,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"TPBank\",\n                    children: \"TPBank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Chi nh\\xE1nh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"branch\",\n                  value: bankInfoForm.branch,\n                  onChange: handleBankInfoChange,\n                  placeholder: \"Nh\\u1EADp chi nh\\xE1nh ng\\xE2n h\\xE0ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              type: \"submit\",\n              children: hasBankInfo ? \"Cập nhật thông tin ngân hàng\" : \"Lưu thông tin ngân hàng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this), hasBankInfo && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              className: \"ms-2\",\n              type: \"button\",\n              onClick: handleCancel,\n              children: \"H\\u1EE7y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: bankInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"S\\u1ED1 t\\xE0i kho\\u1EA3n:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 23\n                }, this), \" \", bankInfo.accountNumber]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"T\\xEAn t\\xE0i kho\\u1EA3n:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 23\n                }, this), \" \", bankInfo.accountName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"T\\xEAn ng\\xE2n h\\xE0ng:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 23\n                }, this), \" \", bankInfo.bankName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Chi nh\\xE1nh:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 23\n                }, this), \" \", bankInfo.branch || \"N/A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-primary\",\n                onClick: handleEdit,\n                children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin ng\\xE2n h\\xE0ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-danger\",\n                className: \"ms-2\",\n                onClick: handleDelete,\n                children: \"X\\xF3a th\\xF4ng tin ng\\xE2n h\\xE0ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 17\n          }, this)\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        as: \"h5\",\n        children: [\"Danh s\\xE1ch doanh thu cho n\\u0103m \", selectedAdminYear]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          responsive: true,\n          striped: true,\n          hover: true,\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"#\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Th\\xE1ng/N\\u0103m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"T\\u1ED5ng doanh thu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 809,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Hoa h\\u1ED3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"S\\u1ED1 ti\\u1EC1n cho ch\\u1EE7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Ng\\xE0y tr\\u1EA3 ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: list && list.length > 0 ? list.map((payment, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [payment.month, \"/\", payment.year]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(payment.amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"text-danger\",\n                children: formatCurrency(payment.amount * 0.15)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"text-success\",\n                children: formatCurrency(payment.amount * 0.85)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `badge ${payment.status === \"COMPLETED\" ? \"bg-success\" : payment.status === \"PENDING\" ? \"bg-warning\" : payment.status === \"CANCELLED\" ? \"bg-danger\" : \"bg-info\"}`,\n                  children: payment.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: payment.paymentDate ? Utils.getDate(payment.paymentDate) : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 21\n              }, this)]\n            }, payment._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"text-center\",\n                children: \"Kh\\xF4ng t\\xECm th\\u1EA5y thanh to\\xE1n n\\xE0o cho k\\u1EF3 n\\xE0y\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 799,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TransactionDetail, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      handleClose: () => setShowModal(false),\n      detailReservation: detailReservation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 865,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 317,\n    columnNumber: 5\n  }, this);\n};\n_s(Transaction, \"3vIxVpfhPTxQav+MlB2G2JuxaIg=\", false, function () {\n  return [useNavigate, useAppDispatch, useAppSelector, useAppSelector, useAppSelector];\n});\n_c = Transaction;\nexport default Transaction;\nvar _c;\n$RefreshReg$(_c, \"Transaction\");", "map": {"version": 3, "names": ["_jsxFileName", "_s", "$RefreshSig$", "React", "useState", "useEffect", "Row", "Col", "Table", "Form", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "Pagination", "showToast", "ToastProvider", "useNavigate", "TransactionDetail", "useDispatch", "useSelector", "ReservationActions", "MonthlyPaymentActions", "Utils", "useAppDispatch", "useAppSelector", "BankInfoActions", "trackSynchronousRequestDataAccessInDev", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Transaction", "navigate", "showModal", "setShowModal", "dispatch", "reservations", "state", "Reservation", "list", "MonthlyPayment", "bankInfo", "hasBankInfo", "showForm", "BankInfo", "selectedAdminYear", "setSelectedAdminYear", "Date", "getFullYear", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "getMonth", "selected<PERSON>ear", "setSelectedYear", "selectedStatus", "setSelectedStatus", "selected<PERSON>ort", "setSelectedSort", "detailReservation", "setDetailReservation", "currentPage", "setCurrentPage", "itemsPerPage", "bankInfoForm", "setBankInfoForm", "accountNumber", "accountName", "bankName", "branch", "type", "FETCH_RESERVATIONS", "payload", "status", "undefined", "month", "year", "sort", "FETCH_MONTHLY_PAYMENTS", "handleBankInfoChange", "e", "name", "value", "target", "handleSubmit", "preventDefault", "error", "console", "log", "UPDATE_BANK_INFO", "success", "SAVE_BANK_INFO", "handleEdit", "SET_SHOW_FORM", "handleDelete", "DELETE_BANK_INFO", "handleCancel", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "months", "years", "Array", "from", "length", "_", "i", "getAvailableMonths", "currentYear", "currentMonth", "slice", "totalItems", "totalPages", "Math", "ceil", "startIndex", "endIndex", "currentReservations", "handlePageChange", "pageNumber", "renderPaginationItems", "items", "maxVisiblePages", "push", "Prev", "disabled", "onClick", "fileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "children", "El<PERSON><PERSON>", "startPage", "max", "endPage", "min", "page", "active", "Next", "totalCustomerPaid", "reduce", "sum", "r", "totalPrice", "onlineReservations", "filter", "offlineReservations", "onlineTotalPaid", "offlineTotalPaid", "totalCommission", "floor", "totalAmountToHost", "completedCount", "pendingCount", "bookedCount", "className", "md", "Header", "as", "Body", "Group", "Label", "Select", "onChange", "newMonth", "Number", "parseInt", "map", "index", "newYear", "responsive", "striped", "hover", "reservation", "cursor", "getDate", "createdAt", "rooms", "roomObj", "idx", "_roomObj$room", "room", "quantity", "width", "height", "paddingTop", "paddingBottom", "_id", "_roomObj$room2", "colSpan", "variant", "onSubmit", "Control", "placeholder", "required", "payment", "paymentDate", "show", "onHide", "handleClose", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/Transaction.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Row,\r\n  Col,\r\n  Table,\r\n  Form,\r\n  But<PERSON>,\r\n  Card,\r\n  Alert,\r\n  Pagination,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport TransactionDetail from \"./TransactionDetail\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport ReservationActions from \"../../redux/reservation/actions\";\r\nimport MonthlyPaymentActions from \"@redux/monthlyPayment/actions\";\r\nimport Utils from \"@utils/Utils\";\r\nimport { useAppDispatch, useAppSelector } from \"../../redux/store\";\r\nimport BankInfoActions from \"../../redux/bankInfo/actions\";\r\nimport { trackSynchronousRequestDataAccessInDev } from \"next/dist/server/app-render/dynamic-rendering\";\r\n\r\nconst Transaction = () => {\r\n  const navigate = useNavigate();\r\n  const [showModal, setShowModal] = useState(false);\r\n  const dispatch = useAppDispatch(); // Sử dụng useAppDispatch thay vì useDispatch\r\n  \r\n  // Sử dụng useAppSelector cho tất cả\r\n  const { reservations } = useAppSelector((state) => state.Reservation);\r\n  const { list } = useAppSelector((state) => state.MonthlyPayment);\r\n  const { bankInfo, hasBankInfo, showForm } = useAppSelector(\r\n    (state) => state.BankInfo\r\n  );\r\n  const [selectedAdminYear, setSelectedAdminYear] = useState(\r\n    new Date().getFullYear()\r\n  );\r\n\r\n  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);\r\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\r\n  const [selectedStatus, setSelectedStatus] = useState(\"All\");\r\n  const [selectedSort, setSelectedSort] = useState(\"desc\");\r\n  const [detailReservation, setDetailReservation] = useState({});\r\n\r\n  // Pagination states\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const itemsPerPage = 10;\r\n\r\n  // Local state cho form\r\n  const [bankInfoForm, setBankInfoForm] = useState({\r\n    accountNumber: \"\",\r\n    accountName: \"\",\r\n    bankName: \"\",\r\n    branch: \"\",\r\n  });\r\n\r\n  // Sync form với Redux state khi có bankInfo\r\n  useEffect(() => {\r\n    if (bankInfo) {\r\n      setBankInfoForm(bankInfo);\r\n    }\r\n  }, [bankInfo]);\r\n\r\n  // Gọi API lấy reservation theo filter, sort, month, year\r\n  useEffect(() => {\r\n    dispatch({\r\n      type: ReservationActions.FETCH_RESERVATIONS,\r\n      payload: {\r\n        status: selectedStatus !== \"All\" ? selectedStatus : undefined,\r\n        month: selectedMonth,\r\n        year: selectedYear,\r\n        sort: selectedSort,\r\n      },\r\n    });\r\n    // Reset về trang 1 khi filter thay đổi\r\n    setCurrentPage(1);\r\n  }, [dispatch, selectedMonth, selectedYear, selectedStatus, selectedSort]);\r\n  useEffect(() => {\r\n    dispatch({\r\n      type: MonthlyPaymentActions.FETCH_MONTHLY_PAYMENTS,\r\n      payload: { year: selectedAdminYear },\r\n    });\r\n  }, [dispatch, selectedAdminYear]);\r\n\r\n  const handleBankInfoChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setBankInfoForm({\r\n      ...bankInfoForm,\r\n      [name]: value,\r\n    });\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Validate form\r\n    if (!bankInfoForm.accountNumber || !bankInfoForm.accountName || !bankInfoForm.bankName) {\r\n      showToast.error('Vui lòng điền đầy đủ thông tin!');\r\n      return;\r\n    }\r\n\r\n    console.log('Current bankInfo state:', bankInfo);\r\n    console.log('Form data:', bankInfoForm);\r\n    console.log('hasBankInfo:', hasBankInfo);\r\n\r\n    if (hasBankInfo) {\r\n      dispatch({\r\n        type: BankInfoActions.UPDATE_BANK_INFO,\r\n        payload: bankInfoForm,\r\n      });\r\n      showToast.success('Cập nhật thông tin ngân hàng thành công!');\r\n    } else {\r\n      dispatch({\r\n        type: BankInfoActions.SAVE_BANK_INFO,\r\n        payload: bankInfoForm,\r\n      });\r\n      showToast.success('Lưu thông tin ngân hàng thành công!');\r\n    }\r\n  };\r\n\r\n  const handleEdit = () => {\r\n    setBankInfoForm(bankInfo);\r\n    dispatch({\r\n      type: BankInfoActions.SET_SHOW_FORM,\r\n      payload: true,\r\n    });\r\n  };\r\n\r\n  const handleDelete = () => {\r\n    if (true) {\r\n      dispatch({ type: BankInfoActions.DELETE_BANK_INFO });\r\n      setBankInfoForm({\r\n        accountNumber: \"\",\r\n        accountName: \"\",\r\n        bankName: \"\",\r\n        branch: \"\",\r\n      });\r\n      showToast.success('Xoá thông tin ngân hàng thành công!');\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setBankInfoForm(\r\n      bankInfo || {\r\n        accountNumber: \"\",\r\n        accountName: \"\",\r\n        bankName: \"\",\r\n        branch: \"\",\r\n      }\r\n    );\r\n    dispatch({\r\n      type: BankInfoActions.SET_SHOW_FORM,\r\n      payload: false,\r\n    });\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    return new Intl.NumberFormat(\"vi-VN\", {\r\n      style: \"currency\",\r\n      currency: \"VND\",\r\n    }).format(amount);\r\n  };\r\n\r\n  const months = [\r\n    \"Tháng 1\",\r\n    \"Tháng 2\",\r\n    \"Tháng 3\",\r\n    \"Tháng 4\",\r\n    \"Tháng 5\",\r\n    \"Tháng 6\",\r\n    \"Tháng 7\",\r\n    \"Tháng 8\",\r\n    \"Tháng 9\",\r\n    \"Tháng 10\",\r\n    \"Tháng 11\",\r\n    \"Tháng 12\",\r\n  ];\r\n\r\n  // Tạo danh sách năm từ 2021 đến 2025\r\n  const years = Array.from({ length: 5 }, (_, i) => 2021 + i);\r\n\r\n  // Logic để hiển thị tháng dựa trên năm được chọn\r\n  const getAvailableMonths = () => {\r\n    const currentYear = new Date().getFullYear();\r\n    const currentMonth = new Date().getMonth(); // 0-11\r\n\r\n    if (selectedYear === currentYear) {\r\n      // Nếu là năm hiện tại (2025), chỉ hiển thị đến tháng hiện tại (tháng 5 = index 4)\r\n      return months.slice(0, currentMonth + 1);\r\n    } else {\r\n      // Nếu là năm khác, hiển thị đủ 12 tháng\r\n      return months;\r\n    }\r\n  };\r\n\r\n  // Pagination logic\r\n  const totalItems = reservations?.length || 0;\r\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentReservations = reservations?.slice(startIndex, endIndex) || [];\r\n\r\n  const handlePageChange = (pageNumber) => {\r\n    setCurrentPage(pageNumber);\r\n  };\r\n\r\n  // Tạo pagination items\r\n  const renderPaginationItems = () => {\r\n    const items = [];\r\n    const maxVisiblePages = 5;\r\n\r\n    // Previous button\r\n    items.push(\r\n      <Pagination.Prev\r\n        key=\"prev\"\r\n        disabled={currentPage === 1}\r\n        onClick={() => handlePageChange(currentPage - 1)}\r\n      />\r\n    );\r\n\r\n    // First page\r\n    if (totalPages > maxVisiblePages && currentPage > 3) {\r\n      items.push(\r\n        <Pagination.Item key={1} onClick={() => handlePageChange(1)}>\r\n          1\r\n        </Pagination.Item>\r\n      );\r\n      if (currentPage > 4) {\r\n        items.push(<Pagination.Ellipsis key=\"ellipsis1\" />);\r\n      }\r\n    }\r\n\r\n    // Visible pages\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let page = startPage; page <= endPage; page++) {\r\n      items.push(\r\n        <Pagination.Item\r\n          key={page}\r\n          active={page === currentPage}\r\n          onClick={() => handlePageChange(page)}\r\n        >\r\n          {page}\r\n        </Pagination.Item>\r\n      );\r\n    }\r\n\r\n    // Last page\r\n    if (totalPages > maxVisiblePages && currentPage < totalPages - 2) {\r\n      if (currentPage < totalPages - 3) {\r\n        items.push(<Pagination.Ellipsis key=\"ellipsis2\" />);\r\n      }\r\n      items.push(\r\n        <Pagination.Item\r\n          key={totalPages}\r\n          onClick={() => handlePageChange(totalPages)}\r\n        >\r\n          {totalPages}\r\n        </Pagination.Item>\r\n      );\r\n    }\r\n\r\n    // Next button\r\n    items.push(\r\n      <Pagination.Next\r\n        key=\"next\"\r\n        disabled={currentPage === totalPages}\r\n        onClick={() => handlePageChange(currentPage + 1)}\r\n      />\r\n    );\r\n\r\n    return items;\r\n  };\r\n\r\n  const totalCustomerPaid = reservations?.reduce(\r\n    (sum, r) => sum + r.totalPrice,\r\n    0\r\n  );\r\n\r\n  // Separate calculations for online and offline reservations\r\n  const onlineReservations =\r\n    reservations?.filter((r) => r.status !== \"OFFLINE\") || [];\r\n  const offlineReservations =\r\n    reservations?.filter((r) => r.status === \"OFFLINE\") || [];\r\n\r\n  const onlineTotalPaid = onlineReservations.reduce(\r\n    (sum, r) => sum + r.totalPrice,\r\n    0\r\n  );\r\n  const offlineTotalPaid = offlineReservations.reduce(\r\n    (sum, r) => sum + r.totalPrice,\r\n    0\r\n  );\r\n\r\n  // Commission only calculated for online reservations\r\n  const totalCommission = Math.floor(onlineTotalPaid * 0.12);\r\n\r\n  // Host amount: online reservations (88%) + offline reservations (100%)\r\n  const totalAmountToHost =\r\n    Math.floor(onlineTotalPaid * 0.88) + offlineTotalPaid;\r\n\r\n  const completedCount =\r\n    reservations?.filter(\r\n      (r) => r.status === \"COMPLETED\" || r.status === \"CHECKED OUT\"\r\n    ).length || 0;\r\n  const pendingCount =\r\n    reservations?.filter((r) => r.status === \"PENDING\").length || 0;\r\n  const bookedCount =\r\n    reservations?.filter(\r\n      (r) => r.status === \"BOOKED\" || r.status === \"CHECKED IN\"\r\n    ).length || 0;\r\n\r\n  return (\r\n    <div className=\"main-content_1\">\r\n      <ToastProvider />\r\n      <h4>Bảng điều khiển thanh toán chủ khách sạn</h4>\r\n\r\n      <Row className=\"mb-4\">\r\n        <Col md={6}>\r\n          <Card>\r\n            <Card.Header as=\"h5\">Kỳ thanh toán</Card.Header>\r\n            <Card.Body>\r\n              <Form>\r\n                <Row className=\"align-items-end\">\r\n                  <Col>\r\n                    <Form.Group className=\"mb-0\">\r\n                      <Form.Label>Tháng</Form.Label>\r\n                      <Form.Select\r\n                        value={selectedMonth}\r\n                        onChange={(e) => {\r\n                          const newMonth = Number.parseInt(e.target.value);\r\n                          setSelectedMonth(newMonth);\r\n                        }}\r\n                      >\r\n                        {getAvailableMonths().map((month, index) => (\r\n                          <option key={index} value={index + 1}>\r\n                            {month}\r\n                          </option>\r\n                        ))}\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </Col>\r\n                  <Col>\r\n                    <Form.Group className=\"mb-0\">\r\n                      <Form.Label>Năm</Form.Label>\r\n                      <Form.Select\r\n                        value={selectedYear}\r\n                        onChange={(e) => {\r\n                          const newYear = Number.parseInt(e.target.value);\r\n                          setSelectedYear(newYear);\r\n\r\n                          // Nếu chuyển sang năm hiện tại và tháng hiện tại > tháng được chọn\r\n                          const currentYear = new Date().getFullYear();\r\n                          const currentMonth = new Date().getMonth();\r\n                          if (\r\n                            newYear === currentYear &&\r\n                            selectedMonth > currentMonth\r\n                          ) {\r\n                            setSelectedMonth(currentMonth);\r\n                          }\r\n                        }}\r\n                      >\r\n                        {years.map((year) => (\r\n                          <option key={year} value={year}>\r\n                            {year}\r\n                          </option>\r\n                        ))}\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </Col>\r\n                  <Col>\r\n                    <Form.Group className=\"mb-0\">\r\n                      <Form.Label>Trạng thái</Form.Label>\r\n                      <Form.Select\r\n                        value={selectedStatus}\r\n                        onChange={(e) => setSelectedStatus(e.target.value)}\r\n                      >\r\n                        <option value=\"All\">Tất cả</option>\r\n                        <option value=\"COMPLETED\">COMPLETED</option>\r\n                        <option value=\"CHECKED OUT\">CHECKED OUT</option>\r\n                        <option value=\"CHECKED IN\">CHECKED IN</option>\r\n                        <option value=\"BOOKED\">BOOKED</option>\r\n                        <option value=\"CANCELLED\">CANCELLED</option>\r\n                        <option value=\"NOT PAID\">NOT PAID</option>\r\n                        <option value=\"OFFLINE\">OFFLINE</option>\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </Col>\r\n                  <Col>\r\n                    <Form.Group className=\"mb-0\">\r\n                      <Form.Label>Lọc theo:</Form.Label>\r\n                      <Form.Select\r\n                        value={selectedSort}\r\n                        onChange={(e) => setSelectedSort(e.target.value)}\r\n                      >\r\n                        <option value=\"desc\">Mới nhất</option>\r\n                        <option value=\"asc\">Cũ nhất</option>\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col md={6}>\r\n          <Card>\r\n            <Card.Header as=\"h5\">Tóm tắt thanh toán</Card.Header>\r\n            <Card.Body>\r\n              <div className=\"d-flex justify-content-between mb-2\">\r\n                <span>Tổng thanh toán của khách:</span>\r\n                <strong>{Utils.formatCurrency(totalCustomerPaid)}</strong>\r\n              </div>\r\n              <div className=\"d-flex justify-content-between mb-2\">\r\n                <span>Tổng hoa hồng (Admin):</span>\r\n                <strong className=\"text-danger\">\r\n                  {Utils.formatCurrency(totalCommission)}\r\n                </strong>\r\n              </div>\r\n              <div className=\"d-flex justify-content-between mb-2\">\r\n                <span>Tổng số tiền cho chủ khách sạn:</span>\r\n                <strong className=\"text-success\">\r\n                  {Utils.formatCurrency(totalAmountToHost)}\r\n                </strong>\r\n              </div>\r\n              <div className=\"d-flex justify-content-between mb-2\">\r\n                <span>Hóa đơn Hoàn thành / Đang xử lý / Đang thực hiện:</span>\r\n                <strong>\r\n                  {completedCount}/{pendingCount}/{bookedCount}\r\n                </strong>\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      <Card className=\"mb-4\">\r\n        <Card.Header as=\"h5\">\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <span>\r\n              Danh sách thanh toán cho {getAvailableMonths()[selectedMonth - 1]}{\" \"}\r\n              - {selectedYear}\r\n            </span>\r\n            <span className=\"text-muted\">\r\n              Hiển thị {startIndex + 1}-{Math.min(endIndex, totalItems)} của{\" \"}\r\n              {totalItems} kết quả\r\n            </span>\r\n          </div>\r\n        </Card.Header>\r\n        <Card.Body>\r\n          <Table responsive striped hover>\r\n            <thead>\r\n              <tr>\r\n                <th>#</th>\r\n                <th>Ngày đặt</th>\r\n                <th>Phòng đặt</th>\r\n                <th>Khách thanh toán</th>\r\n                <th>Hoa hồng</th>\r\n                <th>Số tiền cho chủ</th>\r\n                <th>Trạng thái</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {currentReservations && currentReservations.length > 0 ? (\r\n                currentReservations.map((reservation, index) => {\r\n                  if (reservation.status !== \"OFFLINE\") {\r\n                    return (\r\n                      <tr\r\n                        key={reservation._id}\r\n                        onClick={() => {\r\n                          setShowModal(true);\r\n                          setDetailReservation(reservation);\r\n                        }}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      >\r\n                        <td>{startIndex + index + 1}</td>\r\n                        <td>{Utils.getDate(reservation.createdAt, 18)}</td>\r\n                        <td>\r\n                          <span>\r\n                            {reservation.rooms &&\r\n                              reservation.rooms.length > 0 &&\r\n                              reservation.rooms.map((roomObj, idx) => {\r\n                                console.log(\"ABC: \", roomObj);\r\n                                return (\r\n                                  <div key={idx}>\r\n                                    {roomObj.room?.name} - {roomObj.quantity}{\" \"}\r\n                                    rooms\r\n                                  </div>\r\n                                );\r\n                              })}\r\n                          </span>\r\n                        </td>\r\n                        <td>\r\n                          {Utils.formatCurrency(reservation.totalPrice || 0)}\r\n                        </td>\r\n                        <td className=\"text-danger\">\r\n                          {Utils.formatCurrency(\r\n                            Math.floor(reservation.totalPrice * 0.12 || 0)\r\n                          )}\r\n                        </td>\r\n                        <td className=\"text-success\">\r\n                          {Utils.formatCurrency(\r\n                            Math.floor(reservation.totalPrice * 0.88 || 0)\r\n                          )}\r\n                        </td>\r\n                        <td>\r\n                          <span\r\n                            className={`badge ${\r\n                              reservation.status === \"COMPLETED\"\r\n                                ? \"bg-secondary\"\r\n                                : reservation.status === \"PENDING\"\r\n                                ? \"bg-warning\"\r\n                                : reservation.status === \"CANCELLED\" ||\r\n                                  reservation.status === \"NOT PAID\"\r\n                                ? \"bg-danger\"\r\n                                : reservation.status === \"BOOKED\"\r\n                                ? \"bg-success\"\r\n                                : reservation.status === \"CHECKED OUT\"\r\n                                ? \"bg-info\"\r\n                                : \"bg-primary\"\r\n                            }`}\r\n                            style={{\r\n                              width: \"100px\",\r\n                              height: \"30px\",\r\n                              paddingTop: \"10px\",\r\n                              paddingBottom: \"10px\",\r\n                            }}\r\n                          >\r\n                            {reservation.status}\r\n                          </span>\r\n                        </td>\r\n                      </tr>\r\n                    );\r\n                  } else {\r\n                    return (\r\n                      <tr\r\n                        key={reservation._id}\r\n                        onClick={() => {\r\n                          setShowModal(true);\r\n                          setDetailReservation(reservation);\r\n                        }}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      >\r\n                        <td>{startIndex + index + 1}</td>\r\n                        <td>{Utils.getDate(reservation.createdAt, 18)}</td>\r\n                        <td>\r\n                          <span>\r\n                            {reservation.rooms &&\r\n                              reservation.rooms.length > 0 &&\r\n                              reservation.rooms.map((roomObj, idx) => {\r\n                                console.log(\"ABC: \", roomObj);\r\n                                return (\r\n                                  <div key={idx}>\r\n                                    {roomObj.room?.name} - {roomObj.quantity}{\" \"}\r\n                                    rooms\r\n                                  </div>\r\n                                );\r\n                              })}\r\n                          </span>\r\n                        </td>\r\n                        <td>\r\n                          {Utils.formatCurrency(reservation.totalPrice || 0)}\r\n                        </td>\r\n                        <td className=\"text-danger\">0</td>\r\n                        <td className=\"text-success\">\r\n                          {Utils.formatCurrency(\r\n                            Math.floor(reservation.totalPrice * 1 || 0)\r\n                          )}\r\n                        </td>\r\n                        <td>\r\n                          <span\r\n                            className={`badge ${\r\n                              reservation.status === \"COMPLETED\"\r\n                                ? \"bg-secondary\"\r\n                                : reservation.status === \"PENDING\"\r\n                                ? \"bg-warning\"\r\n                                : reservation.status === \"CANCELLED\" ||\r\n                                  reservation.status === \"NOT PAID\"\r\n                                ? \"bg-danger\"\r\n                                : reservation.status === \"BOOKED\"\r\n                                ? \"bg-success\"\r\n                                : reservation.status === \"CHECKED OUT\"\r\n                                ? \"bg-info\"\r\n                                : \"bg-primary\"\r\n                            }`}\r\n                            style={{\r\n                              width: \"100px\",\r\n                              height: \"30px\",\r\n                              paddingTop: \"10px\",\r\n                              paddingBottom: \"10px\",\r\n                            }}\r\n                          >\r\n                            {reservation.status}\r\n                          </span>\r\n                        </td>\r\n                      </tr>\r\n                    );\r\n                  }\r\n                })\r\n              ) : (\r\n                <tr>\r\n                  <td colSpan=\"7\" className=\"text-center\">\r\n                    Không tìm thấy thanh toán nào cho kỳ này\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n            <tfoot>\r\n              <tr className=\"table-secondary\">\r\n                <td colSpan=\"3\" className=\"text-end\">\r\n                  <strong>Tổng:</strong>\r\n                </td>\r\n                <td>\r\n                  <strong>{Utils.formatCurrency(totalCustomerPaid)}</strong>\r\n                </td>\r\n                <td>\r\n                  <strong>{Utils.formatCurrency(totalCommission)}</strong>\r\n                </td>\r\n                <td>\r\n                  <strong>{Utils.formatCurrency(totalAmountToHost)}</strong>\r\n                </td>\r\n                <td></td>\r\n              </tr>\r\n            </tfoot>\r\n          </Table>\r\n\r\n          {/* Pagination */}\r\n          {totalPages > 1 && (\r\n            <div className=\"d-flex justify-content-center mt-3\">\r\n              <Pagination>{renderPaginationItems()}</Pagination>\r\n            </div>\r\n          )}\r\n        </Card.Body>\r\n      </Card>\r\n\r\n      <Row className=\"mb-4\">\r\n        <Col md={6}>\r\n          <Card>\r\n            <Card.Header as=\"h5\">Thanh toán từ Admin</Card.Header>\r\n            <Card.Body>\r\n              <Form>\r\n                <Form.Group className=\"mb-0\">\r\n                  <Form.Label>Năm</Form.Label>\r\n                  <Form.Select\r\n                    value={selectedAdminYear}\r\n                    onChange={(e) =>\r\n                      setSelectedAdminYear(Number.parseInt(e.target.value))\r\n                    }\r\n                  >\r\n                    {years.map((year) => (\r\n                      <option key={year} value={year}>\r\n                        {year}\r\n                      </option>\r\n                    ))}\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Form>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Phần thông tin tài khoản ngân hàng */}\r\n      <Card className=\"mb-4\">\r\n        <Card.Header as=\"h5\">Thông tin tài khoản ngân hàng</Card.Header>\r\n        <Card.Body>\r\n          {!hasBankInfo && (\r\n            <Alert variant=\"warning\">\r\n              Vui lòng thêm thông tin tài khoản ngân hàng của bạn để nhận thanh\r\n              toán.\r\n            </Alert>\r\n          )}\r\n\r\n          {showForm ? (\r\n            <Form onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Số tài khoản</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"accountNumber\"\r\n                      value={bankInfoForm.accountNumber}\r\n                      onChange={handleBankInfoChange}\r\n                      placeholder=\"Nhập số tài khoản của bạn\"\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Tên tài khoản</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"accountName\"\r\n                      value={bankInfoForm.accountName}\r\n                      onChange={handleBankInfoChange}\r\n                      placeholder=\"Nhập tên chủ tài khoản\"\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Tên ngân hàng</Form.Label>\r\n                    <Form.Select\r\n                      name=\"bankName\"\r\n                      value={bankInfoForm.bankName}\r\n                      onChange={handleBankInfoChange}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn ngân hàng</option>\r\n                      <option value=\"MB Bank\">MB Bank</option>\r\n                      <option value=\"Techcombank\">Techcombank</option>\r\n                      <option value=\"Vietcombank\">Vietcombank</option>\r\n                      <option value=\"BIDV\">BIDV</option>\r\n                      <option value=\"HDBank\">HDBank</option>\r\n                      <option value=\"VPBank\">VPBank</option>\r\n                      <option value=\"TPBank\">TPBank</option>\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Chi nhánh</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"branch\"\r\n                      value={bankInfoForm.branch}\r\n                      onChange={handleBankInfoChange}\r\n                      placeholder=\"Nhập chi nhánh ngân hàng\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n              <div>\r\n                <Button variant=\"primary\" type=\"submit\">\r\n                  {hasBankInfo\r\n                    ? \"Cập nhật thông tin ngân hàng\"\r\n                    : \"Lưu thông tin ngân hàng\"}\r\n                </Button>\r\n                {hasBankInfo && (\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    className=\"ms-2\"\r\n                    type=\"button\"\r\n                    onClick={handleCancel}\r\n                  >\r\n                    Hủy\r\n                  </Button>\r\n                )}\r\n              </div>\r\n            </Form>\r\n          ) : (\r\n            <>\r\n              {bankInfo && (\r\n                <div>\r\n                  <Row className=\"mb-3\">\r\n                    <Col md={6}>\r\n                      <strong>Số tài khoản:</strong> {bankInfo.accountNumber}\r\n                    </Col>\r\n                    <Col md={6}>\r\n                      <strong>Tên tài khoản:</strong> {bankInfo.accountName}\r\n                    </Col>\r\n                  </Row>\r\n                  <Row className=\"mb-3\">\r\n                    <Col md={6}>\r\n                      <strong>Tên ngân hàng:</strong> {bankInfo.bankName}\r\n                    </Col>\r\n                    <Col md={6}>\r\n                      <strong>Chi nhánh:</strong> {bankInfo.branch || \"N/A\"}\r\n                    </Col>\r\n                  </Row>\r\n                  <div>\r\n                    <Button variant=\"outline-primary\" onClick={handleEdit}>\r\n                      Chỉnh sửa thông tin ngân hàng\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline-danger\"\r\n                      className=\"ms-2\"\r\n                      onClick={handleDelete}\r\n                    >\r\n                      Xóa thông tin ngân hàng\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n        </Card.Body>\r\n      </Card>\r\n\r\n      <Card className=\"mb-4\">\r\n        <Card.Header as=\"h5\">\r\n          Danh sách doanh thu cho năm {selectedAdminYear}\r\n        </Card.Header>\r\n        <Card.Body>\r\n          <Table responsive striped hover>\r\n            <thead>\r\n              <tr className=\"text-center\">\r\n                <th>#</th>\r\n                <th>Tháng/Năm</th>\r\n                <th>Tổng doanh thu</th>\r\n                <th>Hoa hồng</th>\r\n                <th>Số tiền cho chủ</th>\r\n                <th>Trạng thái</th>\r\n                <th>Ngày trả tiền</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {list && list.length > 0 ? (\r\n                list.map((payment, index) => (\r\n                  <tr key={payment._id} className=\"text-center\">\r\n                    <td>{index + 1}</td>\r\n                    <td>\r\n                      {payment.month}/{payment.year}\r\n                    </td>\r\n                    <td>{formatCurrency(payment.amount)}</td>\r\n                    <td className=\"text-danger\">\r\n                      {formatCurrency(payment.amount * 0.15)}\r\n                    </td>\r\n                    <td className=\"text-success\">\r\n                      {formatCurrency(payment.amount * 0.85)}\r\n                    </td>\r\n                    <td>\r\n                      <span\r\n                        className={`badge ${\r\n                          payment.status === \"COMPLETED\"\r\n                            ? \"bg-success\"\r\n                            : payment.status === \"PENDING\"\r\n                            ? \"bg-warning\"\r\n                            : payment.status === \"CANCELLED\"\r\n                            ? \"bg-danger\"\r\n                            : \"bg-info\"\r\n                        }`}\r\n                      >\r\n                        {payment.status}\r\n                      </span>\r\n                    </td>\r\n                    <td>\r\n                      {payment.paymentDate\r\n                        ? Utils.getDate(payment.paymentDate)\r\n                        : \"\"}\r\n                    </td>\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td colSpan=\"7\" className=\"text-center\">\r\n                    Không tìm thấy thanh toán nào cho kỳ này\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </Table>\r\n        </Card.Body>\r\n      </Card>\r\n\r\n      <TransactionDetail\r\n        show={showModal}\r\n        onHide={() => setShowModal(false)}\r\n        handleClose={() => setShowModal(false)}\r\n        detailReservation={detailReservation}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Transaction;\r\n"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,YAAA;EAAAC,EAAA,GAAAC,YAAA;AAEb,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,KAAK,MAAM,cAAc;AAChC,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,SAASC,sCAAsC,QAAQ,+CAA+C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvG,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAA9B,EAAA;EACxB,MAAM+B,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,QAAQ,GAAGZ,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEnC;EACA,MAAM;IAAEa;EAAa,CAAC,GAAGZ,cAAc,CAAEa,KAAK,IAAKA,KAAK,CAACC,WAAW,CAAC;EACrE,MAAM;IAAEC;EAAK,CAAC,GAAGf,cAAc,CAAEa,KAAK,IAAKA,KAAK,CAACG,cAAc,CAAC;EAChE,MAAM;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAS,CAAC,GAAGnB,cAAc,CACvDa,KAAK,IAAKA,KAAK,CAACO,QACnB,CAAC;EACD,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CACxD,IAAI2C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACzB,CAAC;EAED,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,IAAI2C,IAAI,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI2C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAC1E,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,MAAM,CAAC;EACxD,MAAM,CAACsD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9D;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM0D,YAAY,GAAG,EAAE;;EAEvB;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC;IAC/C6D,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA/D,SAAS,CAAC,MAAM;IACd,IAAIoC,QAAQ,EAAE;MACZuB,eAAe,CAACvB,QAAQ,CAAC;IAC3B;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACApC,SAAS,CAAC,MAAM;IACd8B,QAAQ,CAAC;MACPkC,IAAI,EAAEjD,kBAAkB,CAACkD,kBAAkB;MAC3CC,OAAO,EAAE;QACPC,MAAM,EAAElB,cAAc,KAAK,KAAK,GAAGA,cAAc,GAAGmB,SAAS;QAC7DC,KAAK,EAAEzB,aAAa;QACpB0B,IAAI,EAAEvB,YAAY;QAClBwB,IAAI,EAAEpB;MACR;IACF,CAAC,CAAC;IACF;IACAK,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC1B,QAAQ,EAAEc,aAAa,EAAEG,YAAY,EAAEE,cAAc,EAAEE,YAAY,CAAC,CAAC;EACzEnD,SAAS,CAAC,MAAM;IACd8B,QAAQ,CAAC;MACPkC,IAAI,EAAEhD,qBAAqB,CAACwD,sBAAsB;MAClDN,OAAO,EAAE;QAAEI,IAAI,EAAE9B;MAAkB;IACrC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,QAAQ,EAAEU,iBAAiB,CAAC,CAAC;EAEjC,MAAMiC,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,eAAe,CAAC;MACd,GAAGD,YAAY;MACf,CAACiB,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,YAAY,GAAIJ,CAAC,IAAK;IAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACrB,YAAY,CAACE,aAAa,IAAI,CAACF,YAAY,CAACG,WAAW,IAAI,CAACH,YAAY,CAACI,QAAQ,EAAE;MACtFrD,SAAS,CAACuE,KAAK,CAAC,iCAAiC,CAAC;MAClD;IACF;IAEAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE9C,QAAQ,CAAC;IAChD6C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAExB,YAAY,CAAC;IACvCuB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE7C,WAAW,CAAC;IAExC,IAAIA,WAAW,EAAE;MACfP,QAAQ,CAAC;QACPkC,IAAI,EAAE5C,eAAe,CAAC+D,gBAAgB;QACtCjB,OAAO,EAAER;MACX,CAAC,CAAC;MACFjD,SAAS,CAAC2E,OAAO,CAAC,0CAA0C,CAAC;IAC/D,CAAC,MAAM;MACLtD,QAAQ,CAAC;QACPkC,IAAI,EAAE5C,eAAe,CAACiE,cAAc;QACpCnB,OAAO,EAAER;MACX,CAAC,CAAC;MACFjD,SAAS,CAAC2E,OAAO,CAAC,qCAAqC,CAAC;IAC1D;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB3B,eAAe,CAACvB,QAAQ,CAAC;IACzBN,QAAQ,CAAC;MACPkC,IAAI,EAAE5C,eAAe,CAACmE,aAAa;MACnCrB,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,IAAI,EAAE;MACR1D,QAAQ,CAAC;QAAEkC,IAAI,EAAE5C,eAAe,CAACqE;MAAiB,CAAC,CAAC;MACpD9B,eAAe,CAAC;QACdC,aAAa,EAAE,EAAE;QACjBC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE;MACV,CAAC,CAAC;MACFtD,SAAS,CAAC2E,OAAO,CAAC,qCAAqC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB/B,eAAe,CACbvB,QAAQ,IAAI;MACVwB,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;IACV,CACF,CAAC;IACDjC,QAAQ,CAAC;MACPkC,IAAI,EAAE5C,eAAe,CAACmE,aAAa;MACnCrB,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyB,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,CACX;;EAED;EACA,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI,GAAGA,CAAC,CAAC;;EAE3D;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,WAAW,GAAG,IAAIhE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,MAAMgE,YAAY,GAAG,IAAIjE,IAAI,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC;;IAE5C,IAAIC,YAAY,KAAK2D,WAAW,EAAE;MAChC;MACA,OAAOR,MAAM,CAACU,KAAK,CAAC,CAAC,EAAED,YAAY,GAAG,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL;MACA,OAAOT,MAAM;IACf;EACF,CAAC;;EAED;EACA,MAAMW,UAAU,GAAG,CAAA9E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,MAAM,KAAI,CAAC;EAC5C,MAAMQ,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACH,UAAU,GAAGpD,YAAY,CAAC;EACvD,MAAMwD,UAAU,GAAG,CAAC1D,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMyD,QAAQ,GAAGD,UAAU,GAAGxD,YAAY;EAC1C,MAAM0D,mBAAmB,GAAG,CAAApF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6E,KAAK,CAACK,UAAU,EAAEC,QAAQ,CAAC,KAAI,EAAE;EAE3E,MAAME,gBAAgB,GAAIC,UAAU,IAAK;IACvC7D,cAAc,CAAC6D,UAAU,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,eAAe,GAAG,CAAC;;IAEzB;IACAD,KAAK,CAACE,IAAI,cACRlG,OAAA,CAACf,UAAU,CAACkH,IAAI;MAEdC,QAAQ,EAAEpE,WAAW,KAAK,CAAE;MAC5BqE,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAAC7D,WAAW,GAAG,CAAC;IAAE,GAF7C,MAAM;MAAAsE,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OAGX,CACH,CAAC;;IAED;IACA,IAAIjB,UAAU,GAAGU,eAAe,IAAIjE,WAAW,GAAG,CAAC,EAAE;MACnDgE,KAAK,CAACE,IAAI,cACRlG,OAAA,CAACf,UAAU,CAACwH,IAAI;QAASJ,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAAC,CAAC,CAAE;QAAAa,QAAA,EAAC;MAE7D,GAFsB,CAAC;QAAAJ,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OAEN,CACnB,CAAC;MACD,IAAIxE,WAAW,GAAG,CAAC,EAAE;QACnBgE,KAAK,CAACE,IAAI,cAAClG,OAAA,CAACf,UAAU,CAAC0H,QAAQ,MAAK,WAAW;UAAAL,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,CAAC;MACrD;IACF;;IAEA;IACA,MAAMI,SAAS,GAAGpB,IAAI,CAACqB,GAAG,CAAC,CAAC,EAAE7E,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAM8E,OAAO,GAAGtB,IAAI,CAACuB,GAAG,CAACxB,UAAU,EAAEvD,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIgF,IAAI,GAAGJ,SAAS,EAAEI,IAAI,IAAIF,OAAO,EAAEE,IAAI,EAAE,EAAE;MAClDhB,KAAK,CAACE,IAAI,cACRlG,OAAA,CAACf,UAAU,CAACwH,IAAI;QAEdQ,MAAM,EAAED,IAAI,KAAKhF,WAAY;QAC7BqE,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAACmB,IAAI,CAAE;QAAAN,QAAA,EAErCM;MAAI,GAJAA,IAAI;QAAAV,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OAKM,CACnB,CAAC;IACH;;IAEA;IACA,IAAIjB,UAAU,GAAGU,eAAe,IAAIjE,WAAW,GAAGuD,UAAU,GAAG,CAAC,EAAE;MAChE,IAAIvD,WAAW,GAAGuD,UAAU,GAAG,CAAC,EAAE;QAChCS,KAAK,CAACE,IAAI,cAAClG,OAAA,CAACf,UAAU,CAAC0H,QAAQ,MAAK,WAAW;UAAAL,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,CAAC;MACrD;MACAR,KAAK,CAACE,IAAI,cACRlG,OAAA,CAACf,UAAU,CAACwH,IAAI;QAEdJ,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAACN,UAAU,CAAE;QAAAmB,QAAA,EAE3CnB;MAAU,GAHNA,UAAU;QAAAe,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OAIA,CACnB,CAAC;IACH;;IAEA;IACAR,KAAK,CAACE,IAAI,cACRlG,OAAA,CAACf,UAAU,CAACiI,IAAI;MAEdd,QAAQ,EAAEpE,WAAW,KAAKuD,UAAW;MACrCc,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAAC7D,WAAW,GAAG,CAAC;IAAE,GAF7C,MAAM;MAAAsE,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OAGX,CACH,CAAC;IAED,OAAOR,KAAK;EACd,CAAC;EAED,MAAMmB,iBAAiB,GAAG3G,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE4G,MAAM,CAC5C,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACC,UAAU,EAC9B,CACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GACtB,CAAAhH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiH,MAAM,CAAEH,CAAC,IAAKA,CAAC,CAAC1E,MAAM,KAAK,SAAS,CAAC,KAAI,EAAE;EAC3D,MAAM8E,mBAAmB,GACvB,CAAAlH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiH,MAAM,CAAEH,CAAC,IAAKA,CAAC,CAAC1E,MAAM,KAAK,SAAS,CAAC,KAAI,EAAE;EAE3D,MAAM+E,eAAe,GAAGH,kBAAkB,CAACJ,MAAM,CAC/C,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACC,UAAU,EAC9B,CACF,CAAC;EACD,MAAMK,gBAAgB,GAAGF,mBAAmB,CAACN,MAAM,CACjD,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACC,UAAU,EAC9B,CACF,CAAC;;EAED;EACA,MAAMM,eAAe,GAAGrC,IAAI,CAACsC,KAAK,CAACH,eAAe,GAAG,IAAI,CAAC;;EAE1D;EACA,MAAMI,iBAAiB,GACrBvC,IAAI,CAACsC,KAAK,CAACH,eAAe,GAAG,IAAI,CAAC,GAAGC,gBAAgB;EAEvD,MAAMI,cAAc,GAClB,CAAAxH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiH,MAAM,CACjBH,CAAC,IAAKA,CAAC,CAAC1E,MAAM,KAAK,WAAW,IAAI0E,CAAC,CAAC1E,MAAM,KAAK,aAClD,CAAC,CAACmC,MAAM,KAAI,CAAC;EACf,MAAMkD,YAAY,GAChB,CAAAzH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiH,MAAM,CAAEH,CAAC,IAAKA,CAAC,CAAC1E,MAAM,KAAK,SAAS,CAAC,CAACmC,MAAM,KAAI,CAAC;EACjE,MAAMmD,WAAW,GACf,CAAA1H,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiH,MAAM,CACjBH,CAAC,IAAKA,CAAC,CAAC1E,MAAM,KAAK,QAAQ,IAAI0E,CAAC,CAAC1E,MAAM,KAAK,YAC/C,CAAC,CAACmC,MAAM,KAAI,CAAC;EAEf,oBACE/E,OAAA;IAAKmI,SAAS,EAAC,gBAAgB;IAAAzB,QAAA,gBAC7B1G,OAAA,CAACb,aAAa;MAAAmH,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBxG,OAAA;MAAA0G,QAAA,EAAI;IAAwC;MAAAJ,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEjDxG,OAAA,CAACtB,GAAG;MAACyJ,SAAS,EAAC,MAAM;MAAAzB,QAAA,gBACnB1G,OAAA,CAACrB,GAAG;QAACyJ,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACT1G,OAAA,CAACjB,IAAI;UAAA2H,QAAA,gBACH1G,OAAA,CAACjB,IAAI,CAACsJ,MAAM;YAACC,EAAE,EAAC,IAAI;YAAA5B,QAAA,EAAC;UAAa;YAAAJ,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChDxG,OAAA,CAACjB,IAAI,CAACwJ,IAAI;YAAA7B,QAAA,eACR1G,OAAA,CAACnB,IAAI;cAAA6H,QAAA,eACH1G,OAAA,CAACtB,GAAG;gBAACyJ,SAAS,EAAC,iBAAiB;gBAAAzB,QAAA,gBAC9B1G,OAAA,CAACrB,GAAG;kBAAA+H,QAAA,eACF1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;oBAACL,SAAS,EAAC,MAAM;oBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;sBAAA/B,QAAA,EAAC;oBAAK;sBAAAJ,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC9BxG,OAAA,CAACnB,IAAI,CAAC6J,MAAM;sBACVrF,KAAK,EAAEhC,aAAc;sBACrBsH,QAAQ,EAAGxF,CAAC,IAAK;wBACf,MAAMyF,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAAC3F,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC;wBAChD/B,gBAAgB,CAACsH,QAAQ,CAAC;sBAC5B,CAAE;sBAAAlC,QAAA,EAEDxB,kBAAkB,CAAC,CAAC,CAAC6D,GAAG,CAAC,CAACjG,KAAK,EAAEkG,KAAK,kBACrChJ,OAAA;wBAAoBqD,KAAK,EAAE2F,KAAK,GAAG,CAAE;wBAAAtC,QAAA,EAClC5D;sBAAK,GADKkG,KAAK;wBAAA1C,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAEV,CACT;oBAAC;sBAAAF,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAAA;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAF,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxG,OAAA,CAACrB,GAAG;kBAAA+H,QAAA,eACF1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;oBAACL,SAAS,EAAC,MAAM;oBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;sBAAA/B,QAAA,EAAC;oBAAG;sBAAAJ,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC5BxG,OAAA,CAACnB,IAAI,CAAC6J,MAAM;sBACVrF,KAAK,EAAE7B,YAAa;sBACpBmH,QAAQ,EAAGxF,CAAC,IAAK;wBACf,MAAM8F,OAAO,GAAGJ,MAAM,CAACC,QAAQ,CAAC3F,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC;wBAC/C5B,eAAe,CAACwH,OAAO,CAAC;;wBAExB;wBACA,MAAM9D,WAAW,GAAG,IAAIhE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;wBAC5C,MAAMgE,YAAY,GAAG,IAAIjE,IAAI,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC;wBAC1C,IACE0H,OAAO,KAAK9D,WAAW,IACvB9D,aAAa,GAAG+D,YAAY,EAC5B;0BACA9D,gBAAgB,CAAC8D,YAAY,CAAC;wBAChC;sBACF,CAAE;sBAAAsB,QAAA,EAED9B,KAAK,CAACmE,GAAG,CAAEhG,IAAI,iBACd/C,OAAA;wBAAmBqD,KAAK,EAAEN,IAAK;wBAAA2D,QAAA,EAC5B3D;sBAAI,GADMA,IAAI;wBAAAuD,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAET,CACT;oBAAC;sBAAAF,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAAA;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAF,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxG,OAAA,CAACrB,GAAG;kBAAA+H,QAAA,eACF1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;oBAACL,SAAS,EAAC,MAAM;oBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;sBAAA/B,QAAA,EAAC;oBAAU;sBAAAJ,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACnCxG,OAAA,CAACnB,IAAI,CAAC6J,MAAM;sBACVrF,KAAK,EAAE3B,cAAe;sBACtBiH,QAAQ,EAAGxF,CAAC,IAAKxB,iBAAiB,CAACwB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;sBAAAqD,QAAA,gBAEnD1G,OAAA;wBAAQqD,KAAK,EAAC,KAAK;wBAAAqD,QAAA,EAAC;sBAAM;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnCxG,OAAA;wBAAQqD,KAAK,EAAC,WAAW;wBAAAqD,QAAA,EAAC;sBAAS;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5CxG,OAAA;wBAAQqD,KAAK,EAAC,aAAa;wBAAAqD,QAAA,EAAC;sBAAW;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAChDxG,OAAA;wBAAQqD,KAAK,EAAC,YAAY;wBAAAqD,QAAA,EAAC;sBAAU;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC9CxG,OAAA;wBAAQqD,KAAK,EAAC,QAAQ;wBAAAqD,QAAA,EAAC;sBAAM;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACtCxG,OAAA;wBAAQqD,KAAK,EAAC,WAAW;wBAAAqD,QAAA,EAAC;sBAAS;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5CxG,OAAA;wBAAQqD,KAAK,EAAC,UAAU;wBAAAqD,QAAA,EAAC;sBAAQ;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC1CxG,OAAA;wBAAQqD,KAAK,EAAC,SAAS;wBAAAqD,QAAA,EAAC;sBAAO;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAF,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAF,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxG,OAAA,CAACrB,GAAG;kBAAA+H,QAAA,eACF1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;oBAACL,SAAS,EAAC,MAAM;oBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;sBAAA/B,QAAA,EAAC;oBAAS;sBAAAJ,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClCxG,OAAA,CAACnB,IAAI,CAAC6J,MAAM;sBACVrF,KAAK,EAAEzB,YAAa;sBACpB+G,QAAQ,EAAGxF,CAAC,IAAKtB,eAAe,CAACsB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;sBAAAqD,QAAA,gBAEjD1G,OAAA;wBAAQqD,KAAK,EAAC,MAAM;wBAAAqD,QAAA,EAAC;sBAAQ;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACtCxG,OAAA;wBAAQqD,KAAK,EAAC,KAAK;wBAAAqD,QAAA,EAAC;sBAAO;wBAAAJ,QAAA,EAAAlI,YAAA;wBAAAmI,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAF,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAF,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxG,OAAA,CAACrB,GAAG;QAACyJ,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACT1G,OAAA,CAACjB,IAAI;UAAA2H,QAAA,gBACH1G,OAAA,CAACjB,IAAI,CAACsJ,MAAM;YAACC,EAAE,EAAC,IAAI;YAAA5B,QAAA,EAAC;UAAkB;YAAAJ,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrDxG,OAAA,CAACjB,IAAI,CAACwJ,IAAI;YAAA7B,QAAA,gBACR1G,OAAA;cAAKmI,SAAS,EAAC,qCAAqC;cAAAzB,QAAA,gBAClD1G,OAAA;gBAAA0G,QAAA,EAAM;cAA0B;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCxG,OAAA;gBAAA0G,QAAA,EAAShH,KAAK,CAAC0E,cAAc,CAAC+C,iBAAiB;cAAC;gBAAAb,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNxG,OAAA;cAAKmI,SAAS,EAAC,qCAAqC;cAAAzB,QAAA,gBAClD1G,OAAA;gBAAA0G,QAAA,EAAM;cAAsB;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnCxG,OAAA;gBAAQmI,SAAS,EAAC,aAAa;gBAAAzB,QAAA,EAC5BhH,KAAK,CAAC0E,cAAc,CAACyD,eAAe;cAAC;gBAAAvB,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxG,OAAA;cAAKmI,SAAS,EAAC,qCAAqC;cAAAzB,QAAA,gBAClD1G,OAAA;gBAAA0G,QAAA,EAAM;cAA+B;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CxG,OAAA;gBAAQmI,SAAS,EAAC,cAAc;gBAAAzB,QAAA,EAC7BhH,KAAK,CAAC0E,cAAc,CAAC2D,iBAAiB;cAAC;gBAAAzB,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxG,OAAA;cAAKmI,SAAS,EAAC,qCAAqC;cAAAzB,QAAA,gBAClD1G,OAAA;gBAAA0G,QAAA,EAAM;cAAiD;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9DxG,OAAA;gBAAA0G,QAAA,GACGsB,cAAc,EAAC,GAAC,EAACC,YAAY,EAAC,GAAC,EAACC,WAAW;cAAA;gBAAA5B,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAF,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxG,OAAA,CAACjB,IAAI;MAACoJ,SAAS,EAAC,MAAM;MAAAzB,QAAA,gBACpB1G,OAAA,CAACjB,IAAI,CAACsJ,MAAM;QAACC,EAAE,EAAC,IAAI;QAAA5B,QAAA,eAClB1G,OAAA;UAAKmI,SAAS,EAAC,mDAAmD;UAAAzB,QAAA,gBAChE1G,OAAA;YAAA0G,QAAA,GAAM,iCACqB,EAACxB,kBAAkB,CAAC,CAAC,CAAC7D,aAAa,GAAG,CAAC,CAAC,EAAE,GAAG,EAAC,IACrE,EAACG,YAAY;UAAA;YAAA8E,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACPxG,OAAA;YAAMmI,SAAS,EAAC,YAAY;YAAAzB,QAAA,GAAC,qBAClB,EAAChB,UAAU,GAAG,CAAC,EAAC,GAAC,EAACF,IAAI,CAACuB,GAAG,CAACpB,QAAQ,EAAEL,UAAU,CAAC,EAAC,WAAI,EAAC,GAAG,EACjEA,UAAU,EAAC,oBACd;UAAA;YAAAgB,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdxG,OAAA,CAACjB,IAAI,CAACwJ,IAAI;QAAA7B,QAAA,gBACR1G,OAAA,CAACpB,KAAK;UAACsK,UAAU;UAACC,OAAO;UAACC,KAAK;UAAA1C,QAAA,gBAC7B1G,OAAA;YAAA0G,QAAA,eACE1G,OAAA;cAAA0G,QAAA,gBACE1G,OAAA;gBAAA0G,QAAA,EAAI;cAAC;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACVxG,OAAA;gBAAA0G,QAAA,EAAI;cAAQ;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAS;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAgB;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAQ;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAe;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAU;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRxG,OAAA;YAAA0G,QAAA,EACGd,mBAAmB,IAAIA,mBAAmB,CAACb,MAAM,GAAG,CAAC,GACpDa,mBAAmB,CAACmD,GAAG,CAAC,CAACM,WAAW,EAAEL,KAAK,KAAK;cAC9C,IAAIK,WAAW,CAACzG,MAAM,KAAK,SAAS,EAAE;gBACpC,oBACE5C,OAAA;kBAEEqG,OAAO,EAAEA,CAAA,KAAM;oBACb/F,YAAY,CAAC,IAAI,CAAC;oBAClByB,oBAAoB,CAACsH,WAAW,CAAC;kBACnC,CAAE;kBACF7E,KAAK,EAAE;oBAAE8E,MAAM,EAAE;kBAAU,CAAE;kBAAA5C,QAAA,gBAE7B1G,OAAA;oBAAA0G,QAAA,EAAKhB,UAAU,GAAGsD,KAAK,GAAG;kBAAC;oBAAA1C,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjCxG,OAAA;oBAAA0G,QAAA,EAAKhH,KAAK,CAAC6J,OAAO,CAACF,WAAW,CAACG,SAAS,EAAE,EAAE;kBAAC;oBAAAlD,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDxG,OAAA;oBAAA0G,QAAA,eACE1G,OAAA;sBAAA0G,QAAA,EACG2C,WAAW,CAACI,KAAK,IAChBJ,WAAW,CAACI,KAAK,CAAC1E,MAAM,GAAG,CAAC,IAC5BsE,WAAW,CAACI,KAAK,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,GAAG,KAAK;wBAAA,IAAAC,aAAA;wBACtClG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE+F,OAAO,CAAC;wBAC7B,oBACE1J,OAAA;0BAAA0G,QAAA,IAAAkD,aAAA,GACGF,OAAO,CAACG,IAAI,cAAAD,aAAA,uBAAZA,aAAA,CAAcxG,IAAI,EAAC,KAAG,EAACsG,OAAO,CAACI,QAAQ,EAAE,GAAG,EAAC,OAEhD;wBAAA,GAHUH,GAAG;0BAAArD,QAAA,EAAAlI,YAAA;0BAAAmI,UAAA;0BAAAC,YAAA;wBAAA,OAGR,CAAC;sBAEV,CAAC;oBAAC;sBAAAF,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLxG,OAAA;oBAAA0G,QAAA,EACGhH,KAAK,CAAC0E,cAAc,CAACiF,WAAW,CAAC9B,UAAU,IAAI,CAAC;kBAAC;oBAAAjB,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACLxG,OAAA;oBAAImI,SAAS,EAAC,aAAa;oBAAAzB,QAAA,EACxBhH,KAAK,CAAC0E,cAAc,CACnBoB,IAAI,CAACsC,KAAK,CAACuB,WAAW,CAAC9B,UAAU,GAAG,IAAI,IAAI,CAAC,CAC/C;kBAAC;oBAAAjB,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLxG,OAAA;oBAAImI,SAAS,EAAC,cAAc;oBAAAzB,QAAA,EACzBhH,KAAK,CAAC0E,cAAc,CACnBoB,IAAI,CAACsC,KAAK,CAACuB,WAAW,CAAC9B,UAAU,GAAG,IAAI,IAAI,CAAC,CAC/C;kBAAC;oBAAAjB,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLxG,OAAA;oBAAA0G,QAAA,eACE1G,OAAA;sBACEmI,SAAS,EAAE,SACTkB,WAAW,CAACzG,MAAM,KAAK,WAAW,GAC9B,cAAc,GACdyG,WAAW,CAACzG,MAAM,KAAK,SAAS,GAChC,YAAY,GACZyG,WAAW,CAACzG,MAAM,KAAK,WAAW,IAClCyG,WAAW,CAACzG,MAAM,KAAK,UAAU,GACjC,WAAW,GACXyG,WAAW,CAACzG,MAAM,KAAK,QAAQ,GAC/B,YAAY,GACZyG,WAAW,CAACzG,MAAM,KAAK,aAAa,GACpC,SAAS,GACT,YAAY,EACf;sBACH4B,KAAK,EAAE;wBACLuF,KAAK,EAAE,OAAO;wBACdC,MAAM,EAAE,MAAM;wBACdC,UAAU,EAAE,MAAM;wBAClBC,aAAa,EAAE;sBACjB,CAAE;sBAAAxD,QAAA,EAED2C,WAAW,CAACzG;oBAAM;sBAAA0D,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,GA9DA6C,WAAW,CAACc,GAAG;kBAAA7D,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OA+DlB,CAAC;cAET,CAAC,MAAM;gBACL,oBACExG,OAAA;kBAEEqG,OAAO,EAAEA,CAAA,KAAM;oBACb/F,YAAY,CAAC,IAAI,CAAC;oBAClByB,oBAAoB,CAACsH,WAAW,CAAC;kBACnC,CAAE;kBACF7E,KAAK,EAAE;oBAAE8E,MAAM,EAAE;kBAAU,CAAE;kBAAA5C,QAAA,gBAE7B1G,OAAA;oBAAA0G,QAAA,EAAKhB,UAAU,GAAGsD,KAAK,GAAG;kBAAC;oBAAA1C,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjCxG,OAAA;oBAAA0G,QAAA,EAAKhH,KAAK,CAAC6J,OAAO,CAACF,WAAW,CAACG,SAAS,EAAE,EAAE;kBAAC;oBAAAlD,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDxG,OAAA;oBAAA0G,QAAA,eACE1G,OAAA;sBAAA0G,QAAA,EACG2C,WAAW,CAACI,KAAK,IAChBJ,WAAW,CAACI,KAAK,CAAC1E,MAAM,GAAG,CAAC,IAC5BsE,WAAW,CAACI,KAAK,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,GAAG,KAAK;wBAAA,IAAAS,cAAA;wBACtC1G,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE+F,OAAO,CAAC;wBAC7B,oBACE1J,OAAA;0BAAA0G,QAAA,IAAA0D,cAAA,GACGV,OAAO,CAACG,IAAI,cAAAO,cAAA,uBAAZA,cAAA,CAAchH,IAAI,EAAC,KAAG,EAACsG,OAAO,CAACI,QAAQ,EAAE,GAAG,EAAC,OAEhD;wBAAA,GAHUH,GAAG;0BAAArD,QAAA,EAAAlI,YAAA;0BAAAmI,UAAA;0BAAAC,YAAA;wBAAA,OAGR,CAAC;sBAEV,CAAC;oBAAC;sBAAAF,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLxG,OAAA;oBAAA0G,QAAA,EACGhH,KAAK,CAAC0E,cAAc,CAACiF,WAAW,CAAC9B,UAAU,IAAI,CAAC;kBAAC;oBAAAjB,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACLxG,OAAA;oBAAImI,SAAS,EAAC,aAAa;oBAAAzB,QAAA,EAAC;kBAAC;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClCxG,OAAA;oBAAImI,SAAS,EAAC,cAAc;oBAAAzB,QAAA,EACzBhH,KAAK,CAAC0E,cAAc,CACnBoB,IAAI,CAACsC,KAAK,CAACuB,WAAW,CAAC9B,UAAU,GAAG,CAAC,IAAI,CAAC,CAC5C;kBAAC;oBAAAjB,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLxG,OAAA;oBAAA0G,QAAA,eACE1G,OAAA;sBACEmI,SAAS,EAAE,SACTkB,WAAW,CAACzG,MAAM,KAAK,WAAW,GAC9B,cAAc,GACdyG,WAAW,CAACzG,MAAM,KAAK,SAAS,GAChC,YAAY,GACZyG,WAAW,CAACzG,MAAM,KAAK,WAAW,IAClCyG,WAAW,CAACzG,MAAM,KAAK,UAAU,GACjC,WAAW,GACXyG,WAAW,CAACzG,MAAM,KAAK,QAAQ,GAC/B,YAAY,GACZyG,WAAW,CAACzG,MAAM,KAAK,aAAa,GACpC,SAAS,GACT,YAAY,EACf;sBACH4B,KAAK,EAAE;wBACLuF,KAAK,EAAE,OAAO;wBACdC,MAAM,EAAE,MAAM;wBACdC,UAAU,EAAE,MAAM;wBAClBC,aAAa,EAAE;sBACjB,CAAE;sBAAAxD,QAAA,EAED2C,WAAW,CAACzG;oBAAM;sBAAA0D,QAAA,EAAAlI,YAAA;sBAAAmI,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAF,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,GA1DA6C,WAAW,CAACc,GAAG;kBAAA7D,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OA2DlB,CAAC;cAET;YACF,CAAC,CAAC,gBAEFxG,OAAA;cAAA0G,QAAA,eACE1G,OAAA;gBAAIqK,OAAO,EAAC,GAAG;gBAAClC,SAAS,EAAC,aAAa;gBAAAzB,QAAA,EAAC;cAExC;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACH;UACL;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACRxG,OAAA;YAAA0G,QAAA,eACE1G,OAAA;cAAImI,SAAS,EAAC,iBAAiB;cAAAzB,QAAA,gBAC7B1G,OAAA;gBAAIqK,OAAO,EAAC,GAAG;gBAAClC,SAAS,EAAC,UAAU;gBAAAzB,QAAA,eAClC1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAK;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACLxG,OAAA;gBAAA0G,QAAA,eACE1G,OAAA;kBAAA0G,QAAA,EAAShH,KAAK,CAAC0E,cAAc,CAAC+C,iBAAiB;gBAAC;kBAAAb,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACLxG,OAAA;gBAAA0G,QAAA,eACE1G,OAAA;kBAAA0G,QAAA,EAAShH,KAAK,CAAC0E,cAAc,CAACyD,eAAe;gBAAC;kBAAAvB,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACLxG,OAAA;gBAAA0G,QAAA,eACE1G,OAAA;kBAAA0G,QAAA,EAAShH,KAAK,CAAC0E,cAAc,CAAC2D,iBAAiB;gBAAC;kBAAAzB,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACLxG,OAAA;gBAAAsG,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGPjB,UAAU,GAAG,CAAC,iBACbvF,OAAA;UAAKmI,SAAS,EAAC,oCAAoC;UAAAzB,QAAA,eACjD1G,OAAA,CAACf,UAAU;YAAAyH,QAAA,EAAEX,qBAAqB,CAAC;UAAC;YAAAO,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OAC/C,CACN;MAAA;QAAAF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAF,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEPxG,OAAA,CAACtB,GAAG;MAACyJ,SAAS,EAAC,MAAM;MAAAzB,QAAA,eACnB1G,OAAA,CAACrB,GAAG;QAACyJ,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACT1G,OAAA,CAACjB,IAAI;UAAA2H,QAAA,gBACH1G,OAAA,CAACjB,IAAI,CAACsJ,MAAM;YAACC,EAAE,EAAC,IAAI;YAAA5B,QAAA,EAAC;UAAmB;YAAAJ,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtDxG,OAAA,CAACjB,IAAI,CAACwJ,IAAI;YAAA7B,QAAA,eACR1G,OAAA,CAACnB,IAAI;cAAA6H,QAAA,eACH1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;gBAACL,SAAS,EAAC,MAAM;gBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;kBAAA/B,QAAA,EAAC;gBAAG;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5BxG,OAAA,CAACnB,IAAI,CAAC6J,MAAM;kBACVrF,KAAK,EAAEpC,iBAAkB;kBACzB0H,QAAQ,EAAGxF,CAAC,IACVjC,oBAAoB,CAAC2H,MAAM,CAACC,QAAQ,CAAC3F,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,CACrD;kBAAAqD,QAAA,EAEA9B,KAAK,CAACmE,GAAG,CAAEhG,IAAI,iBACd/C,OAAA;oBAAmBqD,KAAK,EAAEN,IAAK;oBAAA2D,QAAA,EAC5B3D;kBAAI,GADMA,IAAI;oBAAAuD,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAET,CACT;gBAAC;kBAAAF,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAF,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxG,OAAA,CAACjB,IAAI;MAACoJ,SAAS,EAAC,MAAM;MAAAzB,QAAA,gBACpB1G,OAAA,CAACjB,IAAI,CAACsJ,MAAM;QAACC,EAAE,EAAC,IAAI;QAAA5B,QAAA,EAAC;MAA6B;QAAAJ,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChExG,OAAA,CAACjB,IAAI,CAACwJ,IAAI;QAAA7B,QAAA,GACP,CAAC5F,WAAW,iBACXd,OAAA,CAAChB,KAAK;UAACsL,OAAO,EAAC,SAAS;UAAA5D,QAAA,EAAC;QAGzB;UAAAJ,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,EAEAzF,QAAQ,gBACPf,OAAA,CAACnB,IAAI;UAAC0L,QAAQ,EAAEhH,YAAa;UAAAmD,QAAA,gBAC3B1G,OAAA,CAACtB,GAAG;YAAAgI,QAAA,gBACF1G,OAAA,CAACrB,GAAG;cAACyJ,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACT1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;gBAACL,SAAS,EAAC,MAAM;gBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;kBAAA/B,QAAA,EAAC;gBAAY;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCxG,OAAA,CAACnB,IAAI,CAAC2L,OAAO;kBACX/H,IAAI,EAAC,MAAM;kBACXW,IAAI,EAAC,eAAe;kBACpBC,KAAK,EAAElB,YAAY,CAACE,aAAc;kBAClCsG,QAAQ,EAAEzF,oBAAqB;kBAC/BuH,WAAW,EAAC,uDAA2B;kBACvCC,QAAQ;gBAAA;kBAAApE,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxG,OAAA,CAACrB,GAAG;cAACyJ,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACT1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;gBAACL,SAAS,EAAC,MAAM;gBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;kBAAA/B,QAAA,EAAC;gBAAa;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCxG,OAAA,CAACnB,IAAI,CAAC2L,OAAO;kBACX/H,IAAI,EAAC,MAAM;kBACXW,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAElB,YAAY,CAACG,WAAY;kBAChCqG,QAAQ,EAAEzF,oBAAqB;kBAC/BuH,WAAW,EAAC,6CAAwB;kBACpCC,QAAQ;gBAAA;kBAAApE,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxG,OAAA,CAACtB,GAAG;YAAAgI,QAAA,gBACF1G,OAAA,CAACrB,GAAG;cAACyJ,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACT1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;gBAACL,SAAS,EAAC,MAAM;gBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;kBAAA/B,QAAA,EAAC;gBAAa;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCxG,OAAA,CAACnB,IAAI,CAAC6J,MAAM;kBACVtF,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAElB,YAAY,CAACI,QAAS;kBAC7BoG,QAAQ,EAAEzF,oBAAqB;kBAC/BwH,QAAQ;kBAAAhE,QAAA,gBAER1G,OAAA;oBAAQqD,KAAK,EAAC,EAAE;oBAAAqD,QAAA,EAAC;kBAAc;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCxG,OAAA;oBAAQqD,KAAK,EAAC,SAAS;oBAAAqD,QAAA,EAAC;kBAAO;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCxG,OAAA;oBAAQqD,KAAK,EAAC,aAAa;oBAAAqD,QAAA,EAAC;kBAAW;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChDxG,OAAA;oBAAQqD,KAAK,EAAC,aAAa;oBAAAqD,QAAA,EAAC;kBAAW;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChDxG,OAAA;oBAAQqD,KAAK,EAAC,MAAM;oBAAAqD,QAAA,EAAC;kBAAI;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCxG,OAAA;oBAAQqD,KAAK,EAAC,QAAQ;oBAAAqD,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxG,OAAA;oBAAQqD,KAAK,EAAC,QAAQ;oBAAAqD,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxG,OAAA;oBAAQqD,KAAK,EAAC,QAAQ;oBAAAqD,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAlI,YAAA;oBAAAmI,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAF,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxG,OAAA,CAACrB,GAAG;cAACyJ,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACT1G,OAAA,CAACnB,IAAI,CAAC2J,KAAK;gBAACL,SAAS,EAAC,MAAM;gBAAAzB,QAAA,gBAC1B1G,OAAA,CAACnB,IAAI,CAAC4J,KAAK;kBAAA/B,QAAA,EAAC;gBAAS;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClCxG,OAAA,CAACnB,IAAI,CAAC2L,OAAO;kBACX/H,IAAI,EAAC,MAAM;kBACXW,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAElB,YAAY,CAACK,MAAO;kBAC3BmG,QAAQ,EAAEzF,oBAAqB;kBAC/BuH,WAAW,EAAC;gBAA0B;kBAAAnE,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxG,OAAA;YAAA0G,QAAA,gBACE1G,OAAA,CAAClB,MAAM;cAACwL,OAAO,EAAC,SAAS;cAAC7H,IAAI,EAAC,QAAQ;cAAAiE,QAAA,EACpC5F,WAAW,GACR,8BAA8B,GAC9B;YAAyB;cAAAwF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACR1F,WAAW,iBACVd,OAAA,CAAClB,MAAM;cACLwL,OAAO,EAAC,WAAW;cACnBnC,SAAS,EAAC,MAAM;cAChB1F,IAAI,EAAC,QAAQ;cACb4D,OAAO,EAAElC,YAAa;cAAAuC,QAAA,EACvB;YAED;cAAAJ,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,gBAEPxG,OAAA,CAAAE,SAAA;UAAAwG,QAAA,EACG7F,QAAQ,iBACPb,OAAA;YAAA0G,QAAA,gBACE1G,OAAA,CAACtB,GAAG;cAACyJ,SAAS,EAAC,MAAM;cAAAzB,QAAA,gBACnB1G,OAAA,CAACrB,GAAG;gBAACyJ,EAAE,EAAE,CAAE;gBAAA1B,QAAA,gBACT1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAa;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3F,QAAQ,CAACwB,aAAa;cAAA;gBAAAiE,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNxG,OAAA,CAACrB,GAAG;gBAACyJ,EAAE,EAAE,CAAE;gBAAA1B,QAAA,gBACT1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAc;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3F,QAAQ,CAACyB,WAAW;cAAA;gBAAAgE,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxG,OAAA,CAACtB,GAAG;cAACyJ,SAAS,EAAC,MAAM;cAAAzB,QAAA,gBACnB1G,OAAA,CAACrB,GAAG;gBAACyJ,EAAE,EAAE,CAAE;gBAAA1B,QAAA,gBACT1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAc;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3F,QAAQ,CAAC0B,QAAQ;cAAA;gBAAA+D,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNxG,OAAA,CAACrB,GAAG;gBAACyJ,EAAE,EAAE,CAAE;gBAAA1B,QAAA,gBACT1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAU;kBAAAJ,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3F,QAAQ,CAAC2B,MAAM,IAAI,KAAK;cAAA;gBAAA8D,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxG,OAAA;cAAA0G,QAAA,gBACE1G,OAAA,CAAClB,MAAM;gBAACwL,OAAO,EAAC,iBAAiB;gBAACjE,OAAO,EAAEtC,UAAW;gBAAA2C,QAAA,EAAC;cAEvD;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA,CAAClB,MAAM;gBACLwL,OAAO,EAAC,gBAAgB;gBACxBnC,SAAS,EAAC,MAAM;gBAChB9B,OAAO,EAAEpC,YAAa;gBAAAyC,QAAA,EACvB;cAED;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACH;QACN,gBACD,CACH;MAAA;QAAAF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAF,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEPxG,OAAA,CAACjB,IAAI;MAACoJ,SAAS,EAAC,MAAM;MAAAzB,QAAA,gBACpB1G,OAAA,CAACjB,IAAI,CAACsJ,MAAM;QAACC,EAAE,EAAC,IAAI;QAAA5B,QAAA,GAAC,sCACS,EAACzF,iBAAiB;MAAA;QAAAqF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACdxG,OAAA,CAACjB,IAAI,CAACwJ,IAAI;QAAA7B,QAAA,eACR1G,OAAA,CAACpB,KAAK;UAACsK,UAAU;UAACC,OAAO;UAACC,KAAK;UAAA1C,QAAA,gBAC7B1G,OAAA;YAAA0G,QAAA,eACE1G,OAAA;cAAImI,SAAS,EAAC,aAAa;cAAAzB,QAAA,gBACzB1G,OAAA;gBAAA0G,QAAA,EAAI;cAAC;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACVxG,OAAA;gBAAA0G,QAAA,EAAI;cAAS;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAc;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAQ;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAe;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAU;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBxG,OAAA;gBAAA0G,QAAA,EAAI;cAAa;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRxG,OAAA;YAAA0G,QAAA,EACG/F,IAAI,IAAIA,IAAI,CAACoE,MAAM,GAAG,CAAC,GACtBpE,IAAI,CAACoI,GAAG,CAAC,CAAC4B,OAAO,EAAE3B,KAAK,kBACtBhJ,OAAA;cAAsBmI,SAAS,EAAC,aAAa;cAAAzB,QAAA,gBAC3C1G,OAAA;gBAAA0G,QAAA,EAAKsC,KAAK,GAAG;cAAC;gBAAA1C,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBxG,OAAA;gBAAA0G,QAAA,GACGiE,OAAO,CAAC7H,KAAK,EAAC,GAAC,EAAC6H,OAAO,CAAC5H,IAAI;cAAA;gBAAAuD,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACLxG,OAAA;gBAAA0G,QAAA,EAAKtC,cAAc,CAACuG,OAAO,CAACtG,MAAM;cAAC;gBAAAiC,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzCxG,OAAA;gBAAImI,SAAS,EAAC,aAAa;gBAAAzB,QAAA,EACxBtC,cAAc,CAACuG,OAAO,CAACtG,MAAM,GAAG,IAAI;cAAC;gBAAAiC,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACLxG,OAAA;gBAAImI,SAAS,EAAC,cAAc;gBAAAzB,QAAA,EACzBtC,cAAc,CAACuG,OAAO,CAACtG,MAAM,GAAG,IAAI;cAAC;gBAAAiC,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACLxG,OAAA;gBAAA0G,QAAA,eACE1G,OAAA;kBACEmI,SAAS,EAAE,SACTwC,OAAO,CAAC/H,MAAM,KAAK,WAAW,GAC1B,YAAY,GACZ+H,OAAO,CAAC/H,MAAM,KAAK,SAAS,GAC5B,YAAY,GACZ+H,OAAO,CAAC/H,MAAM,KAAK,WAAW,GAC9B,WAAW,GACX,SAAS,EACZ;kBAAA8D,QAAA,EAEFiE,OAAO,CAAC/H;gBAAM;kBAAA0D,QAAA,EAAAlI,YAAA;kBAAAmI,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAF,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLxG,OAAA;gBAAA0G,QAAA,EACGiE,OAAO,CAACC,WAAW,GAChBlL,KAAK,CAAC6J,OAAO,CAACoB,OAAO,CAACC,WAAW,CAAC,GAClC;cAAE;gBAAAtE,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA/BEmE,OAAO,CAACR,GAAG;cAAA7D,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OAgChB,CACL,CAAC,gBAEFxG,OAAA;cAAA0G,QAAA,eACE1G,OAAA;gBAAIqK,OAAO,EAAC,GAAG;gBAAClC,SAAS,EAAC,aAAa;gBAAAzB,QAAA,EAAC;cAExC;gBAAAJ,QAAA,EAAAlI,YAAA;gBAAAmI,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAF,QAAA,EAAAlI,YAAA;cAAAmI,UAAA;cAAAC,YAAA;YAAA,OACH;UACL;YAAAF,QAAA,EAAAlI,YAAA;YAAAmI,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAF,QAAA,EAAAlI,YAAA;UAAAmI,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAF,QAAA,EAAAlI,YAAA;QAAAmI,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAF,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEPxG,OAAA,CAACX,iBAAiB;MAChBwL,IAAI,EAAExK,SAAU;MAChByK,MAAM,EAAEA,CAAA,KAAMxK,YAAY,CAAC,KAAK,CAAE;MAClCyK,WAAW,EAAEA,CAAA,KAAMzK,YAAY,CAAC,KAAK,CAAE;MACvCwB,iBAAiB,EAAEA;IAAkB;MAAAwE,QAAA,EAAAlI,YAAA;MAAAmI,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAF,QAAA,EAAAlI,YAAA;IAAAmI,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnI,EAAA,CA/0BI8B,WAAW;EAAA,QACEf,WAAW,EAEXO,cAAc,EAGNC,cAAc,EACtBA,cAAc,EACaA,cAAc;AAAA;AAAAoL,EAAA,GARtD7K,WAAW;AAi1BjB,eAAeA,WAAW;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}