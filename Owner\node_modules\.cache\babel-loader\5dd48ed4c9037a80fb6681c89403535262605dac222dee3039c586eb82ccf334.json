{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  fetch_favorite_hotel: (ids, paramsQuery) => {\n    const params = {\n      address: \"\",\n      district: \"\",\n      star: \"0\"\n    };\n    if (paramsQuery.selectedCity != \"\" && paramsQuery.selectedCity) {\n      params.address = paramsQuery.selectedCity;\n    }\n    if (paramsQuery.selectedDistrict != \"\" && paramsQuery.selectedDistrict) {\n      params.district = paramsQuery.selectedDistrict;\n    }\n    if (paramsQuery.selectedStar != \"0\") {\n      params.star = paramsQuery.selectedStar;\n    }\n    return api.post(ApiConstants.FETCH_FAVORITE_HOTELS, {\n      ids,\n      params\n    });\n  },\n  get_all_hotels: () => {\n    return api.get(ApiConstants.FETCH_ALL_HOTEL);\n  },\n  get_top3_hotels: () => {\n    return api.get(ApiConstants.FETCH_TOP3_HOTEL);\n  },\n  fetchOwnerHotel: () => {\n    return api.get(ApiConstants.FETCH_OWNER_HOTEL);\n  },\n  updateHotel: (hotelId, updateData) => {\n    return api.put(`${ApiConstants.UPDATE_HOTEL}/${hotelId}`, updateData);\n  },\n  updateHotelServiceStatus: (hotelId, statusActive, serviceId) => {\n    const url = ApiConstants.UPDATE_HOTEL_SERVICE_STATUS.replace(\":hotelId\", hotelId);\n    return api.put(url, {\n      statusActive: statusActive.statusActive,\n      serviceId\n    });\n  },\n  createHotelService: serviceData => {\n    return api.post(ApiConstants.CREATE_HOTEL_SERVICE, serviceData);\n  },\n  changeStatusHotel: (hotelId, ownerStatus) => {\n    return api.put(`${ApiConstants.CHANGE_STATUS_HOTEL}/${hotelId}`, {\n      ownerStatus\n    });\n  },\n  createHotel: (createHotel, createRoomList, createService) => {\n    return api.post(ApiConstants.CREATE_HOTEL, {\n      createHotel,\n      createRoomList,\n      createService\n    });\n  },\n  // Hotel Image APIs\n  uploadHotelImages: formData => {\n    return api.post(ApiConstants.UPLOAD_HOTEL_IMAGE, formData, {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\"\n      }\n    });\n  },\n  deleteHotelImages: imageIds => {\n    return api.delete(ApiConstants.DELETE_HOTEL_IMAGE, {\n      data: {\n        imageIds\n      }\n    });\n  },\n  create_booking_offline: params => {\n    return api.post(ApiConstants.CREATE_BOOKING_OFFLINE, {\n      params\n    });\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetch_favorite_hotel", "ids", "params<PERSON><PERSON>y", "params", "address", "district", "star", "selectedCity", "selectedDistrict", "selectedStar", "post", "FETCH_FAVORITE_HOTELS", "get_all_hotels", "get", "FETCH_ALL_HOTEL", "get_top3_hotels", "FETCH_TOP3_HOTEL", "fetchOwnerHotel", "FETCH_OWNER_HOTEL", "updateHotel", "hotelId", "updateData", "put", "UPDATE_HOTEL", "updateHotelServiceStatus", "statusActive", "serviceId", "url", "UPDATE_HOTEL_SERVICE_STATUS", "replace", "createHotelService", "serviceData", "CREATE_HOTEL_SERVICE", "changeStatusHotel", "ownerStatus", "CHANGE_STATUS_HOTEL", "createHotel", "createRoomList", "createService", "CREATE_HOTEL", "uploadHotelImages", "formData", "UPLOAD_HOTEL_IMAGE", "headers", "deleteHotelImages", "imageIds", "delete", "DELETE_HOTEL_IMAGE", "data", "create_booking_offline", "CREATE_BOOKING_OFFLINE"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/hotel/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  fetch_favorite_hotel: (ids, paramsQuery) => {\r\n    const params = {\r\n      address: \"\",\r\n      district: \"\",\r\n      star: \"0\",\r\n    };\r\n    if (paramsQuery.selectedCity != \"\" && paramsQuery.selectedCity) {\r\n      params.address = paramsQuery.selectedCity;\r\n    }\r\n    if (paramsQuery.selectedDistrict != \"\" && paramsQuery.selectedDistrict) {\r\n      params.district = paramsQuery.selectedDistrict;\r\n    }\r\n    if (paramsQuery.selectedStar != \"0\") {\r\n      params.star = paramsQuery.selectedStar;\r\n    }\r\n    return api.post(ApiConstants.FETCH_FAVORITE_HOTELS, { ids, params });\r\n  },\r\n  get_all_hotels: () => {\r\n    return api.get(ApiConstants.FETCH_ALL_HOTEL);\r\n  },\r\n  get_top3_hotels: () => {\r\n    return api.get(ApiConstants.FETCH_TOP3_HOTEL);\r\n  },\r\n  fetchOwnerHotel: () => {\r\n    return api.get(ApiConstants.FETCH_OWNER_HOTEL);\r\n  },\r\n  updateHotel: (hotelId, updateData) => {\r\n    return api.put(`${ApiConstants.UPDATE_HOTEL}/${hotelId}`, updateData);\r\n  },\r\n  updateHotelServiceStatus: (hotelId, statusActive, serviceId) => {\r\n    const url = ApiConstants.UPDATE_HOTEL_SERVICE_STATUS.replace(\r\n      \":hotelId\",\r\n      hotelId\r\n    );\r\n    return api.put(url, { statusActive: statusActive.statusActive, serviceId });\r\n  },\r\n  createHotelService: (serviceData) => {\r\n    return api.post(ApiConstants.CREATE_HOTEL_SERVICE, serviceData);\r\n  },\r\n  changeStatusHotel: (hotelId, ownerStatus) => {\r\n    return api.put(`${ApiConstants.CHANGE_STATUS_HOTEL}/${hotelId}`, {\r\n      ownerStatus,\r\n    });\r\n  },\r\n\r\n\r\n  createHotel: (createHotel, createRoomList, createService) => {\r\n    return api.post(ApiConstants.CREATE_HOTEL, { createHotel, createRoomList, createService });\r\n  },\r\n  // Hotel Image APIs\r\n  uploadHotelImages: (formData) => {\r\n    return api.post(ApiConstants.UPLOAD_HOTEL_IMAGE, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n  },\r\n\r\n  deleteHotelImages: (imageIds) => {\r\n    return api.delete(ApiConstants.DELETE_HOTEL_IMAGE, {\r\n      data: { imageIds },\r\n    });\r\n  },\r\n  create_booking_offline: (params) => {\r\n    return api.post(ApiConstants.CREATE_BOOKING_OFFLINE, {params});\r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChBC,oBAAoB,EAAEA,CAACC,GAAG,EAAEC,WAAW,KAAK;IAC1C,MAAMC,MAAM,GAAG;MACbC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE;IACR,CAAC;IACD,IAAIJ,WAAW,CAACK,YAAY,IAAI,EAAE,IAAIL,WAAW,CAACK,YAAY,EAAE;MAC9DJ,MAAM,CAACC,OAAO,GAAGF,WAAW,CAACK,YAAY;IAC3C;IACA,IAAIL,WAAW,CAACM,gBAAgB,IAAI,EAAE,IAAIN,WAAW,CAACM,gBAAgB,EAAE;MACtEL,MAAM,CAACE,QAAQ,GAAGH,WAAW,CAACM,gBAAgB;IAChD;IACA,IAAIN,WAAW,CAACO,YAAY,IAAI,GAAG,EAAE;MACnCN,MAAM,CAACG,IAAI,GAAGJ,WAAW,CAACO,YAAY;IACxC;IACA,OAAOX,GAAG,CAACY,IAAI,CAACb,YAAY,CAACc,qBAAqB,EAAE;MAAEV,GAAG;MAAEE;IAAO,CAAC,CAAC;EACtE,CAAC;EACDS,cAAc,EAAEA,CAAA,KAAM;IACpB,OAAOd,GAAG,CAACe,GAAG,CAAChB,YAAY,CAACiB,eAAe,CAAC;EAC9C,CAAC;EACDC,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAOjB,GAAG,CAACe,GAAG,CAAChB,YAAY,CAACmB,gBAAgB,CAAC;EAC/C,CAAC;EACDC,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAOnB,GAAG,CAACe,GAAG,CAAChB,YAAY,CAACqB,iBAAiB,CAAC;EAChD,CAAC;EACDC,WAAW,EAAEA,CAACC,OAAO,EAAEC,UAAU,KAAK;IACpC,OAAOvB,GAAG,CAACwB,GAAG,CAAC,GAAGzB,YAAY,CAAC0B,YAAY,IAAIH,OAAO,EAAE,EAAEC,UAAU,CAAC;EACvE,CAAC;EACDG,wBAAwB,EAAEA,CAACJ,OAAO,EAAEK,YAAY,EAAEC,SAAS,KAAK;IAC9D,MAAMC,GAAG,GAAG9B,YAAY,CAAC+B,2BAA2B,CAACC,OAAO,CAC1D,UAAU,EACVT,OACF,CAAC;IACD,OAAOtB,GAAG,CAACwB,GAAG,CAACK,GAAG,EAAE;MAAEF,YAAY,EAAEA,YAAY,CAACA,YAAY;MAAEC;IAAU,CAAC,CAAC;EAC7E,CAAC;EACDI,kBAAkB,EAAGC,WAAW,IAAK;IACnC,OAAOjC,GAAG,CAACY,IAAI,CAACb,YAAY,CAACmC,oBAAoB,EAAED,WAAW,CAAC;EACjE,CAAC;EACDE,iBAAiB,EAAEA,CAACb,OAAO,EAAEc,WAAW,KAAK;IAC3C,OAAOpC,GAAG,CAACwB,GAAG,CAAC,GAAGzB,YAAY,CAACsC,mBAAmB,IAAIf,OAAO,EAAE,EAAE;MAC/Dc;IACF,CAAC,CAAC;EACJ,CAAC;EAGDE,WAAW,EAAEA,CAACA,WAAW,EAAEC,cAAc,EAAEC,aAAa,KAAK;IAC3D,OAAOxC,GAAG,CAACY,IAAI,CAACb,YAAY,CAAC0C,YAAY,EAAE;MAAEH,WAAW;MAAEC,cAAc;MAAEC;IAAc,CAAC,CAAC;EAC5F,CAAC;EACD;EACAE,iBAAiB,EAAGC,QAAQ,IAAK;IAC/B,OAAO3C,GAAG,CAACY,IAAI,CAACb,YAAY,CAAC6C,kBAAkB,EAAED,QAAQ,EAAE;MACzDE,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAEDC,iBAAiB,EAAGC,QAAQ,IAAK;IAC/B,OAAO/C,GAAG,CAACgD,MAAM,CAACjD,YAAY,CAACkD,kBAAkB,EAAE;MACjDC,IAAI,EAAE;QAAEH;MAAS;IACnB,CAAC,CAAC;EACJ,CAAC;EACDI,sBAAsB,EAAG9C,MAAM,IAAK;IAClC,OAAOL,GAAG,CAACY,IAAI,CAACb,YAAY,CAACqD,sBAAsB,EAAE;MAAC/C;IAAM,CAAC,CAAC;EAChE;AACF,CAAC;AAED,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}