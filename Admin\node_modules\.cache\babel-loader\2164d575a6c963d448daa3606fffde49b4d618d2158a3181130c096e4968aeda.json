{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Admin\\\\src\\\\pages\\\\dashboard\\\\DashboardPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardPage = ({\n  setActiveTab\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    data: dashboardData,\n    loading,\n    error\n  } = useSelector(state => state.AdminDashboard);\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n\n  // Fetch dashboard data on component mount and when period changes\n  useEffect(() => {\n    dispatch({\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\n      payload: {\n        params: {\n          period: selectedPeriod\n        },\n        onSuccess: data => {\n          console.log('Dashboard data loaded successfully:', data);\n        },\n        onFailed: error => {\n          console.error('Failed to load dashboard data:', error);\n        }\n      }\n    });\n  }, [dispatch, selectedPeriod]);\n\n  // Handle period change\n  const handlePeriodChange = period => {\n    setSelectedPeriod(period);\n  };\n\n  // Format revenue for display\n  const formatRevenue = revenue => {\n    if (revenue >= 1000000) {\n      return (revenue / 1000000).toFixed(1) + 'M';\n    } else if (revenue >= 1000) {\n      return (revenue / 1000).toFixed(1) + 'K';\n    }\n    return (revenue === null || revenue === void 0 ? void 0 : revenue.toLocaleString()) || '0';\n  };\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          height: '400px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"alert-heading\",\n          children: \"L\\u1ED7i!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-danger\",\n          onClick: () => handlePeriodChange(selectedPeriod),\n          children: \"Th\\u1EED l\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Lấy màu cho trạng thái\n  const getStatusColor = status => {\n    switch (status) {\n      case \"Đã thanh toán\":\n      case \"Hoạt động\":\n        return \"success\";\n      case \"Đang xử lý\":\n      case \"Đang xem xét\":\n      case \"Đang chờ\":\n        return \"warning\";\n      case \"Tạm khóa\":\n      case \"Chưa xử lý\":\n        return \"danger\";\n      default:\n        return \"secondary\";\n    }\n  };\n\n  // Lấy màu cho mức độ nghiêm trọng\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case \"Cao\":\n        return \"danger\";\n      case \"Trung bình\":\n        return \"warning\";\n      case \"Thấp\":\n        return \"info\";\n      default:\n        return \"secondary\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"T\\u1ED5ng quan h\\u1EC7 th\\u1ED1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-filter\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select\",\n            value: selectedPeriod,\n            onChange: e => handlePeriodChange(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"day\",\n              children: \"H\\xF4m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"week\",\n              children: \"Tu\\u1EA7n n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"month\",\n              children: \"Th\\xE1ng n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"year\",\n              children: \"N\\u0103m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), \" Xu\\u1EA5t b\\xE1o c\\xE1o\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon hotels\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-building\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.activeHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xE1ch s\\u1EA1n ho\\u1EA1t \\u0111\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon active\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-check-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.pendingApprovals || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon pending\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-hourglass-split\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalCustomers || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon customers\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-people\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Doanh thu h\\u1EC7 th\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"btn-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Ng\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Tu\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-primary\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"N\\u0103m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-body\",\n        children: /*#__PURE__*/_jsxDEV(Line, {\n          data: revenueData,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                position: \"top\"\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: false,\n                grid: {\n                  drawBorder: false\n                },\n                ticks: {\n                  callback: value => value / 1000 + \"K\"\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n b\\u1ED1 kh\\xE1ch s\\u1EA1n theo khu v\\u1EF1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: /*#__PURE__*/_jsxDEV(Doughnut, {\n            data: hotelDistributionData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\"\n                }\n              },\n              cutout: \"70%\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: /*#__PURE__*/_jsxDEV(Pie, {\n            data: hotelCategoryData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\"\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"recent-activities\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activity-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Y\\xEAu c\\u1EA7u ph\\xEA duy\\u1EC7t g\\u1EA7n \\u0111\\xE2y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            onClick: () => setActiveTab(\"approvals\"),\n            className: \"view-all\",\n            children: \"Xem t\\u1EA5t c\\u1EA3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\xEAn kh\\xE1ch s\\u1EA1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EE7 s\\u1EDF h\\u1EEFu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\u1ECBa \\u0111i\\u1EC3m\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ng\\xE0y g\\u1EEDi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Thao t\\xE1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recentApprovals.slice(0, 3).map(approval => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.hotelName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.owner\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.location\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: approval.submittedDate\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge bg-${getStatusColor(approval.status)}`,\n                      children: approval.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-primary\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 304,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 303,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-success\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-check-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 307,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-danger\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-x-lg\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 310,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this)]\n                }, approval.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activity-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"B\\xE1o c\\xE1o vi ph\\u1EA1m g\\u1EA7n \\u0111\\xE2y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            onClick: () => setActiveTab(\"reports\"),\n            className: \"view-all\",\n            children: \"Xem t\\u1EA5t c\\u1EA3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Kh\\xE1ch h\\xE0ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Kh\\xE1ch s\\u1EA1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Lo\\u1EA1i b\\xE1o c\\xE1o\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"M\\u1EE9c \\u0111\\u1ED9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Thao t\\xE1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recentReports.slice(0, 3).map(report => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.customerName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.hotelName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: report.reportType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge bg-${getSeverityColor(report.severity)}`,\n                      children: report.severity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge bg-${getStatusColor(report.status)}`,\n                      children: report.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-primary\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 375,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"btn btn-sm btn-warning\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bi bi-pencil\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 378,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this)]\n                }, report.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"pBlSUJ1rKJpORKZOoh/qh4nmKVQ=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "Line", "Bar", "Pie", "Doughnut", "useDispatch", "useSelector", "AdminDashboardActions", "jsxDEV", "_jsxDEV", "DashboardPage", "setActiveTab", "_s", "dispatch", "data", "dashboardData", "loading", "error", "state", "AdminDashboard", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "type", "FETCH_ADMIN_DASHBOARD_METRICS", "payload", "params", "period", "onSuccess", "console", "log", "onFailed", "handlePeriodChange", "formatRevenue", "revenue", "toFixed", "toLocaleString", "className", "children", "style", "height", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "getStatusColor", "status", "getSeverityColor", "severity", "value", "onChange", "e", "target", "totalHotels", "activeHotels", "pendingApprovals", "totalCustomers", "revenueData", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "scales", "y", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "x", "display", "hotelDistributionData", "cutout", "hotelCategoryData", "href", "recentApprovals", "slice", "map", "approval", "id", "hotelName", "owner", "location", "submittedDate", "recentReports", "report", "customerName", "reportType", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/pages/dashboard/DashboardPage.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\r\n\r\nconst DashboardPage = ({setActiveTab}) => {\r\n  const dispatch = useDispatch();\r\n  const { data: dashboardData, loading, error } = useSelector(state => state.AdminDashboard);\r\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\r\n\r\n  // Fetch dashboard data on component mount and when period changes\r\n  useEffect(() => {\r\n    dispatch({\r\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\r\n      payload: {\r\n        params: { period: selectedPeriod },\r\n        onSuccess: (data) => {\r\n          console.log('Dashboard data loaded successfully:', data);\r\n        },\r\n        onFailed: (error) => {\r\n          console.error('Failed to load dashboard data:', error);\r\n        }\r\n      }\r\n    });\r\n  }, [dispatch, selectedPeriod]);\r\n\r\n  // Handle period change\r\n  const handlePeriodChange = (period) => {\r\n    setSelectedPeriod(period);\r\n  };\r\n\r\n  // Format revenue for display\r\n  const formatRevenue = (revenue) => {\r\n    if (revenue >= 1000000) {\r\n      return (revenue / 1000000).toFixed(1) + 'M';\r\n    } else if (revenue >= 1000) {\r\n      return (revenue / 1000).toFixed(1) + 'K';\r\n    }\r\n    return revenue?.toLocaleString() || '0';\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n          <div className=\"spinner-border text-primary\" role=\"status\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"alert alert-danger\" role=\"alert\">\r\n          <h4 className=\"alert-heading\">Lỗi!</h4>\r\n          <p>{error}</p>\r\n          <button\r\n            className=\"btn btn-outline-danger\"\r\n            onClick={() => handlePeriodChange(selectedPeriod)}\r\n          >\r\n            Thử lại\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n\r\n  // Lấy màu cho trạng thái\r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case \"Đã thanh toán\":\r\n      case \"Hoạt động\":\r\n        return \"success\";\r\n      case \"Đang xử lý\":\r\n      case \"Đang xem xét\":\r\n      case \"Đang chờ\":\r\n        return \"warning\";\r\n      case \"Tạm khóa\":\r\n      case \"Chưa xử lý\":\r\n        return \"danger\";\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n\r\n  // Lấy màu cho mức độ nghiêm trọng\r\n  const getSeverityColor = (severity) => {\r\n    switch (severity) {\r\n      case \"Cao\":\r\n        return \"danger\";\r\n      case \"Trung bình\":\r\n        return \"warning\";\r\n      case \"Thấp\":\r\n        return \"info\";\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n  return (\r\n    <div className=\"dashboard-content\">\r\n      <div className=\"page-header\">\r\n        <h1>Tổng quan hệ thống</h1>\r\n        <div className=\"page-actions\">\r\n          <div className=\"date-filter\">\r\n            <select\r\n              className=\"form-select\"\r\n              value={selectedPeriod}\r\n              onChange={(e) => handlePeriodChange(e.target.value)}\r\n            >\r\n              <option value=\"day\">Hôm nay</option>\r\n              <option value=\"week\">Tuần này</option>\r\n              <option value=\"month\">Tháng này</option>\r\n              <option value=\"year\">Năm nay</option>\r\n            </select>\r\n          </div>\r\n          <button className=\"btn btn-primary\">\r\n            <i className=\"bi bi-download\"></i> Xuất báo cáo\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"stats-cards\">\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalHotels || 0}</h3>\r\n            <p>Tổng số khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon hotels\">\r\n            <i className=\"bi bi-building\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.activeHotels || 0}</h3>\r\n            <p>Khách sạn hoạt động</p>\r\n          </div>\r\n          <div className=\"stat-card-icon active\">\r\n            <i className=\"bi bi-check-circle\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.pendingApprovals || 0}</h3>\r\n            <p>Chờ phê duyệt</p>\r\n          </div>\r\n          <div className=\"stat-card-icon pending\">\r\n            <i className=\"bi bi-hourglass-split\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalCustomers || 0}</h3>\r\n            <p>Tổng số khách hàng</p>\r\n          </div>\r\n          <div className=\"stat-card-icon customers\">\r\n            <i className=\"bi bi-people\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Revenue Chart */}\r\n      <div className=\"chart-container\">\r\n        <div className=\"chart-header\">\r\n          <h2>Doanh thu hệ thống</h2>\r\n          <div className=\"chart-actions\">\r\n            <div className=\"btn-group\">\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Ngày</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Tuần</button>\r\n              <button className=\"btn btn-sm btn-primary\">Tháng</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Năm</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-body\">\r\n          <Line\r\n            data={revenueData}\r\n            options={{\r\n              responsive: true,\r\n              maintainAspectRatio: false,\r\n              plugins: {\r\n                legend: {\r\n                  position: \"top\",\r\n                },\r\n              },\r\n              scales: {\r\n                y: {\r\n                  beginAtZero: false,\r\n                  grid: {\r\n                    drawBorder: false,\r\n                  },\r\n                  ticks: {\r\n                    callback: (value) => value / 1000 + \"K\",\r\n                  },\r\n                },\r\n                x: {\r\n                  grid: {\r\n                    display: false,\r\n                  },\r\n                },\r\n              },\r\n            }}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Distribution Charts */}\r\n      <div className=\"charts-row\">\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân bố khách sạn theo khu vực</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            <Doughnut\r\n              data={hotelDistributionData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"bottom\",\r\n                  },\r\n                },\r\n                cutout: \"70%\",\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân loại khách sạn</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            <Pie\r\n              data={hotelCategoryData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"bottom\",\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recent Activities */}\r\n      <div className=\"recent-activities\">\r\n        <div className=\"activity-container\">\r\n          <div className=\"activity-header\">\r\n            <h2>Yêu cầu phê duyệt gần đây</h2>\r\n            <a\r\n              href=\"#\"\r\n              onClick={() => setActiveTab(\"approvals\")}\r\n              className=\"view-all\"\r\n            >\r\n              Xem tất cả\r\n            </a>\r\n          </div>\r\n          <div className=\"activity-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>ID</th>\r\n                    <th>Tên khách sạn</th>\r\n                    <th>Chủ sở hữu</th>\r\n                    <th>Địa điểm</th>\r\n                    <th>Ngày gửi</th>\r\n                    <th>Trạng thái</th>\r\n                    <th>Thao tác</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {recentApprovals.slice(0, 3).map((approval) => (\r\n                    <tr key={approval.id}>\r\n                      <td>{approval.id}</td>\r\n                      <td>{approval.hotelName}</td>\r\n                      <td>{approval.owner}</td>\r\n                      <td>{approval.location}</td>\r\n                      <td>{approval.submittedDate}</td>\r\n                      <td>\r\n                        <span\r\n                          className={`badge bg-${getStatusColor(\r\n                            approval.status\r\n                          )}`}\r\n                        >\r\n                          {approval.status}\r\n                        </span>\r\n                      </td>\r\n                      <td>\r\n                        <div className=\"action-buttons\">\r\n                          <button className=\"btn btn-sm btn-primary\">\r\n                            <i className=\"bi bi-eye\"></i>\r\n                          </button>\r\n                          <button className=\"btn btn-sm btn-success\">\r\n                            <i className=\"bi bi-check-lg\"></i>\r\n                          </button>\r\n                          <button className=\"btn btn-sm btn-danger\">\r\n                            <i className=\"bi bi-x-lg\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"activity-container\">\r\n          <div className=\"activity-header\">\r\n            <h2>Báo cáo vi phạm gần đây</h2>\r\n            <a\r\n              href=\"#\"\r\n              onClick={() => setActiveTab(\"reports\")}\r\n              className=\"view-all\"\r\n            >\r\n              Xem tất cả\r\n            </a>\r\n          </div>\r\n          <div className=\"activity-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>ID</th>\r\n                    <th>Khách hàng</th>\r\n                    <th>Khách sạn</th>\r\n                    <th>Loại báo cáo</th>\r\n                    <th>Mức độ</th>\r\n                    <th>Trạng thái</th>\r\n                    <th>Thao tác</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {recentReports.slice(0, 3).map((report) => (\r\n                    <tr key={report.id}>\r\n                      <td>{report.id}</td>\r\n                      <td>{report.customerName}</td>\r\n                      <td>{report.hotelName}</td>\r\n                      <td>{report.reportType}</td>\r\n                      <td>\r\n                        <span\r\n                          className={`badge bg-${getSeverityColor(\r\n                            report.severity\r\n                          )}`}\r\n                        >\r\n                          {report.severity}\r\n                        </span>\r\n                      </td>\r\n                      <td>\r\n                        <span\r\n                          className={`badge bg-${getStatusColor(\r\n                            report.status\r\n                          )}`}\r\n                        >\r\n                          {report.status}\r\n                        </span>\r\n                      </td>\r\n                      <td>\r\n                        <div className=\"action-buttons\">\r\n                          <button className=\"btn btn-sm btn-primary\">\r\n                            <i className=\"bi bi-eye\"></i>\r\n                          </button>\r\n                          <button className=\"btn btn-sm btn-warning\">\r\n                            <i className=\"bi bi-pencil\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,aAAa,GAAGA,CAAC;EAACC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI,EAAEC,aAAa;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGX,WAAW,CAACY,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;EAC1F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,OAAO,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACda,QAAQ,CAAC;MACPS,IAAI,EAAEf,qBAAqB,CAACgB,6BAA6B;MACzDC,OAAO,EAAE;QACPC,MAAM,EAAE;UAAEC,MAAM,EAAEN;QAAe,CAAC;QAClCO,SAAS,EAAGb,IAAI,IAAK;UACnBc,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEf,IAAI,CAAC;QAC1D,CAAC;QACDgB,QAAQ,EAAGb,KAAK,IAAK;UACnBW,OAAO,CAACX,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,QAAQ,EAAEO,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMW,kBAAkB,GAAIL,MAAM,IAAK;IACrCL,iBAAiB,CAACK,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMM,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAIA,OAAO,IAAI,OAAO,EAAE;MACtB,OAAO,CAACA,OAAO,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC7C,CAAC,MAAM,IAAID,OAAO,IAAI,IAAI,EAAE;MAC1B,OAAO,CAACA,OAAO,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC1C;IACA,OAAO,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,cAAc,CAAC,CAAC,KAAI,GAAG;EACzC,CAAC;;EAED;EACA,IAAInB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK2B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC5B,OAAA;QAAK2B,SAAS,EAAC,kDAAkD;QAACE,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAF,QAAA,eAC3F5B,OAAA;UAAK2B,SAAS,EAAC,6BAA6B;UAACI,IAAI,EAAC,QAAQ;UAAAH,QAAA,eACxD5B,OAAA;YAAM2B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI3B,KAAK,EAAE;IACT,oBACER,OAAA;MAAK2B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC5B,OAAA;QAAK2B,SAAS,EAAC,oBAAoB;QAACI,IAAI,EAAC,OAAO;QAAAH,QAAA,gBAC9C5B,OAAA;UAAI2B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCnC,OAAA;UAAA4B,QAAA,EAAIpB;QAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdnC,OAAA;UACE2B,SAAS,EAAC,wBAAwB;UAClCS,OAAO,EAAEA,CAAA,KAAMd,kBAAkB,CAACX,cAAc,CAAE;UAAAiB,QAAA,EACnD;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAIA;EACA,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,eAAe;MACpB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,YAAY;MACjB,KAAK,cAAc;MACnB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;MACf,KAAK,YAAY;QACf,OAAO,QAAQ;MACjB;QACE,OAAO,WAAW;IACtB;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,MAAM;MACf;QACE,OAAO,WAAW;IACtB;EACF,CAAC;EACD,oBACExC,OAAA;IAAK2B,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC5B,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAA4B,QAAA,EAAI;MAAkB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BnC,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B5B,OAAA;YACE2B,SAAS,EAAC,aAAa;YACvBc,KAAK,EAAE9B,cAAe;YACtB+B,QAAQ,EAAGC,CAAC,IAAKrB,kBAAkB,CAACqB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAb,QAAA,gBAEpD5B,OAAA;cAAQyC,KAAK,EAAC,KAAK;cAAAb,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCnC,OAAA;cAAQyC,KAAK,EAAC,MAAM;cAAAb,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCnC,OAAA;cAAQyC,KAAK,EAAC,OAAO;cAAAb,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCnC,OAAA;cAAQyC,KAAK,EAAC,MAAM;cAAAb,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnC,OAAA;UAAQ2B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBACjC5B,OAAA;YAAG2B,SAAS,EAAC;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,4BACpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAK2B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAA4B,QAAA,EAAKtB,aAAa,CAACuC,WAAW,IAAI;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCnC,OAAA;YAAA4B,QAAA,EAAG;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC5B,OAAA;YAAG2B,SAAS,EAAC;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK2B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAA4B,QAAA,EAAKtB,aAAa,CAACwC,YAAY,IAAI;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CnC,OAAA;YAAA4B,QAAA,EAAG;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC5B,OAAA;YAAG2B,SAAS,EAAC;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK2B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAA4B,QAAA,EAAKtB,aAAa,CAACyC,gBAAgB,IAAI;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CnC,OAAA;YAAA4B,QAAA,EAAG;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrC5B,OAAA;YAAG2B,SAAS,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK2B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAA4B,QAAA,EAAKtB,aAAa,CAAC0C,cAAc,IAAI;UAAC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5CnC,OAAA;YAAA4B,QAAA,EAAG;UAAkB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvC5B,OAAA;YAAG2B,SAAS,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK2B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5B,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAA4B,QAAA,EAAI;QAAkB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnC,OAAA;UAAK2B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5B,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5B,OAAA;cAAQ2B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClEnC,OAAA;cAAQ2B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClEnC,OAAA;cAAQ2B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzDnC,OAAA;cAAQ2B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK2B,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB5B,OAAA,CAACR,IAAI;UACHa,IAAI,EAAE4C,WAAY;UAClBC,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACPC,MAAM,EAAE;gBACNC,QAAQ,EAAE;cACZ;YACF,CAAC;YACDC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,KAAK;gBAClBC,IAAI,EAAE;kBACJC,UAAU,EAAE;gBACd,CAAC;gBACDC,KAAK,EAAE;kBACLC,QAAQ,EAAGrB,KAAK,IAAKA,KAAK,GAAG,IAAI,GAAG;gBACtC;cACF,CAAC;cACDsB,CAAC,EAAE;gBACDJ,IAAI,EAAE;kBACJK,OAAO,EAAE;gBACX;cACF;YACF;UACF;QAAE;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK2B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB5B,OAAA;QAAK2B,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC5B,OAAA;UAAK2B,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B5B,OAAA;YAAA4B,QAAA,EAAI;UAA8B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB5B,OAAA,CAACL,QAAQ;YACPU,IAAI,EAAE4D,qBAAsB;YAC5Bf,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE;gBACZ;cACF,CAAC;cACDW,MAAM,EAAE;YACV;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK2B,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC5B,OAAA;UAAK2B,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B5B,OAAA;YAAA4B,QAAA,EAAI;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB5B,OAAA,CAACN,GAAG;YACFW,IAAI,EAAE8D,iBAAkB;YACxBjB,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE;gBACZ;cACF;YACF;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK2B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5B,OAAA;QAAK2B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC5B,OAAA;UAAK2B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5B,OAAA;YAAA4B,QAAA,EAAI;UAAyB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCnC,OAAA;YACEoE,IAAI,EAAC,GAAG;YACRhC,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,WAAW,CAAE;YACzCyB,SAAS,EAAC,UAAU;YAAAC,QAAA,EACrB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5B,OAAA;YAAK2B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B5B,OAAA;cAAO2B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClC5B,OAAA;gBAAA4B,QAAA,eACE5B,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAA4B,QAAA,EAAI;kBAAE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACXnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAQ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAQ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAQ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRnC,OAAA;gBAAA4B,QAAA,EACGyC,eAAe,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,QAAQ,iBACxCxE,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAA4B,QAAA,EAAK4C,QAAQ,CAACC;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtBnC,OAAA;oBAAA4B,QAAA,EAAK4C,QAAQ,CAACE;kBAAS;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7BnC,OAAA;oBAAA4B,QAAA,EAAK4C,QAAQ,CAACG;kBAAK;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBnC,OAAA;oBAAA4B,QAAA,EAAK4C,QAAQ,CAACI;kBAAQ;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5BnC,OAAA;oBAAA4B,QAAA,EAAK4C,QAAQ,CAACK;kBAAa;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjCnC,OAAA;oBAAA4B,QAAA,eACE5B,OAAA;sBACE2B,SAAS,EAAE,YAAYU,cAAc,CACnCmC,QAAQ,CAAClC,MACX,CAAC,EAAG;sBAAAV,QAAA,EAEH4C,QAAQ,CAAClC;oBAAM;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLnC,OAAA;oBAAA4B,QAAA,eACE5B,OAAA;sBAAK2B,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7B5B,OAAA;wBAAQ2B,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxC5B,OAAA;0BAAG2B,SAAS,EAAC;wBAAW;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eACTnC,OAAA;wBAAQ2B,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxC5B,OAAA;0BAAG2B,SAAS,EAAC;wBAAgB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACTnC,OAAA;wBAAQ2B,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,eACvC5B,OAAA;0BAAG2B,SAAS,EAAC;wBAAY;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GA3BEqC,QAAQ,CAACC,EAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4BhB,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnC,OAAA;QAAK2B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC5B,OAAA;UAAK2B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5B,OAAA;YAAA4B,QAAA,EAAI;UAAuB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCnC,OAAA;YACEoE,IAAI,EAAC,GAAG;YACRhC,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,SAAS,CAAE;YACvCyB,SAAS,EAAC,UAAU;YAAAC,QAAA,EACrB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5B,OAAA;YAAK2B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B5B,OAAA;cAAO2B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClC5B,OAAA;gBAAA4B,QAAA,eACE5B,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAA4B,QAAA,EAAI;kBAAE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACXnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAY;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBnC,OAAA;oBAAA4B,QAAA,EAAI;kBAAQ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRnC,OAAA;gBAAA4B,QAAA,EACGkD,aAAa,CAACR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEQ,MAAM,iBACpC/E,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAA4B,QAAA,EAAKmD,MAAM,CAACN;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpBnC,OAAA;oBAAA4B,QAAA,EAAKmD,MAAM,CAACC;kBAAY;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BnC,OAAA;oBAAA4B,QAAA,EAAKmD,MAAM,CAACL;kBAAS;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3BnC,OAAA;oBAAA4B,QAAA,EAAKmD,MAAM,CAACE;kBAAU;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5BnC,OAAA;oBAAA4B,QAAA,eACE5B,OAAA;sBACE2B,SAAS,EAAE,YAAYY,gBAAgB,CACrCwC,MAAM,CAACvC,QACT,CAAC,EAAG;sBAAAZ,QAAA,EAEHmD,MAAM,CAACvC;oBAAQ;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLnC,OAAA;oBAAA4B,QAAA,eACE5B,OAAA;sBACE2B,SAAS,EAAE,YAAYU,cAAc,CACnC0C,MAAM,CAACzC,MACT,CAAC,EAAG;sBAAAV,QAAA,EAEHmD,MAAM,CAACzC;oBAAM;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLnC,OAAA;oBAAA4B,QAAA,eACE5B,OAAA;sBAAK2B,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7B5B,OAAA;wBAAQ2B,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxC5B,OAAA;0BAAG2B,SAAS,EAAC;wBAAW;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eACTnC,OAAA;wBAAQ2B,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACxC5B,OAAA;0BAAG2B,SAAS,EAAC;wBAAc;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAhCE4C,MAAM,CAACN,EAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCd,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAlYIF,aAAa;EAAA,QACAL,WAAW,EACoBC,WAAW;AAAA;AAAAqF,EAAA,GAFvDjF,aAAa;AAoYnB,eAAeA,aAAa;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}