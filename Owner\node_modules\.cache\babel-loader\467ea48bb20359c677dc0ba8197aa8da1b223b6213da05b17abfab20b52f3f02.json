{"ast": null, "code": "import { Hotel } from \"lucide-react\";\nimport HotelActions from \"./actions\";\nconst initialState = {\n  hotel: null,\n  hotelDetail: null,\n  error: null,\n  top3Hotels: [],\n  loading: false,\n  data: null,\n  error: null,\n  createHotel: {\n    hotelName: \"\",\n    city: \"\",\n    district: \"\",\n    ward: \"\",\n    specificAddress: \"\",\n    address: \"\",\n    facilities: [],\n    checkInStart: \"\",\n    checkInEnd: \"\",\n    checkOutStart: \"\",\n    checkOutEnd: \"\",\n    phoneNumber: \"\",\n    email: \"\",\n    star: 1,\n    description: \"\",\n    images: [],\n    businessDocuments: [],\n    phoneNumber: \"\",\n    email: \"\"\n  },\n  checkCreateHotel: false,\n  createService: [],\n  createRoom: []\n};\nconst favoriteHotelReducer = (state = initialState, action) => {\n  var _state$data;\n  switch (action.type) {\n    case HotelActions.FETCH_FAVORITE_HOTELS_SUCCESS:\n      return {\n        ...state,\n        hotels: action.payload\n      };\n    case HotelActions.FETCH_OWNER_HOTEL_SUCCESS:\n      return {\n        ...state,\n        hotel: action.payload.hotels[0],\n        data: action.payload,\n        loading: false\n      };\n    case HotelActions.FETCH_All_HOTEL_SUCCESS:\n      return {\n        ...state,\n        hotels: action.payload\n      };\n    case HotelActions.FETCH_TOP3_HOTEL_SUCCESS:\n      return {\n        ...state,\n        top3Hotels: action.payload\n      };\n    case HotelActions.UPDATE_HOTEL_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        data: {\n          ...state.data,\n          ...action.payload\n        }\n      };\n    case HotelActions.UPDATE_HOTEL_SERVICE_STATUS_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        data: {\n          ...state.data,\n          services: state.data.services.map(service => service._id === action.payload._id ? {\n            ...service,\n            statusActive: action.payload.statusActive\n          } : service)\n        }\n      };\n    case HotelActions.CREATE_HOTEL_SERVICE_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        data: {\n          ...state.data,\n          services: [...(((_state$data = state.data) === null || _state$data === void 0 ? void 0 : _state$data.services) || []), action.payload]\n        },\n        error: null\n      };\n    case HotelActions.SAVE_HOTEL_NAME_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...state.createHotel,\n          hotelName: action.payload.hotelName,\n          phoneNumber: action.payload.phoneNumber,\n          email: action.payload.email\n        }\n      };\n    case HotelActions.SAVE_HOTEL_ADDRESS_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...state.createHotel,\n          specificAddress: action.payload.specificAddress,\n          address: action.payload.address,\n          city: action.payload.city,\n          district: action.payload.district,\n          ward: action.payload.ward\n        }\n      };\n    case HotelActions.SAVE_HOTEL_CHECKTIME_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...state.createHotel,\n          checkInStart: action.payload.checkInStart,\n          checkInEnd: action.payload.checkInEnd,\n          checkOutStart: action.payload.checkOutStart,\n          checkOutEnd: action.payload.checkOutEnd\n        }\n      };\n    case HotelActions.SAVE_HOTEL_FACILITIES_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...state.createHotel,\n          facilities: action.payload.facilities\n        }\n      };\n    case HotelActions.SAVE_HOTEL_DESCRIPTION_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...state.createHotel,\n          star: action.payload.star,\n          description: action.payload.description,\n          images: action.payload.images,\n          checkCreateHotel: action.payload.checkCreateHotel\n        }\n      };\n    case HotelActions.EDIT_HOTEL_DESCRIPTION_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...state.createHotel,\n          checkCreateHotel: action.payload.checkCreateHotel\n        }\n      };\n    case HotelActions.CREATE_HOTEL_SUCCESS:\n      {\n        return {\n          ...state,\n          loading: true,\n          error: null,\n          hotel: action.payload.hotel\n        };\n      }\n    case HotelActions.CLEAR_HOTEL_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...initialState.createHotel\n        }\n      };\n    case HotelActions.SAVE_HOTEL_DOCUMENTS_CREATE:\n      return {\n        ...state,\n        createHotel: {\n          ...state.createHotel,\n          businessDocuments: action.payload.businessDocuments\n        }\n      };\n    case HotelActions.SAVE_SERVICE_CREATE:\n      return {\n        ...state,\n        createService: [...state.createService, action.payload]\n      };\n    case HotelActions.EDIT_SERVICE_CREATE:\n      return {\n        ...state,\n        createService: state.createService.map((service, index) => index === action.payload.index ? {\n          ...service,\n          ...action.payload.serviceData\n        } : service)\n      };\n    case HotelActions.DELETE_SERVICE_CREATE:\n      return {\n        ...state,\n        createService: state.createService.filter((_, index) => index !== action.payload.index)\n      };\n    case HotelActions.CLEAR_SERVICE_CREATE:\n      return {\n        ...state,\n        createService: []\n      };\n    default:\n      return state;\n  }\n};\nexport default favoriteHotelReducer;", "map": {"version": 3, "names": ["Hotel", "HotelActions", "initialState", "hotel", "hotelDetail", "error", "top3Hotels", "loading", "data", "createHotel", "hotelName", "city", "district", "ward", "specificAddress", "address", "facilities", "checkInStart", "checkInEnd", "checkOutStart", "checkOutEnd", "phoneNumber", "email", "star", "description", "images", "businessDocuments", "checkCreateHotel", "createService", "createRoom", "favoriteHotelReducer", "state", "action", "_state$data", "type", "FETCH_FAVORITE_HOTELS_SUCCESS", "hotels", "payload", "FETCH_OWNER_HOTEL_SUCCESS", "FETCH_All_HOTEL_SUCCESS", "FETCH_TOP3_HOTEL_SUCCESS", "UPDATE_HOTEL_SUCCESS", "UPDATE_HOTEL_SERVICE_STATUS_SUCCESS", "services", "map", "service", "_id", "statusActive", "CREATE_HOTEL_SERVICE_SUCCESS", "SAVE_HOTEL_NAME_CREATE", "SAVE_HOTEL_ADDRESS_CREATE", "SAVE_HOTEL_CHECKTIME_CREATE", "SAVE_HOTEL_FACILITIES_CREATE", "SAVE_HOTEL_DESCRIPTION_CREATE", "EDIT_HOTEL_DESCRIPTION_CREATE", "CREATE_HOTEL_SUCCESS", "CLEAR_HOTEL_CREATE", "SAVE_HOTEL_DOCUMENTS_CREATE", "SAVE_SERVICE_CREATE", "EDIT_SERVICE_CREATE", "index", "serviceData", "DELETE_SERVICE_CREATE", "filter", "_", "CLEAR_SERVICE_CREATE"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/hotel/reducer.js"], "sourcesContent": ["import { Hotel } from \"lucide-react\";\r\nimport HotelActions from \"./actions\";\r\n\r\nconst initialState = {\r\n  hotel: null,\r\n  hotelDetail: null,\r\n  error: null,\r\n  top3Hotels: [],\r\n  loading: false,\r\n  data: null,\r\n  error: null,\r\n  createHotel: {\r\n    hotelName: \"\",\r\n    city: \"\",\r\n    district: \"\",\r\n    ward: \"\",\r\n    specificAddress: \"\",\r\n    address: \"\",\r\n    facilities: [],\r\n    checkInStart: \"\",\r\n    checkInEnd: \"\",\r\n    checkOutStart: \"\",\r\n    checkOutEnd: \"\",\r\n    phoneNumber: \"\",\r\n    email: \"\",\r\n    star: 1,\r\n    description: \"\",\r\n    images: [],\r\n    businessDocuments: [],\r\n    phoneNumber: \"\",\r\n    email: \"\",\r\n  },\r\n  checkCreateHotel: false,\r\n  createService: [],\r\n  createRoom: [],\r\n};\r\n\r\nconst favoriteHotelReducer = (state = initialState, action) => {\r\n  switch (action.type) {\r\n    case HotelActions.FETCH_FAVORITE_HOTELS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        hotels: action.payload,\r\n      };\r\n    case HotelActions.FETCH_OWNER_HOTEL_SUCCESS:\r\n      return {\r\n        ...state,\r\n        hotel: action.payload.hotels[0],\r\n        data: action.payload,\r\n        loading: false,\r\n      };\r\n    case HotelActions.FETCH_All_HOTEL_SUCCESS:\r\n      return {\r\n        ...state,\r\n        hotels: action.payload,\r\n      };\r\n    case HotelActions.FETCH_TOP3_HOTEL_SUCCESS:\r\n      return {\r\n        ...state,\r\n        top3Hotels: action.payload,\r\n      };\r\n    case HotelActions.UPDATE_HOTEL_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        data: {\r\n          ...state.data,\r\n          ...action.payload,\r\n        },\r\n      };\r\n    case HotelActions.UPDATE_HOTEL_SERVICE_STATUS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        data: {\r\n          ...state.data,\r\n          services: state.data.services.map((service) =>\r\n            service._id === action.payload._id\r\n              ? { ...service, statusActive: action.payload.statusActive }\r\n              : service\r\n          ),\r\n        },\r\n      };\r\n    case HotelActions.CREATE_HOTEL_SERVICE_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        data: {\r\n          ...state.data,\r\n\r\n          services: [...(state.data?.services || []), action.payload],\r\n        },\r\n        error: null,\r\n      };\r\n    case HotelActions.SAVE_HOTEL_NAME_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...state.createHotel,\r\n          hotelName: action.payload.hotelName,\r\n          phoneNumber: action.payload.phoneNumber,\r\n          email: action.payload.email,\r\n        },\r\n      };\r\n    case HotelActions.SAVE_HOTEL_ADDRESS_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...state.createHotel,\r\n          specificAddress: action.payload.specificAddress,\r\n          address: action.payload.address,\r\n          city: action.payload.city,\r\n          district: action.payload.district,\r\n          ward: action.payload.ward,\r\n        },\r\n      };\r\n    case HotelActions.SAVE_HOTEL_CHECKTIME_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...state.createHotel,\r\n          checkInStart: action.payload.checkInStart,\r\n          checkInEnd: action.payload.checkInEnd,\r\n          checkOutStart: action.payload.checkOutStart,\r\n          checkOutEnd: action.payload.checkOutEnd,\r\n        },\r\n      };\r\n    case HotelActions.SAVE_HOTEL_FACILITIES_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...state.createHotel,\r\n          facilities: action.payload.facilities,\r\n        },\r\n      };\r\n    case HotelActions.SAVE_HOTEL_DESCRIPTION_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...state.createHotel,\r\n          star: action.payload.star,\r\n          description: action.payload.description,\r\n          images: action.payload.images,\r\n          checkCreateHotel: action.payload.checkCreateHotel,\r\n        },\r\n      };\r\n    case HotelActions.EDIT_HOTEL_DESCRIPTION_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...state.createHotel,\r\n          checkCreateHotel: action.payload.checkCreateHotel,\r\n        },\r\n      };\r\n    case HotelActions.CREATE_HOTEL_SUCCESS: {\r\n      return {\r\n        ...state,\r\n        loading: true,\r\n        error: null,\r\n        hotel: action.payload.hotel,\r\n      };\r\n    }\r\n    case HotelActions.CLEAR_HOTEL_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...initialState.createHotel,\r\n        },\r\n      };\r\n    case HotelActions.SAVE_HOTEL_DOCUMENTS_CREATE:\r\n      return {\r\n        ...state,\r\n        createHotel: {\r\n          ...state.createHotel,\r\n          businessDocuments: action.payload.businessDocuments,\r\n        },\r\n      };\r\n    case HotelActions.SAVE_SERVICE_CREATE:\r\n      return {\r\n        ...state,\r\n        createService: [...state.createService, action.payload],\r\n      };\r\n    case HotelActions.EDIT_SERVICE_CREATE:\r\n      return {\r\n        ...state,\r\n        createService: state.createService.map((service, index) =>\r\n          index === action.payload.index ? { ...service, ...action.payload.serviceData } : service\r\n        ),\r\n      };\r\n    case HotelActions.DELETE_SERVICE_CREATE:\r\n      return {\r\n        ...state,\r\n        createService: state.createService.filter((_, index) => index !== action.payload.index),\r\n      };\r\n    case HotelActions.CLEAR_SERVICE_CREATE:\r\n      return {\r\n        ...state,\r\n        createService: [],\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default favoriteHotelReducer;\r\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,cAAc;AACpC,OAAOC,YAAY,MAAM,WAAW;AAEpC,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAE,IAAI;EACVH,KAAK,EAAE,IAAI;EACXI,WAAW,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,eAAe,EAAE,EAAE;IACnBC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,iBAAiB,EAAE,EAAE;IACrBL,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,gBAAgB,EAAE,KAAK;EACvBC,aAAa,EAAE,EAAE;EACjBC,UAAU,EAAE;AACd,CAAC;AAED,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,GAAG7B,YAAY,EAAE8B,MAAM,KAAK;EAAA,IAAAC,WAAA;EAC7D,QAAQD,MAAM,CAACE,IAAI;IACjB,KAAKjC,YAAY,CAACkC,6BAA6B;MAC7C,OAAO;QACL,GAAGJ,KAAK;QACRK,MAAM,EAAEJ,MAAM,CAACK;MACjB,CAAC;IACH,KAAKpC,YAAY,CAACqC,yBAAyB;MACzC,OAAO;QACL,GAAGP,KAAK;QACR5B,KAAK,EAAE6B,MAAM,CAACK,OAAO,CAACD,MAAM,CAAC,CAAC,CAAC;QAC/B5B,IAAI,EAAEwB,MAAM,CAACK,OAAO;QACpB9B,OAAO,EAAE;MACX,CAAC;IACH,KAAKN,YAAY,CAACsC,uBAAuB;MACvC,OAAO;QACL,GAAGR,KAAK;QACRK,MAAM,EAAEJ,MAAM,CAACK;MACjB,CAAC;IACH,KAAKpC,YAAY,CAACuC,wBAAwB;MACxC,OAAO;QACL,GAAGT,KAAK;QACRzB,UAAU,EAAE0B,MAAM,CAACK;MACrB,CAAC;IACH,KAAKpC,YAAY,CAACwC,oBAAoB;MACpC,OAAO;QACL,GAAGV,KAAK;QACRxB,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE;UACJ,GAAGuB,KAAK,CAACvB,IAAI;UACb,GAAGwB,MAAM,CAACK;QACZ;MACF,CAAC;IACH,KAAKpC,YAAY,CAACyC,mCAAmC;MACnD,OAAO;QACL,GAAGX,KAAK;QACRxB,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE;UACJ,GAAGuB,KAAK,CAACvB,IAAI;UACbmC,QAAQ,EAAEZ,KAAK,CAACvB,IAAI,CAACmC,QAAQ,CAACC,GAAG,CAAEC,OAAO,IACxCA,OAAO,CAACC,GAAG,KAAKd,MAAM,CAACK,OAAO,CAACS,GAAG,GAC9B;YAAE,GAAGD,OAAO;YAAEE,YAAY,EAAEf,MAAM,CAACK,OAAO,CAACU;UAAa,CAAC,GACzDF,OACN;QACF;MACF,CAAC;IACH,KAAK5C,YAAY,CAAC+C,4BAA4B;MAC5C,OAAO;QACL,GAAGjB,KAAK;QACRxB,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE;UACJ,GAAGuB,KAAK,CAACvB,IAAI;UAEbmC,QAAQ,EAAE,CAAC,IAAI,EAAAV,WAAA,GAAAF,KAAK,CAACvB,IAAI,cAAAyB,WAAA,uBAAVA,WAAA,CAAYU,QAAQ,KAAI,EAAE,CAAC,EAAEX,MAAM,CAACK,OAAO;QAC5D,CAAC;QACDhC,KAAK,EAAE;MACT,CAAC;IACH,KAAKJ,YAAY,CAACgD,sBAAsB;MACtC,OAAO;QACL,GAAGlB,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGsB,KAAK,CAACtB,WAAW;UACpBC,SAAS,EAAEsB,MAAM,CAACK,OAAO,CAAC3B,SAAS;UACnCW,WAAW,EAAEW,MAAM,CAACK,OAAO,CAAChB,WAAW;UACvCC,KAAK,EAAEU,MAAM,CAACK,OAAO,CAACf;QACxB;MACF,CAAC;IACH,KAAKrB,YAAY,CAACiD,yBAAyB;MACzC,OAAO;QACL,GAAGnB,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGsB,KAAK,CAACtB,WAAW;UACpBK,eAAe,EAAEkB,MAAM,CAACK,OAAO,CAACvB,eAAe;UAC/CC,OAAO,EAAEiB,MAAM,CAACK,OAAO,CAACtB,OAAO;UAC/BJ,IAAI,EAAEqB,MAAM,CAACK,OAAO,CAAC1B,IAAI;UACzBC,QAAQ,EAAEoB,MAAM,CAACK,OAAO,CAACzB,QAAQ;UACjCC,IAAI,EAAEmB,MAAM,CAACK,OAAO,CAACxB;QACvB;MACF,CAAC;IACH,KAAKZ,YAAY,CAACkD,2BAA2B;MAC3C,OAAO;QACL,GAAGpB,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGsB,KAAK,CAACtB,WAAW;UACpBQ,YAAY,EAAEe,MAAM,CAACK,OAAO,CAACpB,YAAY;UACzCC,UAAU,EAAEc,MAAM,CAACK,OAAO,CAACnB,UAAU;UACrCC,aAAa,EAAEa,MAAM,CAACK,OAAO,CAAClB,aAAa;UAC3CC,WAAW,EAAEY,MAAM,CAACK,OAAO,CAACjB;QAC9B;MACF,CAAC;IACH,KAAKnB,YAAY,CAACmD,4BAA4B;MAC5C,OAAO;QACL,GAAGrB,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGsB,KAAK,CAACtB,WAAW;UACpBO,UAAU,EAAEgB,MAAM,CAACK,OAAO,CAACrB;QAC7B;MACF,CAAC;IACH,KAAKf,YAAY,CAACoD,6BAA6B;MAC7C,OAAO;QACL,GAAGtB,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGsB,KAAK,CAACtB,WAAW;UACpBc,IAAI,EAAES,MAAM,CAACK,OAAO,CAACd,IAAI;UACzBC,WAAW,EAAEQ,MAAM,CAACK,OAAO,CAACb,WAAW;UACvCC,MAAM,EAAEO,MAAM,CAACK,OAAO,CAACZ,MAAM;UAC7BE,gBAAgB,EAAEK,MAAM,CAACK,OAAO,CAACV;QACnC;MACF,CAAC;IACH,KAAK1B,YAAY,CAACqD,6BAA6B;MAC7C,OAAO;QACL,GAAGvB,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGsB,KAAK,CAACtB,WAAW;UACpBkB,gBAAgB,EAAEK,MAAM,CAACK,OAAO,CAACV;QACnC;MACF,CAAC;IACH,KAAK1B,YAAY,CAACsD,oBAAoB;MAAE;QACtC,OAAO;UACL,GAAGxB,KAAK;UACRxB,OAAO,EAAE,IAAI;UACbF,KAAK,EAAE,IAAI;UACXF,KAAK,EAAE6B,MAAM,CAACK,OAAO,CAAClC;QACxB,CAAC;MACH;IACA,KAAKF,YAAY,CAACuD,kBAAkB;MAClC,OAAO;QACL,GAAGzB,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGP,YAAY,CAACO;QAClB;MACF,CAAC;IACH,KAAKR,YAAY,CAACwD,2BAA2B;MAC3C,OAAO;QACL,GAAG1B,KAAK;QACRtB,WAAW,EAAE;UACX,GAAGsB,KAAK,CAACtB,WAAW;UACpBiB,iBAAiB,EAAEM,MAAM,CAACK,OAAO,CAACX;QACpC;MACF,CAAC;IACH,KAAKzB,YAAY,CAACyD,mBAAmB;MACnC,OAAO;QACL,GAAG3B,KAAK;QACRH,aAAa,EAAE,CAAC,GAAGG,KAAK,CAACH,aAAa,EAAEI,MAAM,CAACK,OAAO;MACxD,CAAC;IACH,KAAKpC,YAAY,CAAC0D,mBAAmB;MACnC,OAAO;QACL,GAAG5B,KAAK;QACRH,aAAa,EAAEG,KAAK,CAACH,aAAa,CAACgB,GAAG,CAAC,CAACC,OAAO,EAAEe,KAAK,KACpDA,KAAK,KAAK5B,MAAM,CAACK,OAAO,CAACuB,KAAK,GAAG;UAAE,GAAGf,OAAO;UAAE,GAAGb,MAAM,CAACK,OAAO,CAACwB;QAAY,CAAC,GAAGhB,OACnF;MACF,CAAC;IACH,KAAK5C,YAAY,CAAC6D,qBAAqB;MACrC,OAAO;QACL,GAAG/B,KAAK;QACRH,aAAa,EAAEG,KAAK,CAACH,aAAa,CAACmC,MAAM,CAAC,CAACC,CAAC,EAAEJ,KAAK,KAAKA,KAAK,KAAK5B,MAAM,CAACK,OAAO,CAACuB,KAAK;MACxF,CAAC;IACH,KAAK3D,YAAY,CAACgE,oBAAoB;MACpC,OAAO;QACL,GAAGlC,KAAK;QACRH,aAAa,EAAE;MACjB,CAAC;IACH;MACE,OAAOG,KAAK;EAChB;AACF,CAAC;AAED,eAAeD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}