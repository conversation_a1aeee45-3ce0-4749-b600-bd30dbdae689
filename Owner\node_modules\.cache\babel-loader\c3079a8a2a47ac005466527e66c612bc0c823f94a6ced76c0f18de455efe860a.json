{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\dash_board\\\\DashBoardPage.jsx\";\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DashBoardPage = ({\n  setActiveTab\n}) => {\n  const revenueData = {\n    labels: [\"T1\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\", \"T8\", \"T9\", \"T10\", \"T11\", \"T12\"],\n    datasets: [{\n      label: \"<PERSON><PERSON>h thu thực tế\",\n      data: [12500, 13200, 15400, 18900, 21500, 25800, 28900, 27600, 24300, 19800, 16500, 22100],\n      borderColor: \"#4361ee\",\n      backgroundColor: \"rgba(67, 97, 238, 0.1)\",\n      tension: 0.4,\n      fill: true\n    }, {\n      label: \"<PERSON><PERSON> đo<PERSON> (AI)\",\n      data: [12000, 13000, 15000, 19000, 22000, 26000, 29000, 28000, 24000, 20000, 17000, 23000],\n      borderColor: \"#f72585\",\n      borderDash: [5, 5],\n      tension: 0.4,\n      fill: false\n    }]\n  };\n  // Dữ liệu biểu đồ phân khúc khách hàng\n  const customerSegmentData = {\n    labels: [\"Doanh nhân\", \"Gia đình\", \"Cặp đôi\", \"Du lịch một mình\", \"Đoàn du lịch\"],\n    datasets: [{\n      data: [35, 25, 20, 10, 10],\n      backgroundColor: [\"#4361ee\", \"#3a0ca3\", \"#4cc9f0\", \"#f72585\", \"#7209b7\"],\n      borderWidth: 1\n    }]\n  };\n\n  // Dữ liệu AI Insights\n  const aiInsights = [{\n    id: 1,\n    title: \"Dự đoán nhu cầu cao điểm\",\n    description: \"Dự kiến nhu cầu tăng 23% vào tháng 7-8. Cân nhắc tăng giá phòng và chuẩn bị nhân sự.\",\n    impact: \"high\",\n    category: \"demand\"\n  }, {\n    id: 2,\n    title: \"Phân tích tỷ lệ hoàn trả\",\n    description: \"Tỷ lệ hoàn trả giảm 5% so với quý trước. Chính sách hủy linh hoạt đang phát huy hiệu quả.\",\n    impact: \"medium\",\n    category: \"operations\"\n  }, {\n    id: 3,\n    title: \"Phân khúc khách hàng mới nổi\",\n    description: \"Phát hiện sự gia tăng 15% khách du lịch một mình. Cân nhắc tạo gói dịch vụ đặc biệt.\",\n    impact: \"medium\",\n    category: \"customers\"\n  }, {\n    id: 4,\n    title: \"Cơ hội tăng doanh thu\",\n    description: \"Phân tích cho thấy tiềm năng tăng 18% doanh thu từ dịch vụ spa nếu có gói combo với phòng.\",\n    impact: \"high\",\n    category: \"revenue\"\n  }, {\n    id: 5,\n    title: \"Tối ưu hóa nhân sự\",\n    description: \"Mô hình dự đoán cho thấy có thể giảm 7% chi phí nhân sự bằng cách điều chỉnh lịch làm việc.\",\n    impact: \"medium\",\n    category: \"operations\"\n  }];\n\n  // Dữ liệu đặt phòng gần đây\n  const recentBookings = [{\n    id: \"B-7829\",\n    guest: \"Nguyễn Văn A\",\n    room: \"Deluxe 301\",\n    checkin: \"15/06/2025\",\n    checkout: \"18/06/2025\",\n    status: \"Đã xác nhận\",\n    amount: \"4,500,000 VND\"\n  }, {\n    id: \"B-7830\",\n    guest: \"Trần Thị B\",\n    room: \"Suite 502\",\n    checkin: \"16/06/2025\",\n    checkout: \"20/06/2025\",\n    status: \"Đã thanh toán\",\n    amount: \"12,800,000 VND\"\n  }, {\n    id: \"B-7831\",\n    guest: \"Lê Văn C\",\n    room: \"Standard 205\",\n    checkin: \"16/06/2025\",\n    checkout: \"17/06/2025\",\n    status: \"Đang xử lý\",\n    amount: \"1,200,000 VND\"\n  }, {\n    id: \"B-7832\",\n    guest: \"Phạm Thị D\",\n    room: \"Deluxe 305\",\n    checkin: \"17/06/2025\",\n    checkout: \"22/06/2025\",\n    status: \"Đã xác nhận\",\n    amount: \"7,500,000 VND\"\n  }, {\n    id: \"B-7833\",\n    guest: \"Hoàng Văn E\",\n    room: \"Suite 501\",\n    checkin: \"18/06/2025\",\n    checkout: \"25/06/2025\",\n    status: \"Đã thanh toán\",\n    amount: \"18,900,000 VND\"\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"T\\u1ED5ng quan kh\\xE1ch s\\u1EA1n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select form-select-sm me-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"H\\xF4m nay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Tu\\u1EA7n n\\xE0y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            defaultValue: \"selected\",\n            children: \"Th\\xE1ng n\\xE0y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"Qu\\xFD n\\xE0y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            children: \"N\\u0103m nay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-sm btn-outline-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-download me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), \" Xu\\u1EA5t b\\xE1o c\\xE1o\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted\",\n                  children: \"T\\u1EF7 l\\u1EC7 l\\u1EA5p \\u0111\\u1EA7y\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"mb-0\",\n                  children: \"82.5%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-arrow-up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this), \" 4.2% so v\\u1EDBi th\\xE1ng tr\\u01B0\\u1EDBc\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-icon light-primary\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-house-door fs-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted\",\n                  children: \"Doanh thu trung b\\xECnh/ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"mb-0\",\n                  children: \"2.4M\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-arrow-up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), \" 6.8% so v\\u1EDBi th\\xE1ng tr\\u01B0\\u1EDBc\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-icon light-success\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-cash-stack fs-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted\",\n                  children: \"\\u0110\\xE1nh gi\\xE1 trung b\\xECnh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"mb-0\",\n                  children: \"4.7/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-arrow-up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this), \" 0.2 so v\\u1EDBi th\\xE1ng tr\\u01B0\\u1EDBc\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-icon light-warning\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-star fs-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted\",\n                  children: \"T\\u1EF7 l\\u1EC7 kh\\xE1ch quay l\\u1EA1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"mb-0\",\n                  children: \"28.3%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-danger\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-arrow-down\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), \" 1.5% so v\\u1EDBi th\\xE1ng tr\\u01B0\\u1EDBc\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-icon light-info\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-repeat fs-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"card-title\",\n                  children: \"Ph\\xE2n t\\xEDch doanh thu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted small mb-0\",\n                  children: \"So s\\xE1nh doanh thu th\\u1EF1c t\\u1EBF v\\u1EDBi d\\u1EF1 \\u0111o\\xE1n AI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"btn-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-sm btn-outline-secondary\",\n                  children: \"Ng\\xE0y\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-sm btn-outline-secondary\",\n                  children: \"Tu\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-sm btn-primary\",\n                  children: \"Th\\xE1ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-sm btn-outline-secondary\",\n                  children: \"N\\u0103m\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              data: revenueData,\n              options: {\n                responsive: true,\n                plugins: {\n                  legend: {\n                    position: \"top\"\n                  }\n                },\n                scales: {\n                  y: {\n                    beginAtZero: false,\n                    grid: {\n                      drawBorder: false\n                    },\n                    ticks: {\n                      callback: value => value / 1000 + \"K\"\n                    }\n                  },\n                  x: {\n                    grid: {\n                      display: false\n                    }\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card h-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: \"Ph\\xE2n kh\\xFAc kh\\xE1ch h\\xE0ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted small mb-4\",\n              children: \"Ph\\xE2n t\\xEDch theo lo\\u1EA1i kh\\xE1ch h\\xE0ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"chart-container\",\n              children: /*#__PURE__*/_jsxDEV(Doughnut, {\n                data: customerSegmentData,\n                options: {\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      position: \"bottom\"\n                    }\n                  },\n                  cutout: \"70%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"card-title\",\n                children: \"AI Insights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"btn btn-sm btn-link text-decoration-none\",\n                onClick: () => {\n                  setActiveTab(\"ai-insights\");\n                },\n                children: \"Xem t\\u1EA5t c\\u1EA3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-insights-list\",\n              children: aiInsights.slice(0, 3).map(insight => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ai-insight-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `badge ${insight.impact === \"high\" ? \"bg-danger\" : \"bg-warning\"} me-2`,\n                    children: insight.impact === \"high\" ? \"Quan trọng\" : \"Trung bình\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: insight.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 small\",\n                  children: insight.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this)]\n              }, insight.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"card-title\",\n                children: \"\\u0110\\u1EB7t ph\\xF2ng g\\u1EA7n \\u0111\\xE2y\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"btn btn-sm btn-link text-decoration-none\",\n                onClick: () => {\n                  setActiveTab(\"bookings\");\n                },\n                children: \"Xem t\\u1EA5t c\\u1EA3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-hover\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"table-light\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Kh\\xE1ch h\\xE0ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Check-in\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Tr\\u1EA1ng th\\xE1i\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"S\\u1ED1 ti\\u1EC1n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: recentBookings.slice(0, 4).map(booking => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: booking.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: booking.guest\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: booking.checkin\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `badge ${booking.status === \"Đã xác nhận\" ? \"bg-success\" : booking.status === \"Đã thanh toán\" ? \"bg-primary\" : \"bg-warning\"}`,\n                        children: booking.status\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: booking.amount\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 25\n                    }, this)]\n                  }, booking.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = DashBoardPage;\nexport default DashBoardPage;\nvar _c;\n$RefreshReg$(_c, \"DashBoardPage\");", "map": {"version": 3, "names": ["Line", "Bar", "Pie", "Doughnut", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DashBoardPage", "setActiveTab", "revenueData", "labels", "datasets", "label", "data", "borderColor", "backgroundColor", "tension", "fill", "borderDash", "customerSegmentData", "borderWidth", "aiInsights", "id", "title", "description", "impact", "category", "recentBookings", "guest", "room", "checkin", "checkout", "status", "amount", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultValue", "options", "responsive", "plugins", "legend", "position", "scales", "y", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "value", "x", "display", "maintainAspectRatio", "cutout", "href", "onClick", "slice", "map", "insight", "booking", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/dash_board/DashBoardPage.jsx"], "sourcesContent": ["import { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\r\nconst DashBoardPage = ({ setActiveTab }) => {\r\n  const revenueData = {\r\n    labels: [\r\n      \"T1\",\r\n      \"T2\",\r\n      \"T3\",\r\n      \"T4\",\r\n      \"T5\",\r\n      \"T6\",\r\n      \"T7\",\r\n      \"T8\",\r\n      \"T9\",\r\n      \"T10\",\r\n      \"T11\",\r\n      \"T12\",\r\n    ],\r\n    datasets: [\r\n      {\r\n        label: \"<PERSON><PERSON><PERSON> thu thực tế\",\r\n        data: [\r\n          12500, 13200, 15400, 18900, 21500, 25800, 28900, 27600, 24300, 19800,\r\n          16500, 22100,\r\n        ],\r\n        borderColor: \"#4361ee\",\r\n        backgroundColor: \"rgba(67, 97, 238, 0.1)\",\r\n        tension: 0.4,\r\n        fill: true,\r\n      },\r\n      {\r\n        label: \"<PERSON><PERSON> đoán (AI)\",\r\n        data: [\r\n          12000, 13000, 15000, 19000, 22000, 26000, 29000, 28000, 24000, 20000,\r\n          17000, 23000,\r\n        ],\r\n        borderColor: \"#f72585\",\r\n        borderDash: [5, 5],\r\n        tension: 0.4,\r\n        fill: false,\r\n      },\r\n    ],\r\n  };\r\n  // <PERSON><PERSON> liệu biểu đồ phân khúc khách hàng\r\n  const customerSegmentData = {\r\n    labels: [\r\n      \"<PERSON><PERSON><PERSON> nhân\",\r\n      \"<PERSON>ia đình\",\r\n      \"Cặp đôi\",\r\n      \"Du lịch một mình\",\r\n      \"Đoàn du lịch\",\r\n    ],\r\n    datasets: [\r\n      {\r\n        data: [35, 25, 20, 10, 10],\r\n        backgroundColor: [\r\n          \"#4361ee\",\r\n          \"#3a0ca3\",\r\n          \"#4cc9f0\",\r\n          \"#f72585\",\r\n          \"#7209b7\",\r\n        ],\r\n        borderWidth: 1,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Dữ liệu AI Insights\r\n  const aiInsights = [\r\n    {\r\n      id: 1,\r\n      title: \"Dự đoán nhu cầu cao điểm\",\r\n      description:\r\n        \"Dự kiến nhu cầu tăng 23% vào tháng 7-8. Cân nhắc tăng giá phòng và chuẩn bị nhân sự.\",\r\n      impact: \"high\",\r\n      category: \"demand\",\r\n    },\r\n    {\r\n      id: 2,\r\n      title: \"Phân tích tỷ lệ hoàn trả\",\r\n      description:\r\n        \"Tỷ lệ hoàn trả giảm 5% so với quý trước. Chính sách hủy linh hoạt đang phát huy hiệu quả.\",\r\n      impact: \"medium\",\r\n      category: \"operations\",\r\n    },\r\n    {\r\n      id: 3,\r\n      title: \"Phân khúc khách hàng mới nổi\",\r\n      description:\r\n        \"Phát hiện sự gia tăng 15% khách du lịch một mình. Cân nhắc tạo gói dịch vụ đặc biệt.\",\r\n      impact: \"medium\",\r\n      category: \"customers\",\r\n    },\r\n    {\r\n      id: 4,\r\n      title: \"Cơ hội tăng doanh thu\",\r\n      description:\r\n        \"Phân tích cho thấy tiềm năng tăng 18% doanh thu từ dịch vụ spa nếu có gói combo với phòng.\",\r\n      impact: \"high\",\r\n      category: \"revenue\",\r\n    },\r\n    {\r\n      id: 5,\r\n      title: \"Tối ưu hóa nhân sự\",\r\n      description:\r\n        \"Mô hình dự đoán cho thấy có thể giảm 7% chi phí nhân sự bằng cách điều chỉnh lịch làm việc.\",\r\n      impact: \"medium\",\r\n      category: \"operations\",\r\n    },\r\n  ];\r\n\r\n  // Dữ liệu đặt phòng gần đây\r\n  const recentBookings = [\r\n    {\r\n      id: \"B-7829\",\r\n      guest: \"Nguyễn Văn A\",\r\n      room: \"Deluxe 301\",\r\n      checkin: \"15/06/2025\",\r\n      checkout: \"18/06/2025\",\r\n      status: \"Đã xác nhận\",\r\n      amount: \"4,500,000 VND\",\r\n    },\r\n    {\r\n      id: \"B-7830\",\r\n      guest: \"Trần Thị B\",\r\n      room: \"Suite 502\",\r\n      checkin: \"16/06/2025\",\r\n      checkout: \"20/06/2025\",\r\n      status: \"Đã thanh toán\",\r\n      amount: \"12,800,000 VND\",\r\n    },\r\n    {\r\n      id: \"B-7831\",\r\n      guest: \"Lê Văn C\",\r\n      room: \"Standard 205\",\r\n      checkin: \"16/06/2025\",\r\n      checkout: \"17/06/2025\",\r\n      status: \"Đang xử lý\",\r\n      amount: \"1,200,000 VND\",\r\n    },\r\n    {\r\n      id: \"B-7832\",\r\n      guest: \"Phạm Thị D\",\r\n      room: \"Deluxe 305\",\r\n      checkin: \"17/06/2025\",\r\n      checkout: \"22/06/2025\",\r\n      status: \"Đã xác nhận\",\r\n      amount: \"7,500,000 VND\",\r\n    },\r\n    {\r\n      id: \"B-7833\",\r\n      guest: \"Hoàng Văn E\",\r\n      room: \"Suite 501\",\r\n      checkin: \"18/06/2025\",\r\n      checkout: \"25/06/2025\",\r\n      status: \"Đã thanh toán\",\r\n      amount: \"18,900,000 VND\",\r\n    },\r\n  ];\r\n  return (\r\n    <>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h4>Tổng quan khách sạn</h4>\r\n        <div className=\"d-flex\">\r\n          <select className=\"form-select form-select-sm me-2\">\r\n            <option>Hôm nay</option>\r\n            <option>Tuần này</option>\r\n            <option defaultValue=\"selected\">Tháng này</option>\r\n            <option>Quý này</option>\r\n            <option>Năm nay</option>\r\n          </select>\r\n          <button className=\"btn btn-sm btn-outline-primary\">\r\n            <i className=\"bi bi-download me-1\"></i> Xuất báo cáo\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* KPI Cards */}\r\n      <div className=\"row mb-4\">\r\n        <div className=\"col-md-3\">\r\n          <div className=\"card h-100\">\r\n            <div className=\"card-body\">\r\n              <div className=\"d-flex justify-content-between\">\r\n                <div>\r\n                  <h6 className=\"text-muted\">Tỷ lệ lấp đầy</h6>\r\n                  <h3 className=\"mb-0\">82.5%</h3>\r\n                  <small className=\"text-success\">\r\n                    <i className=\"bi bi-arrow-up\"></i> 4.2% so với tháng trước\r\n                  </small>\r\n                </div>\r\n                <div className=\"stat-icon light-primary\">\r\n                  <i className=\"bi bi-house-door fs-4\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-md-3\">\r\n          <div className=\"card h-100\">\r\n            <div className=\"card-body\">\r\n              <div className=\"d-flex justify-content-between\">\r\n                <div>\r\n                  <h6 className=\"text-muted\">Doanh thu trung bình/phòng</h6>\r\n                  <h3 className=\"mb-0\">2.4M</h3>\r\n                  <small className=\"text-success\">\r\n                    <i className=\"bi bi-arrow-up\"></i> 6.8% so với tháng trước\r\n                  </small>\r\n                </div>\r\n                <div className=\"stat-icon light-success\">\r\n                  <i className=\"bi bi-cash-stack fs-4\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-md-3\">\r\n          <div className=\"card h-100\">\r\n            <div className=\"card-body\">\r\n              <div className=\"d-flex justify-content-between\">\r\n                <div>\r\n                  <h6 className=\"text-muted\">Đánh giá trung bình</h6>\r\n                  <h3 className=\"mb-0\">4.7/5</h3>\r\n                  <small className=\"text-success\">\r\n                    <i className=\"bi bi-arrow-up\"></i> 0.2 so với tháng trước\r\n                  </small>\r\n                </div>\r\n                <div className=\"stat-icon light-warning\">\r\n                  <i className=\"bi bi-star fs-4\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-md-3\">\r\n          <div className=\"card h-100\">\r\n            <div className=\"card-body\">\r\n              <div className=\"d-flex justify-content-between\">\r\n                <div>\r\n                  <h6 className=\"text-muted\">Tỷ lệ khách quay lại</h6>\r\n                  <h3 className=\"mb-0\">28.3%</h3>\r\n                  <small className=\"text-danger\">\r\n                    <i className=\"bi bi-arrow-down\"></i> 1.5% so với tháng trước\r\n                  </small>\r\n                </div>\r\n                <div className=\"stat-icon light-info\">\r\n                  <i className=\"bi bi-repeat fs-4\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Charts */}\r\n      <div className=\"row mb-4\">\r\n        <div className=\"col-md-8\">\r\n          <div className=\"card\">\r\n            <div className=\"card-body\">\r\n              <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n                <div>\r\n                  <h5 className=\"card-title\">Phân tích doanh thu</h5>\r\n                  <p className=\"text-muted small mb-0\">\r\n                    So sánh doanh thu thực tế với dự đoán AI\r\n                  </p>\r\n                </div>\r\n                <div className=\"btn-group\">\r\n                  <button className=\"btn btn-sm btn-outline-secondary\">\r\n                    Ngày\r\n                  </button>\r\n                  <button className=\"btn btn-sm btn-outline-secondary\">\r\n                    Tuần\r\n                  </button>\r\n                  <button className=\"btn btn-sm btn-primary\">Tháng</button>\r\n                  <button className=\"btn btn-sm btn-outline-secondary\">\r\n                    Năm\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <Line\r\n                data={revenueData}\r\n                options={{\r\n                  responsive: true,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"top\",\r\n                    },\r\n                  },\r\n                  scales: {\r\n                    y: {\r\n                      beginAtZero: false,\r\n                      grid: {\r\n                        drawBorder: false,\r\n                      },\r\n                      ticks: {\r\n                        callback: (value) => value / 1000 + \"K\",\r\n                      },\r\n                    },\r\n                    x: {\r\n                      grid: {\r\n                        display: false,\r\n                      },\r\n                    },\r\n                  },\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-md-4\">\r\n          <div className=\"card h-100\">\r\n            <div className=\"card-body\">\r\n              <h5 className=\"card-title\">Phân khúc khách hàng</h5>\r\n              <p className=\"text-muted small mb-4\">\r\n                Phân tích theo loại khách hàng\r\n              </p>\r\n              <div className=\"chart-container\">\r\n                <Doughnut\r\n                  data={customerSegmentData}\r\n                  options={{\r\n                    responsive: true,\r\n                    maintainAspectRatio: false,\r\n                    plugins: {\r\n                      legend: {\r\n                        position: \"bottom\",\r\n                      },\r\n                    },\r\n                    cutout: \"70%\",\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Insights and Bookings */}\r\n      <div className=\"row\">\r\n        <div className=\"col-md-6\">\r\n          <div className=\"card\">\r\n            <div className=\"card-body\">\r\n              <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                <h5 className=\"card-title\">AI Insights</h5>\r\n                <a\r\n                  href=\"#\"\r\n                  className=\"btn btn-sm btn-link text-decoration-none\"\r\n                  onClick={() => {\r\n                    setActiveTab(\"ai-insights\");\r\n                  }}\r\n                >\r\n                  Xem tất cả\r\n                </a>\r\n              </div>\r\n              <div className=\"ai-insights-list\">\r\n                {aiInsights.slice(0, 3).map((insight) => (\r\n                  <div key={insight.id} className=\"ai-insight-item\">\r\n                    <div className=\"d-flex align-items-center mb-2\">\r\n                      <span\r\n                        className={`badge ${\r\n                          insight.impact === \"high\" ? \"bg-danger\" : \"bg-warning\"\r\n                        } me-2`}\r\n                      >\r\n                        {insight.impact === \"high\"\r\n                          ? \"Quan trọng\"\r\n                          : \"Trung bình\"}\r\n                      </span>\r\n                      <h6 className=\"mb-0\">{insight.title}</h6>\r\n                    </div>\r\n                    <p className=\"mb-0 small\">{insight.description}</p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-md-6\">\r\n          <div className=\"card\">\r\n            <div className=\"card-body\">\r\n              <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                <h5 className=\"card-title\">Đặt phòng gần đây</h5>\r\n                <a\r\n                  href=\"#\"\r\n                  className=\"btn btn-sm btn-link text-decoration-none\"\r\n                  onClick={() => {\r\n                    setActiveTab(\"bookings\");\r\n                  }}\r\n                >\r\n                  Xem tất cả\r\n                </a>\r\n              </div>\r\n              <div className=\"table-responsive\">\r\n                <table className=\"table table-hover\">\r\n                  <thead className=\"table-light\">\r\n                    <tr>\r\n                      <th>ID</th>\r\n                      <th>Khách hàng</th>\r\n                      <th>Check-in</th>\r\n                      <th>Trạng thái</th>\r\n                      <th>Số tiền</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {recentBookings.slice(0, 4).map((booking) => (\r\n                      <tr key={booking.id}>\r\n                        <td>{booking.id}</td>\r\n                        <td>{booking.guest}</td>\r\n                        <td>{booking.checkin}</td>\r\n                        <td>\r\n                          <span\r\n                            className={`badge ${\r\n                              booking.status === \"Đã xác nhận\"\r\n                                ? \"bg-success\"\r\n                                : booking.status === \"Đã thanh toán\"\r\n                                ? \"bg-primary\"\r\n                                : \"bg-warning\"\r\n                            }`}\r\n                          >\r\n                            {booking.status}\r\n                          </span>\r\n                        </td>\r\n                        <td>{booking.amount}</td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default DashBoardPage;"], "mappings": ";AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC3D,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAC1C,MAAMC,WAAW,GAAG;IAClBC,MAAM,EAAE,CACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,CACN;IACDC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,CACJ,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,CACb;MACDC,WAAW,EAAE,SAAS;MACtBC,eAAe,EAAE,wBAAwB;MACzCC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC,EACD;MACEL,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE,CACJ,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,CACb;MACDC,WAAW,EAAE,SAAS;MACtBI,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBF,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;EACD;EACA,MAAME,mBAAmB,GAAG;IAC1BT,MAAM,EAAE,CACN,YAAY,EACZ,UAAU,EACV,SAAS,EACT,kBAAkB,EAClB,cAAc,CACf;IACDC,QAAQ,EAAE,CACR;MACEE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC1BE,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDK,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,CACjB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EACT,sFAAsF;IACxFC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EACT,2FAA2F;IAC7FC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EACT,sFAAsF;IACxFC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EACT,4FAA4F;IAC9FC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EACT,6FAA6F;IAC/FC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACZ,CAAC,CACF;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB;IACEL,EAAE,EAAE,QAAQ;IACZM,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE;EACV,CAAC,EACD;IACEX,EAAE,EAAE,QAAQ;IACZM,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,eAAe;IACvBC,MAAM,EAAE;EACV,CAAC,EACD;IACEX,EAAE,EAAE,QAAQ;IACZM,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACEX,EAAE,EAAE,QAAQ;IACZM,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE;EACV,CAAC,EACD;IACEX,EAAE,EAAE,QAAQ;IACZM,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,eAAe;IACvBC,MAAM,EAAE;EACV,CAAC,CACF;EACD,oBACE7B,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBACE9B,OAAA;MAAK+B,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrE9B,OAAA;QAAA8B,QAAA,EAAI;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BnC,OAAA;QAAK+B,SAAS,EAAC,QAAQ;QAAAD,QAAA,gBACrB9B,OAAA;UAAQ+B,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBACjD9B,OAAA;YAAA8B,QAAA,EAAQ;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxBnC,OAAA;YAAA8B,QAAA,EAAQ;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzBnC,OAAA;YAAQoC,YAAY,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDnC,OAAA;YAAA8B,QAAA,EAAQ;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxBnC,OAAA;YAAA8B,QAAA,EAAQ;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACTnC,OAAA;UAAQ+B,SAAS,EAAC,gCAAgC;UAAAD,QAAA,gBAChD9B,OAAA;YAAG+B,SAAS,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,4BACzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK+B,SAAS,EAAC,UAAU;MAAAD,QAAA,gBACvB9B,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,eACxB9B,OAAA;cAAK+B,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7C9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI+B,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7CnC,OAAA;kBAAI+B,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/BnC,OAAA;kBAAO+B,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBAC7B9B,OAAA;oBAAG+B,SAAS,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,8CACpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNnC,OAAA;gBAAK+B,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,eACtC9B,OAAA;kBAAG+B,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,eACxB9B,OAAA;cAAK+B,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7C9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI+B,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1DnC,OAAA;kBAAI+B,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9BnC,OAAA;kBAAO+B,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBAC7B9B,OAAA;oBAAG+B,SAAS,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,8CACpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNnC,OAAA;gBAAK+B,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,eACtC9B,OAAA;kBAAG+B,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,eACxB9B,OAAA;cAAK+B,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7C9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI+B,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnDnC,OAAA;kBAAI+B,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/BnC,OAAA;kBAAO+B,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBAC7B9B,OAAA;oBAAG+B,SAAS,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,6CACpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNnC,OAAA;gBAAK+B,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,eACtC9B,OAAA;kBAAG+B,SAAS,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,eACxB9B,OAAA;cAAK+B,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7C9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI+B,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpDnC,OAAA;kBAAI+B,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/BnC,OAAA;kBAAO+B,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBAC5B9B,OAAA;oBAAG+B,SAAS,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,8CACtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNnC,OAAA;gBAAK+B,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACnC9B,OAAA;kBAAG+B,SAAS,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK+B,SAAS,EAAC,UAAU;MAAAD,QAAA,gBACvB9B,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB9B,OAAA;cAAK+B,SAAS,EAAC,wDAAwD;cAAAD,QAAA,gBACrE9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAI+B,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnDnC,OAAA;kBAAG+B,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNnC,OAAA;gBAAK+B,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxB9B,OAAA;kBAAQ+B,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAAC;gBAErD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnC,OAAA;kBAAQ+B,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAAC;gBAErD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnC,OAAA;kBAAQ+B,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzDnC,OAAA;kBAAQ+B,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAAC;gBAErD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnC,OAAA,CAACL,IAAI;cACHc,IAAI,EAAEJ,WAAY;cAClBgC,OAAO,EAAE;gBACPC,UAAU,EAAE,IAAI;gBAChBC,OAAO,EAAE;kBACPC,MAAM,EAAE;oBACNC,QAAQ,EAAE;kBACZ;gBACF,CAAC;gBACDC,MAAM,EAAE;kBACNC,CAAC,EAAE;oBACDC,WAAW,EAAE,KAAK;oBAClBC,IAAI,EAAE;sBACJC,UAAU,EAAE;oBACd,CAAC;oBACDC,KAAK,EAAE;sBACLC,QAAQ,EAAGC,KAAK,IAAKA,KAAK,GAAG,IAAI,GAAG;oBACtC;kBACF,CAAC;kBACDC,CAAC,EAAE;oBACDL,IAAI,EAAE;sBACJM,OAAO,EAAE;oBACX;kBACF;gBACF;cACF;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB9B,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDnC,OAAA;cAAG+B,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAC;YAErC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnC,OAAA;cAAK+B,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC9B9B,OAAA,CAACF,QAAQ;gBACPW,IAAI,EAAEM,mBAAoB;gBAC1BsB,OAAO,EAAE;kBACPC,UAAU,EAAE,IAAI;kBAChBc,mBAAmB,EAAE,KAAK;kBAC1Bb,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNC,QAAQ,EAAE;oBACZ;kBACF,CAAC;kBACDY,MAAM,EAAE;gBACV;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK+B,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClB9B,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB9B,OAAA;cAAK+B,SAAS,EAAC,wDAAwD;cAAAD,QAAA,gBACrE9B,OAAA;gBAAI+B,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CnC,OAAA;gBACEsD,IAAI,EAAC,GAAG;gBACRvB,SAAS,EAAC,0CAA0C;gBACpDwB,OAAO,EAAEA,CAAA,KAAM;kBACbnD,YAAY,CAAC,aAAa,CAAC;gBAC7B,CAAE;gBAAA0B,QAAA,EACH;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNnC,OAAA;cAAK+B,SAAS,EAAC,kBAAkB;cAAAD,QAAA,EAC9Bb,UAAU,CAACuC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,OAAO,iBAClC1D,OAAA;gBAAsB+B,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC/C9B,OAAA;kBAAK+B,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7C9B,OAAA;oBACE+B,SAAS,EAAE,SACT2B,OAAO,CAACrC,MAAM,KAAK,MAAM,GAAG,WAAW,GAAG,YAAY,OAChD;oBAAAS,QAAA,EAEP4B,OAAO,CAACrC,MAAM,KAAK,MAAM,GACtB,YAAY,GACZ;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACPnC,OAAA;oBAAI+B,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAE4B,OAAO,CAACvC;kBAAK;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNnC,OAAA;kBAAG+B,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAE4B,OAAO,CAACtC;gBAAW;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA,GAb3CuB,OAAO,CAACxC,EAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvB9B,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnB9B,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB9B,OAAA;cAAK+B,SAAS,EAAC,wDAAwD;cAAAD,QAAA,gBACrE9B,OAAA;gBAAI+B,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDnC,OAAA;gBACEsD,IAAI,EAAC,GAAG;gBACRvB,SAAS,EAAC,0CAA0C;gBACpDwB,OAAO,EAAEA,CAAA,KAAM;kBACbnD,YAAY,CAAC,UAAU,CAAC;gBAC1B,CAAE;gBAAA0B,QAAA,EACH;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNnC,OAAA;cAAK+B,SAAS,EAAC,kBAAkB;cAAAD,QAAA,eAC/B9B,OAAA;gBAAO+B,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAClC9B,OAAA;kBAAO+B,SAAS,EAAC,aAAa;kBAAAD,QAAA,eAC5B9B,OAAA;oBAAA8B,QAAA,gBACE9B,OAAA;sBAAA8B,QAAA,EAAI;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACXnC,OAAA;sBAAA8B,QAAA,EAAI;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBnC,OAAA;sBAAA8B,QAAA,EAAI;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjBnC,OAAA;sBAAA8B,QAAA,EAAI;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBnC,OAAA;sBAAA8B,QAAA,EAAI;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRnC,OAAA;kBAAA8B,QAAA,EACGP,cAAc,CAACiC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEE,OAAO,iBACtC3D,OAAA;oBAAA8B,QAAA,gBACE9B,OAAA;sBAAA8B,QAAA,EAAK6B,OAAO,CAACzC;oBAAE;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrBnC,OAAA;sBAAA8B,QAAA,EAAK6B,OAAO,CAACnC;oBAAK;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxBnC,OAAA;sBAAA8B,QAAA,EAAK6B,OAAO,CAACjC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1BnC,OAAA;sBAAA8B,QAAA,eACE9B,OAAA;wBACE+B,SAAS,EAAE,SACT4B,OAAO,CAAC/B,MAAM,KAAK,aAAa,GAC5B,YAAY,GACZ+B,OAAO,CAAC/B,MAAM,KAAK,eAAe,GAClC,YAAY,GACZ,YAAY,EACf;wBAAAE,QAAA,EAEF6B,OAAO,CAAC/B;sBAAM;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLnC,OAAA;sBAAA8B,QAAA,EAAK6B,OAAO,CAAC9B;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,GAjBlBwB,OAAO,CAACzC,EAAE;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkBf,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACyB,EAAA,GA7aIzD,aAAa;AA+anB,eAAeA,aAAa;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}