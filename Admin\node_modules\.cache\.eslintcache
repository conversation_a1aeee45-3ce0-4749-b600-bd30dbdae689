[{"E:\\WDP301_UROOM\\Admin\\src\\index.js": "1", "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Admin\\src\\App.js": "3", "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js": "5", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js": "6", "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx": "7", "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx": "8", "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx": "9", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx": "10", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx": "11", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx": "12", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx": "13", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx": "14", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx": "15", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx": "16", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx": "17", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx": "18", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx": "19", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx": "20", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx": "21", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx": "22", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js": "23", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js": "24", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx": "25", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx": "26", "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js": "27", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx": "28", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js": "29", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js": "30", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js": "31", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js": "32", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js": "33", "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx": "34", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js": "35", "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx": "36", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx": "37", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js": "38", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js": "39", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js": "40", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js": "41", "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx": "42", "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx": "43", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx": "44", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js": "45", "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx": "46", "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx": "47", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js": "48", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js": "49", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js": "50", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js": "51", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js": "52", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx": "53", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js": "54", "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js": "55", "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js": "56", "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js": "57", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js": "58", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js": "59", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js": "60", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js": "61", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\reducer.js": "62", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\actions.js": "63", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\saga.js": "64", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\factories.js": "65"}, {"size": 839, "mtime": 1751249522444, "results": "66", "hashOfConfig": "67"}, {"size": 375, "mtime": 1750045607470, "results": "68", "hashOfConfig": "67"}, {"size": 4119, "mtime": 1751259196611, "results": "69", "hashOfConfig": "67"}, {"size": 1291, "mtime": 1751249522455, "results": "70", "hashOfConfig": "67"}, {"size": 1451, "mtime": 1751249522455, "results": "71", "hashOfConfig": "67"}, {"size": 1302, "mtime": 1751259177977, "results": "72", "hashOfConfig": "67"}, {"size": 1474, "mtime": 1750045607464, "results": "73", "hashOfConfig": "67"}, {"size": 16232, "mtime": 1751249522445, "results": "74", "hashOfConfig": "67"}, {"size": 10290, "mtime": 1751249522447, "results": "75", "hashOfConfig": "67"}, {"size": 14473, "mtime": 1751249522451, "results": "76", "hashOfConfig": "67"}, {"size": 6183, "mtime": 1751249522452, "results": "77", "hashOfConfig": "67"}, {"size": 16060, "mtime": 1751249522446, "results": "78", "hashOfConfig": "67"}, {"size": 10088, "mtime": 1751249522446, "results": "79", "hashOfConfig": "67"}, {"size": 3050, "mtime": 1751249522448, "results": "80", "hashOfConfig": "67"}, {"size": 1723, "mtime": 1751249522448, "results": "81", "hashOfConfig": "67"}, {"size": 6523, "mtime": 1751249522448, "results": "82", "hashOfConfig": "67"}, {"size": 18076, "mtime": 1751249522448, "results": "83", "hashOfConfig": "67"}, {"size": 5423, "mtime": 1751249522449, "results": "84", "hashOfConfig": "67"}, {"size": 3718, "mtime": 1751249522448, "results": "85", "hashOfConfig": "67"}, {"size": 7339, "mtime": 1751249522450, "results": "86", "hashOfConfig": "67"}, {"size": 7403, "mtime": 1751249522449, "results": "87", "hashOfConfig": "67"}, {"size": 7520, "mtime": 1751249522449, "results": "88", "hashOfConfig": "67"}, {"size": 733, "mtime": 1751503100657, "results": "89", "hashOfConfig": "67"}, {"size": 568, "mtime": 1751503134652, "results": "90", "hashOfConfig": "67"}, {"size": 8490, "mtime": 1751249522450, "results": "91", "hashOfConfig": "67"}, {"size": 15641, "mtime": 1751249522449, "results": "92", "hashOfConfig": "67"}, {"size": 551, "mtime": 1751249522456, "results": "93", "hashOfConfig": "67"}, {"size": 25311, "mtime": 1751249522450, "results": "94", "hashOfConfig": "67"}, {"size": 1433, "mtime": 1751249522452, "results": "95", "hashOfConfig": "67"}, {"size": 1271, "mtime": 1751249522452, "results": "96", "hashOfConfig": "67"}, {"size": 196, "mtime": 1751249522452, "results": "97", "hashOfConfig": "67"}, {"size": 466, "mtime": 1751249522454, "results": "98", "hashOfConfig": "67"}, {"size": 764, "mtime": 1751249522452, "results": "99", "hashOfConfig": "67"}, {"size": 2422, "mtime": 1751249522446, "results": "100", "hashOfConfig": "67"}, {"size": 2230, "mtime": 1751249522452, "results": "101", "hashOfConfig": "67"}, {"size": 6543, "mtime": 1751249522446, "results": "102", "hashOfConfig": "67"}, {"size": 31702, "mtime": 1751502458845, "results": "103", "hashOfConfig": "67"}, {"size": 2761, "mtime": 1751249522454, "results": "104", "hashOfConfig": "67"}, {"size": 955, "mtime": 1751249522454, "results": "105", "hashOfConfig": "67"}, {"size": 9581, "mtime": 1751249522452, "results": "106", "hashOfConfig": "67"}, {"size": 1225, "mtime": 1751249522452, "results": "107", "hashOfConfig": "67"}, {"size": 25958, "mtime": 1751249522450, "results": "108", "hashOfConfig": "67"}, {"size": 13666, "mtime": 1751503610490, "results": "109", "hashOfConfig": "67"}, {"size": 2140, "mtime": 1751249522449, "results": "110", "hashOfConfig": "67"}, {"size": 3227, "mtime": 1751249522455, "results": "111", "hashOfConfig": "67"}, {"size": 2348, "mtime": 1750045607431, "results": "112", "hashOfConfig": "67"}, {"size": 1493, "mtime": 1750045607432, "results": "113", "hashOfConfig": "67"}, {"size": 2085, "mtime": 1751249522453, "results": "114", "hashOfConfig": "67"}, {"size": 551, "mtime": 1751249522453, "results": "115", "hashOfConfig": "67"}, {"size": 342, "mtime": 1751249522452, "results": "116", "hashOfConfig": "67"}, {"size": 322, "mtime": 1751249522453, "results": "117", "hashOfConfig": "67"}, {"size": 595, "mtime": 1751249522454, "results": "118", "hashOfConfig": "67"}, {"size": 18835, "mtime": 1751249522451, "results": "119", "hashOfConfig": "67"}, {"size": 377, "mtime": 1751249522453, "results": "120", "hashOfConfig": "67"}, {"size": 3361, "mtime": 1751503027775, "results": "121", "hashOfConfig": "67"}, {"size": 693, "mtime": 1751249522445, "results": "122", "hashOfConfig": "67"}, {"size": 2658, "mtime": 1751249522445, "results": "123", "hashOfConfig": "67"}, {"size": 7219, "mtime": 1751502458848, "results": "124", "hashOfConfig": "67"}, {"size": 4304, "mtime": 1751502458847, "results": "125", "hashOfConfig": "67"}, {"size": 5605, "mtime": 1751502458847, "results": "126", "hashOfConfig": "67"}, {"size": 1732, "mtime": 1751502458847, "results": "127", "hashOfConfig": "67"}, {"size": 1482, "mtime": 1751503065967, "results": "128", "hashOfConfig": "67"}, {"size": 859, "mtime": 1751503048879, "results": "129", "hashOfConfig": "67"}, {"size": 1434, "mtime": 1751503077096, "results": "130", "hashOfConfig": "67"}, {"size": 274, "mtime": 1751503054991, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tcbxdp", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WDP301_UROOM\\Admin\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\App.js", ["327"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx", ["328"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx", ["329", "330", "331", "332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx", ["347", "348", "349", "350"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx", ["351", "352"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx", ["353"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx", ["354", "355", "356"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx", ["357"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx", ["358", "359"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx", ["360", "361"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx", ["362", "363", "364", "365", "366", "367", "368", "369"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx", ["370", "371", "372", "373"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx", ["374", "375"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx", ["376", "377", "378", "379", "380"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx", ["381", "382", "383", "384", "385", "386", "387"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx", ["388", "389"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx", ["390", "391", "392", "393", "394"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx", ["395"], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx", ["396"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx", ["397", "398", "399", "400", "401", "402"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx", ["403", "404", "405", "406", "407"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx", ["408", "409"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx", ["410", "411", "412", "413", "414", "415", "416", "417"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx", ["418", "419", "420"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js", ["421", "422", "423"], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js", ["424"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\factories.js", [], [], {"ruleId": "425", "severity": 1, "message": "426", "line": 40, "column": 6, "nodeType": "427", "endLine": 40, "endColumn": 17, "suggestions": "428"}, {"ruleId": "429", "severity": 1, "message": "430", "line": 1, "column": 17, "nodeType": "431", "messageId": "432", "endLine": 1, "endColumn": 26}, {"ruleId": "429", "severity": 1, "message": "433", "line": 4, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 4, "endColumn": 14}, {"ruleId": "429", "severity": 1, "message": "434", "line": 4, "column": 16, "nodeType": "431", "messageId": "432", "endLine": 4, "endColumn": 19}, {"ruleId": "429", "severity": 1, "message": "435", "line": 4, "column": 21, "nodeType": "431", "messageId": "432", "endLine": 4, "endColumn": 24}, {"ruleId": "429", "severity": 1, "message": "436", "line": 4, "column": 26, "nodeType": "431", "messageId": "432", "endLine": 4, "endColumn": 34}, {"ruleId": "429", "severity": 1, "message": "437", "line": 21, "column": 8, "nodeType": "431", "messageId": "432", "endLine": 21, "endColumn": 25}, {"ruleId": "429", "severity": 1, "message": "438", "line": 28, "column": 8, "nodeType": "431", "messageId": "432", "endLine": 28, "endColumn": 29}, {"ruleId": "429", "severity": 1, "message": "439", "line": 65, "column": 28, "nodeType": "431", "messageId": "432", "endLine": 65, "endColumn": 47}, {"ruleId": "429", "severity": 1, "message": "440", "line": 67, "column": 25, "nodeType": "431", "messageId": "432", "endLine": 67, "endColumn": 41}, {"ruleId": "441", "severity": 1, "message": "442", "line": 178, "column": 19, "nodeType": "443", "endLine": 178, "endColumn": 76}, {"ruleId": "441", "severity": 1, "message": "442", "line": 188, "column": 19, "nodeType": "443", "endLine": 188, "endColumn": 78}, {"ruleId": "441", "severity": 1, "message": "442", "line": 198, "column": 19, "nodeType": "443", "endLine": 198, "endColumn": 76}, {"ruleId": "441", "severity": 1, "message": "442", "line": 208, "column": 19, "nodeType": "443", "endLine": 208, "endColumn": 77}, {"ruleId": "441", "severity": 1, "message": "442", "line": 218, "column": 19, "nodeType": "443", "endLine": 218, "endColumn": 76}, {"ruleId": "441", "severity": 1, "message": "442", "line": 233, "column": 19, "nodeType": "443", "endLine": 233, "endColumn": 75}, {"ruleId": "441", "severity": 1, "message": "442", "line": 243, "column": 19, "nodeType": "443", "endLine": 243, "endColumn": 84}, {"ruleId": "441", "severity": 1, "message": "442", "line": 253, "column": 19, "nodeType": "443", "endLine": 253, "endColumn": 74}, {"ruleId": "441", "severity": 1, "message": "442", "line": 268, "column": 19, "nodeType": "443", "endLine": 268, "endColumn": 76}, {"ruleId": "441", "severity": 1, "message": "444", "line": 376, "column": 25, "nodeType": "443", "endLine": 391, "endColumn": 26}, {"ruleId": "429", "severity": 1, "message": "445", "line": 9, "column": 3, "nodeType": "431", "messageId": "432", "endLine": 9, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "446", "line": 18, "column": 13, "nodeType": "431", "messageId": "432", "endLine": 18, "endColumn": 20}, {"ruleId": "429", "severity": 1, "message": "447", "line": 23, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 23, "endColumn": 17}, {"ruleId": "429", "severity": 1, "message": "448", "line": 24, "column": 19, "nodeType": "431", "messageId": "432", "endLine": 24, "endColumn": 29}, {"ruleId": "429", "severity": 1, "message": "449", "line": 8, "column": 3, "nodeType": "431", "messageId": "432", "endLine": 8, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "445", "line": 9, "column": 3, "nodeType": "431", "messageId": "432", "endLine": 9, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "450", "line": 85, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 85, "endColumn": 20}, {"ruleId": "429", "severity": 1, "message": "451", "line": 23, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 23, "endColumn": 21}, {"ruleId": "429", "severity": 1, "message": "452", "line": 26, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 26, "endColumn": 21}, {"ruleId": "429", "severity": 1, "message": "453", "line": 64, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 64, "endColumn": 23}, {"ruleId": "429", "severity": 1, "message": "430", "line": 1, "column": 20, "nodeType": "431", "messageId": "432", "endLine": 1, "endColumn": 29}, {"ruleId": "429", "severity": 1, "message": "454", "line": 1, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 1, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "445", "line": 1, "column": 29, "nodeType": "431", "messageId": "432", "endLine": 1, "endColumn": 39}, {"ruleId": "429", "severity": 1, "message": "446", "line": 2, "column": 13, "nodeType": "431", "messageId": "432", "endLine": 2, "endColumn": 20}, {"ruleId": "429", "severity": 1, "message": "447", "line": 6, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 6, "endColumn": 17}, {"ruleId": "429", "severity": 1, "message": "446", "line": 6, "column": 13, "nodeType": "431", "messageId": "432", "endLine": 6, "endColumn": 20}, {"ruleId": "429", "severity": 1, "message": "455", "line": 8, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 8, "endColumn": 18}, {"ruleId": "429", "severity": 1, "message": "447", "line": 10, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 10, "endColumn": 17}, {"ruleId": "441", "severity": 1, "message": "442", "line": 179, "column": 15, "nodeType": "443", "endLine": 179, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 184, "column": 15, "nodeType": "443", "endLine": 184, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 189, "column": 15, "nodeType": "443", "endLine": 189, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 194, "column": 15, "nodeType": "443", "endLine": 194, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 199, "column": 15, "nodeType": "443", "endLine": 199, "endColumn": 49}, {"ruleId": "429", "severity": 1, "message": "456", "line": 2, "column": 3, "nodeType": "431", "messageId": "432", "endLine": 2, "endColumn": 12}, {"ruleId": "429", "severity": 1, "message": "457", "line": 23, "column": 29, "nodeType": "431", "messageId": "432", "endLine": 23, "endColumn": 40}, {"ruleId": "429", "severity": 1, "message": "447", "line": 43, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 43, "endColumn": 17}, {"ruleId": "441", "severity": 1, "message": "444", "line": 422, "column": 31, "nodeType": "443", "endLine": 422, "endColumn": 34}, {"ruleId": "429", "severity": 1, "message": "457", "line": 4, "column": 29, "nodeType": "431", "messageId": "432", "endLine": 4, "endColumn": 40}, {"ruleId": "429", "severity": 1, "message": "458", "line": 6, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 6, "endColumn": 15}, {"ruleId": "429", "severity": 1, "message": "457", "line": 4, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 4, "endColumn": 21}, {"ruleId": "429", "severity": 1, "message": "459", "line": 10, "column": 8, "nodeType": "431", "messageId": "432", "endLine": 10, "endColumn": 19}, {"ruleId": "429", "severity": 1, "message": "460", "line": 12, "column": 8, "nodeType": "431", "messageId": "432", "endLine": 12, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "461", "line": 15, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 15, "endColumn": 17}, {"ruleId": "429", "severity": 1, "message": "462", "line": 63, "column": 9, "nodeType": "431", "messageId": "432", "endLine": 63, "endColumn": 33}, {"ruleId": "429", "severity": 1, "message": "454", "line": 3, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 3, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "463", "line": 3, "column": 21, "nodeType": "431", "messageId": "432", "endLine": 3, "endColumn": 24}, {"ruleId": "441", "severity": 1, "message": "442", "line": 209, "column": 15, "nodeType": "443", "endLine": 209, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 214, "column": 15, "nodeType": "443", "endLine": 214, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 219, "column": 15, "nodeType": "443", "endLine": 219, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 224, "column": 15, "nodeType": "443", "endLine": 224, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 229, "column": 15, "nodeType": "443", "endLine": 229, "endColumn": 49}, {"ruleId": "429", "severity": 1, "message": "457", "line": 4, "column": 29, "nodeType": "431", "messageId": "432", "endLine": 4, "endColumn": 40}, {"ruleId": "441", "severity": 1, "message": "444", "line": 215, "column": 17, "nodeType": "443", "endLine": 219, "endColumn": 52}, {"ruleId": "429", "severity": 1, "message": "457", "line": 3, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 3, "endColumn": 21}, {"ruleId": "429", "severity": 1, "message": "460", "line": 10, "column": 8, "nodeType": "431", "messageId": "432", "endLine": 10, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "464", "line": 22, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 22, "endColumn": 19}, {"ruleId": "429", "severity": 1, "message": "465", "line": 27, "column": 17, "nodeType": "431", "messageId": "432", "endLine": 27, "endColumn": 25}, {"ruleId": "441", "severity": 1, "message": "442", "line": 203, "column": 17, "nodeType": "443", "endLine": 203, "endColumn": 89}, {"ruleId": "429", "severity": 1, "message": "466", "line": 13, "column": 8, "nodeType": "431", "messageId": "432", "endLine": 13, "endColumn": 19}, {"ruleId": "425", "severity": 1, "message": "467", "line": 132, "column": 6, "nodeType": "427", "endLine": 132, "endColumn": 8, "suggestions": "468"}, {"ruleId": "441", "severity": 1, "message": "444", "line": 19, "column": 11, "nodeType": "443", "endLine": 24, "endColumn": 12}, {"ruleId": "441", "severity": 1, "message": "444", "line": 30, "column": 11, "nodeType": "443", "endLine": 35, "endColumn": 12}, {"ruleId": "441", "severity": 1, "message": "444", "line": 41, "column": 11, "nodeType": "443", "endLine": 46, "endColumn": 12}, {"ruleId": "441", "severity": 1, "message": "444", "line": 52, "column": 11, "nodeType": "443", "endLine": 57, "endColumn": 12}, {"ruleId": "441", "severity": 1, "message": "444", "line": 63, "column": 11, "nodeType": "443", "endLine": 68, "endColumn": 12}, {"ruleId": "441", "severity": 1, "message": "444", "line": 73, "column": 11, "nodeType": "443", "endLine": 78, "endColumn": 12}, {"ruleId": "441", "severity": 1, "message": "442", "line": 171, "column": 15, "nodeType": "443", "endLine": 171, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 176, "column": 15, "nodeType": "443", "endLine": 176, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 181, "column": 15, "nodeType": "443", "endLine": 181, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 186, "column": 15, "nodeType": "443", "endLine": 186, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "442", "line": 191, "column": 15, "nodeType": "443", "endLine": 191, "endColumn": 49}, {"ruleId": "429", "severity": 1, "message": "469", "line": 16, "column": 3, "nodeType": "431", "messageId": "432", "endLine": 16, "endColumn": 11}, {"ruleId": "429", "severity": 1, "message": "470", "line": 24, "column": 3, "nodeType": "431", "messageId": "432", "endLine": 24, "endColumn": 11}, {"ruleId": "429", "severity": 1, "message": "471", "line": 10, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 10, "endColumn": 26}, {"ruleId": "429", "severity": 1, "message": "472", "line": 33, "column": 10, "nodeType": "431", "messageId": "432", "endLine": 33, "endColumn": 20}, {"ruleId": "425", "severity": 1, "message": "473", "line": 80, "column": 6, "nodeType": "427", "endLine": 80, "endColumn": 8, "suggestions": "474"}, {"ruleId": "425", "severity": 1, "message": "475", "line": 84, "column": 6, "nodeType": "427", "endLine": 84, "endColumn": 20, "suggestions": "476"}, {"ruleId": "477", "severity": 1, "message": "478", "line": 119, "column": 29, "nodeType": "479", "messageId": "480", "endLine": 119, "endColumn": 31}, {"ruleId": "425", "severity": 1, "message": "473", "line": 142, "column": 6, "nodeType": "427", "endLine": 142, "endColumn": 44, "suggestions": "481"}, {"ruleId": "477", "severity": 1, "message": "478", "line": 417, "column": 46, "nodeType": "479", "messageId": "480", "endLine": 417, "endColumn": 48}, {"ruleId": "477", "severity": 1, "message": "478", "line": 430, "column": 38, "nodeType": "479", "messageId": "480", "endLine": 430, "endColumn": 40}, {"ruleId": "429", "severity": 1, "message": "434", "line": 2, "column": 16, "nodeType": "431", "messageId": "432", "endLine": 2, "endColumn": 19}, {"ruleId": "441", "severity": 1, "message": "442", "line": 269, "column": 13, "nodeType": "443", "endLine": 273, "endColumn": 14}, {"ruleId": "441", "severity": 1, "message": "442", "line": 332, "column": 13, "nodeType": "443", "endLine": 336, "endColumn": 14}, {"ruleId": "482", "severity": 1, "message": "483", "line": 10, "column": 3, "nodeType": "484", "messageId": "480", "endLine": 10, "endColumn": 17}, {"ruleId": "482", "severity": 1, "message": "485", "line": 11, "column": 3, "nodeType": "484", "messageId": "480", "endLine": 11, "endColumn": 18}, {"ruleId": "482", "severity": 1, "message": "486", "line": 48, "column": 3, "nodeType": "484", "messageId": "480", "endLine": 48, "endColumn": 28}, {"ruleId": "487", "severity": 1, "message": "488", "line": 37, "column": 1, "nodeType": "489", "endLine": 108, "endColumn": 3}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["490"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Line' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Doughnut' is defined but never used.", "'AccountManagement' is defined but never used.", "'ListFeedbackAdminPage' is defined but never used.", "'setSidebarCollapsed' is assigned a value but never used.", "'setNotifications' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'Routers' is defined but never used.", "'navigate' is assigned a value but never used.", "'setReviews' is assigned a value but never used.", "'Pagination' is defined but never used.", "'getSeverity' is assigned a value but never used.", "'handleAccept' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'Col' is defined but never used.", "'useState' is defined but never used.", "'Container' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'Route' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'Row' is defined but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'GoogleLogin' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRefunds'. Either include it or remove the dependency array.", ["491"], "'Dropdown' is defined but never used.", "'FaFilter' is defined but never used.", "'initializeSocket' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["492"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["493"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["494"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "495", "fix": "496"}, {"desc": "497", "fix": "498"}, {"desc": "499", "fix": "500"}, {"desc": "501", "fix": "502"}, {"desc": "503", "fix": "504"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "505", "text": "506"}, "Update the dependencies array to be: [fetchRefunds]", {"range": "507", "text": "508"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "509", "text": "510"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "511", "text": "512"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "513", "text": "514"}, [1916, 1927], "[Auth?._id, dispatch]", [3634, 3636], "[fetchRefunds]", [2772, 2774], "[fetchAllUser]", [2831, 2845], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4480, 4518], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]"]