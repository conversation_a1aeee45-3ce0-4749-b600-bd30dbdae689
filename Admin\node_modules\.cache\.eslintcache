[{"E:\\WDP301_UROOM\\Admin\\src\\index.js": "1", "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Admin\\src\\App.js": "3", "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js": "5", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js": "6", "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx": "7", "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx": "8", "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx": "9", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx": "10", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx": "11", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx": "12", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx": "13", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx": "14", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx": "15", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx": "16", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx": "17", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx": "18", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx": "19", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx": "20", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx": "21", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx": "22", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js": "23", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js": "24", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx": "25", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx": "26", "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js": "27", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx": "28", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js": "29", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js": "30", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js": "31", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js": "32", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js": "33", "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx": "34", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js": "35", "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx": "36", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx": "37", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js": "38", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js": "39", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js": "40", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js": "41", "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx": "42", "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx": "43", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx": "44", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js": "45", "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx": "46", "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx": "47", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js": "48", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js": "49", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js": "50", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js": "51", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js": "52", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx": "53", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js": "54", "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js": "55", "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js": "56", "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js": "57", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js": "58", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js": "59", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js": "60", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js": "61", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\reducer.js": "62", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\actions.js": "63", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\saga.js": "64", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\factories.js": "65", "E:\\WDP301_UROOM\\Admin\\src\\utils\\fonts.js": "66"}, {"size": 839, "mtime": 1751249522444, "results": "67", "hashOfConfig": "68"}, {"size": 375, "mtime": 1750045607470, "results": "69", "hashOfConfig": "68"}, {"size": 4119, "mtime": 1751259196611, "results": "70", "hashOfConfig": "68"}, {"size": 1291, "mtime": 1751249522455, "results": "71", "hashOfConfig": "68"}, {"size": 1451, "mtime": 1751249522455, "results": "72", "hashOfConfig": "68"}, {"size": 1302, "mtime": 1751259177977, "results": "73", "hashOfConfig": "68"}, {"size": 1474, "mtime": 1750045607464, "results": "74", "hashOfConfig": "68"}, {"size": 16232, "mtime": 1751249522445, "results": "75", "hashOfConfig": "68"}, {"size": 10290, "mtime": 1751249522447, "results": "76", "hashOfConfig": "68"}, {"size": 14473, "mtime": 1751249522451, "results": "77", "hashOfConfig": "68"}, {"size": 6183, "mtime": 1751249522452, "results": "78", "hashOfConfig": "68"}, {"size": 16060, "mtime": 1751249522446, "results": "79", "hashOfConfig": "68"}, {"size": 10088, "mtime": 1751249522446, "results": "80", "hashOfConfig": "68"}, {"size": 3050, "mtime": 1751249522448, "results": "81", "hashOfConfig": "68"}, {"size": 1723, "mtime": 1751249522448, "results": "82", "hashOfConfig": "68"}, {"size": 6523, "mtime": 1751249522448, "results": "83", "hashOfConfig": "68"}, {"size": 18076, "mtime": 1751249522448, "results": "84", "hashOfConfig": "68"}, {"size": 5423, "mtime": 1751249522449, "results": "85", "hashOfConfig": "68"}, {"size": 3718, "mtime": 1751249522448, "results": "86", "hashOfConfig": "68"}, {"size": 7339, "mtime": 1751249522450, "results": "87", "hashOfConfig": "68"}, {"size": 7403, "mtime": 1751249522449, "results": "88", "hashOfConfig": "68"}, {"size": 7520, "mtime": 1751249522449, "results": "89", "hashOfConfig": "68"}, {"size": 733, "mtime": 1751503100657, "results": "90", "hashOfConfig": "68"}, {"size": 568, "mtime": 1751503134652, "results": "91", "hashOfConfig": "68"}, {"size": 8490, "mtime": 1751249522450, "results": "92", "hashOfConfig": "68"}, {"size": 15641, "mtime": 1751249522449, "results": "93", "hashOfConfig": "68"}, {"size": 551, "mtime": 1751249522456, "results": "94", "hashOfConfig": "68"}, {"size": 25311, "mtime": 1751249522450, "results": "95", "hashOfConfig": "68"}, {"size": 1433, "mtime": 1751249522452, "results": "96", "hashOfConfig": "68"}, {"size": 1271, "mtime": 1751249522452, "results": "97", "hashOfConfig": "68"}, {"size": 196, "mtime": 1751249522452, "results": "98", "hashOfConfig": "68"}, {"size": 466, "mtime": 1751249522454, "results": "99", "hashOfConfig": "68"}, {"size": 764, "mtime": 1751249522452, "results": "100", "hashOfConfig": "68"}, {"size": 2422, "mtime": 1751249522446, "results": "101", "hashOfConfig": "68"}, {"size": 2230, "mtime": 1751249522452, "results": "102", "hashOfConfig": "68"}, {"size": 6543, "mtime": 1751249522446, "results": "103", "hashOfConfig": "68"}, {"size": 31702, "mtime": 1751502458845, "results": "104", "hashOfConfig": "68"}, {"size": 2761, "mtime": 1751249522454, "results": "105", "hashOfConfig": "68"}, {"size": 955, "mtime": 1751249522454, "results": "106", "hashOfConfig": "68"}, {"size": 9581, "mtime": 1751249522452, "results": "107", "hashOfConfig": "68"}, {"size": 1225, "mtime": 1751249522452, "results": "108", "hashOfConfig": "68"}, {"size": 25958, "mtime": 1751249522450, "results": "109", "hashOfConfig": "68"}, {"size": 28357, "mtime": 1751510977872, "results": "110", "hashOfConfig": "68"}, {"size": 2140, "mtime": 1751249522449, "results": "111", "hashOfConfig": "68"}, {"size": 3227, "mtime": 1751249522455, "results": "112", "hashOfConfig": "68"}, {"size": 2348, "mtime": 1750045607431, "results": "113", "hashOfConfig": "68"}, {"size": 1493, "mtime": 1750045607432, "results": "114", "hashOfConfig": "68"}, {"size": 2085, "mtime": 1751249522453, "results": "115", "hashOfConfig": "68"}, {"size": 551, "mtime": 1751249522453, "results": "116", "hashOfConfig": "68"}, {"size": 342, "mtime": 1751249522452, "results": "117", "hashOfConfig": "68"}, {"size": 322, "mtime": 1751249522453, "results": "118", "hashOfConfig": "68"}, {"size": 595, "mtime": 1751249522454, "results": "119", "hashOfConfig": "68"}, {"size": 18835, "mtime": 1751249522451, "results": "120", "hashOfConfig": "68"}, {"size": 377, "mtime": 1751249522453, "results": "121", "hashOfConfig": "68"}, {"size": 3361, "mtime": 1751503027775, "results": "122", "hashOfConfig": "68"}, {"size": 693, "mtime": 1751249522445, "results": "123", "hashOfConfig": "68"}, {"size": 2658, "mtime": 1751249522445, "results": "124", "hashOfConfig": "68"}, {"size": 7219, "mtime": 1751502458848, "results": "125", "hashOfConfig": "68"}, {"size": 4304, "mtime": 1751502458847, "results": "126", "hashOfConfig": "68"}, {"size": 5605, "mtime": 1751502458847, "results": "127", "hashOfConfig": "68"}, {"size": 1732, "mtime": 1751502458847, "results": "128", "hashOfConfig": "68"}, {"size": 1482, "mtime": 1751503065967, "results": "129", "hashOfConfig": "68"}, {"size": 859, "mtime": 1751503048879, "results": "130", "hashOfConfig": "68"}, {"size": 1434, "mtime": 1751503077096, "results": "131", "hashOfConfig": "68"}, {"size": 274, "mtime": 1751503054991, "results": "132", "hashOfConfig": "68"}, {"size": 636, "mtime": 1751249522456, "results": "133", "hashOfConfig": "68"}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tcbxdp", {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WDP301_UROOM\\Admin\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\App.js", ["332"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx", ["333"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx", ["334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx", ["352", "353", "354", "355"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx", ["356", "357"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx", ["358"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx", ["359", "360", "361"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx", ["362"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx", ["363", "364"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx", ["365", "366"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx", ["367", "368", "369", "370", "371", "372", "373", "374"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx", ["375", "376", "377", "378"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx", ["379", "380"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx", ["381", "382", "383", "384", "385"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx", ["386", "387", "388", "389", "390", "391", "392"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx", ["393", "394"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx", ["395", "396", "397", "398", "399"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx", ["400"], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx", ["401"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx", ["402", "403", "404", "405", "406", "407"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx", ["408", "409", "410", "411", "412"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx", ["413", "414"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx", ["415", "416", "417", "418", "419", "420", "421", "422"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx", ["423"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js", ["424", "425", "426"], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js", ["427"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\fonts.js", [], [], {"ruleId": "428", "severity": 1, "message": "429", "line": 40, "column": 6, "nodeType": "430", "endLine": 40, "endColumn": 17, "suggestions": "431"}, {"ruleId": "432", "severity": 1, "message": "433", "line": 1, "column": 17, "nodeType": "434", "messageId": "435", "endLine": 1, "endColumn": 26}, {"ruleId": "432", "severity": 1, "message": "436", "line": 4, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 4, "endColumn": 14}, {"ruleId": "432", "severity": 1, "message": "437", "line": 4, "column": 16, "nodeType": "434", "messageId": "435", "endLine": 4, "endColumn": 19}, {"ruleId": "432", "severity": 1, "message": "438", "line": 4, "column": 21, "nodeType": "434", "messageId": "435", "endLine": 4, "endColumn": 24}, {"ruleId": "432", "severity": 1, "message": "439", "line": 4, "column": 26, "nodeType": "434", "messageId": "435", "endLine": 4, "endColumn": 34}, {"ruleId": "432", "severity": 1, "message": "440", "line": 21, "column": 8, "nodeType": "434", "messageId": "435", "endLine": 21, "endColumn": 25}, {"ruleId": "432", "severity": 1, "message": "441", "line": 28, "column": 8, "nodeType": "434", "messageId": "435", "endLine": 28, "endColumn": 29}, {"ruleId": "432", "severity": 1, "message": "442", "line": 65, "column": 28, "nodeType": "434", "messageId": "435", "endLine": 65, "endColumn": 47}, {"ruleId": "432", "severity": 1, "message": "443", "line": 67, "column": 25, "nodeType": "434", "messageId": "435", "endLine": 67, "endColumn": 41}, {"ruleId": "444", "severity": 1, "message": "445", "line": 178, "column": 19, "nodeType": "446", "endLine": 178, "endColumn": 76}, {"ruleId": "444", "severity": 1, "message": "445", "line": 188, "column": 19, "nodeType": "446", "endLine": 188, "endColumn": 78}, {"ruleId": "444", "severity": 1, "message": "445", "line": 198, "column": 19, "nodeType": "446", "endLine": 198, "endColumn": 76}, {"ruleId": "444", "severity": 1, "message": "445", "line": 208, "column": 19, "nodeType": "446", "endLine": 208, "endColumn": 77}, {"ruleId": "444", "severity": 1, "message": "445", "line": 218, "column": 19, "nodeType": "446", "endLine": 218, "endColumn": 76}, {"ruleId": "444", "severity": 1, "message": "445", "line": 233, "column": 19, "nodeType": "446", "endLine": 233, "endColumn": 75}, {"ruleId": "444", "severity": 1, "message": "445", "line": 243, "column": 19, "nodeType": "446", "endLine": 243, "endColumn": 84}, {"ruleId": "444", "severity": 1, "message": "445", "line": 253, "column": 19, "nodeType": "446", "endLine": 253, "endColumn": 74}, {"ruleId": "444", "severity": 1, "message": "445", "line": 268, "column": 19, "nodeType": "446", "endLine": 268, "endColumn": 76}, {"ruleId": "444", "severity": 1, "message": "447", "line": 376, "column": 25, "nodeType": "446", "endLine": 391, "endColumn": 26}, {"ruleId": "432", "severity": 1, "message": "448", "line": 9, "column": 3, "nodeType": "434", "messageId": "435", "endLine": 9, "endColumn": 13}, {"ruleId": "432", "severity": 1, "message": "449", "line": 18, "column": 13, "nodeType": "434", "messageId": "435", "endLine": 18, "endColumn": 20}, {"ruleId": "432", "severity": 1, "message": "450", "line": 23, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 23, "endColumn": 17}, {"ruleId": "432", "severity": 1, "message": "451", "line": 24, "column": 19, "nodeType": "434", "messageId": "435", "endLine": 24, "endColumn": 29}, {"ruleId": "432", "severity": 1, "message": "452", "line": 8, "column": 3, "nodeType": "434", "messageId": "435", "endLine": 8, "endColumn": 13}, {"ruleId": "432", "severity": 1, "message": "448", "line": 9, "column": 3, "nodeType": "434", "messageId": "435", "endLine": 9, "endColumn": 13}, {"ruleId": "432", "severity": 1, "message": "453", "line": 85, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 85, "endColumn": 20}, {"ruleId": "432", "severity": 1, "message": "454", "line": 23, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 23, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "455", "line": 26, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 26, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "456", "line": 64, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 64, "endColumn": 23}, {"ruleId": "432", "severity": 1, "message": "433", "line": 1, "column": 20, "nodeType": "434", "messageId": "435", "endLine": 1, "endColumn": 29}, {"ruleId": "432", "severity": 1, "message": "457", "line": 1, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 1, "endColumn": 13}, {"ruleId": "432", "severity": 1, "message": "448", "line": 1, "column": 29, "nodeType": "434", "messageId": "435", "endLine": 1, "endColumn": 39}, {"ruleId": "432", "severity": 1, "message": "449", "line": 2, "column": 13, "nodeType": "434", "messageId": "435", "endLine": 2, "endColumn": 20}, {"ruleId": "432", "severity": 1, "message": "450", "line": 6, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 6, "endColumn": 17}, {"ruleId": "432", "severity": 1, "message": "449", "line": 6, "column": 13, "nodeType": "434", "messageId": "435", "endLine": 6, "endColumn": 20}, {"ruleId": "432", "severity": 1, "message": "458", "line": 8, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 8, "endColumn": 18}, {"ruleId": "432", "severity": 1, "message": "450", "line": 10, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 10, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "445", "line": 179, "column": 15, "nodeType": "446", "endLine": 179, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 184, "column": 15, "nodeType": "446", "endLine": 184, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 189, "column": 15, "nodeType": "446", "endLine": 189, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 194, "column": 15, "nodeType": "446", "endLine": 194, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 199, "column": 15, "nodeType": "446", "endLine": 199, "endColumn": 49}, {"ruleId": "432", "severity": 1, "message": "459", "line": 2, "column": 3, "nodeType": "434", "messageId": "435", "endLine": 2, "endColumn": 12}, {"ruleId": "432", "severity": 1, "message": "460", "line": 23, "column": 29, "nodeType": "434", "messageId": "435", "endLine": 23, "endColumn": 40}, {"ruleId": "432", "severity": 1, "message": "450", "line": 43, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 43, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "447", "line": 422, "column": 31, "nodeType": "446", "endLine": 422, "endColumn": 34}, {"ruleId": "432", "severity": 1, "message": "460", "line": 4, "column": 29, "nodeType": "434", "messageId": "435", "endLine": 4, "endColumn": 40}, {"ruleId": "432", "severity": 1, "message": "461", "line": 6, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 6, "endColumn": 15}, {"ruleId": "432", "severity": 1, "message": "460", "line": 4, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 4, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "462", "line": 10, "column": 8, "nodeType": "434", "messageId": "435", "endLine": 10, "endColumn": 19}, {"ruleId": "432", "severity": 1, "message": "463", "line": 12, "column": 8, "nodeType": "434", "messageId": "435", "endLine": 12, "endColumn": 13}, {"ruleId": "432", "severity": 1, "message": "464", "line": 15, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 15, "endColumn": 17}, {"ruleId": "432", "severity": 1, "message": "465", "line": 63, "column": 9, "nodeType": "434", "messageId": "435", "endLine": 63, "endColumn": 33}, {"ruleId": "432", "severity": 1, "message": "457", "line": 3, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 3, "endColumn": 13}, {"ruleId": "432", "severity": 1, "message": "466", "line": 3, "column": 21, "nodeType": "434", "messageId": "435", "endLine": 3, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "445", "line": 209, "column": 15, "nodeType": "446", "endLine": 209, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 214, "column": 15, "nodeType": "446", "endLine": 214, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 219, "column": 15, "nodeType": "446", "endLine": 219, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 224, "column": 15, "nodeType": "446", "endLine": 224, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 229, "column": 15, "nodeType": "446", "endLine": 229, "endColumn": 49}, {"ruleId": "432", "severity": 1, "message": "460", "line": 4, "column": 29, "nodeType": "434", "messageId": "435", "endLine": 4, "endColumn": 40}, {"ruleId": "444", "severity": 1, "message": "447", "line": 215, "column": 17, "nodeType": "446", "endLine": 219, "endColumn": 52}, {"ruleId": "432", "severity": 1, "message": "460", "line": 3, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 3, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "463", "line": 10, "column": 8, "nodeType": "434", "messageId": "435", "endLine": 10, "endColumn": 13}, {"ruleId": "432", "severity": 1, "message": "467", "line": 22, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 22, "endColumn": 19}, {"ruleId": "432", "severity": 1, "message": "468", "line": 27, "column": 17, "nodeType": "434", "messageId": "435", "endLine": 27, "endColumn": 25}, {"ruleId": "444", "severity": 1, "message": "445", "line": 203, "column": 17, "nodeType": "446", "endLine": 203, "endColumn": 89}, {"ruleId": "432", "severity": 1, "message": "469", "line": 13, "column": 8, "nodeType": "434", "messageId": "435", "endLine": 13, "endColumn": 19}, {"ruleId": "428", "severity": 1, "message": "470", "line": 132, "column": 6, "nodeType": "430", "endLine": 132, "endColumn": 8, "suggestions": "471"}, {"ruleId": "444", "severity": 1, "message": "447", "line": 19, "column": 11, "nodeType": "446", "endLine": 24, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "447", "line": 30, "column": 11, "nodeType": "446", "endLine": 35, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "447", "line": 41, "column": 11, "nodeType": "446", "endLine": 46, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "447", "line": 52, "column": 11, "nodeType": "446", "endLine": 57, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "447", "line": 63, "column": 11, "nodeType": "446", "endLine": 68, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "447", "line": 73, "column": 11, "nodeType": "446", "endLine": 78, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "445", "line": 171, "column": 15, "nodeType": "446", "endLine": 171, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 176, "column": 15, "nodeType": "446", "endLine": 176, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 181, "column": 15, "nodeType": "446", "endLine": 181, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 186, "column": 15, "nodeType": "446", "endLine": 186, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "445", "line": 191, "column": 15, "nodeType": "446", "endLine": 191, "endColumn": 49}, {"ruleId": "432", "severity": 1, "message": "472", "line": 16, "column": 3, "nodeType": "434", "messageId": "435", "endLine": 16, "endColumn": 11}, {"ruleId": "432", "severity": 1, "message": "473", "line": 24, "column": 3, "nodeType": "434", "messageId": "435", "endLine": 24, "endColumn": 11}, {"ruleId": "432", "severity": 1, "message": "474", "line": 10, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 10, "endColumn": 26}, {"ruleId": "432", "severity": 1, "message": "475", "line": 33, "column": 10, "nodeType": "434", "messageId": "435", "endLine": 33, "endColumn": 20}, {"ruleId": "428", "severity": 1, "message": "476", "line": 80, "column": 6, "nodeType": "430", "endLine": 80, "endColumn": 8, "suggestions": "477"}, {"ruleId": "428", "severity": 1, "message": "478", "line": 84, "column": 6, "nodeType": "430", "endLine": 84, "endColumn": 20, "suggestions": "479"}, {"ruleId": "480", "severity": 1, "message": "481", "line": 119, "column": 29, "nodeType": "482", "messageId": "483", "endLine": 119, "endColumn": 31}, {"ruleId": "428", "severity": 1, "message": "476", "line": 142, "column": 6, "nodeType": "430", "endLine": 142, "endColumn": 44, "suggestions": "484"}, {"ruleId": "480", "severity": 1, "message": "481", "line": 417, "column": 46, "nodeType": "482", "messageId": "483", "endLine": 417, "endColumn": 48}, {"ruleId": "480", "severity": 1, "message": "481", "line": 430, "column": 38, "nodeType": "482", "messageId": "483", "endLine": 430, "endColumn": 40}, {"ruleId": "432", "severity": 1, "message": "437", "line": 2, "column": 16, "nodeType": "434", "messageId": "435", "endLine": 2, "endColumn": 19}, {"ruleId": "485", "severity": 1, "message": "486", "line": 10, "column": 3, "nodeType": "487", "messageId": "483", "endLine": 10, "endColumn": 17}, {"ruleId": "485", "severity": 1, "message": "488", "line": 11, "column": 3, "nodeType": "487", "messageId": "483", "endLine": 11, "endColumn": 18}, {"ruleId": "485", "severity": 1, "message": "489", "line": 48, "column": 3, "nodeType": "487", "messageId": "483", "endLine": 48, "endColumn": 28}, {"ruleId": "490", "severity": 1, "message": "491", "line": 37, "column": 1, "nodeType": "492", "endLine": 108, "endColumn": 3}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["493"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Line' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Doughnut' is defined but never used.", "'AccountManagement' is defined but never used.", "'ListFeedbackAdminPage' is defined but never used.", "'setSidebarCollapsed' is assigned a value but never used.", "'setNotifications' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'Routers' is defined but never used.", "'navigate' is assigned a value but never used.", "'setReviews' is assigned a value but never used.", "'Pagination' is defined but never used.", "'getSeverity' is assigned a value but never used.", "'handleAccept' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'Col' is defined but never used.", "'useState' is defined but never used.", "'Container' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'Route' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'Row' is defined but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'GoogleLogin' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRefunds'. Either include it or remove the dependency array.", ["494"], "'Dropdown' is defined but never used.", "'FaFilter' is defined but never used.", "'initializeSocket' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["495"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["496"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["497"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "498", "fix": "499"}, {"desc": "500", "fix": "501"}, {"desc": "502", "fix": "503"}, {"desc": "504", "fix": "505"}, {"desc": "506", "fix": "507"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "508", "text": "509"}, "Update the dependencies array to be: [fetchRefunds]", {"range": "510", "text": "511"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "512", "text": "513"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "514", "text": "515"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "516", "text": "517"}, [1916, 1927], "[Auth?._id, dispatch]", [3634, 3636], "[fetchRefunds]", [2772, 2774], "[fetchAllUser]", [2831, 2845], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4480, 4518], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]"]