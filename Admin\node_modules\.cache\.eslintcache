[{"E:\\WDP301_UROOM\\Admin\\src\\index.js": "1", "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Admin\\src\\App.js": "3", "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js": "5", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js": "6", "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx": "7", "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx": "8", "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx": "9", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx": "10", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx": "11", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx": "12", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx": "13", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx": "14", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx": "15", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx": "16", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx": "17", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx": "18", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx": "19", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx": "20", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx": "21", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx": "22", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js": "23", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js": "24", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx": "25", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx": "26", "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js": "27", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx": "28", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js": "29", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js": "30", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js": "31", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js": "32", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js": "33", "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx": "34", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js": "35", "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx": "36", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx": "37", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js": "38", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js": "39", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js": "40", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js": "41", "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx": "42", "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx": "43", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx": "44", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js": "45", "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx": "46", "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx": "47", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js": "48", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js": "49", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js": "50", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js": "51", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js": "52", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx": "53", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js": "54", "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js": "55", "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js": "56", "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js": "57", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js": "58", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js": "59", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js": "60", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js": "61"}, {"size": 839, "mtime": 1751249522444, "results": "62", "hashOfConfig": "63"}, {"size": 375, "mtime": 1750045607470, "results": "64", "hashOfConfig": "63"}, {"size": 4119, "mtime": 1751259196611, "results": "65", "hashOfConfig": "63"}, {"size": 1291, "mtime": 1751249522455, "results": "66", "hashOfConfig": "63"}, {"size": 1451, "mtime": 1751249522455, "results": "67", "hashOfConfig": "63"}, {"size": 1302, "mtime": 1751259177977, "results": "68", "hashOfConfig": "63"}, {"size": 1474, "mtime": 1750045607464, "results": "69", "hashOfConfig": "63"}, {"size": 16232, "mtime": 1751249522445, "results": "70", "hashOfConfig": "63"}, {"size": 10290, "mtime": 1751249522447, "results": "71", "hashOfConfig": "63"}, {"size": 14473, "mtime": 1751249522451, "results": "72", "hashOfConfig": "63"}, {"size": 6183, "mtime": 1751249522452, "results": "73", "hashOfConfig": "63"}, {"size": 16060, "mtime": 1751249522446, "results": "74", "hashOfConfig": "63"}, {"size": 10088, "mtime": 1751249522446, "results": "75", "hashOfConfig": "63"}, {"size": 3050, "mtime": 1751249522448, "results": "76", "hashOfConfig": "63"}, {"size": 1723, "mtime": 1751249522448, "results": "77", "hashOfConfig": "63"}, {"size": 6523, "mtime": 1751249522448, "results": "78", "hashOfConfig": "63"}, {"size": 18076, "mtime": 1751249522448, "results": "79", "hashOfConfig": "63"}, {"size": 5423, "mtime": 1751249522449, "results": "80", "hashOfConfig": "63"}, {"size": 3718, "mtime": 1751249522448, "results": "81", "hashOfConfig": "63"}, {"size": 7339, "mtime": 1751249522450, "results": "82", "hashOfConfig": "63"}, {"size": 7403, "mtime": 1751249522449, "results": "83", "hashOfConfig": "63"}, {"size": 7520, "mtime": 1751249522449, "results": "84", "hashOfConfig": "63"}, {"size": 626, "mtime": 1751502458849, "results": "85", "hashOfConfig": "63"}, {"size": 484, "mtime": 1751502458849, "results": "86", "hashOfConfig": "63"}, {"size": 8490, "mtime": 1751249522450, "results": "87", "hashOfConfig": "63"}, {"size": 15641, "mtime": 1751249522449, "results": "88", "hashOfConfig": "63"}, {"size": 551, "mtime": 1751249522456, "results": "89", "hashOfConfig": "63"}, {"size": 25311, "mtime": 1751249522450, "results": "90", "hashOfConfig": "63"}, {"size": 1433, "mtime": 1751249522452, "results": "91", "hashOfConfig": "63"}, {"size": 1271, "mtime": 1751249522452, "results": "92", "hashOfConfig": "63"}, {"size": 196, "mtime": 1751249522452, "results": "93", "hashOfConfig": "63"}, {"size": 466, "mtime": 1751249522454, "results": "94", "hashOfConfig": "63"}, {"size": 764, "mtime": 1751249522452, "results": "95", "hashOfConfig": "63"}, {"size": 2422, "mtime": 1751249522446, "results": "96", "hashOfConfig": "63"}, {"size": 2230, "mtime": 1751249522452, "results": "97", "hashOfConfig": "63"}, {"size": 6543, "mtime": 1751249522446, "results": "98", "hashOfConfig": "63"}, {"size": 31702, "mtime": 1751502458845, "results": "99", "hashOfConfig": "63"}, {"size": 2761, "mtime": 1751249522454, "results": "100", "hashOfConfig": "63"}, {"size": 955, "mtime": 1751249522454, "results": "101", "hashOfConfig": "63"}, {"size": 9581, "mtime": 1751249522452, "results": "102", "hashOfConfig": "63"}, {"size": 1225, "mtime": 1751249522452, "results": "103", "hashOfConfig": "63"}, {"size": 25958, "mtime": 1751249522450, "results": "104", "hashOfConfig": "63"}, {"size": 16312, "mtime": 1751249522447, "results": "105", "hashOfConfig": "63"}, {"size": 2140, "mtime": 1751249522449, "results": "106", "hashOfConfig": "63"}, {"size": 3227, "mtime": 1751249522455, "results": "107", "hashOfConfig": "63"}, {"size": 2348, "mtime": 1750045607431, "results": "108", "hashOfConfig": "63"}, {"size": 1493, "mtime": 1750045607432, "results": "109", "hashOfConfig": "63"}, {"size": 2085, "mtime": 1751249522453, "results": "110", "hashOfConfig": "63"}, {"size": 551, "mtime": 1751249522453, "results": "111", "hashOfConfig": "63"}, {"size": 342, "mtime": 1751249522452, "results": "112", "hashOfConfig": "63"}, {"size": 322, "mtime": 1751249522453, "results": "113", "hashOfConfig": "63"}, {"size": 595, "mtime": 1751249522454, "results": "114", "hashOfConfig": "63"}, {"size": 18835, "mtime": 1751249522451, "results": "115", "hashOfConfig": "63"}, {"size": 377, "mtime": 1751249522453, "results": "116", "hashOfConfig": "63"}, {"size": 3282, "mtime": 1751502458844, "results": "117", "hashOfConfig": "63"}, {"size": 693, "mtime": 1751249522445, "results": "118", "hashOfConfig": "63"}, {"size": 2658, "mtime": 1751249522445, "results": "119", "hashOfConfig": "63"}, {"size": 7219, "mtime": 1751502458848, "results": "120", "hashOfConfig": "63"}, {"size": 4304, "mtime": 1751502458847, "results": "121", "hashOfConfig": "63"}, {"size": 5605, "mtime": 1751502458847, "results": "122", "hashOfConfig": "63"}, {"size": 1732, "mtime": 1751502458847, "results": "123", "hashOfConfig": "63"}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tcbxdp", {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WDP301_UROOM\\Admin\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\App.js", ["307"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx", ["308"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx", ["309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325", "326"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx", ["327", "328", "329", "330"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx", ["331", "332"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx", ["333"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx", ["334", "335", "336"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx", ["337"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx", ["338", "339"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx", ["340", "341"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx", ["342", "343", "344", "345", "346", "347", "348", "349"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx", ["350", "351", "352", "353"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx", ["354", "355"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx", ["356", "357", "358", "359", "360"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx", ["361", "362", "363", "364", "365", "366", "367"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx", ["368", "369"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx", ["370", "371", "372", "373", "374"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx", ["375"], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx", ["376"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx", ["377", "378", "379", "380", "381", "382"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx", ["383", "384", "385", "386", "387"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx", ["388", "389"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx", ["390", "391", "392", "393", "394", "395", "396", "397"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx", ["398", "399", "400", "401", "402"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js", ["403", "404", "405"], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js", ["406"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js", [], [], {"ruleId": "407", "severity": 1, "message": "408", "line": 40, "column": 6, "nodeType": "409", "endLine": 40, "endColumn": 17, "suggestions": "410"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 1, "column": 17, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 26}, {"ruleId": "411", "severity": 1, "message": "415", "line": 4, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 14}, {"ruleId": "411", "severity": 1, "message": "416", "line": 4, "column": 16, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 19}, {"ruleId": "411", "severity": 1, "message": "417", "line": 4, "column": 21, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 24}, {"ruleId": "411", "severity": 1, "message": "418", "line": 4, "column": 26, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 34}, {"ruleId": "411", "severity": 1, "message": "419", "line": 21, "column": 8, "nodeType": "413", "messageId": "414", "endLine": 21, "endColumn": 25}, {"ruleId": "411", "severity": 1, "message": "420", "line": 28, "column": 8, "nodeType": "413", "messageId": "414", "endLine": 28, "endColumn": 29}, {"ruleId": "411", "severity": 1, "message": "421", "line": 65, "column": 28, "nodeType": "413", "messageId": "414", "endLine": 65, "endColumn": 47}, {"ruleId": "411", "severity": 1, "message": "422", "line": 67, "column": 25, "nodeType": "413", "messageId": "414", "endLine": 67, "endColumn": 41}, {"ruleId": "423", "severity": 1, "message": "424", "line": 178, "column": 19, "nodeType": "425", "endLine": 178, "endColumn": 76}, {"ruleId": "423", "severity": 1, "message": "424", "line": 188, "column": 19, "nodeType": "425", "endLine": 188, "endColumn": 78}, {"ruleId": "423", "severity": 1, "message": "424", "line": 198, "column": 19, "nodeType": "425", "endLine": 198, "endColumn": 76}, {"ruleId": "423", "severity": 1, "message": "424", "line": 208, "column": 19, "nodeType": "425", "endLine": 208, "endColumn": 77}, {"ruleId": "423", "severity": 1, "message": "424", "line": 218, "column": 19, "nodeType": "425", "endLine": 218, "endColumn": 76}, {"ruleId": "423", "severity": 1, "message": "424", "line": 233, "column": 19, "nodeType": "425", "endLine": 233, "endColumn": 75}, {"ruleId": "423", "severity": 1, "message": "424", "line": 243, "column": 19, "nodeType": "425", "endLine": 243, "endColumn": 84}, {"ruleId": "423", "severity": 1, "message": "424", "line": 253, "column": 19, "nodeType": "425", "endLine": 253, "endColumn": 74}, {"ruleId": "423", "severity": 1, "message": "424", "line": 268, "column": 19, "nodeType": "425", "endLine": 268, "endColumn": 76}, {"ruleId": "423", "severity": 1, "message": "426", "line": 376, "column": 25, "nodeType": "425", "endLine": 391, "endColumn": 26}, {"ruleId": "411", "severity": 1, "message": "427", "line": 9, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 9, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "428", "line": 18, "column": 13, "nodeType": "413", "messageId": "414", "endLine": 18, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "429", "line": 23, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 23, "endColumn": 17}, {"ruleId": "411", "severity": 1, "message": "430", "line": 24, "column": 19, "nodeType": "413", "messageId": "414", "endLine": 24, "endColumn": 29}, {"ruleId": "411", "severity": 1, "message": "431", "line": 8, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 8, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "427", "line": 9, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 9, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "432", "line": 85, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 85, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "433", "line": 23, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 23, "endColumn": 21}, {"ruleId": "411", "severity": 1, "message": "434", "line": 26, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 26, "endColumn": 21}, {"ruleId": "411", "severity": 1, "message": "435", "line": 64, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 64, "endColumn": 23}, {"ruleId": "411", "severity": 1, "message": "412", "line": 1, "column": 20, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 29}, {"ruleId": "411", "severity": 1, "message": "436", "line": 1, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "427", "line": 1, "column": 29, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 39}, {"ruleId": "411", "severity": 1, "message": "428", "line": 2, "column": 13, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "429", "line": 6, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 6, "endColumn": 17}, {"ruleId": "411", "severity": 1, "message": "428", "line": 6, "column": 13, "nodeType": "413", "messageId": "414", "endLine": 6, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "437", "line": 8, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 8, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "429", "line": 10, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 10, "endColumn": 17}, {"ruleId": "423", "severity": 1, "message": "424", "line": 179, "column": 15, "nodeType": "425", "endLine": 179, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 184, "column": 15, "nodeType": "425", "endLine": 184, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 189, "column": 15, "nodeType": "425", "endLine": 189, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 194, "column": 15, "nodeType": "425", "endLine": 194, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 199, "column": 15, "nodeType": "425", "endLine": 199, "endColumn": 49}, {"ruleId": "411", "severity": 1, "message": "438", "line": 2, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 12}, {"ruleId": "411", "severity": 1, "message": "439", "line": 23, "column": 29, "nodeType": "413", "messageId": "414", "endLine": 23, "endColumn": 40}, {"ruleId": "411", "severity": 1, "message": "429", "line": 43, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 43, "endColumn": 17}, {"ruleId": "423", "severity": 1, "message": "426", "line": 422, "column": 31, "nodeType": "425", "endLine": 422, "endColumn": 34}, {"ruleId": "411", "severity": 1, "message": "439", "line": 4, "column": 29, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 40}, {"ruleId": "411", "severity": 1, "message": "440", "line": 6, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 6, "endColumn": 15}, {"ruleId": "411", "severity": 1, "message": "439", "line": 4, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 21}, {"ruleId": "411", "severity": 1, "message": "441", "line": 10, "column": 8, "nodeType": "413", "messageId": "414", "endLine": 10, "endColumn": 19}, {"ruleId": "411", "severity": 1, "message": "442", "line": 12, "column": 8, "nodeType": "413", "messageId": "414", "endLine": 12, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "443", "line": 15, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 15, "endColumn": 17}, {"ruleId": "411", "severity": 1, "message": "444", "line": 63, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 63, "endColumn": 33}, {"ruleId": "411", "severity": 1, "message": "436", "line": 3, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "445", "line": 3, "column": 21, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 24}, {"ruleId": "423", "severity": 1, "message": "424", "line": 209, "column": 15, "nodeType": "425", "endLine": 209, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 214, "column": 15, "nodeType": "425", "endLine": 214, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 219, "column": 15, "nodeType": "425", "endLine": 219, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 224, "column": 15, "nodeType": "425", "endLine": 224, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 229, "column": 15, "nodeType": "425", "endLine": 229, "endColumn": 49}, {"ruleId": "411", "severity": 1, "message": "439", "line": 4, "column": 29, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 40}, {"ruleId": "423", "severity": 1, "message": "426", "line": 215, "column": 17, "nodeType": "425", "endLine": 219, "endColumn": 52}, {"ruleId": "411", "severity": 1, "message": "439", "line": 3, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 21}, {"ruleId": "411", "severity": 1, "message": "442", "line": 10, "column": 8, "nodeType": "413", "messageId": "414", "endLine": 10, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "446", "line": 22, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 22, "endColumn": 19}, {"ruleId": "411", "severity": 1, "message": "447", "line": 27, "column": 17, "nodeType": "413", "messageId": "414", "endLine": 27, "endColumn": 25}, {"ruleId": "423", "severity": 1, "message": "424", "line": 203, "column": 17, "nodeType": "425", "endLine": 203, "endColumn": 89}, {"ruleId": "411", "severity": 1, "message": "448", "line": 13, "column": 8, "nodeType": "413", "messageId": "414", "endLine": 13, "endColumn": 19}, {"ruleId": "407", "severity": 1, "message": "449", "line": 132, "column": 6, "nodeType": "409", "endLine": 132, "endColumn": 8, "suggestions": "450"}, {"ruleId": "423", "severity": 1, "message": "426", "line": 19, "column": 11, "nodeType": "425", "endLine": 24, "endColumn": 12}, {"ruleId": "423", "severity": 1, "message": "426", "line": 30, "column": 11, "nodeType": "425", "endLine": 35, "endColumn": 12}, {"ruleId": "423", "severity": 1, "message": "426", "line": 41, "column": 11, "nodeType": "425", "endLine": 46, "endColumn": 12}, {"ruleId": "423", "severity": 1, "message": "426", "line": 52, "column": 11, "nodeType": "425", "endLine": 57, "endColumn": 12}, {"ruleId": "423", "severity": 1, "message": "426", "line": 63, "column": 11, "nodeType": "425", "endLine": 68, "endColumn": 12}, {"ruleId": "423", "severity": 1, "message": "426", "line": 73, "column": 11, "nodeType": "425", "endLine": 78, "endColumn": 12}, {"ruleId": "423", "severity": 1, "message": "424", "line": 171, "column": 15, "nodeType": "425", "endLine": 171, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 176, "column": 15, "nodeType": "425", "endLine": 176, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 181, "column": 15, "nodeType": "425", "endLine": 181, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 186, "column": 15, "nodeType": "425", "endLine": 186, "endColumn": 49}, {"ruleId": "423", "severity": 1, "message": "424", "line": 191, "column": 15, "nodeType": "425", "endLine": 191, "endColumn": 49}, {"ruleId": "411", "severity": 1, "message": "451", "line": 16, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 16, "endColumn": 11}, {"ruleId": "411", "severity": 1, "message": "452", "line": 24, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 24, "endColumn": 11}, {"ruleId": "411", "severity": 1, "message": "453", "line": 10, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 10, "endColumn": 26}, {"ruleId": "411", "severity": 1, "message": "454", "line": 33, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 33, "endColumn": 20}, {"ruleId": "407", "severity": 1, "message": "455", "line": 80, "column": 6, "nodeType": "409", "endLine": 80, "endColumn": 8, "suggestions": "456"}, {"ruleId": "407", "severity": 1, "message": "457", "line": 84, "column": 6, "nodeType": "409", "endLine": 84, "endColumn": 20, "suggestions": "458"}, {"ruleId": "459", "severity": 1, "message": "460", "line": 119, "column": 29, "nodeType": "461", "messageId": "462", "endLine": 119, "endColumn": 31}, {"ruleId": "407", "severity": 1, "message": "455", "line": 142, "column": 6, "nodeType": "409", "endLine": 142, "endColumn": 44, "suggestions": "463"}, {"ruleId": "459", "severity": 1, "message": "460", "line": 417, "column": 46, "nodeType": "461", "messageId": "462", "endLine": 417, "endColumn": 48}, {"ruleId": "459", "severity": 1, "message": "460", "line": 430, "column": 38, "nodeType": "461", "messageId": "462", "endLine": 430, "endColumn": 40}, {"ruleId": "411", "severity": 1, "message": "437", "line": 1, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "416", "line": 2, "column": 16, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 19}, {"ruleId": "411", "severity": 1, "message": "464", "line": 183, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 183, "endColumn": 19}, {"ruleId": "423", "severity": 1, "message": "424", "line": 414, "column": 13, "nodeType": "425", "endLine": 418, "endColumn": 14}, {"ruleId": "423", "severity": 1, "message": "424", "line": 477, "column": 13, "nodeType": "425", "endLine": 481, "endColumn": 14}, {"ruleId": "465", "severity": 1, "message": "466", "line": 10, "column": 3, "nodeType": "467", "messageId": "462", "endLine": 10, "endColumn": 17}, {"ruleId": "465", "severity": 1, "message": "468", "line": 11, "column": 3, "nodeType": "467", "messageId": "462", "endLine": 11, "endColumn": 18}, {"ruleId": "465", "severity": 1, "message": "469", "line": 48, "column": 3, "nodeType": "467", "messageId": "462", "endLine": 48, "endColumn": 28}, {"ruleId": "470", "severity": 1, "message": "471", "line": 37, "column": 1, "nodeType": "472", "endLine": 108, "endColumn": 3}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["473"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Line' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Doughnut' is defined but never used.", "'AccountManagement' is defined but never used.", "'ListFeedbackAdminPage' is defined but never used.", "'setSidebarCollapsed' is assigned a value but never used.", "'setNotifications' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'Routers' is defined but never used.", "'navigate' is assigned a value but never used.", "'setReviews' is assigned a value but never used.", "'Pagination' is defined but never used.", "'getSeverity' is assigned a value but never used.", "'handleAccept' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'Col' is defined but never used.", "'useState' is defined but never used.", "'Container' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'Route' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'Row' is defined but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'GoogleLogin' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRefunds'. Either include it or remove the dependency array.", ["474"], "'Dropdown' is defined but never used.", "'FaFilter' is defined but never used.", "'initializeSocket' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["475"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["476"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["477"], "'hotelHosts' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "478", "fix": "479"}, {"desc": "480", "fix": "481"}, {"desc": "482", "fix": "483"}, {"desc": "484", "fix": "485"}, {"desc": "486", "fix": "487"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "488", "text": "489"}, "Update the dependencies array to be: [fetchRefunds]", {"range": "490", "text": "491"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "492", "text": "493"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "494", "text": "495"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "496", "text": "497"}, [1916, 1927], "[Auth?._id, dispatch]", [3634, 3636], "[fetchRefunds]", [2772, 2774], "[fetchAllUser]", [2831, 2845], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4480, 4518], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]"]