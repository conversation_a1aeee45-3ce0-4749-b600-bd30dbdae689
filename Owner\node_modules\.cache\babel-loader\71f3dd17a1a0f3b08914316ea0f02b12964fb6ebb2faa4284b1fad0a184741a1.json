{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\TransactionDetail.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Container, Row, Col, Card, Button, Table, Form, Image, Modal } from \"react-bootstrap\";\nimport { FaStar, FaRegStar } from \"react-icons/fa\";\nimport \"../../css/hotelHost/BookingBill.css\";\nimport { useParams } from \"react-router-dom\";\nimport Utils from \"@utils/Utils\";\nimport { useAppSelector } from \"@redux/store\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TransactionDetail = ({\n  detailReservation,\n  show,\n  handleClose\n}) => {\n  _s();\n  var _detailReservation$ho, _detailReservation$ho2, _detailReservation$ho3, _detailReservation$ho4, _detailReservation$ho5, _detailReservation$ho6, _detailReservation$ho7, _detailReservation$us, _detailReservation$us2, _detailReservation$us3, _detailReservation$ho8, _detailReservation$ho9;\n  const Auth = useAppSelector(state => state.Auth.Auth);\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calculate total price from rooms and services\n  const calculateTotalPrice = (rooms, services = [], checkInDate, checkOutDate) => {\n    // Calculate number of nights\n    const calculateNights = (checkIn, checkOut) => {\n      if (!checkIn || !checkOut) return 1;\n      const checkInDate = new Date(checkIn);\n      const checkOutDate = new Date(checkOut);\n      const timeDiff = checkOutDate.getTime() - checkInDate.getTime();\n      const nights = Math.ceil(timeDiff / (1000 * 3600 * 24));\n      return nights > 0 ? nights : 1;\n    };\n    const nights = calculateNights(checkInDate, checkOutDate);\n\n    // Calculate rooms total\n    const roomsTotal = rooms && Array.isArray(rooms) ? rooms.reduce((total, roomItem) => {\n      var _roomItem$room;\n      const roomPrice = ((_roomItem$room = roomItem.room) === null || _roomItem$room === void 0 ? void 0 : _roomItem$room.price) || 0;\n      const quantity = roomItem.quantity || 1;\n      // Room price = price per night × quantity × number of nights\n      return total + roomPrice * quantity * nights;\n    }, 0) : 0;\n\n    // Calculate services total\n    const servicesTotal = services && Array.isArray(services) ? services.reduce((total, serviceItem) => {\n      var _serviceItem$service, _serviceItem$selectDa;\n      const servicePrice = ((_serviceItem$service = serviceItem.service) === null || _serviceItem$service === void 0 ? void 0 : _serviceItem$service.price) || 0;\n      const quantity = serviceItem.quantity || 1;\n      const daysCount = ((_serviceItem$selectDa = serviceItem.selectDate) === null || _serviceItem$selectDa === void 0 ? void 0 : _serviceItem$selectDa.length) || 1;\n      // Service price = price × quantity × selected days\n      return total + servicePrice * quantity * daysCount;\n    }, 0) : 0;\n    return roomsTotal + servicesTotal;\n  };\n\n  // Format currency for display\n  const formatCurrency = amount => {\n    if (amount === undefined || amount === null) return \"N/A\";\n    try {\n      return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: \"USD\",\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      }).format(amount);\n    } catch (error) {\n      console.error(\"Error formatting currency:\", error);\n      return `${amount}`;\n    }\n  };\n\n  // Format date for display\n  const formatDate = dateString => {\n    if (!dateString) return \"N/A\";\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"vi-VN\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\"\n      });\n    } catch (error) {\n      console.error(\"Error formatting date:\", error);\n      return \"N/A\";\n    }\n  };\n  console.log(\"detailReservation: \", detailReservation);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: handleClose,\n    size: \"xl\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        fluid: true,\n        className: \"booking-bill-container\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          children: [/*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: \"Transaction Detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"close\",\n            onClick: handleClose\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          className: \"g-0\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            className: \"hotel-info-section\",\n            style: {\n              paddingTop: \"20px\",\n              paddingLeft: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Image, {\n              src: detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$ho = detailReservation.hotel) === null || _detailReservation$ho === void 0 ? void 0 : (_detailReservation$ho2 = _detailReservation$ho.images) === null || _detailReservation$ho2 === void 0 ? void 0 : (_detailReservation$ho3 = _detailReservation$ho2[0]) === null || _detailReservation$ho3 === void 0 ? void 0 : _detailReservation$ho3.url,\n              alt: \"Hotel Room\",\n              style: {\n                height: \"510px\",\n                width: \"100%\",\n                objectFit: \"cover\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hotel-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"hotel-name-title\",\n                children: \"Hotel Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-full-name\",\n                children: (detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$ho4 = detailReservation.hotel) === null || _detailReservation$ho4 === void 0 ? void 0 : _detailReservation$ho4.hotelName) || (detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$ho5 = detailReservation.hotel) === null || _detailReservation$ho5 === void 0 ? void 0 : _detailReservation$ho5.name) || \"Hotel Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"check-dates-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"check-date-box\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"date-label\",\n                    children: \"Check-in Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"date-value\",\n                    children: formatDate(detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.checkInDate)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"star-rating-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"star-hotel-text\",\n                    children: \"Star Hotel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(StarRating, {\n                    rating: (_detailReservation$ho6 = detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$ho7 = detailReservation.hotel) === null || _detailReservation$ho7 === void 0 ? void 0 : _detailReservation$ho7.star) !== null && _detailReservation$ho6 !== void 0 ? _detailReservation$ho6 : 4\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"check-date-box\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"date-label\",\n                    children: \"Check-out Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"date-value\",\n                    children: formatDate(detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.checkOutDate)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 7,\n            className: \"bill-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bill-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"uroom-title\",\n                children: /*#__PURE__*/_jsxDEV(\"b\", {\n                  style: {\n                    fontSize: 30\n                  },\n                  children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: \"#f8e71c\"\n                    },\n                    children: \"OO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 23\n                  }, this), \"M\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-bill-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Booking Bill\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"date-created\",\n                  children: [\"Date created: \", formatDate(detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.createdAt)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"section-title\",\n                children: \"I. CUSTOMER INFORMATION\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"info-label\",\n                  children: \"Customer name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 8,\n                  className: \"info-value\",\n                  children: (detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$us = detailReservation.user) === null || _detailReservation$us === void 0 ? void 0 : _detailReservation$us.name) || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"info-label\",\n                  children: \"Phone number:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 8,\n                  className: \"info-value\",\n                  children: (detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$us2 = detailReservation.user) === null || _detailReservation$us2 === void 0 ? void 0 : _detailReservation$us2.phoneNumber) || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"info-label\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 8,\n                  className: \"info-value\",\n                  children: (detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$us3 = detailReservation.user) === null || _detailReservation$us3 === void 0 ? void 0 : _detailReservation$us3.email) || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"section-title\",\n                children: \"II. HOTEL INFORMATION\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"info-label\",\n                  children: \"Phone number:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 8,\n                  className: \"info-value\",\n                  children: (detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$ho8 = detailReservation.hotel) === null || _detailReservation$ho8 === void 0 ? void 0 : _detailReservation$ho8.phoneNumber) || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"info-label\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 8,\n                  className: \"info-value\",\n                  children: (Auth === null || Auth === void 0 ? void 0 : Auth.email) || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), (detailReservation === null || detailReservation === void 0 ? void 0 : (_detailReservation$ho9 = detailReservation.hotel) === null || _detailReservation$ho9 === void 0 ? void 0 : _detailReservation$ho9.address) && /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"info-label\",\n                  children: \"Address:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 8,\n                  className: \"info-value\",\n                  children: detailReservation.hotel.address\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-section\",\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-2\",\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  className: \"info-label\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: \"III. BOOKING INFORMATION\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Table, {\n                bordered: true,\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"STT\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Rooms and Services\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Price\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [detailReservation !== null && detailReservation !== void 0 && detailReservation.rooms && Array.isArray(detailReservation.rooms) ? detailReservation.rooms.map((roomItem, index) => {\n                    var _roomItem$room2, _roomItem$room3, _roomItem$room4;\n                    const nights = (() => {\n                      if (!detailReservation.checkInDate || !detailReservation.checkOutDate) return 1;\n                      const checkInDate = new Date(detailReservation.checkInDate);\n                      const checkOutDate = new Date(detailReservation.checkOutDate);\n                      const timeDiff = checkOutDate.getTime() - checkInDate.getTime();\n                      const nights = Math.ceil(timeDiff / (1000 * 3600 * 24));\n                      return nights > 0 ? nights : 1;\n                    })();\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Room:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 277,\n                          columnNumber: 31\n                        }, this), \" \", ((_roomItem$room2 = roomItem.room) === null || _roomItem$room2 === void 0 ? void 0 : _roomItem$room2.name) || \"Phòng\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 278,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: [formatCurrency(((_roomItem$room3 = roomItem.room) === null || _roomItem$room3 === void 0 ? void 0 : _roomItem$room3.price) || 0), \" \\xD7 \", roomItem.quantity, \" room \\xD7 \", nights, \" nights\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 279,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [roomItem.quantity || 1, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 285,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: [\"\\xD7 \", nights, \" nights\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 286,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 283,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: formatCurrency((((_roomItem$room4 = roomItem.room) === null || _roomItem$room4 === void 0 ? void 0 : _roomItem$room4.price) || 0) * (roomItem.quantity || 1) * nights)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 29\n                      }, this)]\n                    }, `room-${index}`, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 27\n                    }, this);\n                  }) : null, detailReservation !== null && detailReservation !== void 0 && detailReservation.services && Array.isArray(detailReservation.services) && detailReservation.services.length > 0 ? detailReservation.services.map((serviceItem, index) => {\n                    var _detailReservation$ro, _serviceItem$service2, _serviceItem$selectDa2, _serviceItem$selectDa3, _serviceItem$selectDa4, _serviceItem$service3;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: (((_detailReservation$ro = detailReservation.rooms) === null || _detailReservation$ro === void 0 ? void 0 : _detailReservation$ro.length) || 0) + index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 304,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Service:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 306,\n                          columnNumber: 29\n                        }, this), \" \", ((_serviceItem$service2 = serviceItem.service) === null || _serviceItem$service2 === void 0 ? void 0 : _serviceItem$service2.name) || \"Dịch vụ\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 307,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: [\"Dates: \", ((_serviceItem$selectDa2 = serviceItem.selectDate) === null || _serviceItem$selectDa2 === void 0 ? void 0 : _serviceItem$selectDa2.map(date => formatDate(date)).join(\", \")) || \"N/A\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 308,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [serviceItem.quantity / ((_serviceItem$selectDa3 = serviceItem.selectDate) === null || _serviceItem$selectDa3 === void 0 ? void 0 : _serviceItem$selectDa3.length) || 1, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 316,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: [\"\\xD7 \", ((_serviceItem$selectDa4 = serviceItem.selectDate) === null || _serviceItem$selectDa4 === void 0 ? void 0 : _serviceItem$selectDa4.length) || 1, \" days\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 317,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: formatCurrency((((_serviceItem$service3 = serviceItem.service) === null || _serviceItem$service3 === void 0 ? void 0 : _serviceItem$service3.price) || 0) * (serviceItem.quantity || 1))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 27\n                      }, this)]\n                    }, `service-${index}`, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 25\n                    }, this);\n                  }) : null, (!(detailReservation !== null && detailReservation !== void 0 && detailReservation.rooms) || detailReservation.rooms.length === 0) && (!(detailReservation !== null && detailReservation !== void 0 && detailReservation.services) || detailReservation.services.length === 0) && /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: /*#__PURE__*/_jsxDEV(\"td\", {\n                      colSpan: 4,\n                      className: \"text-center\",\n                      children: \"No booking information available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"total-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      colSpan: 2,\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Total amount\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      colSpan: 2,\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: formatCurrency((detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.totalAmount) || (detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.totalPrice) || calculateTotalPrice(detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.rooms, detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.services, detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.checkInDate, detailReservation === null || detailReservation === void 0 ? void 0 : detailReservation.checkOutDate))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"section-title\",\n                children: \"IV. CUSTOMER SIGNATURE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"terms-checkbox\",\n                label: \"Agree to the Hotel and Website Terms & Privacy\",\n                className: \"terms-checkbox\",\n                defaultChecked: true,\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s(TransactionDetail, \"7VZnJTKl3XXTBY6T1w0VogTiSKs=\", false, function () {\n  return [useAppSelector];\n});\n_c = TransactionDetail;\nexport default TransactionDetail;\nvar _c;\n$RefreshReg$(_c, \"TransactionDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Form", "Image", "Modal", "FaStar", "FaRegStar", "useParams", "Utils", "useAppSelector", "jsxDEV", "_jsxDEV", "TransactionDetail", "detailReservation", "show", "handleClose", "_s", "_detailReservation$ho", "_detailReservation$ho2", "_detailReservation$ho3", "_detailReservation$ho4", "_detailReservation$ho5", "_detailReservation$ho6", "_detailReservation$ho7", "_detailReservation$us", "_detailReservation$us2", "_detailReservation$us3", "_detailReservation$ho8", "_detailReservation$ho9", "<PERSON><PERSON>", "state", "StarRating", "rating", "className", "children", "Array", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "calculateTotalPrice", "rooms", "services", "checkInDate", "checkOutDate", "calculateNights", "checkIn", "checkOut", "Date", "timeDiff", "getTime", "nights", "Math", "ceil", "roomsTotal", "isArray", "reduce", "total", "roomItem", "_roomItem$room", "roomPrice", "room", "price", "quantity", "servicesTotal", "serviceItem", "_serviceItem$service", "_serviceItem$selectDa", "servicePrice", "service", "daysCount", "selectDate", "length", "formatCurrency", "amount", "undefined", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "error", "console", "formatDate", "dateString", "date", "toLocaleDateString", "day", "month", "year", "log", "onHide", "size", "fluid", "Header", "Title", "variant", "onClick", "md", "paddingTop", "paddingLeft", "src", "hotel", "images", "url", "alt", "height", "width", "objectFit", "hotelName", "name", "star", "fontSize", "color", "createdAt", "user", "phoneNumber", "email", "address", "bordered", "_roomItem$room2", "_roomItem$room3", "_roomItem$room4", "_detailReservation$ro", "_serviceItem$service2", "_serviceItem$selectDa2", "_serviceItem$selectDa3", "_serviceItem$selectDa4", "_serviceItem$service3", "join", "colSpan", "totalAmount", "totalPrice", "Check", "type", "id", "label", "defaultChecked", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/TransactionDetail.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Button,\r\n  Table,\r\n  Form,\r\n  Image,\r\n  Modal,\r\n} from \"react-bootstrap\";\r\nimport { FaStar, FaRegStar } from \"react-icons/fa\";\r\nimport \"../../css/hotelHost/BookingBill.css\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport Utils from \"@utils/Utils\";\r\nimport { useAppSelector } from \"@redux/store\";\r\n\r\nconst TransactionDetail = ({ detailReservation, show, handleClose }) => {\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Calculate total price from rooms and services\r\n  const calculateTotalPrice = (rooms, services = [], checkInDate, checkOutDate) => {\r\n    // Calculate number of nights\r\n    const calculateNights = (checkIn, checkOut) => {\r\n      if (!checkIn || !checkOut) return 1;\r\n      const checkInDate = new Date(checkIn);\r\n      const checkOutDate = new Date(checkOut);\r\n      const timeDiff = checkOutDate.getTime() - checkInDate.getTime();\r\n      const nights = Math.ceil(timeDiff / (1000 * 3600 * 24));\r\n      return nights > 0 ? nights : 1;\r\n    };\r\n\r\n    const nights = calculateNights(checkInDate, checkOutDate);\r\n\r\n    // Calculate rooms total\r\n    const roomsTotal = rooms && Array.isArray(rooms) \r\n      ? rooms.reduce((total, roomItem) => {\r\n          const roomPrice = roomItem.room?.price || 0;\r\n          const quantity = roomItem.quantity || 1;\r\n          // Room price = price per night × quantity × number of nights\r\n          return total + roomPrice * quantity * nights;\r\n        }, 0)\r\n      : 0;\r\n\r\n    // Calculate services total\r\n    const servicesTotal = services && Array.isArray(services)\r\n      ? services.reduce((total, serviceItem) => {\r\n          const servicePrice = serviceItem.service?.price || 0;\r\n          const quantity = serviceItem.quantity || 1;\r\n          const daysCount = serviceItem.selectDate?.length || 1;\r\n          // Service price = price × quantity × selected days\r\n          return total + servicePrice * quantity * daysCount;\r\n        }, 0)\r\n      : 0;\r\n\r\n    return roomsTotal + servicesTotal;\r\n  };\r\n\r\n  // Format currency for display\r\n  const formatCurrency = (amount) => {\r\n    if (amount === undefined || amount === null) return \"N/A\";\r\n    try {\r\n      return new Intl.NumberFormat(\"en-US\", {\r\n        style: \"currency\",\r\n        currency: \"USD\",\r\n        minimumFractionDigits: 0,\r\n        maximumFractionDigits: 0,\r\n      }).format(amount);\r\n    } catch (error) {\r\n      console.error(\"Error formatting currency:\", error);\r\n      return `${amount}`;\r\n    }\r\n  };\r\n\r\n  // Format date for display\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n    try {\r\n      const date = new Date(dateString);\r\n      return date.toLocaleDateString(\"vi-VN\", {\r\n        day: \"2-digit\",\r\n        month: \"2-digit\",\r\n        year: \"numeric\",\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error formatting date:\", error);\r\n      return \"N/A\";\r\n    }\r\n  };\r\n\r\n  console.log(\"detailReservation: \", detailReservation);\r\n\r\n  return (\r\n    <Modal show={show} onHide={handleClose} size=\"xl\">\r\n      <div className=\"p-3\">\r\n        <Container fluid className=\"booking-bill-container\">\r\n          <Modal.Header>\r\n            <Modal.Title>Transaction Detail</Modal.Title>\r\n            <Button variant=\"close\" onClick={handleClose} />\r\n          </Modal.Header>\r\n\r\n          <Row className=\"g-0\">\r\n            {/* Left side - Hotel Image and Info */}\r\n            <Col\r\n              md={5}\r\n              className=\"hotel-info-section\"\r\n              style={{ paddingTop: \"20px\", paddingLeft: \"20px\" }}\r\n            >\r\n              <Image\r\n                src={\r\n                  detailReservation?.hotel?.images?.[0]?.url\r\n                }\r\n                alt=\"Hotel Room\"\r\n                style={{\r\n                  height: \"510px\",\r\n                  width: \"100%\",\r\n                  objectFit: \"cover\",\r\n                }}\r\n              />\r\n              <div className=\"hotel-details\">\r\n                <h5 className=\"hotel-name-title\">Hotel Name</h5>\r\n                <p className=\"hotel-full-name\">\r\n                  {detailReservation?.hotel?.hotelName ||\r\n                   detailReservation?.hotel?.name ||\r\n                   \"Hotel Name\"}\r\n                </p>\r\n\r\n                <div className=\"check-dates-container\">\r\n                  <div className=\"check-date-box\">\r\n                    <p className=\"date-label\">Check-in Date</p>\r\n                    <p className=\"date-value\">\r\n                      {formatDate(detailReservation?.checkInDate)}\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"star-rating-container\">\r\n                    <p className=\"star-hotel-text\">Star Hotel</p>\r\n                    <StarRating rating={detailReservation?.hotel?.star ?? 4} />\r\n                  </div>\r\n\r\n                  <div className=\"check-date-box\">\r\n                    <p className=\"date-label\">Check-out Date</p>\r\n                    <p className=\"date-value\">\r\n                      {formatDate(detailReservation?.checkOutDate)}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Col>\r\n\r\n            {/* Right side - Booking Bill */}\r\n            <Col md={7} className=\"bill-section\">\r\n              <div className=\"bill-header\">\r\n                <h2 className=\"uroom-title\">\r\n                  <b style={{ fontSize: 30 }}>\r\n                    UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n                  </b>\r\n                </h2>\r\n                <div className=\"booking-bill-header\">\r\n                  <h4>Booking Bill</h4>\r\n                  <p className=\"date-created\">\r\n                    Date created: {formatDate(detailReservation?.createdAt)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Customer Information */}\r\n              <div className=\"info-section\">\r\n                <h5 className=\"section-title\">I. CUSTOMER INFORMATION</h5>\r\n                <Row className=\"mb-2\">\r\n                  <Col md={4} className=\"info-label\">\r\n                    Customer name:\r\n                  </Col>\r\n                  <Col md={8} className=\"info-value\">\r\n                    {detailReservation?.user?.name || \"N/A\"}\r\n                  </Col>\r\n                </Row>\r\n                <Row className=\"mb-2\">\r\n                  <Col md={4} className=\"info-label\">\r\n                    Phone number:\r\n                  </Col>\r\n                  <Col md={8} className=\"info-value\">\r\n                    {detailReservation?.user?.phoneNumber || \"N/A\"}\r\n                  </Col>\r\n                </Row>\r\n                <Row className=\"mb-2\">\r\n                  <Col md={4} className=\"info-label\">\r\n                    Email:\r\n                  </Col>\r\n                  <Col md={8} className=\"info-value\">\r\n                    {detailReservation?.user?.email || \"N/A\"}\r\n                  </Col>\r\n                </Row>\r\n              </div>\r\n\r\n              {/* Hotel Information */}\r\n              <div className=\"info-section\">\r\n                <h5 className=\"section-title\">II. HOTEL INFORMATION</h5>\r\n                <Row className=\"mb-2\">\r\n                  <Col md={4} className=\"info-label\">\r\n                    Phone number:\r\n                  </Col>\r\n                  <Col md={8} className=\"info-value\">\r\n                    {detailReservation?.hotel?.phoneNumber || \"N/A\"}\r\n                  </Col>\r\n                </Row>\r\n                <Row className=\"mb-2\">\r\n                  <Col md={4} className=\"info-label\">\r\n                    Email:\r\n                  </Col>\r\n                  <Col md={8} className=\"info-value\">\r\n                    {Auth?.email || \"N/A\"}\r\n                  </Col>\r\n                </Row>\r\n                {detailReservation?.hotel?.address && (\r\n                  <Row className=\"mb-2\">\r\n                    <Col md={4} className=\"info-label\">\r\n                      Address:\r\n                    </Col>\r\n                    <Col md={8} className=\"info-value\">\r\n                      {detailReservation.hotel.address}\r\n                    </Col>\r\n                  </Row>\r\n                )}\r\n              </div>\r\n\r\n              {/* Booking Information */}\r\n              <div className=\"info-section\">\r\n                <Row className=\"mb-2\">\r\n                  <Col md={12} className=\"info-label\">\r\n                    <h5>III. BOOKING INFORMATION</h5>\r\n                  </Col>\r\n                </Row>\r\n                <Table bordered>\r\n                  <thead>\r\n                    <tr>\r\n                      <th>STT</th>\r\n                      <th>Rooms and Services</th>\r\n                      <th>Quantity</th>\r\n                      <th>Price</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {/* Rooms */}\r\n                    {detailReservation?.rooms &&\r\n                    Array.isArray(detailReservation.rooms) ? (\r\n                      detailReservation.rooms.map((roomItem, index) => {\r\n                        const nights = (() => {\r\n                          if (!detailReservation.checkInDate || !detailReservation.checkOutDate) return 1;\r\n                          const checkInDate = new Date(detailReservation.checkInDate);\r\n                          const checkOutDate = new Date(detailReservation.checkOutDate);\r\n                          const timeDiff = checkOutDate.getTime() - checkInDate.getTime();\r\n                          const nights = Math.ceil(timeDiff / (1000 * 3600 * 24));\r\n                          return nights > 0 ? nights : 1;\r\n                        })();\r\n\r\n                        return (\r\n                          <tr key={`room-${index}`}>\r\n                            <td>{index + 1}</td>\r\n                            <td>\r\n                              <strong>Room:</strong> {roomItem.room?.name || \"Phòng\"}\r\n                              <br />\r\n                              <small className=\"text-muted\">\r\n                                {formatCurrency(roomItem.room?.price || 0)} × {roomItem.quantity} room × {nights} nights\r\n                              </small>\r\n                            </td>\r\n                            <td>\r\n                              {roomItem.quantity || 1}\r\n                              <br />\r\n                              <small className=\"text-muted\">× {nights} nights</small>\r\n                            </td>\r\n                            <td>\r\n                              {formatCurrency(\r\n                                (roomItem.room?.price || 0) * (roomItem.quantity || 1) * nights\r\n                              )}\r\n                            </td>\r\n                          </tr>\r\n                        );\r\n                      })\r\n                    ) : null}\r\n\r\n                    {/* Services */}\r\n                    {detailReservation?.services &&\r\n                    Array.isArray(detailReservation.services) &&\r\n                    detailReservation.services.length > 0 ? (\r\n                      detailReservation.services.map((serviceItem, index) => (\r\n                        <tr key={`service-${index}`}>\r\n                          <td>{(detailReservation.rooms?.length || 0) + index + 1}</td>\r\n                          <td>\r\n                            <strong>Service:</strong> {serviceItem.service?.name || \"Dịch vụ\"}\r\n                            <br />\r\n                            <small className=\"text-muted\">\r\n                              Dates: {serviceItem.selectDate?.map(date => \r\n                                formatDate(date)\r\n                              ).join(\", \") || \"N/A\"}\r\n                            </small>\r\n                          </td>\r\n                          <td>\r\n                            {serviceItem.quantity / serviceItem.selectDate?.length || 1}\r\n                            <br />\r\n                            <small className=\"text-muted\">\r\n                              × {serviceItem.selectDate?.length || 1} days\r\n                            </small>\r\n                          </td>\r\n                          <td>\r\n                            {formatCurrency(\r\n                              (serviceItem.service?.price || 0) * (serviceItem.quantity || 1)\r\n                            )}\r\n                          </td>\r\n                        </tr>\r\n                      ))\r\n                    ) : null}\r\n\r\n                    {/* Show message if no rooms or services */}\r\n                    {(!detailReservation?.rooms || detailReservation.rooms.length === 0) &&\r\n                     (!detailReservation?.services || detailReservation.services.length === 0) && (\r\n                      <tr>\r\n                        <td colSpan={4} className=\"text-center\">\r\n                          No booking information available\r\n                        </td>\r\n                      </tr>\r\n                    )}\r\n\r\n                    {/* Total Row */}\r\n                    <tr className=\"total-row\">\r\n                      <td colSpan={2}>\r\n                        <strong>Total amount</strong>\r\n                      </td>\r\n                      <td colSpan={2}>\r\n                        <strong>\r\n                          {formatCurrency(\r\n                            detailReservation?.totalAmount ||\r\n                            detailReservation?.totalPrice ||\r\n                            calculateTotalPrice(\r\n                              detailReservation?.rooms,\r\n                              detailReservation?.services,\r\n                              detailReservation?.checkInDate,\r\n                              detailReservation?.checkOutDate\r\n                            )\r\n                          )}\r\n                        </strong>\r\n                      </td>\r\n                    </tr>\r\n                  </tbody>\r\n                </Table>\r\n              </div>\r\n\r\n              {/* Customer Signature */}\r\n              <div className=\"info-section\">\r\n                <h5 className=\"section-title\">IV. CUSTOMER SIGNATURE</h5>\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"terms-checkbox\"\r\n                  label=\"Agree to the Hotel and Website Terms & Privacy\"\r\n                  className=\"terms-checkbox\"\r\n                  defaultChecked={true}\r\n                  disabled={true}\r\n                />\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default TransactionDetail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,KAAK,QACA,iBAAiB;AACxB,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,OAAO,qCAAqC;AAC5C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,cAAc;AAChC,SAASC,cAAc,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC,IAAI;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtE,MAAMC,IAAI,GAAGpB,cAAc,CAAEqB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;;EAEvD;EACA,MAAME,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACErB,OAAA;MAAKsB,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGN,MAAM,gBACZrB,OAAA,CAACN,MAAM;QAAa4B,SAAS,EAAC;MAAa,GAA9BK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9C/B,OAAA,CAACL,SAAS;QAAa2B,SAAS,EAAC;MAAM,GAAvBK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,GAAG,EAAE,EAAEC,WAAW,EAAEC,YAAY,KAAK;IAC/E;IACA,MAAMC,eAAe,GAAGA,CAACC,OAAO,EAAEC,QAAQ,KAAK;MAC7C,IAAI,CAACD,OAAO,IAAI,CAACC,QAAQ,EAAE,OAAO,CAAC;MACnC,MAAMJ,WAAW,GAAG,IAAIK,IAAI,CAACF,OAAO,CAAC;MACrC,MAAMF,YAAY,GAAG,IAAII,IAAI,CAACD,QAAQ,CAAC;MACvC,MAAME,QAAQ,GAAGL,YAAY,CAACM,OAAO,CAAC,CAAC,GAAGP,WAAW,CAACO,OAAO,CAAC,CAAC;MAC/D,MAAMC,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;MACvD,OAAOE,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;IAChC,CAAC;IAED,MAAMA,MAAM,GAAGN,eAAe,CAACF,WAAW,EAAEC,YAAY,CAAC;;IAEzD;IACA,MAAMU,UAAU,GAAGb,KAAK,IAAIT,KAAK,CAACuB,OAAO,CAACd,KAAK,CAAC,GAC5CA,KAAK,CAACe,MAAM,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;MAAA,IAAAC,cAAA;MAChC,MAAMC,SAAS,GAAG,EAAAD,cAAA,GAAAD,QAAQ,CAACG,IAAI,cAAAF,cAAA,uBAAbA,cAAA,CAAeG,KAAK,KAAI,CAAC;MAC3C,MAAMC,QAAQ,GAAGL,QAAQ,CAACK,QAAQ,IAAI,CAAC;MACvC;MACA,OAAON,KAAK,GAAGG,SAAS,GAAGG,QAAQ,GAAGZ,MAAM;IAC9C,CAAC,EAAE,CAAC,CAAC,GACL,CAAC;;IAEL;IACA,MAAMa,aAAa,GAAGtB,QAAQ,IAAIV,KAAK,CAACuB,OAAO,CAACb,QAAQ,CAAC,GACrDA,QAAQ,CAACc,MAAM,CAAC,CAACC,KAAK,EAAEQ,WAAW,KAAK;MAAA,IAAAC,oBAAA,EAAAC,qBAAA;MACtC,MAAMC,YAAY,GAAG,EAAAF,oBAAA,GAAAD,WAAW,CAACI,OAAO,cAAAH,oBAAA,uBAAnBA,oBAAA,CAAqBJ,KAAK,KAAI,CAAC;MACpD,MAAMC,QAAQ,GAAGE,WAAW,CAACF,QAAQ,IAAI,CAAC;MAC1C,MAAMO,SAAS,GAAG,EAAAH,qBAAA,GAAAF,WAAW,CAACM,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBK,MAAM,KAAI,CAAC;MACrD;MACA,OAAOf,KAAK,GAAGW,YAAY,GAAGL,QAAQ,GAAGO,SAAS;IACpD,CAAC,EAAE,CAAC,CAAC,GACL,CAAC;IAEL,OAAOhB,UAAU,GAAGU,aAAa;EACnC,CAAC;;EAED;EACA,MAAMS,cAAc,GAAIC,MAAM,IAAK;IACjC,IAAIA,MAAM,KAAKC,SAAS,IAAID,MAAM,KAAK,IAAI,EAAE,OAAO,KAAK;IACzD,IAAI;MACF,OAAO,IAAIE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAE,KAAK;QACfC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MACzB,CAAC,CAAC,CAACC,MAAM,CAACR,MAAM,CAAC;IACnB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO,GAAGT,MAAM,EAAE;IACpB;EACF,CAAC;;EAED;EACA,MAAMW,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIvC,IAAI,CAACsC,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACtCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,KAAK;IACd;EACF,CAAC;EAEDC,OAAO,CAACQ,GAAG,CAAC,qBAAqB,EAAElF,iBAAiB,CAAC;EAErD,oBACEF,OAAA,CAACP,KAAK;IAACU,IAAI,EAAEA,IAAK;IAACkF,MAAM,EAAEjF,WAAY;IAACkF,IAAI,EAAC,IAAI;IAAA/D,QAAA,eAC/CvB,OAAA;MAAKsB,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBvB,OAAA,CAACf,SAAS;QAACsG,KAAK;QAACjE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACjDvB,OAAA,CAACP,KAAK,CAAC+F,MAAM;UAAAjE,QAAA,gBACXvB,OAAA,CAACP,KAAK,CAACgG,KAAK;YAAAlE,QAAA,EAAC;UAAkB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7C/B,OAAA,CAACX,MAAM;YAACqG,OAAO,EAAC,OAAO;YAACC,OAAO,EAAEvF;UAAY;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAEf/B,OAAA,CAACd,GAAG;UAACoC,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAElBvB,OAAA,CAACb,GAAG;YACFyG,EAAE,EAAE,CAAE;YACNtE,SAAS,EAAC,oBAAoB;YAC9BgD,KAAK,EAAE;cAAEuB,UAAU,EAAE,MAAM;cAAEC,WAAW,EAAE;YAAO,CAAE;YAAAvE,QAAA,gBAEnDvB,OAAA,CAACR,KAAK;cACJuG,GAAG,EACD7F,iBAAiB,aAAjBA,iBAAiB,wBAAAI,qBAAA,GAAjBJ,iBAAiB,CAAE8F,KAAK,cAAA1F,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0B2F,MAAM,cAAA1F,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAmC,CAAC,CAAC,cAAAC,sBAAA,uBAArCA,sBAAA,CAAuC0F,GACxC;cACDC,GAAG,EAAC,YAAY;cAChB7B,KAAK,EAAE;gBACL8B,MAAM,EAAE,OAAO;gBACfC,KAAK,EAAE,MAAM;gBACbC,SAAS,EAAE;cACb;YAAE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF/B,OAAA;cAAKsB,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvB,OAAA;gBAAIsB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChD/B,OAAA;gBAAGsB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAC3B,CAAArB,iBAAiB,aAAjBA,iBAAiB,wBAAAO,sBAAA,GAAjBP,iBAAiB,CAAE8F,KAAK,cAAAvF,sBAAA,uBAAxBA,sBAAA,CAA0B8F,SAAS,MACnCrG,iBAAiB,aAAjBA,iBAAiB,wBAAAQ,sBAAA,GAAjBR,iBAAiB,CAAE8F,KAAK,cAAAtF,sBAAA,uBAAxBA,sBAAA,CAA0B8F,IAAI,KAC9B;cAAY;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAEJ/B,OAAA;gBAAKsB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCvB,OAAA;kBAAKsB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BvB,OAAA;oBAAGsB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAa;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3C/B,OAAA;oBAAGsB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACtBsD,UAAU,CAAC3E,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiC,WAAW;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN/B,OAAA;kBAAKsB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpCvB,OAAA;oBAAGsB,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7C/B,OAAA,CAACoB,UAAU;oBAACC,MAAM,GAAAV,sBAAA,GAAET,iBAAiB,aAAjBA,iBAAiB,wBAAAU,sBAAA,GAAjBV,iBAAiB,CAAE8F,KAAK,cAAApF,sBAAA,uBAAxBA,sBAAA,CAA0B6F,IAAI,cAAA9F,sBAAA,cAAAA,sBAAA,GAAI;kBAAE;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eAEN/B,OAAA;kBAAKsB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BvB,OAAA;oBAAGsB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5C/B,OAAA;oBAAGsB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACtBsD,UAAU,CAAC3E,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEkC,YAAY;kBAAC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/B,OAAA,CAACb,GAAG;YAACyG,EAAE,EAAE,CAAE;YAACtE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAClCvB,OAAA;cAAKsB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvB,OAAA;gBAAIsB,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACzBvB,OAAA;kBAAGsE,KAAK,EAAE;oBAAEoC,QAAQ,EAAE;kBAAG,CAAE;kBAAAnF,QAAA,GAAC,IACxB,eAAAvB,OAAA;oBAAMsE,KAAK,EAAE;sBAAEqC,KAAK,EAAE;oBAAU,CAAE;oBAAApF,QAAA,EAAC;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAChD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACL/B,OAAA;gBAAKsB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCvB,OAAA;kBAAAuB,QAAA,EAAI;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrB/B,OAAA;kBAAGsB,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAC,gBACZ,EAACsD,UAAU,CAAC3E,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE0G,SAAS,CAAC;gBAAA;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/B,OAAA;cAAKsB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvB,OAAA;gBAAIsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAuB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1D/B,OAAA,CAACd,GAAG;gBAACoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvB,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAEnC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN/B,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC/B,CAAArB,iBAAiB,aAAjBA,iBAAiB,wBAAAW,qBAAA,GAAjBX,iBAAiB,CAAE2G,IAAI,cAAAhG,qBAAA,uBAAvBA,qBAAA,CAAyB2F,IAAI,KAAI;gBAAK;kBAAA5E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/B,OAAA,CAACd,GAAG;gBAACoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvB,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAEnC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN/B,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC/B,CAAArB,iBAAiB,aAAjBA,iBAAiB,wBAAAY,sBAAA,GAAjBZ,iBAAiB,CAAE2G,IAAI,cAAA/F,sBAAA,uBAAvBA,sBAAA,CAAyBgG,WAAW,KAAI;gBAAK;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/B,OAAA,CAACd,GAAG;gBAACoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvB,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAEnC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN/B,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC/B,CAAArB,iBAAiB,aAAjBA,iBAAiB,wBAAAa,sBAAA,GAAjBb,iBAAiB,CAAE2G,IAAI,cAAA9F,sBAAA,uBAAvBA,sBAAA,CAAyBgG,KAAK,KAAI;gBAAK;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/B,OAAA;cAAKsB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvB,OAAA;gBAAIsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD/B,OAAA,CAACd,GAAG;gBAACoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvB,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAEnC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN/B,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC/B,CAAArB,iBAAiB,aAAjBA,iBAAiB,wBAAAc,sBAAA,GAAjBd,iBAAiB,CAAE8F,KAAK,cAAAhF,sBAAA,uBAAxBA,sBAAA,CAA0B8F,WAAW,KAAI;gBAAK;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/B,OAAA,CAACd,GAAG;gBAACoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvB,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAEnC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN/B,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC/B,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,KAAK,KAAI;gBAAK;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL,CAAA7B,iBAAiB,aAAjBA,iBAAiB,wBAAAe,sBAAA,GAAjBf,iBAAiB,CAAE8F,KAAK,cAAA/E,sBAAA,uBAAxBA,sBAAA,CAA0B+F,OAAO,kBAChChH,OAAA,CAACd,GAAG;gBAACoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvB,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAEnC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN/B,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,CAAE;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC/BrB,iBAAiB,CAAC8F,KAAK,CAACgB;gBAAO;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN/B,OAAA;cAAKsB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvB,OAAA,CAACd,GAAG;gBAACoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBvB,OAAA,CAACb,GAAG;kBAACyG,EAAE,EAAE,EAAG;kBAACtE,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACjCvB,OAAA;oBAAAuB,QAAA,EAAI;kBAAwB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/B,OAAA,CAACV,KAAK;gBAAC2H,QAAQ;gBAAA1F,QAAA,gBACbvB,OAAA;kBAAAuB,QAAA,eACEvB,OAAA;oBAAAuB,QAAA,gBACEvB,OAAA;sBAAAuB,QAAA,EAAI;oBAAG;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACZ/B,OAAA;sBAAAuB,QAAA,EAAI;oBAAkB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3B/B,OAAA;sBAAAuB,QAAA,EAAI;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjB/B,OAAA;sBAAAuB,QAAA,EAAI;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR/B,OAAA;kBAAAuB,QAAA,GAEGrB,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAE+B,KAAK,IACzBT,KAAK,CAACuB,OAAO,CAAC7C,iBAAiB,CAAC+B,KAAK,CAAC,GACpC/B,iBAAiB,CAAC+B,KAAK,CAACR,GAAG,CAAC,CAACyB,QAAQ,EAAEvB,KAAK,KAAK;oBAAA,IAAAuF,eAAA,EAAAC,eAAA,EAAAC,eAAA;oBAC/C,MAAMzE,MAAM,GAAG,CAAC,MAAM;sBACpB,IAAI,CAACzC,iBAAiB,CAACiC,WAAW,IAAI,CAACjC,iBAAiB,CAACkC,YAAY,EAAE,OAAO,CAAC;sBAC/E,MAAMD,WAAW,GAAG,IAAIK,IAAI,CAACtC,iBAAiB,CAACiC,WAAW,CAAC;sBAC3D,MAAMC,YAAY,GAAG,IAAII,IAAI,CAACtC,iBAAiB,CAACkC,YAAY,CAAC;sBAC7D,MAAMK,QAAQ,GAAGL,YAAY,CAACM,OAAO,CAAC,CAAC,GAAGP,WAAW,CAACO,OAAO,CAAC,CAAC;sBAC/D,MAAMC,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;sBACvD,OAAOE,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;oBAChC,CAAC,EAAE,CAAC;oBAEJ,oBACE3C,OAAA;sBAAAuB,QAAA,gBACEvB,OAAA;wBAAAuB,QAAA,EAAKI,KAAK,GAAG;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpB/B,OAAA;wBAAAuB,QAAA,gBACEvB,OAAA;0BAAAuB,QAAA,EAAQ;wBAAK;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAmF,eAAA,GAAAhE,QAAQ,CAACG,IAAI,cAAA6D,eAAA,uBAAbA,eAAA,CAAeV,IAAI,KAAI,OAAO,eACtDxG,OAAA;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACN/B,OAAA;0BAAOsB,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAC1B0C,cAAc,CAAC,EAAAkD,eAAA,GAAAjE,QAAQ,CAACG,IAAI,cAAA8D,eAAA,uBAAbA,eAAA,CAAe7D,KAAK,KAAI,CAAC,CAAC,EAAC,QAAG,EAACJ,QAAQ,CAACK,QAAQ,EAAC,aAAQ,EAACZ,MAAM,EAAC,SACnF;wBAAA;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACL/B,OAAA;wBAAAuB,QAAA,GACG2B,QAAQ,CAACK,QAAQ,IAAI,CAAC,eACvBvD,OAAA;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACN/B,OAAA;0BAAOsB,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAC,OAAE,EAACoB,MAAM,EAAC,SAAO;wBAAA;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACL/B,OAAA;wBAAAuB,QAAA,EACG0C,cAAc,CACb,CAAC,EAAAmD,eAAA,GAAAlE,QAAQ,CAACG,IAAI,cAAA+D,eAAA,uBAAbA,eAAA,CAAe9D,KAAK,KAAI,CAAC,KAAKJ,QAAQ,CAACK,QAAQ,IAAI,CAAC,CAAC,GAAGZ,MAC3D;sBAAC;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA,GAlBE,QAAQJ,KAAK,EAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAmBpB,CAAC;kBAET,CAAC,CAAC,GACA,IAAI,EAGP7B,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEgC,QAAQ,IAC5BV,KAAK,CAACuB,OAAO,CAAC7C,iBAAiB,CAACgC,QAAQ,CAAC,IACzChC,iBAAiB,CAACgC,QAAQ,CAAC8B,MAAM,GAAG,CAAC,GACnC9D,iBAAiB,CAACgC,QAAQ,CAACT,GAAG,CAAC,CAACgC,WAAW,EAAE9B,KAAK;oBAAA,IAAA0F,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;oBAAA,oBAChD1H,OAAA;sBAAAuB,QAAA,gBACEvB,OAAA;wBAAAuB,QAAA,EAAK,CAAC,EAAA8F,qBAAA,GAAAnH,iBAAiB,CAAC+B,KAAK,cAAAoF,qBAAA,uBAAvBA,qBAAA,CAAyBrD,MAAM,KAAI,CAAC,IAAIrC,KAAK,GAAG;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7D/B,OAAA;wBAAAuB,QAAA,gBACEvB,OAAA;0BAAAuB,QAAA,EAAQ;wBAAQ;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAuF,qBAAA,GAAA7D,WAAW,CAACI,OAAO,cAAAyD,qBAAA,uBAAnBA,qBAAA,CAAqBd,IAAI,KAAI,SAAS,eACjExG,OAAA;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACN/B,OAAA;0BAAOsB,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAC,SACrB,EAAC,EAAAgG,sBAAA,GAAA9D,WAAW,CAACM,UAAU,cAAAwD,sBAAA,uBAAtBA,sBAAA,CAAwB9F,GAAG,CAACsD,IAAI,IACtCF,UAAU,CAACE,IAAI,CACjB,CAAC,CAAC4C,IAAI,CAAC,IAAI,CAAC,KAAI,KAAK;wBAAA;0BAAA/F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACL/B,OAAA;wBAAAuB,QAAA,GACGkC,WAAW,CAACF,QAAQ,KAAAiE,sBAAA,GAAG/D,WAAW,CAACM,UAAU,cAAAyD,sBAAA,uBAAtBA,sBAAA,CAAwBxD,MAAM,KAAI,CAAC,eAC3DhE,OAAA;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACN/B,OAAA;0BAAOsB,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAC,OAC1B,EAAC,EAAAkG,sBAAA,GAAAhE,WAAW,CAACM,UAAU,cAAA0D,sBAAA,uBAAtBA,sBAAA,CAAwBzD,MAAM,KAAI,CAAC,EAAC,OACzC;wBAAA;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACL/B,OAAA;wBAAAuB,QAAA,EACG0C,cAAc,CACb,CAAC,EAAAyD,qBAAA,GAAAjE,WAAW,CAACI,OAAO,cAAA6D,qBAAA,uBAAnBA,qBAAA,CAAqBpE,KAAK,KAAI,CAAC,KAAKG,WAAW,CAACF,QAAQ,IAAI,CAAC,CAChE;sBAAC;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA,GAtBE,WAAWJ,KAAK,EAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuBvB,CAAC;kBAAA,CACN,CAAC,GACA,IAAI,EAGP,CAAC,EAAC7B,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAE+B,KAAK,KAAI/B,iBAAiB,CAAC+B,KAAK,CAAC+B,MAAM,KAAK,CAAC,MACjE,EAAC9D,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEgC,QAAQ,KAAIhC,iBAAiB,CAACgC,QAAQ,CAAC8B,MAAM,KAAK,CAAC,CAAC,iBACxEhE,OAAA;oBAAAuB,QAAA,eACEvB,OAAA;sBAAI4H,OAAO,EAAE,CAAE;sBAACtG,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAExC;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACL,eAGD/B,OAAA;oBAAIsB,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACvBvB,OAAA;sBAAI4H,OAAO,EAAE,CAAE;sBAAArG,QAAA,eACbvB,OAAA;wBAAAuB,QAAA,EAAQ;sBAAY;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACL/B,OAAA;sBAAI4H,OAAO,EAAE,CAAE;sBAAArG,QAAA,eACbvB,OAAA;wBAAAuB,QAAA,EACG0C,cAAc,CACb,CAAA/D,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE2H,WAAW,MAC9B3H,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4H,UAAU,KAC7B9F,mBAAmB,CACjB9B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE+B,KAAK,EACxB/B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEgC,QAAQ,EAC3BhC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiC,WAAW,EAC9BjC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEkC,YACrB,CACF;sBAAC;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGN/B,OAAA;cAAKsB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvB,OAAA;gBAAIsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzD/B,OAAA,CAACT,IAAI,CAACwI,KAAK;gBACTC,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,gBAAgB;gBACnBC,KAAK,EAAC,gDAAgD;gBACtD5G,SAAS,EAAC,gBAAgB;gBAC1B6G,cAAc,EAAE,IAAK;gBACrBC,QAAQ,EAAE;cAAK;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAC1B,EAAA,CA3WIJ,iBAAiB;EAAA,QACRH,cAAc;AAAA;AAAAuI,EAAA,GADvBpI,iBAAiB;AA6WvB,eAAeA,iBAAiB;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}