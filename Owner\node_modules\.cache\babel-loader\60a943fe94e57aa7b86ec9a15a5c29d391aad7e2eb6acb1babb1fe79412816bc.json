{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  METADATA_BOUNDARY_NAME: null,\n  OUTLET_BOUNDARY_NAME: null,\n  VIEWPORT_BOUNDARY_NAME: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  METADATA_BOUNDARY_NAME: function () {\n    return METADATA_BOUNDARY_NAME;\n  },\n  OUTLET_BOUNDARY_NAME: function () {\n    return OUTLET_BOUNDARY_NAME;\n  },\n  VIEWPORT_BOUNDARY_NAME: function () {\n    return VIEWPORT_BOUNDARY_NAME;\n  }\n});\nconst METADATA_BOUNDARY_NAME = '__next_metadata_boundary__';\nconst VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__';\nconst OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__';", "map": {"version": 3, "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\lib\\metadata\\metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "mappings": ";;;;;;;;;;;;;;;;;EAAaA,sBAAsB,WAAAA,CAAA;WAAtBA,sBAAA;;EAEAC,oBAAoB,WAAAA,CAAA;WAApBA,oBAAA;;EADAC,sBAAsB,WAAAA,CAAA;WAAtBA,sBAAA;;;AADN,MAAMF,sBAAA,GAAyB;AAC/B,MAAME,sBAAA,GAAyB;AAC/B,MAAMD,oBAAA,GAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}