{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\create_hotel\\\\RoomNameForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Container, Row, Col, Form, Button, Card, Navbar, ProgressBar } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { useNavigate } from \"react-router-dom\";\nimport RoomActions from \"@redux/room/actions\";\nimport { useDispatch } from \"react-redux\";\nimport { useAppSelector } from \"@redux/store\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RoomNamingForm() {\n  _s();\n  const [roomName, setRoomName] = useState(\"\");\n  const [customName, setCustomName] = useState(\"\");\n  const [useCustomName, setUseCustomName] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n  const createRoom = useAppSelector(state => state.Room.createRoom);\n  useEffect(() => {\n    // If createRoom data exists, populate formData with it\n    if (createRoom.name !== \"\") {\n      setUseCustomName(true);\n      setCustomName(createRoom.name);\n    }\n  }, [createRoom]);\n\n  // Room name options\n  const roomNameOptions = [\"Phòng Standard\", \"Phòng Family\", \"Phòng Deluxe\", \"Phòng Suite\", \"Phòng Superior\", \"Phòng Executive\", \"Phòng Premium\", \"Phòng Luxury\"];\n  const validateForm = () => {\n    const newErrors = {};\n    if (useCustomName) {\n      if (!customName.trim()) {\n        newErrors.customName = \"Tên phòng tùy chỉnh không được để trống\";\n      } else if (customName.trim().length < 3) {\n        newErrors.customName = \"Tên phòng phải có ít nhất 3 ký tự\";\n      } else if (customName.trim().length > 50) {\n        newErrors.customName = \"Tên phòng không được vượt quá 50 ký tự\";\n      }\n    } else {\n      if (!roomName) {\n        newErrors.roomName = \"Vui lòng chọn tên phòng\";\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const dispatch = useDispatch();\n  const handleContinue = () => {\n    if (validateForm()) {\n      dispatch({\n        type: RoomActions.SAVE_ROOM_NAME_CREATE,\n        payload: {\n          name: useCustomName ? customName : roomName\n        }\n      });\n\n      // Navigate to next step\n      navigate(\"/RoomNamingForm\");\n    }\n    if (validateForm()) {\n      navigate(\"/RoomImageForm\");\n    }\n  };\n  const styles = {\n    bookingApp: {\n      minHeight: \"100vh\",\n      backgroundColor: \"#f8f9fa\"\n    },\n    container: {\n      maxWidth: \"1000px\",\n      margin: \"50px auto\",\n      padding: \"20px\"\n    },\n    title: {\n      fontSize: \"28px\",\n      fontWeight: \"bold\",\n      marginBottom: \"30px\"\n    },\n    formSection: {\n      marginBottom: \"20px\"\n    },\n    infoCard: {\n      border: \"1px solid #e7e7e7\",\n      borderRadius: \"4px\",\n      padding: \"20px\",\n      backgroundColor: \"#fff\"\n    },\n    infoHeader: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      marginBottom: \"15px\"\n    },\n    infoTitle: {\n      display: \"flex\",\n      alignItems: \"center\",\n      fontWeight: \"bold\",\n      fontSize: \"16px\"\n    },\n    lightBulbIcon: {\n      marginRight: \"10px\",\n      color: \"#febb02\"\n    },\n    closeButton: {\n      background: \"none\",\n      border: \"none\",\n      fontSize: \"20px\",\n      cursor: \"pointer\"\n    },\n    bulletPoint: {\n      marginBottom: \"10px\"\n    },\n    highlightText: {\n      color: \"#0071c2\",\n      cursor: \"pointer\"\n    },\n    buttonContainer: {\n      display: \"flex\",\n      marginTop: \"30px\"\n    },\n    backButton: {\n      width: \"50px\",\n      marginRight: \"10px\",\n      backgroundColor: \"white\",\n      color: \"#0071c2\",\n      border: \"1px solid #0071c2\"\n    },\n    continueButton: {\n      flex: 1,\n      backgroundColor: \"#0071c2\",\n      border: \"none\"\n    },\n    navbarCustom: {\n      backgroundColor: \"#003580\",\n      padding: \"10px 0\"\n    },\n    customNameSection: {\n      marginTop: \"15px\",\n      padding: \"15px\",\n      backgroundColor: \"#f8f9fa\",\n      borderRadius: \"5px\",\n      border: \"1px solid #dee2e6\"\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.bookingApp,\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      style: styles.navbarCustom,\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n          href: \"#home\",\n          className: \"text-white fw-bold\",\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            style: {\n              fontSize: 30\n            },\n            children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#f8e71c\"\n              },\n              children: \"OO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), \"M\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-label mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Th\\xF4ng tin c\\u01A1 b\\u1EA3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n          style: {\n            height: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 1, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 2, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"secondary\",\n            now: 25\n          }, 3, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"secondary\",\n            now: 25\n          }, 4, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      style: styles.container,\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: styles.title,\n        children: \"T\\xEAn c\\u1EE7a ph\\xF2ng n\\xE0y l\\xE0 g\\xEC?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 7,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.infoCard,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0110\\xE2y l\\xE0 t\\xEAn m\\xE0 kh\\xE1ch s\\u1EBD th\\u1EA5y tr\\xEAn trang ch\\u1ED7 ngh\\u1EC9 c\\u1EE7a Qu\\xFD v\\u1ECB. H\\xE3y ch\\u1ECDn t\\xEAn mi\\xEAu t\\u1EA3 ph\\xF2ng n\\xE0y ch\\xEDnh x\\xE1c nh\\u1EA5t.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              style: styles.formSection,\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"T\\xEAn ph\\xF2ng *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: roomName,\n                onChange: e => {\n                  setRoomName(e.target.value);\n                  setUseCustomName(false);\n                  if (errors.roomName) {\n                    setErrors(prev => ({\n                      ...prev,\n                      roomName: \"\"\n                    }));\n                  }\n                },\n                isInvalid: !!errors.roomName,\n                disabled: useCustomName,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn t\\xEAn ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), roomNameOptions.map(name => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: name,\n                  children: name\n                }, name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                type: \"invalid\",\n                children: errors.roomName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"useCustomName\",\n                label: \"S\\u1EED d\\u1EE5ng t\\xEAn ph\\xF2ng t\\xF9y ch\\u1EC9nh\",\n                checked: useCustomName,\n                onChange: e => {\n                  setUseCustomName(e.target.checked);\n                  if (!e.target.checked) {\n                    setCustomName(\"\");\n                    if (errors.customName) {\n                      setErrors(prev => ({\n                        ...prev,\n                        customName: \"\"\n                      }));\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), useCustomName && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.customNameSection,\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"T\\xEAn ph\\xF2ng t\\xF9y ch\\u1EC9nh *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  placeholder: \"Nh\\u1EADp t\\xEAn ph\\xF2ng t\\xF9y ch\\u1EC9nh\",\n                  value: customName,\n                  onChange: e => {\n                    setCustomName(e.target.value);\n                    if (errors.customName) {\n                      setErrors(prev => ({\n                        ...prev,\n                        customName: \"\"\n                      }));\n                    }\n                  },\n                  isInvalid: !!errors.customName,\n                  maxLength: 50\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                  type: \"invalid\",\n                  children: errors.customName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: [customName.length, \"/50 k\\xFD t\\u1EF1\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-info\",\n                children: \"\\uD83D\\uDCA1 T\\xEAn t\\xF9y ch\\u1EC9nh s\\u1EBD hi\\u1EC3n th\\u1ECB tr\\xEAn trang web c\\u1EE7a b\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 p-3\",\n              style: {\n                backgroundColor: \"#e8f4f8\",\n                borderRadius: \"5px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"T\\xEAn ph\\xF2ng s\\u1EBD hi\\u1EC3n th\\u1ECB:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: \"16px\",\n                  fontWeight: \"bold\",\n                  color: \"#0071c2\"\n                },\n                children: useCustomName ? customName || \"Chưa nhập tên\" : roomName || \"Chưa chọn tên\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.infoCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.infoHeader,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.infoTitle,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: styles.lightBulbIcon,\n                  children: \"\\uD83D\\uDCA1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), \"T\\xEAn ph\\xF2ng ti\\xEAu chu\\u1EA9n vs t\\xF9y ch\\u1EC9nh\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"T\\xEAn ph\\xF2ng ti\\xEAu chu\\u1EA9n:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  paddingLeft: \"20px\",\n                  marginTop: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"Cung c\\u1EA5p \\u0111\\u1EA7y \\u0111\\u1EE7 th\\xF4ng tin h\\u01A1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"Theo h\\u1EC7 th\\u1ED1ng th\\u1ED1ng nh\\u1EA5t tr\\xEAn trang web\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"D\\u1EC5 hi\\u1EC3u cho kh\\xE1ch qu\\u1ED1c t\\u1EBF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"\\u0110\\u01B0\\u1EE3c phi\\xEAn d\\u1ECBch sang nhi\\u1EC1u ng\\xF4n ng\\u1EEF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"T\\xEAn ph\\xF2ng t\\xF9y ch\\u1EC9nh:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  paddingLeft: \"20px\",\n                  marginTop: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"Th\\u1EC3 hi\\u1EC7n c\\xE1 t\\xEDnh ri\\xEAng c\\u1EE7a kh\\xE1ch s\\u1EA1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"T\\u1EA1o \\u1EA5n t\\u01B0\\u1EE3ng \\u0111\\u1EB7c bi\\u1EC7t v\\u1EDBi kh\\xE1ch\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"Linh ho\\u1EA1t trong c\\xE1ch \\u0111\\u1EB7t t\\xEAn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: styles.bulletPoint,\n                  children: \"Ph\\xF9 h\\u1EE3p v\\u1EDBi th\\u01B0\\u01A1ng hi\\u1EC7u c\\u1EE7a b\\u1EA1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.buttonContainer,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: styles.backButton,\n          onClick: () => {\n            navigate(\"/CreateRoom\");\n          },\n          children: \"\\u2190\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          style: styles.continueButton,\n          onClick: handleContinue,\n          children: \"Ti\\u1EBFp t\\u1EE5c\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n}\n_s(RoomNamingForm, \"pRBpigW6+YGLWmLQ+Ifn50DNruQ=\", false, function () {\n  return [useNavigate, useAppSelector, useDispatch];\n});\n_c = RoomNamingForm;\nexport default RoomNamingForm;\nvar _c;\n$RefreshReg$(_c, \"RoomNamingForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Form", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON>", "ProgressBar", "useNavigate", "RoomActions", "useDispatch", "useAppSelector", "jsxDEV", "_jsxDEV", "RoomNamingForm", "_s", "roomName", "setRoomName", "customName", "setCustomName", "useCustomName", "setUseCustomName", "errors", "setErrors", "navigate", "createRoom", "state", "Room", "name", "roomNameOptions", "validateForm", "newErrors", "trim", "length", "Object", "keys", "dispatch", "handleContinue", "type", "SAVE_ROOM_NAME_CREATE", "payload", "styles", "bookingApp", "minHeight", "backgroundColor", "container", "max<PERSON><PERSON><PERSON>", "margin", "padding", "title", "fontSize", "fontWeight", "marginBottom", "formSection", "infoCard", "border", "borderRadius", "infoHeader", "display", "justifyContent", "alignItems", "infoTitle", "lightBulbIcon", "marginRight", "color", "closeButton", "background", "cursor", "bulletPoint", "highlightText", "buttonContainer", "marginTop", "backButton", "width", "continueButton", "flex", "navbarCustom", "customNameSection", "style", "children", "Brand", "href", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "height", "variant", "now", "md", "Group", "Label", "Select", "value", "onChange", "e", "target", "prev", "isInvalid", "disabled", "map", "Control", "<PERSON><PERSON><PERSON>", "Check", "id", "label", "checked", "placeholder", "max<PERSON><PERSON><PERSON>", "Text", "paddingLeft", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/create_hotel/RoomNameForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Form,\r\n  Button,\r\n  Card,\r\n  Navbar,\r\n  ProgressBar,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport RoomActions from \"@redux/room/actions\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useAppSelector } from \"@redux/store\";\r\n\r\nfunction RoomNamingForm() {\r\n  const [roomName, setRoomName] = useState(\"\");\r\n  const [customName, setCustomName] = useState(\"\");\r\n  const [useCustomName, setUseCustomName] = useState(false);\r\n  const [errors, setErrors] = useState({});\r\n  const navigate = useNavigate();\r\n    const createRoom = useAppSelector(state => state.Room.createRoom);\r\n  useEffect(() => {\r\n      // If createRoom data exists, populate formData with it\r\n      if (createRoom.name !== \"\") {\r\n        setUseCustomName(true)\r\n        setCustomName(createRoom.name);\r\n      }\r\n    }, [createRoom]);\r\n\r\n  // Room name options\r\n  const roomNameOptions = [\r\n    \"Phòng Standard\",\r\n    \"Phòng Family\",\r\n    \"Phòng Deluxe\",\r\n    \"Phòng Suite\",\r\n    \"Phòng Superior\",\r\n    \"Phòng Executive\",\r\n    \"Phòng Premium\",\r\n    \"Phòng Luxury\",\r\n  ];\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    if (useCustomName) {\r\n      if (!customName.trim()) {\r\n        newErrors.customName = \"Tên phòng tùy chỉnh không được để trống\";\r\n      } else if (customName.trim().length < 3) {\r\n        newErrors.customName = \"Tên phòng phải có ít nhất 3 ký tự\";\r\n      } else if (customName.trim().length > 50) {\r\n        newErrors.customName = \"Tên phòng không được vượt quá 50 ký tự\";\r\n      }\r\n    } else {\r\n      if (!roomName) {\r\n        newErrors.roomName = \"Vui lòng chọn tên phòng\";\r\n      }\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const dispatch = useDispatch();\r\n  const handleContinue = () => {\r\n    if (validateForm()) {\r\n      dispatch({\r\n        type: RoomActions.SAVE_ROOM_NAME_CREATE,\r\n        payload: {\r\n          name: useCustomName ? customName : roomName,\r\n        },\r\n      });\r\n\r\n      // Navigate to next step\r\n      navigate(\"/RoomNamingForm\");\r\n    }\r\n    if (validateForm()) {\r\n      navigate(\"/RoomImageForm\");\r\n    }\r\n  };\r\n\r\n  const styles = {\r\n    bookingApp: {\r\n      minHeight: \"100vh\",\r\n      backgroundColor: \"#f8f9fa\",\r\n    },\r\n    container: {\r\n      maxWidth: \"1000px\",\r\n      margin: \"50px auto\",\r\n      padding: \"20px\",\r\n    },\r\n    title: {\r\n      fontSize: \"28px\",\r\n      fontWeight: \"bold\",\r\n      marginBottom: \"30px\",\r\n    },\r\n    formSection: {\r\n      marginBottom: \"20px\",\r\n    },\r\n    infoCard: {\r\n      border: \"1px solid #e7e7e7\",\r\n      borderRadius: \"4px\",\r\n      padding: \"20px\",\r\n      backgroundColor: \"#fff\",\r\n    },\r\n    infoHeader: {\r\n      display: \"flex\",\r\n      justifyContent: \"space-between\",\r\n      alignItems: \"center\",\r\n      marginBottom: \"15px\",\r\n    },\r\n    infoTitle: {\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      fontWeight: \"bold\",\r\n      fontSize: \"16px\",\r\n    },\r\n    lightBulbIcon: {\r\n      marginRight: \"10px\",\r\n      color: \"#febb02\",\r\n    },\r\n    closeButton: {\r\n      background: \"none\",\r\n      border: \"none\",\r\n      fontSize: \"20px\",\r\n      cursor: \"pointer\",\r\n    },\r\n    bulletPoint: {\r\n      marginBottom: \"10px\",\r\n    },\r\n    highlightText: {\r\n      color: \"#0071c2\",\r\n      cursor: \"pointer\",\r\n    },\r\n    buttonContainer: {\r\n      display: \"flex\",\r\n      marginTop: \"30px\",\r\n    },\r\n    backButton: {\r\n      width: \"50px\",\r\n      marginRight: \"10px\",\r\n      backgroundColor: \"white\",\r\n      color: \"#0071c2\",\r\n      border: \"1px solid #0071c2\",\r\n    },\r\n    continueButton: {\r\n      flex: 1,\r\n      backgroundColor: \"#0071c2\",\r\n      border: \"none\",\r\n    },\r\n    navbarCustom: {\r\n      backgroundColor: \"#003580\",\r\n      padding: \"10px 0\",\r\n    },\r\n    customNameSection: {\r\n      marginTop: \"15px\",\r\n      padding: \"15px\",\r\n      backgroundColor: \"#f8f9fa\",\r\n      borderRadius: \"5px\",\r\n      border: \"1px solid #dee2e6\",\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div style={styles.bookingApp}>\r\n      {/* Navigation Bar */}\r\n      <Navbar style={styles.navbarCustom}>\r\n        <Container>\r\n          <Navbar.Brand href=\"#home\" className=\"text-white fw-bold\">\r\n            <b style={{ fontSize: 30 }}>\r\n              UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n            </b>\r\n          </Navbar.Brand>\r\n        </Container>\r\n      </Navbar>\r\n\r\n      {/* Progress Bar */}\r\n      <Container className=\"mt-4 mb-4\">\r\n        <div className=\"progress-section\">\r\n          <div className=\"progress-label mb-2\">\r\n            <h5>Thông tin cơ bản</h5>\r\n          </div>\r\n          <ProgressBar style={{ height: \"20px\" }}>\r\n            <ProgressBar variant=\"primary\" now={25} key={1} />\r\n            <ProgressBar variant=\"primary\" now={25} key={2} />\r\n            <ProgressBar variant=\"secondary\" now={25} key={3} />\r\n            <ProgressBar variant=\"secondary\" now={25} key={4} />\r\n          </ProgressBar>\r\n        </div>\r\n      </Container>\r\n\r\n      <Container style={styles.container}>\r\n        <h1 style={styles.title}>Tên của phòng này là gì?</h1>\r\n\r\n        <Row>\r\n          <Col md={7}>\r\n            <div style={styles.infoCard}>\r\n              <p>\r\n                Đây là tên mà khách sẽ thấy trên trang chỗ nghỉ của Quý vị. Hãy\r\n                chọn tên miêu tả phòng này chính xác nhất.\r\n              </p>\r\n\r\n              <Form.Group className=\"mb-3\" style={styles.formSection}>\r\n                <Form.Label>Tên phòng *</Form.Label>\r\n\r\n                {/* Standard room names */}\r\n                <Form.Select\r\n                  value={roomName}\r\n                  onChange={(e) => {\r\n                    setRoomName(e.target.value);\r\n                    setUseCustomName(false);\r\n                    if (errors.roomName) {\r\n                      setErrors((prev) => ({ ...prev, roomName: \"\" }));\r\n                    }\r\n                  }}\r\n                  isInvalid={!!errors.roomName}\r\n                  disabled={useCustomName}\r\n                >\r\n                  <option value=\"\">Chọn tên phòng</option>\r\n                  {roomNameOptions.map((name) => (\r\n                    <option key={name} value={name}>\r\n                      {name}\r\n                    </option>\r\n                  ))}\r\n                </Form.Select>\r\n                <Form.Control.Feedback type=\"invalid\">\r\n                  {errors.roomName}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n\r\n              {/* Custom name option */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"useCustomName\"\r\n                  label=\"Sử dụng tên phòng tùy chỉnh\"\r\n                  checked={useCustomName}\r\n                  onChange={(e) => {\r\n                    setUseCustomName(e.target.checked);\r\n                    if (!e.target.checked) {\r\n                      setCustomName(\"\");\r\n                      if (errors.customName) {\r\n                        setErrors((prev) => ({ ...prev, customName: \"\" }));\r\n                      }\r\n                    }\r\n                  }}\r\n                />\r\n              </Form.Group>\r\n\r\n              {/* Custom name input */}\r\n              {useCustomName && (\r\n                <div style={styles.customNameSection}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Tên phòng tùy chỉnh *</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      placeholder=\"Nhập tên phòng tùy chỉnh\"\r\n                      value={customName}\r\n                      onChange={(e) => {\r\n                        setCustomName(e.target.value);\r\n                        if (errors.customName) {\r\n                          setErrors((prev) => ({ ...prev, customName: \"\" }));\r\n                        }\r\n                      }}\r\n                      isInvalid={!!errors.customName}\r\n                      maxLength={50}\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      {errors.customName}\r\n                    </Form.Control.Feedback>\r\n                    <Form.Text className=\"text-muted\">\r\n                      {customName.length}/50 ký tự\r\n                    </Form.Text>\r\n                  </Form.Group>\r\n                  <small className=\"text-info\">\r\n                    💡 Tên tùy chỉnh sẽ hiển thị trên trang web của bạn\r\n                  </small>\r\n                </div>\r\n              )}\r\n\r\n              {/* Preview */}\r\n              <div\r\n                className=\"mt-3 p-3\"\r\n                style={{ backgroundColor: \"#e8f4f8\", borderRadius: \"5px\" }}\r\n              >\r\n                <small className=\"text-muted\">\r\n                  <strong>Tên phòng sẽ hiển thị:</strong>\r\n                </small>\r\n                <div\r\n                  style={{\r\n                    fontSize: \"16px\",\r\n                    fontWeight: \"bold\",\r\n                    color: \"#0071c2\",\r\n                  }}\r\n                >\r\n                  {useCustomName\r\n                    ? customName || \"Chưa nhập tên\"\r\n                    : roomName || \"Chưa chọn tên\"}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n\r\n          <Col md={5}>\r\n            <div style={styles.infoCard}>\r\n              <div style={styles.infoHeader}>\r\n                <div style={styles.infoTitle}>\r\n                  <span style={styles.lightBulbIcon}>💡</span>\r\n                  Tên phòng tiêu chuẩn vs tùy chỉnh\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mb-3\">\r\n                <strong>Tên phòng tiêu chuẩn:</strong>\r\n                <ul style={{ paddingLeft: \"20px\", marginTop: \"10px\" }}>\r\n                  <li style={styles.bulletPoint}>\r\n                    Cung cấp đầy đủ thông tin hơn\r\n                  </li>\r\n                  <li style={styles.bulletPoint}>\r\n                    Theo hệ thống thống nhất trên trang web\r\n                  </li>\r\n                  <li style={styles.bulletPoint}>Dễ hiểu cho khách quốc tế</li>\r\n                  <li style={styles.bulletPoint}>\r\n                    Được phiên dịch sang nhiều ngôn ngữ\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n\r\n              <div>\r\n                <strong>Tên phòng tùy chỉnh:</strong>\r\n                <ul style={{ paddingLeft: \"20px\", marginTop: \"10px\" }}>\r\n                  <li style={styles.bulletPoint}>\r\n                    Thể hiện cá tính riêng của khách sạn\r\n                  </li>\r\n                  <li style={styles.bulletPoint}>\r\n                    Tạo ấn tượng đặc biệt với khách\r\n                  </li>\r\n                  <li style={styles.bulletPoint}>\r\n                    Linh hoạt trong cách đặt tên\r\n                  </li>\r\n                  <li style={styles.bulletPoint}>\r\n                    Phù hợp với thương hiệu của bạn\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <div style={styles.buttonContainer}>\r\n          <Button\r\n            style={styles.backButton}\r\n            onClick={() => {\r\n              navigate(\"/CreateRoom\");\r\n            }}\r\n          >\r\n            ←\r\n          </Button>\r\n          <Button style={styles.continueButton} onClick={handleContinue}>\r\n            Tiếp tục\r\n          </Button>\r\n        </div>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default RoomNamingForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,WAAW,QACN,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,cAAc,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM0B,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC5B,MAAMiB,UAAU,GAAGd,cAAc,CAACe,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACF,UAAU,CAAC;EACnE1B,SAAS,CAAC,MAAM;IACZ;IACA,IAAI0B,UAAU,CAACG,IAAI,KAAK,EAAE,EAAE;MAC1BP,gBAAgB,CAAC,IAAI,CAAC;MACtBF,aAAa,CAACM,UAAU,CAACG,IAAI,CAAC;IAChC;EACF,CAAC,EAAE,CAACH,UAAU,CAAC,CAAC;;EAElB;EACA,MAAMI,eAAe,GAAG,CACtB,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,eAAe,EACf,cAAc,CACf;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAIX,aAAa,EAAE;MACjB,IAAI,CAACF,UAAU,CAACc,IAAI,CAAC,CAAC,EAAE;QACtBD,SAAS,CAACb,UAAU,GAAG,yCAAyC;MAClE,CAAC,MAAM,IAAIA,UAAU,CAACc,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;QACvCF,SAAS,CAACb,UAAU,GAAG,mCAAmC;MAC5D,CAAC,MAAM,IAAIA,UAAU,CAACc,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,EAAE,EAAE;QACxCF,SAAS,CAACb,UAAU,GAAG,wCAAwC;MACjE;IACF,CAAC,MAAM;MACL,IAAI,CAACF,QAAQ,EAAE;QACbe,SAAS,CAACf,QAAQ,GAAG,yBAAyB;MAChD;IACF;IAEAO,SAAS,CAACQ,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIP,YAAY,CAAC,CAAC,EAAE;MAClBM,QAAQ,CAAC;QACPE,IAAI,EAAE7B,WAAW,CAAC8B,qBAAqB;QACvCC,OAAO,EAAE;UACPZ,IAAI,EAAER,aAAa,GAAGF,UAAU,GAAGF;QACrC;MACF,CAAC,CAAC;;MAEF;MACAQ,QAAQ,CAAC,iBAAiB,CAAC;IAC7B;IACA,IAAIM,YAAY,CAAC,CAAC,EAAE;MAClBN,QAAQ,CAAC,gBAAgB,CAAC;IAC5B;EACF,CAAC;EAED,MAAMiB,MAAM,GAAG;IACbC,UAAU,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,eAAe,EAAE;IACnB,CAAC;IACDC,SAAS,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,WAAW;MACnBC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,YAAY,EAAE;IAChB,CAAC;IACDC,WAAW,EAAE;MACXD,YAAY,EAAE;IAChB,CAAC;IACDE,QAAQ,EAAE;MACRC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,KAAK;MACnBR,OAAO,EAAE,MAAM;MACfJ,eAAe,EAAE;IACnB,CAAC;IACDa,UAAU,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,QAAQ;MACpBR,YAAY,EAAE;IAChB,CAAC;IACDS,SAAS,EAAE;MACTH,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBT,UAAU,EAAE,MAAM;MAClBD,QAAQ,EAAE;IACZ,CAAC;IACDY,aAAa,EAAE;MACbC,WAAW,EAAE,MAAM;MACnBC,KAAK,EAAE;IACT,CAAC;IACDC,WAAW,EAAE;MACXC,UAAU,EAAE,MAAM;MAClBX,MAAM,EAAE,MAAM;MACdL,QAAQ,EAAE,MAAM;MAChBiB,MAAM,EAAE;IACV,CAAC;IACDC,WAAW,EAAE;MACXhB,YAAY,EAAE;IAChB,CAAC;IACDiB,aAAa,EAAE;MACbL,KAAK,EAAE,SAAS;MAChBG,MAAM,EAAE;IACV,CAAC;IACDG,eAAe,EAAE;MACfZ,OAAO,EAAE,MAAM;MACfa,SAAS,EAAE;IACb,CAAC;IACDC,UAAU,EAAE;MACVC,KAAK,EAAE,MAAM;MACbV,WAAW,EAAE,MAAM;MACnBnB,eAAe,EAAE,OAAO;MACxBoB,KAAK,EAAE,SAAS;MAChBT,MAAM,EAAE;IACV,CAAC;IACDmB,cAAc,EAAE;MACdC,IAAI,EAAE,CAAC;MACP/B,eAAe,EAAE,SAAS;MAC1BW,MAAM,EAAE;IACV,CAAC;IACDqB,YAAY,EAAE;MACZhC,eAAe,EAAE,SAAS;MAC1BI,OAAO,EAAE;IACX,CAAC;IACD6B,iBAAiB,EAAE;MACjBN,SAAS,EAAE,MAAM;MACjBvB,OAAO,EAAE,MAAM;MACfJ,eAAe,EAAE,SAAS;MAC1BY,YAAY,EAAE,KAAK;MACnBD,MAAM,EAAE;IACV;EACF,CAAC;EAED,oBACE1C,OAAA;IAAKiE,KAAK,EAAErC,MAAM,CAACC,UAAW;IAAAqC,QAAA,gBAE5BlE,OAAA,CAACP,MAAM;MAACwE,KAAK,EAAErC,MAAM,CAACmC,YAAa;MAAAG,QAAA,eACjClE,OAAA,CAACb,SAAS;QAAA+E,QAAA,eACRlE,OAAA,CAACP,MAAM,CAAC0E,KAAK;UAACC,IAAI,EAAC,OAAO;UAACC,SAAS,EAAC,oBAAoB;UAAAH,QAAA,eACvDlE,OAAA;YAAGiE,KAAK,EAAE;cAAE5B,QAAQ,EAAE;YAAG,CAAE;YAAA6B,QAAA,GAAC,IACxB,eAAAlE,OAAA;cAAMiE,KAAK,EAAE;gBAAEd,KAAK,EAAE;cAAU,CAAE;cAAAe,QAAA,EAAC;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGTzE,OAAA,CAACb,SAAS;MAACkF,SAAS,EAAC,WAAW;MAAAH,QAAA,eAC9BlE,OAAA;QAAKqE,SAAS,EAAC,kBAAkB;QAAAH,QAAA,gBAC/BlE,OAAA;UAAKqE,SAAS,EAAC,qBAAqB;UAAAH,QAAA,eAClClE,OAAA;YAAAkE,QAAA,EAAI;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNzE,OAAA,CAACN,WAAW;UAACuE,KAAK,EAAE;YAAES,MAAM,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACrClE,OAAA,CAACN,WAAW;YAACiF,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzE,OAAA,CAACN,WAAW;YAACiF,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzE,OAAA,CAACN,WAAW;YAACiF,OAAO,EAAC,WAAW;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpDzE,OAAA,CAACN,WAAW;YAACiF,OAAO,EAAC,WAAW;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEZzE,OAAA,CAACb,SAAS;MAAC8E,KAAK,EAAErC,MAAM,CAACI,SAAU;MAAAkC,QAAA,gBACjClE,OAAA;QAAIiE,KAAK,EAAErC,MAAM,CAACQ,KAAM;QAAA8B,QAAA,EAAC;MAAwB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEtDzE,OAAA,CAACZ,GAAG;QAAA8E,QAAA,gBACFlE,OAAA,CAACX,GAAG;UAACwF,EAAE,EAAE,CAAE;UAAAX,QAAA,eACTlE,OAAA;YAAKiE,KAAK,EAAErC,MAAM,CAACa,QAAS;YAAAyB,QAAA,gBAC1BlE,OAAA;cAAAkE,QAAA,EAAG;YAGH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJzE,OAAA,CAACV,IAAI,CAACwF,KAAK;cAACT,SAAS,EAAC,MAAM;cAACJ,KAAK,EAAErC,MAAM,CAACY,WAAY;cAAA0B,QAAA,gBACrDlE,OAAA,CAACV,IAAI,CAACyF,KAAK;gBAAAb,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAGpCzE,OAAA,CAACV,IAAI,CAAC0F,MAAM;gBACVC,KAAK,EAAE9E,QAAS;gBAChB+E,QAAQ,EAAGC,CAAC,IAAK;kBACf/E,WAAW,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBAC3BzE,gBAAgB,CAAC,KAAK,CAAC;kBACvB,IAAIC,MAAM,CAACN,QAAQ,EAAE;oBACnBO,SAAS,CAAE2E,IAAI,KAAM;sBAAE,GAAGA,IAAI;sBAAElF,QAAQ,EAAE;oBAAG,CAAC,CAAC,CAAC;kBAClD;gBACF,CAAE;gBACFmF,SAAS,EAAE,CAAC,CAAC7E,MAAM,CAACN,QAAS;gBAC7BoF,QAAQ,EAAEhF,aAAc;gBAAA2D,QAAA,gBAExBlE,OAAA;kBAAQiF,KAAK,EAAC,EAAE;kBAAAf,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvCzD,eAAe,CAACwE,GAAG,CAAEzE,IAAI,iBACxBf,OAAA;kBAAmBiF,KAAK,EAAElE,IAAK;kBAAAmD,QAAA,EAC5BnD;gBAAI,GADMA,IAAI;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eACdzE,OAAA,CAACV,IAAI,CAACmG,OAAO,CAACC,QAAQ;gBAACjE,IAAI,EAAC,SAAS;gBAAAyC,QAAA,EAClCzD,MAAM,CAACN;cAAQ;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGbzE,OAAA,CAACV,IAAI,CAACwF,KAAK;cAACT,SAAS,EAAC,MAAM;cAAAH,QAAA,eAC1BlE,OAAA,CAACV,IAAI,CAACqG,KAAK;gBACTlE,IAAI,EAAC,UAAU;gBACfmE,EAAE,EAAC,eAAe;gBAClBC,KAAK,EAAC,qDAA6B;gBACnCC,OAAO,EAAEvF,aAAc;gBACvB2E,QAAQ,EAAGC,CAAC,IAAK;kBACf3E,gBAAgB,CAAC2E,CAAC,CAACC,MAAM,CAACU,OAAO,CAAC;kBAClC,IAAI,CAACX,CAAC,CAACC,MAAM,CAACU,OAAO,EAAE;oBACrBxF,aAAa,CAAC,EAAE,CAAC;oBACjB,IAAIG,MAAM,CAACJ,UAAU,EAAE;sBACrBK,SAAS,CAAE2E,IAAI,KAAM;wBAAE,GAAGA,IAAI;wBAAEhF,UAAU,EAAE;sBAAG,CAAC,CAAC,CAAC;oBACpD;kBACF;gBACF;cAAE;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,EAGZlE,aAAa,iBACZP,OAAA;cAAKiE,KAAK,EAAErC,MAAM,CAACoC,iBAAkB;cAAAE,QAAA,gBACnClE,OAAA,CAACV,IAAI,CAACwF,KAAK;gBAACT,SAAS,EAAC,MAAM;gBAAAH,QAAA,gBAC1BlE,OAAA,CAACV,IAAI,CAACyF,KAAK;kBAAAb,QAAA,EAAC;gBAAqB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9CzE,OAAA,CAACV,IAAI,CAACmG,OAAO;kBACXhE,IAAI,EAAC,MAAM;kBACXsE,WAAW,EAAC,6CAA0B;kBACtCd,KAAK,EAAE5E,UAAW;kBAClB6E,QAAQ,EAAGC,CAAC,IAAK;oBACf7E,aAAa,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;oBAC7B,IAAIxE,MAAM,CAACJ,UAAU,EAAE;sBACrBK,SAAS,CAAE2E,IAAI,KAAM;wBAAE,GAAGA,IAAI;wBAAEhF,UAAU,EAAE;sBAAG,CAAC,CAAC,CAAC;oBACpD;kBACF,CAAE;kBACFiF,SAAS,EAAE,CAAC,CAAC7E,MAAM,CAACJ,UAAW;kBAC/B2F,SAAS,EAAE;gBAAG;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFzE,OAAA,CAACV,IAAI,CAACmG,OAAO,CAACC,QAAQ;kBAACjE,IAAI,EAAC,SAAS;kBAAAyC,QAAA,EAClCzD,MAAM,CAACJ;gBAAU;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACxBzE,OAAA,CAACV,IAAI,CAAC2G,IAAI;kBAAC5B,SAAS,EAAC,YAAY;kBAAAH,QAAA,GAC9B7D,UAAU,CAACe,MAAM,EAAC,mBACrB;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACbzE,OAAA;gBAAOqE,SAAS,EAAC,WAAW;gBAAAH,QAAA,EAAC;cAE7B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eAGDzE,OAAA;cACEqE,SAAS,EAAC,UAAU;cACpBJ,KAAK,EAAE;gBAAElC,eAAe,EAAE,SAAS;gBAAEY,YAAY,EAAE;cAAM,CAAE;cAAAuB,QAAA,gBAE3DlE,OAAA;gBAAOqE,SAAS,EAAC,YAAY;gBAAAH,QAAA,eAC3BlE,OAAA;kBAAAkE,QAAA,EAAQ;gBAAsB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACRzE,OAAA;gBACEiE,KAAK,EAAE;kBACL5B,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,MAAM;kBAClBa,KAAK,EAAE;gBACT,CAAE;gBAAAe,QAAA,EAED3D,aAAa,GACVF,UAAU,IAAI,eAAe,GAC7BF,QAAQ,IAAI;cAAe;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzE,OAAA,CAACX,GAAG;UAACwF,EAAE,EAAE,CAAE;UAAAX,QAAA,eACTlE,OAAA;YAAKiE,KAAK,EAAErC,MAAM,CAACa,QAAS;YAAAyB,QAAA,gBAC1BlE,OAAA;cAAKiE,KAAK,EAAErC,MAAM,CAACgB,UAAW;cAAAsB,QAAA,eAC5BlE,OAAA;gBAAKiE,KAAK,EAAErC,MAAM,CAACoB,SAAU;gBAAAkB,QAAA,gBAC3BlE,OAAA;kBAAMiE,KAAK,EAAErC,MAAM,CAACqB,aAAc;kBAAAiB,QAAA,EAAC;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,2DAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzE,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAH,QAAA,gBACnBlE,OAAA;gBAAAkE,QAAA,EAAQ;cAAqB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCzE,OAAA;gBAAIiE,KAAK,EAAE;kBAAEiC,WAAW,EAAE,MAAM;kBAAExC,SAAS,EAAE;gBAAO,CAAE;gBAAAQ,QAAA,gBACpDlE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAE/B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLzE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAE/B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLzE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7DzE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAE/B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENzE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAAkE,QAAA,EAAQ;cAAoB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrCzE,OAAA;gBAAIiE,KAAK,EAAE;kBAAEiC,WAAW,EAAE,MAAM;kBAAExC,SAAS,EAAE;gBAAO,CAAE;gBAAAQ,QAAA,gBACpDlE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAE/B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLzE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAE/B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLzE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAE/B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLzE,OAAA;kBAAIiE,KAAK,EAAErC,MAAM,CAAC2B,WAAY;kBAAAW,QAAA,EAAC;gBAE/B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzE,OAAA;QAAKiE,KAAK,EAAErC,MAAM,CAAC6B,eAAgB;QAAAS,QAAA,gBACjClE,OAAA,CAACT,MAAM;UACL0E,KAAK,EAAErC,MAAM,CAAC+B,UAAW;UACzBwC,OAAO,EAAEA,CAAA,KAAM;YACbxF,QAAQ,CAAC,aAAa,CAAC;UACzB,CAAE;UAAAuD,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzE,OAAA,CAACT,MAAM;UAAC0E,KAAK,EAAErC,MAAM,CAACiC,cAAe;UAACsC,OAAO,EAAE3E,cAAe;UAAA0C,QAAA,EAAC;QAE/D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAACvE,EAAA,CA9VQD,cAAc;EAAA,QAKJN,WAAW,EACPG,cAAc,EA0ClBD,WAAW;AAAA;AAAAuG,EAAA,GAhDrBnG,cAAc;AAgWvB,eAAeA,cAAc;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}