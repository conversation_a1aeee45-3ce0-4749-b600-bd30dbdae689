{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\create_hotel\\\\RoomImageForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Navbar, Container, Button, Form, Card, ProgressBar, Row, Col, Spinner, Alert } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FiArrowLeft } from \"react-icons/fi\";\nimport { useNavigate } from \"react-router-dom\";\nimport { toast } from \"react-toastify\";\nimport { useDispatch } from \"react-redux\";\nimport RoomActions from \"@redux/room/actions\";\nimport { useAppDispatch, useAppSelector } from \"@redux/store\";\nimport Factories from \"@redux/room/factories\"; // Add this import\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction RoomImageForm() {\n  _s();\n  const navigate = useNavigate();\n  const [images, setImages] = useState([]);\n  const [imageFiles, setImageFiles] = useState([]); // Add this for storing files\n  const [isUploadingImages, setIsUploadingImages] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [errors, setErrors] = useState({});\n  const createRoom = useAppSelector(state => state.Room.createRoom);\n  console.log(\"createRoom:\", createRoom);\n  useEffect(() => {\n    // If createRoom data exists, populate images with it\n    if (createRoom.images && createRoom.images.length > 0) {\n      setImages(createRoom.images);\n    }\n  }, [createRoom]);\n  const handleImageChange = async event => {\n    const files = Array.from(event.target.files);\n    if (files.length === 0) return;\n\n    // Validate file types\n    const validTypes = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/webp\"];\n    const invalidFiles = files.filter(file => !validTypes.includes(file.type));\n    if (invalidFiles.length > 0) {\n      toast.error(\"Chỉ chấp nhận file ảnh định dạng JPG, PNG, WEBP\");\n      return;\n    }\n\n    // Validate file sizes (max 5MB each)\n    const oversizedFiles = files.filter(file => file.size > 5 * 1024 * 1024);\n    if (oversizedFiles.length > 0) {\n      toast.error(\"Kích thước file không được vượt quá 5MB\");\n      return;\n    }\n    setIsUploadingImages(true);\n    setUploadProgress(0);\n    try {\n      // Use Factories.uploadRoomImages like in RoomListingPage\n      const uploadResponse = await Factories.uploadRoomImages(files);\n      if (uploadResponse && !uploadResponse.error) {\n        // Add uploaded image URLs to images array\n        const uploadedImages = uploadResponse.data.images.map(img => img.url);\n        setImages(prev => [...prev, ...uploadedImages]);\n        setUploadProgress(100);\n        toast.success(`Đã upload ${files.length} ảnh thành công!`);\n      } else {\n        throw new Error((uploadResponse === null || uploadResponse === void 0 ? void 0 : uploadResponse.message) || \"Upload failed\");\n      }\n\n      // Clear file input\n      event.target.value = \"\";\n    } catch (error) {\n      console.error(\"Error uploading images:\", error);\n      toast.error(\"Có lỗi xảy ra khi upload ảnh\");\n    } finally {\n      setIsUploadingImages(false);\n    }\n  };\n  const removeImage = index => {\n    const totalImages = images.length;\n    if (totalImages <= 5) {\n      toast.error(\"Phòng phải có ít nhất 5 ảnh!\");\n      return;\n    }\n    setImages(images.filter((_, i) => i !== index));\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    const totalImages = images.length;\n    if (totalImages < 5) {\n      newErrors.images = \"Phòng phải có ít nhất 5 ảnh\";\n    }\n\n    // Don't allow continue if images are still uploading\n    if (isUploadingImages) {\n      newErrors.uploading = \"Vui lòng chờ upload hoàn tất\";\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const dispatch = useAppDispatch();\n  const continueStep = () => {\n    if (validateForm()) {\n      dispatch({\n        type: RoomActions.SAVE_ROOM_IMAGES_CREATE,\n        payload: {\n          images: [...images] // Only save uploaded image URLs\n        }\n      });\n      console.log(\"images:\", images);\n      // Navigate to next step\n      navigate(\"/PricingSetupForm\");\n    } else {\n      if (isUploadingImages) {\n        toast.error(\"Vui lòng chờ upload hoàn tất\");\n      } else {\n        toast.error(\"Vui lòng upload đủ 5 ảnh để tiếp tục\");\n      }\n    }\n  };\n  const totalImages = images.length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"booking-app\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      className: \"navbar-custom\",\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n          href: \"#home\",\n          className: \"text-white fw-bold\",\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            style: {\n              fontSize: 30\n            },\n            children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#f8e71c\"\n              },\n              children: \"OO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), \"M\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-label mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Th\\xF4ng tin c\\u01A1 b\\u1EA3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n          style: {\n            height: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 1, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 2, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 25\n          }, 3, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"secondary\",\n            now: 25\n          }, 4, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"main-content py-4\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 7,\n          children: [/*#__PURE__*/_jsxDEV(Container, {\n            className: \"main-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"main-heading\",\n                children: \"H\\xECnh \\u1EA3nh v\\u1EC1 ph\\xF2ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"facility-form-card\",\n              style: {\n                backgroundColor: \"white\",\n                borderRadius: \"4px\",\n                padding: \"20px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: [\"H\\xECnh \\u1EA3nh ph\\xF2ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-danger\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 38\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted ms-2\",\n                      children: [\"(\", totalImages, \"/5+ \\u1EA3nh)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"file\",\n                    multiple: true,\n                    accept: \"image/*\",\n                    onChange: handleImageChange,\n                    disabled: isUploadingImages,\n                    isInvalid: !!errors.images\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                    type: \"invalid\",\n                    children: errors.images\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                    className: \"text-muted\",\n                    children: [\"Ch\\u1EA5p nh\\u1EADn JPG, PNG, WEBP. T\\u1ED1i \\u0111a 5MB m\\u1ED7i \\u1EA3nh.\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"T\\u1ED1i thi\\u1EC3u 5 \\u1EA3nh.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [\"T\\u1ED5ng s\\u1ED1 \\u1EA3nh: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: totalImages\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 38\n                      }, this), totalImages < 5 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-danger ms-2\",\n                        children: [\"(C\\u1EA7n th\\xEAm \", 5 - totalImages, \" \\u1EA3nh)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 27\n                      }, this), totalImages >= 5 && !isUploadingImages && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-success ms-2\",\n                        children: \"\\u2713 \\u0110\\u1EE7 s\\u1ED1 l\\u01B0\\u1EE3ng \\u1EA3nh\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 224,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this), isUploadingImages && /*#__PURE__*/_jsxDEV(Alert, {\n                    variant: \"info\",\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                        animation: \"border\",\n                        size: \"sm\",\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"\\u0110ang upload \\u1EA3nh l\\xEAn Cloudinary... \", uploadProgress, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"progress\",\n                      style: {\n                        height: \"8px\"\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress-bar\",\n                        role: \"progressbar\",\n                        style: {\n                          width: `${uploadProgress}%`\n                        },\n                        \"aria-valuenow\": uploadProgress,\n                        \"aria-valuemin\": \"0\",\n                        \"aria-valuemax\": \"100\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted d-block mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [\"\\u1EA2nh \\u0111\\xE3 upload (\", images.length, \"):\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      className: \"mt-2\",\n                      children: images.map((img, index) => /*#__PURE__*/_jsxDEV(Col, {\n                        md: 3,\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            position: \"relative\"\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                            src: img,\n                            alt: `Uploaded ${index + 1}`,\n                            style: {\n                              width: \"100%\",\n                              height: \"100px\",\n                              objectFit: \"cover\",\n                              borderRadius: \"5px\",\n                              border: \"1px solid #28a745\" // Green border for uploaded images\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 265,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"danger\",\n                            size: \"sm\",\n                            style: {\n                              position: \"absolute\",\n                              top: \"5px\",\n                              right: \"5px\",\n                              padding: \"2px 6px\"\n                            },\n                            onClick: () => removeImage(index),\n                            disabled: isUploadingImages || totalImages <= 5,\n                            title: totalImages <= 5 ? \"Không thể xóa - cần tối thiểu 5 ảnh\" : \"Xóa ảnh\",\n                            children: \"\\xD7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 276,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              position: \"absolute\",\n                              bottom: \"5px\",\n                              left: \"5px\",\n                              backgroundColor: \"rgba(40, 167, 69, 0.9)\",\n                              color: \"white\",\n                              padding: \"2px 6px\",\n                              borderRadius: \"3px\",\n                              fontSize: \"10px\",\n                              fontWeight: \"bold\"\n                            },\n                            children: \"\\u2713 \\u0110\\xC3 L\\u01AFU\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 291,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 264,\n                          columnNumber: 31\n                        }, this)\n                      }, `uploaded-${index}`, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), errors.images && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-danger mt-2 small\",\n                    children: errors.images\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"navigation-buttons mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              onClick: () => {\n                navigate(\"/RoomNamingForm\");\n              },\n              disabled: isUploadingImages,\n              children: /*#__PURE__*/_jsxDEV(FiArrowLeft, {\n                className: \"back-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              className: \"continue-button\",\n              onClick: continueStep,\n              disabled: isUploadingImages || totalImages < 5,\n              children: isUploadingImages ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                  animation: \"border\",\n                  size: \"sm\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), \"\\u0110ang upload...\"]\n              }, void 0, true) : \"Tiếp tục\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-cards\",\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-icon lightbulb\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      role: \"img\",\n                      \"aria-label\": \"lightbulb\",\n                      children: \"\\uD83D\\uDCA1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-content\",\n                    children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"info-title\",\n                      children: \"L\\u1EDDi khuy\\xEAn v\\u1EC1 h\\xECnh \\u1EA3nh ph\\xF2ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"info-text\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0110\\u1EC3 c\\xF3 \\u1EA3nh \\u0111\\u1EB9p nh\\u1EA5t:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"info-list\",\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Ch\\u1EE5p \\u1EA3nh trong \\u0111i\\u1EC1u ki\\u1EC7n \\xE1nh s\\xE1ng t\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Hi\\u1EC3n th\\u1ECB \\u0111\\u1EA7y \\u0111\\u1EE7 kh\\xF4ng gian ph\\xF2ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"L\\xE0m s\\u1EA1ch ph\\xF2ng tr\\u01B0\\u1EDBc khi ch\\u1EE5p\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Ch\\u1EE5p t\\u1EEB nhi\\u1EC1u g\\xF3c \\u0111\\u1ED9 kh\\xE1c nhau\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Tr\\xE1nh ch\\u1EE5p \\u1EA3nh m\\u1EDD ho\\u1EB7c nghi\\xEAng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card mt-3\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-icon thumbs-up\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      role: \"img\",\n                      \"aria-label\": \"thumbs-up\",\n                      children: \"\\uD83D\\uDC4D\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-content\",\n                    children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"info-title\",\n                      children: \"Y\\xEAu c\\u1EA7u v\\u1EC1 \\u1EA3nh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"info-list\",\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"T\\u1ED1i thi\\u1EC3u:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 398,\n                        columnNumber: 25\n                      }, this), \" 5 \\u1EA3nh\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"\\u0110\\u1ECBnh d\\u1EA1ng:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 25\n                      }, this), \" JPG, PNG, WEBP\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"K\\xEDch th\\u01B0\\u1EDBc:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 404,\n                        columnNumber: 25\n                      }, this), \" T\\u1ED1i \\u0111a 5MB/\\u1EA3nh\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Ch\\u1EA5t l\\u01B0\\u1EE3ng:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 25\n                      }, this), \" HD (1280x720) tr\\u1EDF l\\xEAn\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: \"true\",\n      children: `\n        .booking-app {\n          min-height: 100vh;\n          background-color: #f8f9fa;\n        }\n\n        .navbar-custom {\n          background-color: #003580;\n          padding: 10px 0;\n        }\n\n        .main-content {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .main-heading {\n          font-size: 28px;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 20px;\n        }\n\n        .navigation-buttons {\n          display: flex;\n          justify-content: space-between;\n        }\n\n        .back-button {\n          width: 45px;\n          height: 45px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-color: #0071c2;\n          color: #0071c2;\n        }\n\n        .continue-button {\n          flex-grow: 1;\n          margin-left: 10px;\n          height: 45px;\n          background-color: #0071c2;\n          border: none;\n          font-weight: bold;\n        }\n\n        .continue-button:hover {\n          background-color: #005999;\n        }\n\n        .continue-button:disabled {\n          background-color: #6c757d;\n          border-color: #6c757d;\n        }\n\n        .info-card {\n          background-color: #fff;\n          border-radius: 4px;\n          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n          border: none;\n        }\n\n        .info-icon {\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 50%;\n          font-size: 20px;\n        }\n\n        .thumbs-up {\n          background-color: #f5f5f5;\n          color: #0071c2;\n        }\n\n        .lightbulb {\n          background-color: #f5f5f5;\n          color: #0071c2;\n        }\n\n        .info-content {\n          flex-grow: 1;\n          padding: 0 15px;\n        }\n\n        .info-title {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 0;\n        }\n\n        .info-list {\n          padding-left: 20px;\n          margin-bottom: 0;\n        }\n\n        .info-list li {\n          margin-bottom: 5px;\n        }\n\n        .info-text {\n          font-size: 14px;\n          color: #333;\n          margin-bottom: 10px;\n          line-height: 1.5;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n}\n_s(RoomImageForm, \"ihds2ZUGFCRTzyODDDSh7dzopyo=\", false, function () {\n  return [useNavigate, useAppSelector, useAppDispatch];\n});\n_c = RoomImageForm;\nexport default RoomImageForm;\nvar _c;\n$RefreshReg$(_c, \"RoomImageForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "Container", "<PERSON><PERSON>", "Form", "Card", "ProgressBar", "Row", "Col", "Spinner", "<PERSON><PERSON>", "FiArrowLeft", "useNavigate", "toast", "useDispatch", "RoomActions", "useAppDispatch", "useAppSelector", "Factories", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoomImageForm", "_s", "navigate", "images", "setImages", "imageFiles", "setImageFiles", "isUploadingImages", "setIsUploadingImages", "uploadProgress", "setUploadProgress", "errors", "setErrors", "createRoom", "state", "Room", "console", "log", "length", "handleImageChange", "event", "files", "Array", "from", "target", "validTypes", "invalidFiles", "filter", "file", "includes", "type", "error", "oversizedFiles", "size", "uploadResponse", "uploadRoomImages", "uploadedImages", "data", "map", "img", "url", "prev", "success", "Error", "message", "value", "removeImage", "index", "totalImages", "_", "i", "validateForm", "newErrors", "uploading", "Object", "keys", "dispatch", "continueStep", "SAVE_ROOM_IMAGES_CREATE", "payload", "className", "children", "Brand", "href", "style", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "height", "variant", "now", "md", "backgroundColor", "borderRadius", "padding", "Label", "Control", "multiple", "accept", "onChange", "disabled", "isInvalid", "<PERSON><PERSON><PERSON>", "Text", "animation", "role", "width", "position", "src", "alt", "objectFit", "border", "top", "right", "onClick", "title", "bottom", "left", "fontWeight", "Body", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/create_hotel/RoomImageForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  Container,\r\n  <PERSON><PERSON>,\r\n  Form,\r\n  Card,\r\n  ProgressBar,\r\n  <PERSON>,\r\n  <PERSON>,\r\n  Spin<PERSON>,\r\n  <PERSON><PERSON>,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FiArrowLeft } from \"react-icons/fi\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport RoomActions from \"@redux/room/actions\";\r\nimport { useAppDispatch, useAppSelector } from \"@redux/store\";\r\nimport Factories from \"@redux/room/factories\"; // Add this import\r\n\r\nfunction RoomImageForm() {\r\n  const navigate = useNavigate();\r\n  const [images, setImages] = useState([]);\r\n  const [imageFiles, setImageFiles] = useState([]); // Add this for storing files\r\n  const [isUploadingImages, setIsUploadingImages] = useState(false);\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [errors, setErrors] = useState({});\r\n  const createRoom = useAppSelector(state => state.Room.createRoom);\r\n  console.log(\"createRoom:\", createRoom);\r\n\r\n  useEffect(() => {\r\n    // If createRoom data exists, populate images with it\r\n    if (createRoom.images && createRoom.images.length > 0) {\r\n      setImages(createRoom.images);\r\n    }\r\n  }, [createRoom]);\r\n\r\n  const handleImageChange = async (event) => {\r\n    const files = Array.from(event.target.files);\r\n    if (files.length === 0) return;\r\n\r\n    // Validate file types\r\n    const validTypes = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/webp\"];\r\n    const invalidFiles = files.filter(\r\n      (file) => !validTypes.includes(file.type)\r\n    );\r\n\r\n    if (invalidFiles.length > 0) {\r\n      toast.error(\"Chỉ chấp nhận file ảnh định dạng JPG, PNG, WEBP\");\r\n      return;\r\n    }\r\n\r\n    // Validate file sizes (max 5MB each)\r\n    const oversizedFiles = files.filter((file) => file.size > 5 * 1024 * 1024);\r\n    if (oversizedFiles.length > 0) {\r\n      toast.error(\"Kích thước file không được vượt quá 5MB\");\r\n      return;\r\n    }\r\n\r\n    setIsUploadingImages(true);\r\n    setUploadProgress(0);\r\n\r\n    try {\r\n      // Use Factories.uploadRoomImages like in RoomListingPage\r\n      const uploadResponse = await Factories.uploadRoomImages(files);\r\n      \r\n      if (uploadResponse && !uploadResponse.error) {\r\n        // Add uploaded image URLs to images array\r\n        const uploadedImages = uploadResponse.data.images.map((img) => img.url);\r\n        setImages((prev) => [...prev, ...uploadedImages]);\r\n        \r\n        setUploadProgress(100);\r\n        toast.success(`Đã upload ${files.length} ảnh thành công!`);\r\n      } else {\r\n        throw new Error(uploadResponse?.message || \"Upload failed\");\r\n      }\r\n\r\n      // Clear file input\r\n      event.target.value = \"\";\r\n    } catch (error) {\r\n      console.error(\"Error uploading images:\", error);\r\n      toast.error(\"Có lỗi xảy ra khi upload ảnh\");\r\n    } finally {\r\n      setIsUploadingImages(false);\r\n    }\r\n  };\r\n\r\n  const removeImage = (index) => {\r\n    const totalImages = images.length;\r\n\r\n    if (totalImages <= 5) {\r\n      toast.error(\"Phòng phải có ít nhất 5 ảnh!\");\r\n      return;\r\n    }\r\n\r\n    setImages(images.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n    const totalImages = images.length;\r\n\r\n    if (totalImages < 5) {\r\n      newErrors.images = \"Phòng phải có ít nhất 5 ảnh\";\r\n    }\r\n\r\n    // Don't allow continue if images are still uploading\r\n    if (isUploadingImages) {\r\n      newErrors.uploading = \"Vui lòng chờ upload hoàn tất\";\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const dispatch = useAppDispatch();\r\n\r\n  const continueStep = () => {\r\n    if (validateForm()) {\r\n      dispatch({\r\n        type: RoomActions.SAVE_ROOM_IMAGES_CREATE,\r\n        payload: {\r\n          images: [...images], // Only save uploaded image URLs\r\n        },\r\n      });\r\n\r\n      console.log(\"images:\", images);\r\n      // Navigate to next step\r\n      navigate(\"/PricingSetupForm\");\r\n    } else {\r\n      if (isUploadingImages) {\r\n        toast.error(\"Vui lòng chờ upload hoàn tất\");\r\n      } else {\r\n        toast.error(\"Vui lòng upload đủ 5 ảnh để tiếp tục\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const totalImages = images.length;\r\n\r\n  return (\r\n    <div className=\"booking-app\">\r\n      <Navbar className=\"navbar-custom\">\r\n        <Container>\r\n          <Navbar.Brand href=\"#home\" className=\"text-white fw-bold\">\r\n            <b style={{ fontSize: 30 }}>\r\n              UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n            </b>\r\n          </Navbar.Brand>\r\n        </Container>\r\n      </Navbar>\r\n\r\n      {/* Progress Bar */}\r\n      <Container className=\"mt-4 mb-4\">\r\n        <div className=\"progress-section\">\r\n          <div className=\"progress-label mb-2\">\r\n            <h5>Thông tin cơ bản</h5>\r\n          </div>\r\n          <ProgressBar style={{ height: \"20px\" }}>\r\n            <ProgressBar variant=\"primary\" now={25} key={1} />\r\n            <ProgressBar variant=\"primary\" now={25} key={2} />\r\n            <ProgressBar variant=\"primary\" now={25} key={3} />\r\n            <ProgressBar variant=\"secondary\" now={25} key={4} />\r\n          </ProgressBar>\r\n        </div>\r\n      </Container>\r\n\r\n      {/* Main Content */}\r\n      <Container className=\"main-content py-4\">\r\n        <Row>\r\n          <Col md={7}>\r\n            <Container className=\"main-content\">\r\n              <div className=\"mb-4\">\r\n                <h1 className=\"main-heading\">Hình ảnh về phòng</h1>\r\n              </div>\r\n\r\n              {/* Image Upload Form */}\r\n              <div\r\n                className=\"facility-form-card\"\r\n                style={{\r\n                  backgroundColor: \"white\",\r\n                  borderRadius: \"4px\",\r\n                  padding: \"20px\",\r\n                }}\r\n              >\r\n                <Row className=\"mb-3\">\r\n                  <Col md={12}>\r\n                    <Form.Label>\r\n                      Hình ảnh phòng <span className=\"text-danger\">*</span>\r\n                      <span className=\"text-muted ms-2\">\r\n                        ({totalImages}/5+ ảnh)\r\n                      </span>\r\n                    </Form.Label>\r\n\r\n                    <Form.Control\r\n                      type=\"file\"\r\n                      multiple\r\n                      accept=\"image/*\"\r\n                      onChange={handleImageChange}\r\n                      disabled={isUploadingImages}\r\n                      isInvalid={!!errors.images}\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      {errors.images}\r\n                    </Form.Control.Feedback>\r\n\r\n                    <Form.Text className=\"text-muted\">\r\n                      Chấp nhận JPG, PNG, WEBP. Tối đa 5MB mỗi ảnh.{\" \"}\r\n                      <strong>Tối thiểu 5 ảnh.</strong>\r\n                    </Form.Text>\r\n\r\n                    {/* Image count status */}\r\n                    <div className=\"mt-2\">\r\n                      <small className=\"text-muted\">\r\n                        Tổng số ảnh: <strong>{totalImages}</strong>\r\n                        {totalImages < 5 && (\r\n                          <span className=\"text-danger ms-2\">\r\n                            (Cần thêm {5 - totalImages} ảnh)\r\n                          </span>\r\n                        )}\r\n                        {totalImages >= 5 && !isUploadingImages && (\r\n                          <span className=\"text-success ms-2\">\r\n                            ✓ Đủ số lượng ảnh\r\n                          </span>\r\n                        )}\r\n                      </small>\r\n                    </div>\r\n\r\n                    {/* Loading indicator */}\r\n                    {isUploadingImages && (\r\n                      <Alert variant=\"info\" className=\"mt-3\">\r\n                        <div className=\"d-flex align-items-center mb-2\">\r\n                          <Spinner\r\n                            animation=\"border\"\r\n                            size=\"sm\"\r\n                            className=\"me-2\"\r\n                          />\r\n                          <span>Đang upload ảnh lên Cloudinary... {uploadProgress}%</span>\r\n                        </div>\r\n                        <div className=\"progress\" style={{ height: \"8px\" }}>\r\n                          <div\r\n                            className=\"progress-bar\"\r\n                            role=\"progressbar\"\r\n                            style={{ width: `${uploadProgress}%` }}\r\n                            aria-valuenow={uploadProgress}\r\n                            aria-valuemin=\"0\"\r\n                            aria-valuemax=\"100\"\r\n                          ></div>\r\n                        </div>\r\n                      </Alert>\r\n                    )}\r\n\r\n                    {/* Show uploaded images */}\r\n                    {images.length > 0 && (\r\n                      <div className=\"mt-3\">\r\n                        <small className=\"text-muted d-block mb-2\">\r\n                          <strong>Ảnh đã upload ({images.length}):</strong>\r\n                        </small>\r\n                        <Row className=\"mt-2\">\r\n                          {images.map((img, index) => (\r\n                            <Col md={3} key={`uploaded-${index}`} className=\"mb-2\">\r\n                              <div style={{ position: \"relative\" }}>\r\n                                <img\r\n                                  src={img}\r\n                                  alt={`Uploaded ${index + 1}`}\r\n                                  style={{\r\n                                    width: \"100%\",\r\n                                    height: \"100px\",\r\n                                    objectFit: \"cover\",\r\n                                    borderRadius: \"5px\",\r\n                                    border: \"1px solid #28a745\", // Green border for uploaded images\r\n                                  }}\r\n                                />\r\n                                <Button\r\n                                  variant=\"danger\"\r\n                                  size=\"sm\"\r\n                                  style={{\r\n                                    position: \"absolute\",\r\n                                    top: \"5px\",\r\n                                    right: \"5px\",\r\n                                    padding: \"2px 6px\",\r\n                                  }}\r\n                                  onClick={() => removeImage(index)}\r\n                                  disabled={isUploadingImages || totalImages <= 5}\r\n                                  title={totalImages <= 5 ? \"Không thể xóa - cần tối thiểu 5 ảnh\" : \"Xóa ảnh\"}\r\n                                >\r\n                                  ×\r\n                                </Button>\r\n                                <div\r\n                                  style={{\r\n                                    position: \"absolute\",\r\n                                    bottom: \"5px\",\r\n                                    left: \"5px\",\r\n                                    backgroundColor: \"rgba(40, 167, 69, 0.9)\",\r\n                                    color: \"white\",\r\n                                    padding: \"2px 6px\",\r\n                                    borderRadius: \"3px\",\r\n                                    fontSize: \"10px\",\r\n                                    fontWeight: \"bold\",\r\n                                  }}\r\n                                >\r\n                                  ✓ ĐÃ LƯU\r\n                                </div>\r\n                              </div>\r\n                            </Col>\r\n                          ))}\r\n                        </Row>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Error message for images */}\r\n                    {errors.images && (\r\n                      <div className=\"text-danger mt-2 small\">\r\n                        {errors.images}\r\n                      </div>\r\n                    )}\r\n                  </Col>\r\n                </Row>\r\n              </div>\r\n            </Container>\r\n\r\n            <div className=\"navigation-buttons mt-4\">\r\n              <Button\r\n                variant=\"outline-primary\"\r\n                onClick={() => {\r\n                  navigate(\"/RoomNamingForm\");\r\n                }}\r\n                disabled={isUploadingImages}\r\n              >\r\n                <FiArrowLeft className=\"back-icon\" />\r\n              </Button>\r\n              <Button\r\n                variant=\"primary\"\r\n                className=\"continue-button\"\r\n                onClick={continueStep}\r\n                disabled={isUploadingImages || totalImages < 5}\r\n              >\r\n                {isUploadingImages ? (\r\n                  <>\r\n                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                    Đang upload...\r\n                  </>\r\n                ) : (\r\n                  \"Tiếp tục\"\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n\r\n          <Col md={5}>\r\n            <div className=\"info-cards\">\r\n              <Card className=\"info-card\">\r\n                <Card.Body>\r\n                  <div className=\"d-flex justify-content-between align-items-start\">\r\n                    <div className=\"info-icon lightbulb\">\r\n                      <span role=\"img\" aria-label=\"lightbulb\">\r\n                        💡\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"info-content\">\r\n                      <h5 className=\"info-title\">\r\n                        Lời khuyên về hình ảnh phòng\r\n                      </h5>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"mt-3\">\r\n                    <p className=\"info-text\">\r\n                      <strong>Để có ảnh đẹp nhất:</strong>\r\n                    </p>\r\n                    <ul className=\"info-list\">\r\n                      <li>Chụp ảnh trong điều kiện ánh sáng tốt</li>\r\n                      <li>Hiển thị đầy đủ không gian phòng</li>\r\n                      <li>Làm sạch phòng trước khi chụp</li>\r\n                      <li>Chụp từ nhiều góc độ khác nhau</li>\r\n                      <li>Tránh chụp ảnh mờ hoặc nghiêng</li>\r\n                    </ul>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n\r\n              <Card className=\"info-card mt-3\">\r\n                <Card.Body>\r\n                  <div className=\"d-flex justify-content-between align-items-start\">\r\n                    <div className=\"info-icon thumbs-up\">\r\n                      <span role=\"img\" aria-label=\"thumbs-up\">\r\n                        👍\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"info-content\">\r\n                      <h5 className=\"info-title\">Yêu cầu về ảnh</h5>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"mt-3\">\r\n                    <ul className=\"info-list\">\r\n                      <li>\r\n                        <strong>Tối thiểu:</strong> 5 ảnh\r\n                      </li>\r\n                      <li>\r\n                        <strong>Định dạng:</strong> JPG, PNG, WEBP\r\n                      </li>\r\n                      <li>\r\n                        <strong>Kích thước:</strong> Tối đa 5MB/ảnh\r\n                      </li>\r\n                      <li>\r\n                        <strong>Chất lượng:</strong> HD (1280x720) trở lên\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n\r\n      {/* ...existing styles... */}\r\n      <style jsx=\"true\">{`\r\n        .booking-app {\r\n          min-height: 100vh;\r\n          background-color: #f8f9fa;\r\n        }\r\n\r\n        .navbar-custom {\r\n          background-color: #003580;\r\n          padding: 10px 0;\r\n        }\r\n\r\n        .main-content {\r\n          max-width: 1200px;\r\n          margin: 0 auto;\r\n        }\r\n\r\n        .main-heading {\r\n          font-size: 28px;\r\n          font-weight: bold;\r\n          color: #333;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .navigation-buttons {\r\n          display: flex;\r\n          justify-content: space-between;\r\n        }\r\n\r\n        .back-button {\r\n          width: 45px;\r\n          height: 45px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-color: #0071c2;\r\n          color: #0071c2;\r\n        }\r\n\r\n        .continue-button {\r\n          flex-grow: 1;\r\n          margin-left: 10px;\r\n          height: 45px;\r\n          background-color: #0071c2;\r\n          border: none;\r\n          font-weight: bold;\r\n        }\r\n\r\n        .continue-button:hover {\r\n          background-color: #005999;\r\n        }\r\n\r\n        .continue-button:disabled {\r\n          background-color: #6c757d;\r\n          border-color: #6c757d;\r\n        }\r\n\r\n        .info-card {\r\n          background-color: #fff;\r\n          border-radius: 4px;\r\n          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\r\n          border: none;\r\n        }\r\n\r\n        .info-icon {\r\n          width: 40px;\r\n          height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 50%;\r\n          font-size: 20px;\r\n        }\r\n\r\n        .thumbs-up {\r\n          background-color: #f5f5f5;\r\n          color: #0071c2;\r\n        }\r\n\r\n        .lightbulb {\r\n          background-color: #f5f5f5;\r\n          color: #0071c2;\r\n        }\r\n\r\n        .info-content {\r\n          flex-grow: 1;\r\n          padding: 0 15px;\r\n        }\r\n\r\n        .info-title {\r\n          font-size: 16px;\r\n          font-weight: bold;\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .info-list {\r\n          padding-left: 20px;\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .info-list li {\r\n          margin-bottom: 5px;\r\n        }\r\n\r\n        .info-text {\r\n          font-size: 14px;\r\n          color: #333;\r\n          margin-bottom: 10px;\r\n          line-height: 1.5;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default RoomImageForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,KAAK,QACA,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,OAAOC,SAAS,MAAM,uBAAuB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMqC,UAAU,GAAGnB,cAAc,CAACoB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACF,UAAU,CAAC;EACjEG,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEJ,UAAU,CAAC;EAEtCpC,SAAS,CAAC,MAAM;IACd;IACA,IAAIoC,UAAU,CAACV,MAAM,IAAIU,UAAU,CAACV,MAAM,CAACe,MAAM,GAAG,CAAC,EAAE;MACrDd,SAAS,CAACS,UAAU,CAACV,MAAM,CAAC;IAC9B;EACF,CAAC,EAAE,CAACU,UAAU,CAAC,CAAC;EAEhB,MAAMM,iBAAiB,GAAG,MAAOC,KAAK,IAAK;IACzC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACH,KAAK,CAAC;IAC5C,IAAIA,KAAK,CAACH,MAAM,KAAK,CAAC,EAAE;;IAExB;IACA,MAAMO,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IACzE,MAAMC,YAAY,GAAGL,KAAK,CAACM,MAAM,CAC9BC,IAAI,IAAK,CAACH,UAAU,CAACI,QAAQ,CAACD,IAAI,CAACE,IAAI,CAC1C,CAAC;IAED,IAAIJ,YAAY,CAACR,MAAM,GAAG,CAAC,EAAE;MAC3B5B,KAAK,CAACyC,KAAK,CAAC,iDAAiD,CAAC;MAC9D;IACF;;IAEA;IACA,MAAMC,cAAc,GAAGX,KAAK,CAACM,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1E,IAAID,cAAc,CAACd,MAAM,GAAG,CAAC,EAAE;MAC7B5B,KAAK,CAACyC,KAAK,CAAC,yCAAyC,CAAC;MACtD;IACF;IAEAvB,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAMwB,cAAc,GAAG,MAAMvC,SAAS,CAACwC,gBAAgB,CAACd,KAAK,CAAC;MAE9D,IAAIa,cAAc,IAAI,CAACA,cAAc,CAACH,KAAK,EAAE;QAC3C;QACA,MAAMK,cAAc,GAAGF,cAAc,CAACG,IAAI,CAAClC,MAAM,CAACmC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,GAAG,CAAC;QACvEpC,SAAS,CAAEqC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAGL,cAAc,CAAC,CAAC;QAEjD1B,iBAAiB,CAAC,GAAG,CAAC;QACtBpB,KAAK,CAACoD,OAAO,CAAC,aAAarB,KAAK,CAACH,MAAM,kBAAkB,CAAC;MAC5D,CAAC,MAAM;QACL,MAAM,IAAIyB,KAAK,CAAC,CAAAT,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEU,OAAO,KAAI,eAAe,CAAC;MAC7D;;MAEA;MACAxB,KAAK,CAACI,MAAM,CAACqB,KAAK,GAAG,EAAE;IACzB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CzC,KAAK,CAACyC,KAAK,CAAC,8BAA8B,CAAC;IAC7C,CAAC,SAAS;MACRvB,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMsC,WAAW,GAAIC,KAAK,IAAK;IAC7B,MAAMC,WAAW,GAAG7C,MAAM,CAACe,MAAM;IAEjC,IAAI8B,WAAW,IAAI,CAAC,EAAE;MACpB1D,KAAK,CAACyC,KAAK,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA3B,SAAS,CAACD,MAAM,CAACwB,MAAM,CAAC,CAACsB,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK,CAAC,CAAC;EACjD,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMJ,WAAW,GAAG7C,MAAM,CAACe,MAAM;IAEjC,IAAI8B,WAAW,GAAG,CAAC,EAAE;MACnBI,SAAS,CAACjD,MAAM,GAAG,6BAA6B;IAClD;;IAEA;IACA,IAAII,iBAAiB,EAAE;MACrB6C,SAAS,CAACC,SAAS,GAAG,8BAA8B;IACtD;IAEAzC,SAAS,CAACwC,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAAClC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMsC,QAAQ,GAAG/D,cAAc,CAAC,CAAC;EAEjC,MAAMgE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIN,YAAY,CAAC,CAAC,EAAE;MAClBK,QAAQ,CAAC;QACP1B,IAAI,EAAEtC,WAAW,CAACkE,uBAAuB;QACzCC,OAAO,EAAE;UACPxD,MAAM,EAAE,CAAC,GAAGA,MAAM,CAAC,CAAE;QACvB;MACF,CAAC,CAAC;MAEFa,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEd,MAAM,CAAC;MAC9B;MACAD,QAAQ,CAAC,mBAAmB,CAAC;IAC/B,CAAC,MAAM;MACL,IAAIK,iBAAiB,EAAE;QACrBjB,KAAK,CAACyC,KAAK,CAAC,8BAA8B,CAAC;MAC7C,CAAC,MAAM;QACLzC,KAAK,CAACyC,KAAK,CAAC,sCAAsC,CAAC;MACrD;IACF;EACF,CAAC;EAED,MAAMiB,WAAW,GAAG7C,MAAM,CAACe,MAAM;EAEjC,oBACErB,OAAA;IAAK+D,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BhE,OAAA,CAACnB,MAAM;MAACkF,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC/BhE,OAAA,CAAClB,SAAS;QAAAkF,QAAA,eACRhE,OAAA,CAACnB,MAAM,CAACoF,KAAK;UAACC,IAAI,EAAC,OAAO;UAACH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACvDhE,OAAA;YAAGmE,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAG,CAAE;YAAAJ,QAAA,GAAC,IACxB,eAAAhE,OAAA;cAAMmE,KAAK,EAAE;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAL,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGTzE,OAAA,CAAClB,SAAS;MAACiF,SAAS,EAAC,WAAW;MAAAC,QAAA,eAC9BhE,OAAA;QAAK+D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhE,OAAA;UAAK+D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClChE,OAAA;YAAAgE,QAAA,EAAI;UAAgB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNzE,OAAA,CAACd,WAAW;UAACiF,KAAK,EAAE;YAAEO,MAAM,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACrChE,OAAA,CAACd,WAAW;YAACyF,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzE,OAAA,CAACd,WAAW;YAACyF,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzE,OAAA,CAACd,WAAW;YAACyF,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDzE,OAAA,CAACd,WAAW;YAACyF,OAAO,EAAC,WAAW;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZzE,OAAA,CAAClB,SAAS;MAACiF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACtChE,OAAA,CAACb,GAAG;QAAA6E,QAAA,gBACFhE,OAAA,CAACZ,GAAG;UAACyF,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACThE,OAAA,CAAClB,SAAS;YAACiF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACjChE,OAAA;cAAK+D,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBhE,OAAA;gBAAI+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eAGNzE,OAAA;cACE+D,SAAS,EAAC,oBAAoB;cAC9BI,KAAK,EAAE;gBACLW,eAAe,EAAE,OAAO;gBACxBC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACX,CAAE;cAAAhB,QAAA,eAEFhE,OAAA,CAACb,GAAG;gBAAC4E,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBhE,OAAA,CAACZ,GAAG;kBAACyF,EAAE,EAAE,EAAG;kBAAAb,QAAA,gBACVhE,OAAA,CAAChB,IAAI,CAACiG,KAAK;oBAAAjB,QAAA,GAAC,4BACK,eAAAhE,OAAA;sBAAM+D,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrDzE,OAAA;sBAAM+D,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,GAAC,GAC/B,EAACb,WAAW,EAAC,eAChB;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eAEbzE,OAAA,CAAChB,IAAI,CAACkG,OAAO;oBACXjD,IAAI,EAAC,MAAM;oBACXkD,QAAQ;oBACRC,MAAM,EAAC,SAAS;oBAChBC,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE5E,iBAAkB;oBAC5B6E,SAAS,EAAE,CAAC,CAACzE,MAAM,CAACR;kBAAO;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACFzE,OAAA,CAAChB,IAAI,CAACkG,OAAO,CAACM,QAAQ;oBAACvD,IAAI,EAAC,SAAS;oBAAA+B,QAAA,EAClClD,MAAM,CAACR;kBAAM;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eAExBzE,OAAA,CAAChB,IAAI,CAACyG,IAAI;oBAAC1B,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,6EACa,EAAC,GAAG,eACjDhE,OAAA;sBAAAgE,QAAA,EAAQ;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eAGZzE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBhE,OAAA;sBAAO+D,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,8BACf,eAAAhE,OAAA;wBAAAgE,QAAA,EAASb;sBAAW;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,EAC1CtB,WAAW,GAAG,CAAC,iBACdnD,OAAA;wBAAM+D,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,GAAC,oBACvB,EAAC,CAAC,GAAGb,WAAW,EAAC,YAC7B;sBAAA;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP,EACAtB,WAAW,IAAI,CAAC,IAAI,CAACzC,iBAAiB,iBACrCV,OAAA;wBAAM+D,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAEpC;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,EAGL/D,iBAAiB,iBAChBV,OAAA,CAACV,KAAK;oBAACqF,OAAO,EAAC,MAAM;oBAACZ,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACpChE,OAAA;sBAAK+D,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7ChE,OAAA,CAACX,OAAO;wBACNqG,SAAS,EAAC,QAAQ;wBAClBtD,IAAI,EAAC,IAAI;wBACT2B,SAAS,EAAC;sBAAM;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACFzE,OAAA;wBAAAgE,QAAA,GAAM,iDAAkC,EAACpD,cAAc,EAAC,GAAC;sBAAA;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACNzE,OAAA;sBAAK+D,SAAS,EAAC,UAAU;sBAACI,KAAK,EAAE;wBAAEO,MAAM,EAAE;sBAAM,CAAE;sBAAAV,QAAA,eACjDhE,OAAA;wBACE+D,SAAS,EAAC,cAAc;wBACxB4B,IAAI,EAAC,aAAa;wBAClBxB,KAAK,EAAE;0BAAEyB,KAAK,EAAE,GAAGhF,cAAc;wBAAI,CAAE;wBACvC,iBAAeA,cAAe;wBAC9B,iBAAc,GAAG;wBACjB,iBAAc;sBAAK;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACR,EAGAnE,MAAM,CAACe,MAAM,GAAG,CAAC,iBAChBrB,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAO+D,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,eACxChE,OAAA;wBAAAgE,QAAA,GAAQ,8BAAe,EAAC1D,MAAM,CAACe,MAAM,EAAC,IAAE;sBAAA;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACRzE,OAAA,CAACb,GAAG;sBAAC4E,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB1D,MAAM,CAACmC,GAAG,CAAC,CAACC,GAAG,EAAEQ,KAAK,kBACrBlD,OAAA,CAACZ,GAAG;wBAACyF,EAAE,EAAE,CAAE;wBAA2Bd,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACpDhE,OAAA;0BAAKmE,KAAK,EAAE;4BAAE0B,QAAQ,EAAE;0BAAW,CAAE;0BAAA7B,QAAA,gBACnChE,OAAA;4BACE8F,GAAG,EAAEpD,GAAI;4BACTqD,GAAG,EAAE,YAAY7C,KAAK,GAAG,CAAC,EAAG;4BAC7BiB,KAAK,EAAE;8BACLyB,KAAK,EAAE,MAAM;8BACblB,MAAM,EAAE,OAAO;8BACfsB,SAAS,EAAE,OAAO;8BAClBjB,YAAY,EAAE,KAAK;8BACnBkB,MAAM,EAAE,mBAAmB,CAAE;4BAC/B;0BAAE;4BAAA3B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACFzE,OAAA,CAACjB,MAAM;4BACL4F,OAAO,EAAC,QAAQ;4BAChBvC,IAAI,EAAC,IAAI;4BACT+B,KAAK,EAAE;8BACL0B,QAAQ,EAAE,UAAU;8BACpBK,GAAG,EAAE,KAAK;8BACVC,KAAK,EAAE,KAAK;8BACZnB,OAAO,EAAE;4BACX,CAAE;4BACFoB,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAACC,KAAK,CAAE;4BAClCoC,QAAQ,EAAE5E,iBAAiB,IAAIyC,WAAW,IAAI,CAAE;4BAChDkD,KAAK,EAAElD,WAAW,IAAI,CAAC,GAAG,qCAAqC,GAAG,SAAU;4BAAAa,QAAA,EAC7E;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACTzE,OAAA;4BACEmE,KAAK,EAAE;8BACL0B,QAAQ,EAAE,UAAU;8BACpBS,MAAM,EAAE,KAAK;8BACbC,IAAI,EAAE,KAAK;8BACXzB,eAAe,EAAE,wBAAwB;8BACzCT,KAAK,EAAE,OAAO;8BACdW,OAAO,EAAE,SAAS;8BAClBD,YAAY,EAAE,KAAK;8BACnBX,QAAQ,EAAE,MAAM;8BAChBoC,UAAU,EAAE;4BACd,CAAE;4BAAAxC,QAAA,EACH;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC,GA3CS,YAAYvB,KAAK,EAAE;wBAAAoB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA4C/B,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA3D,MAAM,CAACR,MAAM,iBACZN,OAAA;oBAAK+D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EACpClD,MAAM,CAACR;kBAAM;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEZzE,OAAA;YAAK+D,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtChE,OAAA,CAACjB,MAAM;cACL4F,OAAO,EAAC,iBAAiB;cACzByB,OAAO,EAAEA,CAAA,KAAM;gBACb/F,QAAQ,CAAC,iBAAiB,CAAC;cAC7B,CAAE;cACFiF,QAAQ,EAAE5E,iBAAkB;cAAAsD,QAAA,eAE5BhE,OAAA,CAACT,WAAW;gBAACwE,SAAS,EAAC;cAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACTzE,OAAA,CAACjB,MAAM;cACL4F,OAAO,EAAC,SAAS;cACjBZ,SAAS,EAAC,iBAAiB;cAC3BqC,OAAO,EAAExC,YAAa;cACtB0B,QAAQ,EAAE5E,iBAAiB,IAAIyC,WAAW,GAAG,CAAE;cAAAa,QAAA,EAE9CtD,iBAAiB,gBAChBV,OAAA,CAAAE,SAAA;gBAAA8D,QAAA,gBACEhE,OAAA,CAACX,OAAO;kBAACqG,SAAS,EAAC,QAAQ;kBAACtD,IAAI,EAAC,IAAI;kBAAC2B,SAAS,EAAC;gBAAM;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAE3D;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzE,OAAA,CAACZ,GAAG;UAACyF,EAAE,EAAE,CAAE;UAAAb,QAAA,eACThE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhE,OAAA,CAACf,IAAI;cAAC8E,SAAS,EAAC,WAAW;cAAAC,QAAA,eACzBhE,OAAA,CAACf,IAAI,CAACwH,IAAI;gBAAAzC,QAAA,gBACRhE,OAAA;kBAAK+D,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBAC/DhE,OAAA;oBAAK+D,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClChE,OAAA;sBAAM2F,IAAI,EAAC,KAAK;sBAAC,cAAW,WAAW;sBAAA3B,QAAA,EAAC;oBAExC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNzE,OAAA;oBAAK+D,SAAS,EAAC,cAAc;oBAAAC,QAAA,eAC3BhE,OAAA;sBAAI+D,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAE3B;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzE,OAAA;kBAAK+D,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBhE,OAAA;oBAAG+D,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACtBhE,OAAA;sBAAAgE,QAAA,EAAQ;oBAAmB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACJzE,OAAA;oBAAI+D,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACvBhE,OAAA;sBAAAgE,QAAA,EAAI;oBAAqC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9CzE,OAAA;sBAAAgE,QAAA,EAAI;oBAAgC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzCzE,OAAA;sBAAAgE,QAAA,EAAI;oBAA6B;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtCzE,OAAA;sBAAAgE,QAAA,EAAI;oBAA8B;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvCzE,OAAA;sBAAAgE,QAAA,EAAI;oBAA8B;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEPzE,OAAA,CAACf,IAAI;cAAC8E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC9BhE,OAAA,CAACf,IAAI,CAACwH,IAAI;gBAAAzC,QAAA,gBACRhE,OAAA;kBAAK+D,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBAC/DhE,OAAA;oBAAK+D,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClChE,OAAA;sBAAM2F,IAAI,EAAC,KAAK;sBAAC,cAAW,WAAW;sBAAA3B,QAAA,EAAC;oBAExC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNzE,OAAA;oBAAK+D,SAAS,EAAC,cAAc;oBAAAC,QAAA,eAC3BhE,OAAA;sBAAI+D,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzE,OAAA;kBAAK+D,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBhE,OAAA;oBAAI+D,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACvBhE,OAAA;sBAAAgE,QAAA,gBACEhE,OAAA;wBAAAgE,QAAA,EAAQ;sBAAU;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC7B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzE,OAAA;sBAAAgE,QAAA,gBACEhE,OAAA;wBAAAgE,QAAA,EAAQ;sBAAU;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,mBAC7B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzE,OAAA;sBAAAgE,QAAA,gBACEhE,OAAA;wBAAAgE,QAAA,EAAQ;sBAAW;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,kCAC9B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzE,OAAA;sBAAAgE,QAAA,gBACEhE,OAAA;wBAAAgE,QAAA,EAAQ;sBAAW;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,kCAC9B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZzE,OAAA;MAAO0G,GAAG,EAAC,MAAM;MAAA1C,QAAA,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACrE,EAAA,CA5fQD,aAAa;EAAA,QACHX,WAAW,EAMTK,cAAc,EAwFhBD,cAAc;AAAA;AAAA+G,EAAA,GA/FxBxG,aAAa;AA8ftB,eAAeA,aAAa;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}