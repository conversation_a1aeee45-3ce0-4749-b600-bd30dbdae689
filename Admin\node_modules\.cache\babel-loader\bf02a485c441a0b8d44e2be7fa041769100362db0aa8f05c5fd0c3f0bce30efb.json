{"ast": null, "code": "// Admin Dashboard Actions\nexport const FETCH_ADMIN_DASHBOARD_METRICS = 'FETCH_ADMIN_DASHBOARD_METRICS';\nexport const FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS = 'FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS';\nexport const FETCH_ADMIN_DASHBOARD_METRICS_FAILURE = 'FETCH_ADMIN_DASHBOARD_METRICS_FAILURE';\nconst AdminDashboardActions = {\n  FETCH_ADMIN_DASHBOARD_METRICS,\n  FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS,\n  FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n  // Action creators\n  fetchAdminDashboardMetrics: payload => ({\n    type: FETCH_ADMIN_DASHBOARD_METRICS,\n    payload\n  }),\n  fetchAdminDashboardMetricsSuccess: data => ({\n    type: FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS,\n    payload: data\n  }),\n  fetchAdminDashboardMetricsFailure: error => ({\n    type: FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n    payload: error\n  })\n};\nexport default AdminDashboardActions;", "map": {"version": 3, "names": ["FETCH_ADMIN_DASHBOARD_METRICS", "FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS", "FETCH_ADMIN_DASHBOARD_METRICS_FAILURE", "AdminDashboardActions", "fetchAdminDashboardMetrics", "payload", "type", "fetchAdminDashboardMetricsSuccess", "data", "fetchAdminDashboardMetricsFailure", "error"], "sources": ["E:/WDP301_UROOM/Admin/src/redux/adminDashboard/actions.js"], "sourcesContent": ["// Admin Dashboard Actions\nexport const FET<PERSON>_ADMIN_DASHBOARD_METRICS = 'FETCH_ADMIN_DASHBOARD_METRICS';\nexport const FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS = 'FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS';\nexport const FETCH_ADMIN_DASHBOARD_METRICS_FAILURE = 'FETCH_ADMIN_DASHBOARD_METRICS_FAILURE';\n\nconst AdminDashboardActions = {\n  FETCH_ADMIN_DASHBOARD_METRICS,\n  FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS,\n  FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n\n  // Action creators\n  fetchAdminDashboardMetrics: (payload) => ({\n    type: FETCH_ADMIN_DASHBOARD_METRICS,\n    payload\n  }),\n\n  fetchAdminDashboardMetricsSuccess: (data) => ({\n    type: FETCH_ADMIN_DASHBOARD_METRICS_SUCCESS,\n    payload: data\n  }),\n\n  fetchAdminDashboardMetricsFailure: (error) => ({\n    type: FETCH_ADMIN_DASHBOARD_METRICS_FAILURE,\n    payload: error\n  })\n};\n\nexport default AdminDashboardActions;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,6BAA6B,GAAG,+BAA+B;AAC5E,OAAO,MAAMC,qCAAqC,GAAG,uCAAuC;AAC5F,OAAO,MAAMC,qCAAqC,GAAG,uCAAuC;AAE5F,MAAMC,qBAAqB,GAAG;EAC5BH,6BAA6B;EAC7BC,qCAAqC;EACrCC,qCAAqC;EAErC;EACAE,0BAA0B,EAAGC,OAAO,KAAM;IACxCC,IAAI,EAAEN,6BAA6B;IACnCK;EACF,CAAC,CAAC;EAEFE,iCAAiC,EAAGC,IAAI,KAAM;IAC5CF,IAAI,EAAEL,qCAAqC;IAC3CI,OAAO,EAAEG;EACX,CAAC,CAAC;EAEFC,iCAAiC,EAAGC,KAAK,KAAM;IAC7CJ,IAAI,EAAEJ,qCAAqC;IAC3CG,OAAO,EAAEK;EACX,CAAC;AACH,CAAC;AAED,eAAeP,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}