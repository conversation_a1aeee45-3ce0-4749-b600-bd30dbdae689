{"version": 3, "file": "main.js", "sources": ["../src/main.ts"], "sourcesContent": ["import _provinces from './data/provinces.json';\nimport _districts from './data/districts.json';\nimport _wards from './data/wards.json';\n\nimport { Province, District, Ward, ProvinceDetail } from './types';\n\nexport const provinces: Province[] = _provinces;\nexport const districts: District[] = _districts;\nexport const wards: Ward[] = _wards;\n\nexport const getProvinces = (): Province[] => {\n  return provinces;\n};\n\nexport const getDistricts = (province_code?: string): District[] => {\n  return province_code\n    ? districts.filter((d) => d.province_code === province_code)\n    : districts;\n};\n\nexport const getWards = (district_code?: string): Ward[] => {\n  return district_code\n    ? wards.filter((ward) => ward.district_code === district_code)\n    : wards;\n};\n\nexport const getProvincesWithDetail = (code?: string): ProvinceDetail => {\n  const tree: any = {};\n  provinces.forEach((province: Province) => {\n    tree[province.code] = province;\n  });\n\n  districts.forEach((district) => {\n    if (!tree[district.province_code].districts) {\n      tree[district.province_code].districts = {};\n    }\n    tree[district.province_code].districts[district.code] = district;\n  });\n\n  wards.forEach((ward) => {\n    if (!tree[ward.province_code].districts[ward.district_code].wards) {\n      tree[ward.province_code].districts[ward.district_code].wards = {};\n    }\n    tree[ward.province_code].districts[ward.district_code].wards[ward.code] =\n      ward;\n  });\n\n  return code ? tree[code] : tree;\n};\n\nexport { Province, District, Ward, ProvinceDetail };\n"], "names": ["provinces", "districts", "province_code", "filter", "d", "code", "tree", "for<PERSON>ach", "province", "district", "wards", "ward", "district_code"], "mappings": "IAMsBA,o6FACAC,0wmrEAOM,SAACC,GAC3B,OAAoBA,EAChBD,EAAUE,OAAO,SAACC,GAAD,OAAQA,EAACF,gBAAkBA,IAC5CD,wBAPsB,WAC1B,OACDD,kCAcqC,SAACK,GACrC,IAAUC,EAAQ,GAoBlB,OAnBAN,EAAUO,QAAQ,SAACC,GACjBF,EAAKE,EAASH,MAAQG,IAGxBP,EAAUM,QAAQ,SAACE,GACZH,EAAKG,EAASP,eAAeD,YAChCK,EAAKG,EAASP,eAAeD,UAAY,IAE3CK,EAAKG,EAASP,eAAeD,UAAUQ,EAASJ,MAAQI,IAG1DC,EAAMH,QAAQ,SAACI,GACRL,EAAKK,EAAKT,eAAeD,UAAUU,EAAKC,eAAeF,QAC1DJ,EAAKK,EAAKT,eAAeD,UAAUU,EAAKC,eAAeF,MAAQ,IAEjEJ,EAAKK,EAAKT,eAAeD,UAAUU,EAAKC,eAAeF,MAAMC,EAAKN,MAChEM,IAGGN,EAAOC,EAAKD,GAAQC,oBA3BL,SAACM,GACvB,OAAoBA,EAChBF,EAAMP,OAAO,SAACQ,UAAaA,EAACC,gBAAkBA,IAC9CF"}