{"ast": null, "code": "const RoomActions = {\n  // Room CRUD Actions\n  FETCH_ROOMS_SUCCESS: 'FETCH_ROOMS_SUCCESS',\n  FETCH_ROOM_DETAIL_SUCCESS: 'FETCH_ROOM_DETAIL_SUCCESS',\n  CREATE_ROOM_SUCCESS: 'CREATE_ROOM_SUCCESS',\n  UPDATE_ROOM_SUCCESS: 'UPDATE_ROOM_SUCCESS',\n  DELETE_ROOM_SUCCESS: 'DELETE_ROOM_SUCCESS',\n  // Room Create Form Actions\n  SAVE_ROOM_NAME_CREATE: 'SAVE_ROOM_NAME_CREATE',\n  SAVE_ROOM_IMAGES_CREATE: 'SAVE_ROOM_IMAGES_CREATE',\n  SAVE_ROOM_PRICING_CREATE: 'SAVE_ROOM_PRICING_CREATE',\n  SAVE_ROOM_DETAILS_CREATE: 'SAVE_ROOM_DETAILS_CREATE',\n  CLEAR_ROOM_CREATE: 'CLEAR_ROOM_CREATE',\n  // Room Create List Management (for hotel creation process)\n  SAVE_ROOM_TO_CREATE_LIST: 'SAVE_ROOM_TO_CREATE_LIST',\n  EDIT_ROOM_IN_CREATE_LIST: 'EDIT_ROOM_IN_CREATE_LIST',\n  DELETE_ROOM_FROM_CREATE_LIST: 'DELETE_ROOM_FROM_CREATE_LIST',\n  CLEAR_ROOM_CREATE_LIST: 'CLEAR_ROOM_CREATE_LIST',\n  // Room Status Actions\n  TOGGLE_ROOM_STATUS: 'TOGGLE_ROOM_STATUS',\n  SET_ROOM_AVAILABILITY: 'SET_ROOM_AVAILABILITY',\n  // Loading States\n  SET_LOADING: 'SET_LOADING',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  FETCH_ROOM: \"FETCH_ROOM\",\n  FETCH_ROOM_SUCCESS: \"FETCH_ROOM_SUCCESS\"\n};\nexport default RoomActions;", "map": {"version": 3, "names": ["RoomActions", "FETCH_ROOMS_SUCCESS", "FETCH_ROOM_DETAIL_SUCCESS", "CREATE_ROOM_SUCCESS", "UPDATE_ROOM_SUCCESS", "DELETE_ROOM_SUCCESS", "SAVE_ROOM_NAME_CREATE", "SAVE_ROOM_IMAGES_CREATE", "SAVE_ROOM_PRICING_CREATE", "SAVE_ROOM_DETAILS_CREATE", "CLEAR_ROOM_CREATE", "SAVE_ROOM_TO_CREATE_LIST", "EDIT_ROOM_IN_CREATE_LIST", "DELETE_ROOM_FROM_CREATE_LIST", "CLEAR_ROOM_CREATE_LIST", "TOGGLE_ROOM_STATUS", "SET_ROOM_AVAILABILITY", "SET_LOADING", "SET_ERROR", "CLEAR_ERROR", "FETCH_ROOM", "FETCH_ROOM_SUCCESS"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/room/actions.js"], "sourcesContent": ["const RoomActions = {\r\n  // Room CRUD Actions\r\n  FETCH_ROOMS_SUCCESS: 'FETCH_ROOMS_SUCCESS',\r\n  FETCH_ROOM_DETAIL_SUCCESS: 'FETCH_ROOM_DETAIL_SUCCESS',\r\n  CREATE_ROOM_SUCCESS: 'CREATE_ROOM_SUCCESS',\r\n  UPDATE_ROOM_SUCCESS: 'UPDATE_ROOM_SUCCESS',\r\n  DELETE_ROOM_SUCCESS: 'DELETE_ROOM_SUCCESS',\r\n  \r\n  // Room Create Form Actions\r\n  SAVE_ROOM_NAME_CREATE: 'SAVE_ROOM_NAME_CREATE',\r\n  SAVE_ROOM_IMAGES_CREATE: 'SAVE_ROOM_IMAGES_CREATE', \r\n  SAVE_ROOM_PRICING_CREATE: 'SAVE_ROOM_PRICING_CREATE',\r\n  SAVE_ROOM_DETAILS_CREATE: 'SAVE_ROOM_DETAILS_CREATE',\r\n  CLEAR_ROOM_CREATE: 'CLEAR_ROOM_CREATE',\r\n  \r\n  // Room Create List Management (for hotel creation process)\r\n  SAVE_ROOM_TO_CREATE_LIST: 'SAVE_ROOM_TO_CREATE_LIST',\r\n  EDIT_ROOM_IN_CREATE_LIST: 'EDIT_ROOM_IN_CREATE_LIST',\r\n  DELETE_ROOM_FROM_CREATE_LIST: 'DELETE_ROOM_FROM_CREATE_LIST',\r\n  CLEAR_ROOM_CREATE_LIST: 'CLEAR_ROOM_CREATE_LIST',\r\n  \r\n  // Room Status Actions\r\n  TOGGLE_ROOM_STATUS: 'TOGGLE_ROOM_STATUS',\r\n  SET_ROOM_AVAILABILITY: 'SET_ROOM_AVAILABILITY',\r\n  \r\n  // Loading States\r\n  SET_LOADING: 'SET_LOADING',\r\n  SET_ERROR: 'SET_ERROR',\r\n  CLEAR_ERROR: 'CLEAR_ERROR',\r\n\r\n  FETCH_ROOM:\"FETCH_ROOM\",\r\n  FETCH_ROOM_SUCCESS:\"FETCH_ROOM_SUCCESS\",\r\n};\r\n\r\nexport default RoomActions;\r\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAClB;EACAC,mBAAmB,EAAE,qBAAqB;EAC1CC,yBAAyB,EAAE,2BAA2B;EACtDC,mBAAmB,EAAE,qBAAqB;EAC1CC,mBAAmB,EAAE,qBAAqB;EAC1CC,mBAAmB,EAAE,qBAAqB;EAE1C;EACAC,qBAAqB,EAAE,uBAAuB;EAC9CC,uBAAuB,EAAE,yBAAyB;EAClDC,wBAAwB,EAAE,0BAA0B;EACpDC,wBAAwB,EAAE,0BAA0B;EACpDC,iBAAiB,EAAE,mBAAmB;EAEtC;EACAC,wBAAwB,EAAE,0BAA0B;EACpDC,wBAAwB,EAAE,0BAA0B;EACpDC,4BAA4B,EAAE,8BAA8B;EAC5DC,sBAAsB,EAAE,wBAAwB;EAEhD;EACAC,kBAAkB,EAAE,oBAAoB;EACxCC,qBAAqB,EAAE,uBAAuB;EAE9C;EACAC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAE1BC,UAAU,EAAC,YAAY;EACvBC,kBAAkB,EAAC;AACrB,CAAC;AAED,eAAerB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}