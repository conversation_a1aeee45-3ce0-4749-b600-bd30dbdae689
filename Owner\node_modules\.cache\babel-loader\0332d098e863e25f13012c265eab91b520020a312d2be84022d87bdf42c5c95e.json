{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\management_booking\\\\ManagementBooking.jsx\",\n  _s = $RefreshSig$();\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport Factories from \"@redux/hotel/factories\";\nimport RoomActions from \"@redux/room/actions\";\nimport { useAppDispatch, useAppSelector } from \"@redux/store\";\nimport Utils from \"@utils/Utils\";\nimport { useEffect, useState } from \"react\";\nimport Select from \"react-select\";\n\n// Options for select inputs\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst adultsOptions = Array.from({\n  length: 20\n}, (_, i) => ({\n  value: i + 1,\n  label: `${i + 1} Adults`\n}));\nconst childrenOptions = Array.from({\n  length: 11\n}, (_, i) => ({\n  value: i,\n  label: `${i} Children`\n}));\n\n// Select styles\nconst selectStyles = {\n  control: provided => ({\n    ...provided,\n    border: \"1px solid #ddd\",\n    borderRadius: \"8px\",\n    minHeight: \"40px\",\n    boxShadow: \"none\",\n    \"&:hover\": {\n      borderColor: \"#1a2b49\"\n    }\n  })\n};\nconst ManagementBooking = () => {\n  _s();\n  var _hotelInfo$, _hotelInfo$$services;\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const [hotelInfo, setHotelInfo] = useState(null);\n  const [hotelId, setHotelId] = useState(\"\");\n  const [rooms, setRooms] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [isSearching, setIsSearching] = useState(false);\n\n  // Date states\n  const today = new Date().toISOString().split(\"T\")[0];\n  const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split(\"T\")[0];\n  const [checkinDate, setCheckinDate] = useState(today);\n  const [checkoutDate, setCheckoutDate] = useState(tomorrow);\n\n  // Guest selection states\n  const [selectedAdults, setSelectedAdults] = useState(adultsOptions[1]);\n  const [selectedChildren, setSelectedChildren] = useState(childrenOptions[0]);\n  const [searchParams, setSearchParams] = useState({\n    checkinDate: today,\n    checkoutDate: tomorrow,\n    numberOfPeople: 1,\n    page: 1,\n    limit: 10\n  });\n\n  // Add new states for reservation modal\n  const [showReservationModal, setShowReservationModal] = useState(false);\n  const [selectedRoomsForReservation, setSelectedRoomsForReservation] = useState({});\n  const [selectedServicesForReservation, setSelectedServicesForReservation] = useState({});\n  const [serviceQuantities, setServiceQuantities] = useState({});\n  const [serviceSelectedDates, setServiceSelectedDates] = useState({});\n  const [reservationCustomer, setReservationCustomer] = useState({\n    name: \"\",\n    email: \"\",\n    phone: \"\"\n  });\n  const dispatch = useAppDispatch();\n  const fetchHotelInfo = () => {\n    setLoading(true);\n    dispatch({\n      type: HotelActions.FETCH_OWNER_HOTEL,\n      payload: {\n        userId: Auth._id,\n        onSuccess: data => {\n          setHotelInfo(data.hotels);\n          setHotelId(data.hotels[0]._id);\n          setLoading(false);\n        },\n        onFailed: () => {\n          showToast.error(\"Lấy thông tin khách sạn thất bại\");\n          setLoading(false);\n        },\n        onError: err => {\n          console.error(err);\n          showToast.error(\"Lỗi máy chủ khi lấy thông tin khách sạn\");\n          setLoading(false);\n        }\n      }\n    });\n  };\n  useEffect(() => {\n    fetchHotelInfo();\n  }, []);\n\n  // Update searchParams when dates or guest count changes\n  useEffect(() => {\n    setSearchParams({\n      checkinDate: checkinDate,\n      checkoutDate: checkoutDate,\n      numberOfPeople: selectedAdults.value + selectedChildren.value,\n      page: 1,\n      limit: 10\n    });\n  }, [checkinDate, checkoutDate, selectedAdults, selectedChildren]);\n  const fetchRooms = isMounted => {\n    dispatch({\n      type: RoomActions.FETCH_ROOM,\n      payload: {\n        hotelId,\n        query: searchParams,\n        onSuccess: roomList => {\n          if (isMounted) {\n            if (Array.isArray(roomList)) {\n              setRooms(roomList);\n            } else {\n              console.warn(\"Unexpected data format received:\", roomList);\n            }\n          }\n        },\n        onFailed: msg => {\n          if (isMounted) {\n            console.error(\"Failed to fetch rooms:\", msg);\n          }\n        },\n        onError: err => {\n          if (isMounted) {\n            console.error(\"Server error:\", err);\n          }\n        }\n      }\n    });\n  };\n\n  // Fetch rooms when hotelId or searchParams change\n  useEffect(() => {\n    let isMounted = true;\n    if (hotelId && searchParams.checkinDate && searchParams.checkoutDate) {\n      fetchRooms(isMounted);\n    }\n    return () => {\n      isMounted = false;\n    };\n  }, [hotelId, dispatch, searchParams]);\n  const handleSearchRooms = () => {\n    setSelectedRoomsForReservation({});\n    setSelectedServicesForReservation({});\n    setServiceQuantities({});\n    setServiceSelectedDates({});\n    setIsSearching(true);\n\n    // Update searchParams to trigger room fetch\n    setSearchParams({\n      checkinDate: checkinDate,\n      checkoutDate: checkoutDate,\n      numberOfPeople: selectedAdults.value + selectedChildren.value,\n      page: 1,\n      limit: 10\n    });\n\n    // Reset searching state after a short delay\n    setTimeout(() => {\n      setIsSearching(false);\n    }, 1000);\n  };\n\n  // Add new functions for reservation\n  const handleRoomQuantityChange = (roomId, quantity) => {\n    setSelectedRoomsForReservation(prev => ({\n      ...prev,\n      [roomId]: quantity\n    }));\n  };\n  const handleServiceSelection = service => {\n    setSelectedServicesForReservation(prev => {\n      const isSelected = prev[service._id];\n      if (isSelected) {\n        const newSelected = {\n          ...prev\n        };\n        delete newSelected[service._id];\n\n        // Remove service quantities and dates\n        setServiceQuantities(prev => {\n          const newQuantities = {\n            ...prev\n          };\n          delete newQuantities[service._id];\n          return newQuantities;\n        });\n        setServiceSelectedDates(prev => {\n          const newDates = {\n            ...prev\n          };\n          delete newDates[service._id];\n          return newDates;\n        });\n        return newSelected;\n      } else {\n        setServiceQuantities(prev => ({\n          ...prev,\n          [service._id]: 1\n        }));\n        return {\n          ...prev,\n          [service._id]: service\n        };\n      }\n    });\n  };\n  const handleServiceQuantityChange = (serviceId, quantity) => {\n    if (quantity < 1) return;\n    setServiceQuantities(prev => ({\n      ...prev,\n      [serviceId]: quantity\n    }));\n  };\n  const getDatesBetween = (startDate, endDate) => {\n    const dates = [];\n    let currentDate = new Date(startDate);\n    const lastDate = new Date(endDate);\n    while (currentDate < lastDate) {\n      dates.push(new Date(currentDate));\n      currentDate.setDate(currentDate.getDate() + 1);\n    }\n    return dates;\n  };\n  const handleDateSelection = (serviceId, date) => {\n    setServiceSelectedDates(prev => {\n      const currentDates = prev[serviceId] || [];\n      const dateStr = date.toISOString();\n      if (currentDates.includes(dateStr)) {\n        return {\n          ...prev,\n          [serviceId]: currentDates.filter(d => d !== dateStr)\n        };\n      } else {\n        return {\n          ...prev,\n          [serviceId]: [...currentDates, dateStr]\n        };\n      }\n    });\n  };\n  const handleReservationSubmit = async () => {\n    // Validate room selection\n    const selectedRoomIds = Object.keys(selectedRoomsForReservation).filter(roomId => selectedRoomsForReservation[roomId] > 0);\n    if (selectedRoomIds.length === 0) {\n      showToast.error(\"Vui lòng chọn ít nhất một phòng\");\n      return;\n    }\n\n    // Validate date selection\n    if (!checkinDate || !checkoutDate) {\n      showToast.error(\"Vui lòng chọn ngày nhận phòng và trả phòng\");\n      return;\n    }\n    if (new Date(checkinDate) >= new Date(checkoutDate)) {\n      showToast.error(\"Ngày trả phòng phải sau ngày nhận phòng\");\n      return;\n    }\n    if (new Date(checkinDate) < new Date().setHours(0, 0, 0, 0)) {\n      showToast.error(\"Ngày nhận phòng không được là ngày trong quá khứ\");\n      return;\n    }\n\n    // Validate room availability\n    const roomValidationErrors = [];\n    selectedRoomIds.forEach(roomId => {\n      const room = rooms.find(r => r._id === roomId);\n      const selectedQuantity = selectedRoomsForReservation[roomId];\n      if (room && selectedQuantity > room.availableQuantity) {\n        roomValidationErrors.push(`Phòng ${room.name}: Chỉ còn ${room.availableQuantity} phòng trống, bạn đã chọn ${selectedQuantity} phòng`);\n      }\n    });\n    if (roomValidationErrors.length > 0) {\n      showToast.error(roomValidationErrors.join(\"; \"));\n      return;\n    }\n\n    // Validate guest capacity\n    const totalGuestCapacity = selectedRoomIds.reduce((total, roomId) => {\n      const room = rooms.find(r => r._id === roomId);\n      const quantity = selectedRoomsForReservation[roomId];\n      return total + (room ? room.capacity * quantity : 0);\n    }, 0);\n    const totalGuests = selectedAdults.value + selectedChildren.value;\n    if (totalGuests > totalGuestCapacity) {\n      showToast.error(`Số lượng khách (${totalGuests}) vượt quá sức chứa của phòng đã chọn (${totalGuestCapacity})`);\n      return;\n    }\n\n    // Validate service selection (if any services are selected)\n    const selectedServices = Object.values(selectedServicesForReservation);\n    const serviceValidationErrors = [];\n    selectedServices.forEach(service => {\n      const selectedDates = serviceSelectedDates[service._id] || [];\n      const quantity = serviceQuantities[service._id] || 1;\n      if (selectedDates.length === 0) {\n        serviceValidationErrors.push(`Dịch vụ ${service.name}: Vui lòng chọn ít nhất một ngày sử dụng`);\n      }\n      if (quantity < 1) {\n        serviceValidationErrors.push(`Dịch vụ ${service.name}: Số lượng phải lớn hơn 0`);\n      }\n\n      // Validate service dates are within booking period\n      const checkinTime = new Date(checkinDate).getTime();\n      const checkoutTime = new Date(checkoutDate).getTime();\n      selectedDates.forEach(dateStr => {\n        const serviceDate = new Date(dateStr).getTime();\n        if (serviceDate < checkinTime || serviceDate >= checkoutTime) {\n          serviceValidationErrors.push(`Dịch vụ ${service.name}: Ngày sử dụng phải trong khoảng thời gian lưu trú`);\n        }\n      });\n    });\n    if (serviceValidationErrors.length > 0) {\n      showToast.error(serviceValidationErrors.join(\"; \"));\n      return;\n    }\n\n    // Validate customer information (if needed for offline booking)\n    if (!Auth._id) {\n      showToast.error(\"Thông tin người dùng không hợp lệ\");\n      return;\n    }\n\n    // Validate hotel selection\n    if (!hotelId) {\n      showToast.error(\"Vui lòng chọn khách sạn\");\n      return;\n    }\n\n    // Calculate and validate total price\n    const totalPrice = calculateTotalPrice();\n    if (totalPrice <= 0) {\n      showToast.error(\"Tổng giá trị đặt phòng phải lớn hơn 0\");\n      return;\n    }\n\n    // Validate minimum booking duration (if needed)\n    const nights = Math.ceil((new Date(checkoutDate) - new Date(checkinDate)) / (1000 * 60 * 60 * 24));\n    if (nights <= 0) {\n      showToast.error(\"Thời gian lưu trú phải ít nhất 1 đêm\");\n      return;\n    }\n\n    // Process reservation data\n    const reservationData = {\n      hotelId: hotelId,\n      checkInDate: checkinDate,\n      checkOutDate: checkoutDate,\n      totalPrice: totalPrice,\n      finalPrice: totalPrice,\n      roomDetails: selectedRoomIds.map(roomId => ({\n        room: {\n          _id: roomId\n        },\n        amount: selectedRoomsForReservation[roomId]\n      })),\n      serviceDetails: Object.values(selectedServicesForReservation).map(service => ({\n        _id: service._id,\n        quantity: serviceQuantities[service._id] || 1,\n        selectedDates: serviceSelectedDates[service._id] || []\n      }))\n    };\n    console.log(\"Reservation Data:\", reservationData);\n    try {\n      const response = await Factories.create_booking_offline(reservationData);\n      console.log(\"response >> \", response);\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n        var _response$data, _response$data$unpaid;\n        console.log(\"response >> \", response);\n        const unpaidReservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n        if (!unpaidReservationId) {\n          showToast.error(\"Không thể tạo đặt phòng. Vui lòng thử lại.\");\n          return;\n        }\n        setSelectedRoomsForReservation({});\n        setSelectedServicesForReservation({});\n        setServiceQuantities({});\n        setServiceSelectedDates({});\n        fetchRooms(true);\n        showToast.success(\"Đặt phòng thành công.\");\n      } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n        var _response$data2, _response$data2$reser;\n        console.log(\"response >> \", response);\n        const reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n        if (!reservationId) {\n          showToast.error(\"Không thể tạo đặt phòng. Vui lòng thử lại.\");\n          return;\n        }\n        setSelectedRoomsForReservation({});\n        setSelectedServicesForReservation({});\n        setServiceQuantities({});\n        setServiceSelectedDates({});\n        fetchRooms(true);\n        showToast.success(\"Bạn đã đặt phòng thành công.\");\n      } else {\n        var _response$data3;\n        const errorMessage = (response === null || response === void 0 ? void 0 : (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message) || \"Lỗi không xác định khi tạo đặt phòng\";\n        showToast.error(errorMessage);\n        console.log(\"error create booking:\", response);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error creating reservation:\", error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || (error === null || error === void 0 ? void 0 : error.message) || \"Lỗi máy chủ khi tạo đặt phòng. Vui lòng thử lại.\";\n      showToast.error(errorMessage);\n    } finally {\n      // Reset modal state\n      setShowReservationModal(false);\n\n      // Reset form data\n      setSelectedRoomsForReservation({});\n      setSelectedServicesForReservation({});\n      setServiceQuantities({});\n      setServiceSelectedDates({});\n    }\n  };\n  const calculateTotalPrice = () => {\n    let total = 0;\n    const nights = Math.ceil((new Date(checkoutDate) - new Date(checkinDate)) / (1000 * 60 * 60 * 24));\n\n    // Calculate room prices\n    Object.keys(selectedRoomsForReservation).forEach(roomId => {\n      const room = rooms.find(r => r._id === roomId);\n      if (room && selectedRoomsForReservation[roomId] > 0) {\n        total += room.price * selectedRoomsForReservation[roomId] * nights;\n      }\n    });\n\n    // Calculate service prices\n    Object.values(selectedServicesForReservation).forEach(service => {\n      const quantity = serviceQuantities[service._id] || 1;\n      const selectedDates = serviceSelectedDates[service._id] || [];\n      total += service.price * quantity * selectedDates.length;\n    });\n    return total;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"management-booking\",\n    children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Qu\\u1EA3n l\\xFD \\u0111\\u1EB7t ph\\xF2ng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-section\",\n      style: {\n        background: \"#f8f9fa\",\n        padding: \"20px\",\n        borderRadius: \"10px\",\n        marginBottom: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"T\\xECm ki\\u1EBFm ph\\xF2ng tr\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-success\",\n          onClick: () => setShowReservationModal(true),\n          disabled: !hotelId || rooms.length === 0,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-plus me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), \"T\\u1EA1o \\u0111\\u01A1n \\u0111\\u1EB7t ph\\xF2ng offline\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-3 mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"checkinDate\",\n            className: \"form-label\",\n            children: \"Ng\\xE0y nh\\u1EADn ph\\xF2ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            id: \"checkinDate\",\n            className: \"form-control\",\n            value: checkinDate,\n            min: today,\n            onChange: e => setCheckinDate(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-3 mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"checkoutDate\",\n            className: \"form-label\",\n            children: \"Ng\\xE0y tr\\u1EA3 ph\\xF2ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            id: \"checkoutDate\",\n            className: \"form-control\",\n            value: checkoutDate,\n            min: checkinDate || today,\n            onChange: e => setCheckoutDate(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-2 mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Ng\\u01B0\\u1EDDi l\\u1EDBn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedAdults,\n            onChange: setSelectedAdults,\n            options: adultsOptions,\n            styles: selectStyles,\n            isSearchable: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-2 mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Tr\\u1EBB em\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedChildren,\n            onChange: setSelectedChildren,\n            options: childrenOptions,\n            styles: selectStyles,\n            isSearchable: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-2 mb-3 d-flex align-items-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary w-100\",\n            onClick: handleSearchRooms,\n            disabled: isSearching || !checkinDate || !checkoutDate,\n            children: isSearching ? \"Đang tìm...\" : \"Tìm kiếm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"rooms-section mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mb-0\",\n          children: \"Danh s\\xE1ch ph\\xF2ng tr\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge bg-secondary fs-6\",\n          children: [rooms.length, \" lo\\u1EA1i ph\\xF2ng\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          style: {\n            width: \"3rem\",\n            height: \"3rem\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-3 text-muted\",\n          children: \"\\u0110ang t\\u1EA3i danh s\\xE1ch ph\\xF2ng...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 11\n      }, this) : rooms.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row g-4\",\n        children: rooms.map(room => {\n          var _room$roomType;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card h-100 shadow-sm border-0\",\n              style: {\n                transition: \"transform 0.2s ease-in-out\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header bg-white border-0 pb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"card-title mb-1 text-primary fw-bold\",\n                  children: room.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted small mb-0\",\n                  children: ((_room$roomType = room.roomType) === null || _room$roomType === void 0 ? void 0 : _room$roomType.name) || room.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body pt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"h4 text-success fw-bold mb-0\",\n                    children: Utils.formatCurrency(room.price)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-muted ms-2\",\n                    children: \"/\\u0111\\xEAm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row g-2 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center p-2 bg-light rounded\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-users text-primary me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted d-block\",\n                          children: \"S\\u1EE9c ch\\u1EE9a\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 636,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: [room.capacity, \" ng\\u01B0\\u1EDDi\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 639,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center p-2 bg-light rounded\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-door-open text-success me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted d-block\",\n                          children: \"C\\xF2n tr\\u1ED1ng\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 647,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: room.availableQuantity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 650,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row g-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-12\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center p-2 border rounded\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-muted\",\n                        children: \"\\u0110\\xE3 \\u0111\\u1EB7t:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"badge bg-warning text-dark\",\n                        children: [Number(room.quantity) - Number(room.availableQuantity), \" \", \"ph\\xF2ng\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 661,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-footer bg-white border-0 pt-0\",\n                children: room.availableQuantity > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-check-circle me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"C\\xF3 ph\\xF2ng tr\\u1ED1ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center text-danger\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-times-circle me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"H\\u1EBFt ph\\xF2ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this)\n          }, room._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bed text-muted\",\n            style: {\n              fontSize: \"4rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-muted\",\n          children: hotelId ? \"Không có phòng trống trong khoảng thời gian này\" : \"Vui lòng chọn ngày để xem phòng trống\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted mb-0\",\n          children: \"Th\\u1EED thay \\u0111\\u1ED5i ng\\xE0y checkin/checkout ho\\u1EB7c s\\u1ED1 l\\u01B0\\u1EE3ng kh\\xE1ch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `modal fade ${showReservationModal ? \"show\" : \"\"}`,\n      style: {\n        display: showReservationModal ? \"block\" : \"none\"\n      },\n      tabIndex: \"-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-dialog modal-xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"modal-title\",\n              children: \"T\\u1EA1o \\u0111\\u1EB7t ph\\xF2ng m\\u1EDBi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-close\",\n              onClick: () => setShowReservationModal(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"Th\\xF4ng tin kh\\xE1ch h\\xE0ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"T\\xEAn kh\\xE1ch h\\xE0ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    value: Auth.name,\n                    onChange: e => setReservationCustomer(prev => ({\n                      ...prev,\n                      name: e.target.value\n                    })),\n                    disabled: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    className: \"form-control\",\n                    value: Auth.email,\n                    onChange: e => setReservationCustomer(prev => ({\n                      ...prev,\n                      email: e.target.value\n                    })),\n                    disabled: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    className: \"form-control\",\n                    value: Auth.phoneNumber,\n                    onChange: e => setReservationCustomer(prev => ({\n                      ...prev,\n                      phone: e.target.value\n                    })),\n                    disabled: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: \"T\\xF3m t\\u1EAFt \\u0111\\u1EB7t ph\\xF2ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-light p-3 rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Check-in:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 25\n                      }, this), \" \", checkinDate]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Check-out:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 784,\n                        columnNumber: 25\n                      }, this), \" \", checkoutDate]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"S\\u1ED1 kh\\xE1ch:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 787,\n                        columnNumber: 25\n                      }, this), \" \", selectedAdults.value + selectedChildren.value]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 786,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"T\\u1ED5ng ti\\u1EC1n:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 792,\n                        columnNumber: 25\n                      }, this), \" \", Utils.formatCurrency(calculateTotalPrice())]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 791,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"Ch\\u1ECDn ph\\xF2ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    maxHeight: \"520px\",\n                    overflowY: \"auto\"\n                  },\n                  children: rooms.map(room => {\n                    var _room$roomType2;\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"card-body p-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"card-title\",\n                          children: room.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 806,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"card-text small\",\n                          children: [(_room$roomType2 = room.roomType) === null || _room$roomType2 === void 0 ? void 0 : _room$roomType2.name, \" -\", \" \", Utils.formatCurrency(room.price), \"/\\u0111\\xEAm\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 807,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"small text-muted\",\n                          children: [\"C\\xF2n tr\\u1ED1ng: \", room.availableQuantity, \" ph\\xF2ng\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 811,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                            className: \"me-2\",\n                            children: \"S\\u1ED1 l\\u01B0\\u1EE3ng:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 815,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                            className: \"form-select form-select-sm\",\n                            style: {\n                              width: \"80px\"\n                            },\n                            value: selectedRoomsForReservation[room._id] || 0,\n                            onChange: e => handleRoomQuantityChange(room._id, parseInt(e.target.value)),\n                            children: Array.from({\n                              length: room.availableQuantity + 1\n                            }, (_, i) => /*#__PURE__*/_jsxDEV(\"option\", {\n                              value: i,\n                              children: i\n                            }, i, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 830,\n                              columnNumber: 35\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 816,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 814,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 25\n                      }, this)\n                    }, room._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 23\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"Ch\\u1ECDn d\\u1ECBch v\\u1EE5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    maxHeight: \"520px\",\n                    overflowY: \"auto\"\n                  },\n                  children: hotelInfo === null || hotelInfo === void 0 ? void 0 : (_hotelInfo$ = hotelInfo[0]) === null || _hotelInfo$ === void 0 ? void 0 : (_hotelInfo$$services = _hotelInfo$.services) === null || _hotelInfo$$services === void 0 ? void 0 : _hotelInfo$$services.map(service => {\n                    const isSelected = selectedServicesForReservation[service._id];\n                    const quantity = serviceQuantities[service._id] || 1;\n                    const selectedDates = serviceSelectedDates[service._id] || [];\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"card-body p-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"form-check\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            className: \"form-check-input\",\n                            type: \"checkbox\",\n                            checked: !!isSelected,\n                            onChange: () => handleServiceSelection(service)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 858,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            className: \"form-check-label\",\n                            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: service.name\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 865,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 864,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 857,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"small text-muted\",\n                          children: [Utils.formatCurrency(service.price), \"/\", service.type]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 868,\n                          columnNumber: 29\n                        }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center mb-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                              className: \"me-2\",\n                              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 876,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"input-group input-group-sm\",\n                              style: {\n                                width: \"120px\"\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"btn btn-outline-secondary\",\n                                type: \"button\",\n                                onClick: () => handleServiceQuantityChange(service._id, quantity - 1),\n                                disabled: quantity <= 1,\n                                children: \"-\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 881,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                                type: \"text\",\n                                className: \"form-control text-center\",\n                                value: quantity,\n                                readOnly: true\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 894,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"btn btn-outline-secondary\",\n                                type: \"button\",\n                                onClick: () => handleServiceQuantityChange(service._id, quantity + 1),\n                                children: \"+\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 900,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 877,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 875,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                              className: \"form-label small\",\n                              children: \"Ch\\u1ECDn ng\\xE0y s\\u1EED d\\u1EE5ng:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 916,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex flex-wrap gap-1\",\n                              children: getDatesBetween(new Date(checkinDate), new Date(checkoutDate)).map(date => {\n                                const dateStr = date.toISOString();\n                                const isDateSelected = selectedDates.includes(dateStr);\n                                return /*#__PURE__*/_jsxDEV(\"button\", {\n                                  type: \"button\",\n                                  className: `btn btn-sm ${isDateSelected ? \"btn-primary\" : \"btn-outline-primary\"}`,\n                                  onClick: () => handleDateSelection(service._id, date),\n                                  children: [date.getDate(), \"/\", date.getMonth() + 1]\n                                }, dateStr, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 929,\n                                  columnNumber: 41\n                                }, this);\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 919,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 915,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 874,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 856,\n                        columnNumber: 27\n                      }, this)\n                    }, service._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-footer\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: () => setShowReservationModal(false),\n              children: \"H\\u1EE7y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-primary\",\n              onClick: handleReservationSubmit,\n              children: \"T\\u1EA1o \\u0111\\u1EB7t ph\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 715,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 7\n    }, this), showReservationModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-backdrop fade show\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 980,\n      columnNumber: 32\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 495,\n    columnNumber: 5\n  }, this);\n};\n_s(ManagementBooking, \"+iEMkZoZd44kFaw3fcmb5qlKRps=\", false, function () {\n  return [useAppSelector, useAppDispatch];\n});\n_c = ManagementBooking;\nexport default ManagementBooking;\nvar _c;\n$RefreshReg$(_c, \"ManagementBooking\");", "map": {"version": 3, "names": ["showToast", "ToastProvider", "HotelActions", "Factories", "RoomActions", "useAppDispatch", "useAppSelector", "Utils", "useEffect", "useState", "Select", "jsxDEV", "_jsxDEV", "adultsOptions", "Array", "from", "length", "_", "i", "value", "label", "childrenOptions", "selectStyles", "control", "provided", "border", "borderRadius", "minHeight", "boxShadow", "borderColor", "ManagementBooking", "_s", "_hotelInfo$", "_hotelInfo$$services", "<PERSON><PERSON>", "state", "hotelInfo", "setHotelInfo", "hotelId", "setHotelId", "rooms", "setRooms", "loading", "setLoading", "isSearching", "setIsSearching", "today", "Date", "toISOString", "split", "tomorrow", "now", "checkinDate", "setCheckinDate", "checkoutDate", "setCheckoutDate", "selectedAdults", "setSelectedAdults", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedChildren", "searchParams", "setSearchParams", "numberOfPeople", "page", "limit", "showReservationModal", "setShowReservationModal", "selectedRoomsForReservation", "setSelectedRoomsForReservation", "selectedServicesForReservation", "setSelectedServicesForReservation", "serviceQuantities", "setServiceQuantities", "serviceSelectedDates", "setServiceSelectedDates", "reservationCustomer", "setReservationCustomer", "name", "email", "phone", "dispatch", "fetchHotelInfo", "type", "FETCH_OWNER_HOTEL", "payload", "userId", "_id", "onSuccess", "data", "hotels", "onFailed", "error", "onError", "err", "console", "fetchRooms", "isMounted", "FETCH_ROOM", "query", "roomList", "isArray", "warn", "msg", "handleSearchRooms", "setTimeout", "handleRoomQuantityChange", "roomId", "quantity", "prev", "handleServiceSelection", "service", "isSelected", "newSelected", "newQuantities", "newDates", "handleServiceQuantityChange", "serviceId", "getDatesBetween", "startDate", "endDate", "dates", "currentDate", "lastDate", "push", "setDate", "getDate", "handleDateSelection", "date", "currentDates", "dateStr", "includes", "filter", "d", "handleReservationSubmit", "selectedRoomIds", "Object", "keys", "setHours", "roomValidationErrors", "for<PERSON>ach", "room", "find", "r", "selectedQuantity", "availableQuantity", "join", "totalGuestCapacity", "reduce", "total", "capacity", "totalGuests", "selectedServices", "values", "serviceValidationErrors", "selectedDates", "checkinTime", "getTime", "checkoutTime", "serviceDate", "totalPrice", "calculateTotalPrice", "nights", "Math", "ceil", "reservationData", "checkInDate", "checkOutDate", "finalPrice", "roomDetails", "map", "amount", "serviceDetails", "log", "response", "create_booking_offline", "status", "_response$data", "_response$data$unpaid", "unpaidReservationId", "unpaidReservation", "success", "_response$data2", "_response$data2$reser", "reservationId", "reservation", "_response$data3", "errorMessage", "message", "_error$response", "_error$response$data", "price", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "padding", "marginBottom", "onClick", "disabled", "htmlFor", "id", "min", "onChange", "e", "target", "options", "styles", "isSearchable", "role", "width", "height", "_room$roomType", "transition", "roomType", "formatCurrency", "Number", "fontSize", "display", "tabIndex", "phoneNumber", "maxHeight", "overflowY", "_room$roomType2", "parseInt", "services", "checked", "readOnly", "isDateSelected", "getMonth", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/management_booking/ManagementBooking.jsx"], "sourcesContent": ["import { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport Factories from \"@redux/hotel/factories\";\r\nimport RoomActions from \"@redux/room/actions\";\r\nimport { useAppDispatch, useAppSelector } from \"@redux/store\";\r\nimport Utils from \"@utils/Utils\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Select from \"react-select\";\r\n\r\n// Options for select inputs\r\nconst adultsOptions = Array.from({ length: 20 }, (_, i) => ({\r\n  value: i + 1,\r\n  label: `${i + 1} Adults`,\r\n}));\r\n\r\nconst childrenOptions = Array.from({ length: 11 }, (_, i) => ({\r\n  value: i,\r\n  label: `${i} Children`,\r\n}));\r\n\r\n// Select styles\r\nconst selectStyles = {\r\n  control: (provided) => ({\r\n    ...provided,\r\n    border: \"1px solid #ddd\",\r\n    borderRadius: \"8px\",\r\n    minHeight: \"40px\",\r\n    boxShadow: \"none\",\r\n    \"&:hover\": {\r\n      borderColor: \"#1a2b49\",\r\n    },\r\n  }),\r\n};\r\n\r\nconst ManagementBooking = () => {\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const [hotelInfo, setHotelInfo] = useState(null);\r\n  const [hotelId, setHotelId] = useState(\"\");\r\n  const [rooms, setRooms] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n\r\n  // Date states\r\n  const today = new Date().toISOString().split(\"T\")[0];\r\n  const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000)\r\n    .toISOString()\r\n    .split(\"T\")[0];\r\n\r\n  const [checkinDate, setCheckinDate] = useState(today);\r\n  const [checkoutDate, setCheckoutDate] = useState(tomorrow);\r\n\r\n  // Guest selection states\r\n  const [selectedAdults, setSelectedAdults] = useState(adultsOptions[1]);\r\n  const [selectedChildren, setSelectedChildren] = useState(childrenOptions[0]);\r\n\r\n  const [searchParams, setSearchParams] = useState({\r\n    checkinDate: today,\r\n    checkoutDate: tomorrow,\r\n    numberOfPeople: 1,\r\n    page: 1,\r\n    limit: 10,\r\n  });\r\n\r\n  // Add new states for reservation modal\r\n  const [showReservationModal, setShowReservationModal] = useState(false);\r\n  const [selectedRoomsForReservation, setSelectedRoomsForReservation] =\r\n    useState({});\r\n  const [selectedServicesForReservation, setSelectedServicesForReservation] =\r\n    useState({});\r\n  const [serviceQuantities, setServiceQuantities] = useState({});\r\n  const [serviceSelectedDates, setServiceSelectedDates] = useState({});\r\n  const [reservationCustomer, setReservationCustomer] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n  });\r\n\r\n  const dispatch = useAppDispatch();\r\n\r\n  const fetchHotelInfo = () => {\r\n    setLoading(true);\r\n    dispatch({\r\n      type: HotelActions.FETCH_OWNER_HOTEL,\r\n      payload: {\r\n        userId: Auth._id,\r\n        onSuccess: (data) => {\r\n          setHotelInfo(data.hotels);\r\n          setHotelId(data.hotels[0]._id);\r\n          setLoading(false);\r\n        },\r\n        onFailed: () => {\r\n          showToast.error(\"Lấy thông tin khách sạn thất bại\");\r\n          setLoading(false);\r\n        },\r\n        onError: (err) => {\r\n          console.error(err);\r\n          showToast.error(\"Lỗi máy chủ khi lấy thông tin khách sạn\");\r\n          setLoading(false);\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchHotelInfo();\r\n  }, []);\r\n\r\n  // Update searchParams when dates or guest count changes\r\n  useEffect(() => {\r\n    setSearchParams({\r\n      checkinDate: checkinDate,\r\n      checkoutDate: checkoutDate,\r\n      numberOfPeople: selectedAdults.value + selectedChildren.value,\r\n      page: 1,\r\n      limit: 10,\r\n    });\r\n  }, [checkinDate, checkoutDate, selectedAdults, selectedChildren]);\r\n\r\n  const fetchRooms = (isMounted) => {\r\n    dispatch({\r\n      type: RoomActions.FETCH_ROOM,\r\n      payload: {\r\n        hotelId,\r\n        query: searchParams,\r\n        onSuccess: (roomList) => {\r\n          if (isMounted) {\r\n            if (Array.isArray(roomList)) {\r\n              setRooms(roomList);\r\n            } else {\r\n              console.warn(\"Unexpected data format received:\", roomList);\r\n            }\r\n          }\r\n        },\r\n        onFailed: (msg) => {\r\n          if (isMounted) {\r\n            console.error(\"Failed to fetch rooms:\", msg);\r\n          }\r\n        },\r\n        onError: (err) => {\r\n          if (isMounted) {\r\n            console.error(\"Server error:\", err);\r\n          }\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  // Fetch rooms when hotelId or searchParams change\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    if (hotelId && searchParams.checkinDate && searchParams.checkoutDate) {\r\n        fetchRooms(isMounted);\r\n    }\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [hotelId, dispatch, searchParams]);\r\n\r\n  const handleSearchRooms = () => {\r\n    setSelectedRoomsForReservation({});\r\n    setSelectedServicesForReservation({});\r\n    setServiceQuantities({});\r\n    setServiceSelectedDates({});\r\n    setIsSearching(true);\r\n\r\n    // Update searchParams to trigger room fetch\r\n    setSearchParams({\r\n      checkinDate: checkinDate,\r\n      checkoutDate: checkoutDate,\r\n      numberOfPeople: selectedAdults.value + selectedChildren.value,\r\n      page: 1,\r\n      limit: 10,\r\n    });\r\n\r\n    // Reset searching state after a short delay\r\n    setTimeout(() => {\r\n      setIsSearching(false);\r\n    }, 1000);\r\n  };\r\n\r\n  // Add new functions for reservation\r\n  const handleRoomQuantityChange = (roomId, quantity) => {\r\n    setSelectedRoomsForReservation((prev) => ({\r\n      ...prev,\r\n      [roomId]: quantity,\r\n    }));\r\n  };\r\n\r\n  const handleServiceSelection = (service) => {\r\n    setSelectedServicesForReservation((prev) => {\r\n      const isSelected = prev[service._id];\r\n      if (isSelected) {\r\n        const newSelected = { ...prev };\r\n        delete newSelected[service._id];\r\n\r\n        // Remove service quantities and dates\r\n        setServiceQuantities((prev) => {\r\n          const newQuantities = { ...prev };\r\n          delete newQuantities[service._id];\r\n          return newQuantities;\r\n        });\r\n        setServiceSelectedDates((prev) => {\r\n          const newDates = { ...prev };\r\n          delete newDates[service._id];\r\n          return newDates;\r\n        });\r\n\r\n        return newSelected;\r\n      } else {\r\n        setServiceQuantities((prev) => ({\r\n          ...prev,\r\n          [service._id]: 1,\r\n        }));\r\n        return {\r\n          ...prev,\r\n          [service._id]: service,\r\n        };\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleServiceQuantityChange = (serviceId, quantity) => {\r\n    if (quantity < 1) return;\r\n    setServiceQuantities((prev) => ({\r\n      ...prev,\r\n      [serviceId]: quantity,\r\n    }));\r\n  };\r\n\r\n  const getDatesBetween = (startDate, endDate) => {\r\n    const dates = [];\r\n    let currentDate = new Date(startDate);\r\n    const lastDate = new Date(endDate);\r\n\r\n    while (currentDate < lastDate) {\r\n      dates.push(new Date(currentDate));\r\n      currentDate.setDate(currentDate.getDate() + 1);\r\n    }\r\n\r\n    return dates;\r\n  };\r\n\r\n  const handleDateSelection = (serviceId, date) => {\r\n    setServiceSelectedDates((prev) => {\r\n      const currentDates = prev[serviceId] || [];\r\n      const dateStr = date.toISOString();\r\n\r\n      if (currentDates.includes(dateStr)) {\r\n        return {\r\n          ...prev,\r\n          [serviceId]: currentDates.filter((d) => d !== dateStr),\r\n        };\r\n      } else {\r\n        return {\r\n          ...prev,\r\n          [serviceId]: [...currentDates, dateStr],\r\n        };\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleReservationSubmit = async () => {\r\n    // Validate room selection\r\n    const selectedRoomIds = Object.keys(selectedRoomsForReservation).filter(\r\n      (roomId) => selectedRoomsForReservation[roomId] > 0\r\n    );\r\n\r\n    if (selectedRoomIds.length === 0) {\r\n      showToast.error(\"Vui lòng chọn ít nhất một phòng\");\r\n      return;\r\n    }\r\n\r\n    // Validate date selection\r\n    if (!checkinDate || !checkoutDate) {\r\n      showToast.error(\"Vui lòng chọn ngày nhận phòng và trả phòng\");\r\n      return;\r\n    }\r\n\r\n    if (new Date(checkinDate) >= new Date(checkoutDate)) {\r\n      showToast.error(\"Ngày trả phòng phải sau ngày nhận phòng\");\r\n      return;\r\n    }\r\n\r\n    if (new Date(checkinDate) < new Date().setHours(0, 0, 0, 0)) {\r\n      showToast.error(\"Ngày nhận phòng không được là ngày trong quá khứ\");\r\n      return;\r\n    }\r\n\r\n    // Validate room availability\r\n    const roomValidationErrors = [];\r\n    selectedRoomIds.forEach((roomId) => {\r\n      const room = rooms.find((r) => r._id === roomId);\r\n      const selectedQuantity = selectedRoomsForReservation[roomId];\r\n\r\n      if (room && selectedQuantity > room.availableQuantity) {\r\n        roomValidationErrors.push(\r\n          `Phòng ${room.name}: Chỉ còn ${room.availableQuantity} phòng trống, bạn đã chọn ${selectedQuantity} phòng`\r\n        );\r\n      }\r\n    });\r\n\r\n    if (roomValidationErrors.length > 0) {\r\n      showToast.error(roomValidationErrors.join(\"; \"));\r\n      return;\r\n    }\r\n\r\n    // Validate guest capacity\r\n    const totalGuestCapacity = selectedRoomIds.reduce((total, roomId) => {\r\n      const room = rooms.find((r) => r._id === roomId);\r\n      const quantity = selectedRoomsForReservation[roomId];\r\n      return total + (room ? room.capacity * quantity : 0);\r\n    }, 0);\r\n\r\n    const totalGuests = selectedAdults.value + selectedChildren.value;\r\n    if (totalGuests > totalGuestCapacity) {\r\n      showToast.error(\r\n        `Số lượng khách (${totalGuests}) vượt quá sức chứa của phòng đã chọn (${totalGuestCapacity})`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Validate service selection (if any services are selected)\r\n    const selectedServices = Object.values(selectedServicesForReservation);\r\n    const serviceValidationErrors = [];\r\n\r\n    selectedServices.forEach((service) => {\r\n      const selectedDates = serviceSelectedDates[service._id] || [];\r\n      const quantity = serviceQuantities[service._id] || 1;\r\n\r\n      if (selectedDates.length === 0) {\r\n        serviceValidationErrors.push(\r\n          `Dịch vụ ${service.name}: Vui lòng chọn ít nhất một ngày sử dụng`\r\n        );\r\n      }\r\n\r\n      if (quantity < 1) {\r\n        serviceValidationErrors.push(\r\n          `Dịch vụ ${service.name}: Số lượng phải lớn hơn 0`\r\n        );\r\n      }\r\n\r\n      // Validate service dates are within booking period\r\n      const checkinTime = new Date(checkinDate).getTime();\r\n      const checkoutTime = new Date(checkoutDate).getTime();\r\n\r\n      selectedDates.forEach((dateStr) => {\r\n        const serviceDate = new Date(dateStr).getTime();\r\n        if (serviceDate < checkinTime || serviceDate >= checkoutTime) {\r\n          serviceValidationErrors.push(\r\n            `Dịch vụ ${service.name}: Ngày sử dụng phải trong khoảng thời gian lưu trú`\r\n          );\r\n        }\r\n      });\r\n    });\r\n\r\n    if (serviceValidationErrors.length > 0) {\r\n      showToast.error(serviceValidationErrors.join(\"; \"));\r\n      return;\r\n    }\r\n\r\n    // Validate customer information (if needed for offline booking)\r\n    if (!Auth._id) {\r\n      showToast.error(\"Thông tin người dùng không hợp lệ\");\r\n      return;\r\n    }\r\n\r\n    // Validate hotel selection\r\n    if (!hotelId) {\r\n      showToast.error(\"Vui lòng chọn khách sạn\");\r\n      return;\r\n    }\r\n\r\n    // Calculate and validate total price\r\n    const totalPrice = calculateTotalPrice();\r\n    if (totalPrice <= 0) {\r\n      showToast.error(\"Tổng giá trị đặt phòng phải lớn hơn 0\");\r\n      return;\r\n    }\r\n\r\n    // Validate minimum booking duration (if needed)\r\n    const nights = Math.ceil(\r\n      (new Date(checkoutDate) - new Date(checkinDate)) / (1000 * 60 * 60 * 24)\r\n    );\r\n    if (nights <= 0) {\r\n      showToast.error(\"Thời gian lưu trú phải ít nhất 1 đêm\");\r\n      return;\r\n    }\r\n\r\n    // Process reservation data\r\n    const reservationData = {\r\n      hotelId: hotelId,\r\n      checkInDate: checkinDate,\r\n      checkOutDate: checkoutDate,\r\n      totalPrice: totalPrice,\r\n      finalPrice: totalPrice,\r\n      roomDetails: selectedRoomIds.map((roomId) => ({\r\n        room: {\r\n          _id: roomId,\r\n        },\r\n        amount: selectedRoomsForReservation[roomId],\r\n      })),\r\n      serviceDetails: Object.values(selectedServicesForReservation).map(\r\n        (service) => ({\r\n          _id: service._id,\r\n          quantity: serviceQuantities[service._id] || 1,\r\n          selectedDates: serviceSelectedDates[service._id] || [],\r\n        })\r\n      ),\r\n    };\r\n\r\n    console.log(\"Reservation Data:\", reservationData);\r\n\r\n    try {\r\n      const response = await Factories.create_booking_offline(reservationData);\r\n      console.log(\"response >> \", response);\r\n      if (response?.status === 200) {\r\n        console.log(\"response >> \", response);\r\n        const unpaidReservationId = response?.data?.unpaidReservation?._id;\r\n\r\n        if (!unpaidReservationId) {\r\n          showToast.error(\"Không thể tạo đặt phòng. Vui lòng thử lại.\");\r\n          return;\r\n        }\r\n        setSelectedRoomsForReservation({});\r\n        setSelectedServicesForReservation({});\r\n        setServiceQuantities({});\r\n        setServiceSelectedDates({});\r\n        fetchRooms(true);\r\n        showToast.success(\"Đặt phòng thành công.\");\r\n      } else if (response?.status === 201) {\r\n        console.log(\"response >> \", response);\r\n        const reservationId = response?.data?.reservation?._id;\r\n\r\n        if (!reservationId) {\r\n          showToast.error(\"Không thể tạo đặt phòng. Vui lòng thử lại.\");\r\n          return;\r\n        }\r\n        setSelectedRoomsForReservation({});\r\n        setSelectedServicesForReservation({});\r\n        setServiceQuantities({});\r\n        setServiceSelectedDates({});\r\n        fetchRooms(true);\r\n        showToast.success(\"Bạn đã đặt phòng thành công.\");\r\n      } else {\r\n        const errorMessage =\r\n          response?.data?.message || \"Lỗi không xác định khi tạo đặt phòng\";\r\n        showToast.error(errorMessage);\r\n        console.log(\"error create booking:\", response);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating reservation:\", error);\r\n      const errorMessage =\r\n        error?.response?.data?.message ||\r\n        error?.message ||\r\n        \"Lỗi máy chủ khi tạo đặt phòng. Vui lòng thử lại.\";\r\n      showToast.error(errorMessage);\r\n    } finally {\r\n      // Reset modal state\r\n      setShowReservationModal(false);\r\n\r\n      // Reset form data\r\n      setSelectedRoomsForReservation({});\r\n      setSelectedServicesForReservation({});\r\n      setServiceQuantities({});\r\n      setServiceSelectedDates({});\r\n    }\r\n  };\r\n\r\n  const calculateTotalPrice = () => {\r\n    let total = 0;\r\n    const nights = Math.ceil(\r\n      (new Date(checkoutDate) - new Date(checkinDate)) / (1000 * 60 * 60 * 24)\r\n    );\r\n\r\n    // Calculate room prices\r\n    Object.keys(selectedRoomsForReservation).forEach((roomId) => {\r\n      const room = rooms.find((r) => r._id === roomId);\r\n      if (room && selectedRoomsForReservation[roomId] > 0) {\r\n        total += room.price * selectedRoomsForReservation[roomId] * nights;\r\n      }\r\n    });\r\n\r\n    // Calculate service prices\r\n    Object.values(selectedServicesForReservation).forEach((service) => {\r\n      const quantity = serviceQuantities[service._id] || 1;\r\n      const selectedDates = serviceSelectedDates[service._id] || [];\r\n      total += service.price * quantity * selectedDates.length;\r\n    });\r\n\r\n    return total;\r\n  };\r\n\r\n  return (\r\n    <div className=\"management-booking\">\r\n      <ToastProvider />\r\n      <h1>Quản lý đặt phòng</h1>\r\n\r\n      {/* Search Section */}\r\n      <div\r\n        className=\"search-section\"\r\n        style={{\r\n          background: \"#f8f9fa\",\r\n          padding: \"20px\",\r\n          borderRadius: \"10px\",\r\n          marginBottom: \"20px\",\r\n        }}\r\n      >\r\n        <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n          <h3>Tìm kiếm phòng trống</h3>\r\n          <button\r\n            className=\"btn btn-success\"\r\n            onClick={() => setShowReservationModal(true)}\r\n            disabled={!hotelId || rooms.length === 0}\r\n          >\r\n            <i className=\"fas fa-plus me-2\"></i>\r\n            Tạo đơn đặt phòng offline\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"row\">\r\n          <div className=\"col-md-3 mb-3\">\r\n            <label htmlFor=\"checkinDate\" className=\"form-label\">\r\n              Ngày nhận phòng\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              id=\"checkinDate\"\r\n              className=\"form-control\"\r\n              value={checkinDate}\r\n              min={today}\r\n              onChange={(e) => setCheckinDate(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"col-md-3 mb-3\">\r\n            <label htmlFor=\"checkoutDate\" className=\"form-label\">\r\n              Ngày trả phòng\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              id=\"checkoutDate\"\r\n              className=\"form-control\"\r\n              value={checkoutDate}\r\n              min={checkinDate || today}\r\n              onChange={(e) => setCheckoutDate(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"col-md-2 mb-3\">\r\n            <label className=\"form-label\">Người lớn</label>\r\n            <Select\r\n              value={selectedAdults}\r\n              onChange={setSelectedAdults}\r\n              options={adultsOptions}\r\n              styles={selectStyles}\r\n              isSearchable={false}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"col-md-2 mb-3\">\r\n            <label className=\"form-label\">Trẻ em</label>\r\n            <Select\r\n              value={selectedChildren}\r\n              onChange={setSelectedChildren}\r\n              options={childrenOptions}\r\n              styles={selectStyles}\r\n              isSearchable={false}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"col-md-2 mb-3 d-flex align-items-end\">\r\n            <button\r\n              className=\"btn btn-primary w-100\"\r\n              onClick={handleSearchRooms}\r\n              disabled={isSearching || !checkinDate || !checkoutDate}\r\n            >\r\n              {isSearching ? \"Đang tìm...\" : \"Tìm kiếm\"}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Rooms Section */}\r\n      <div className=\"rooms-section mb-4\">\r\n        <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n          <h3 className=\"mb-0\">Danh sách phòng trống</h3>\r\n          <span className=\"badge bg-secondary fs-6\">\r\n            {rooms.length} loại phòng\r\n          </span>\r\n        </div>\r\n\r\n        {loading ? (\r\n          <div className=\"text-center py-5\">\r\n            <div\r\n              className=\"spinner-border text-primary\"\r\n              role=\"status\"\r\n              style={{ width: \"3rem\", height: \"3rem\" }}\r\n            >\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n            <p className=\"mt-3 text-muted\">Đang tải danh sách phòng...</p>\r\n          </div>\r\n        ) : rooms.length > 0 ? (\r\n          <div className=\"row g-4\">\r\n            {rooms.map((room) => (\r\n              <div key={room._id} className=\"col-lg-4 col-md-6\">\r\n                <div\r\n                  className=\"card h-100 shadow-sm border-0\"\r\n                  style={{ transition: \"transform 0.2s ease-in-out\" }}\r\n                >\r\n                  <div className=\"card-header bg-white border-0 pb-2\">\r\n                    <h5 className=\"card-title mb-1 text-primary fw-bold\">\r\n                      {room.name}\r\n                    </h5>\r\n                    <p className=\"text-muted small mb-0\">\r\n                      {room.roomType?.name || room.type}\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"card-body pt-2\">\r\n                    {/* Price Section */}\r\n                    <div className=\"d-flex align-items-center mb-3\">\r\n                      <span className=\"h4 text-success fw-bold mb-0\">\r\n                        {Utils.formatCurrency(room.price)}\r\n                      </span>\r\n                      <span className=\"text-muted ms-2\">/đêm</span>\r\n                    </div>\r\n\r\n                    {/* Room Info Grid */}\r\n                    <div className=\"row g-2 mb-3\">\r\n                      <div className=\"col-6\">\r\n                        <div className=\"d-flex align-items-center p-2 bg-light rounded\">\r\n                          <i className=\"fas fa-users text-primary me-2\"></i>\r\n                          <div>\r\n                            <small className=\"text-muted d-block\">\r\n                              Sức chứa\r\n                            </small>\r\n                            <strong>{room.capacity} người</strong>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-6\">\r\n                        <div className=\"d-flex align-items-center p-2 bg-light rounded\">\r\n                          <i className=\"fas fa-door-open text-success me-2\"></i>\r\n                          <div>\r\n                            <small className=\"text-muted d-block\">\r\n                              Còn trống\r\n                            </small>\r\n                            <strong>{room.availableQuantity}</strong>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Booking Status */}\r\n                    <div className=\"row g-2\">\r\n                      <div className=\"col-12\">\r\n                        <div className=\"d-flex justify-content-between align-items-center p-2 border rounded\">\r\n                          <span className=\"text-muted\">Đã đặt:</span>\r\n                          <span className=\"badge bg-warning text-dark\">\r\n                            {Number(room.quantity) -\r\n                              Number(room.availableQuantity)}{\" \"}\r\n                            phòng\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Availability Indicator */}\r\n                  <div className=\"card-footer bg-white border-0 pt-0\">\r\n                    {room.availableQuantity > 0 ? (\r\n                      <div className=\"d-flex align-items-center text-success\">\r\n                        <i className=\"fas fa-check-circle me-2\"></i>\r\n                        <small>Có phòng trống</small>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"d-flex align-items-center text-danger\">\r\n                        <i className=\"fas fa-times-circle me-2\"></i>\r\n                        <small>Hết phòng</small>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-5\">\r\n            <div className=\"mb-4\">\r\n              <i\r\n                className=\"fas fa-bed text-muted\"\r\n                style={{ fontSize: \"4rem\" }}\r\n              ></i>\r\n            </div>\r\n            <h5 className=\"text-muted\">\r\n              {hotelId\r\n                ? \"Không có phòng trống trong khoảng thời gian này\"\r\n                : \"Vui lòng chọn ngày để xem phòng trống\"}\r\n            </h5>\r\n            <p className=\"text-muted mb-0\">\r\n              Thử thay đổi ngày checkin/checkout hoặc số lượng khách\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Reservation Modal */}\r\n      <div\r\n        className={`modal fade ${showReservationModal ? \"show\" : \"\"}`}\r\n        style={{ display: showReservationModal ? \"block\" : \"none\" }}\r\n        tabIndex=\"-1\"\r\n      >\r\n        <div className=\"modal-dialog modal-xl\">\r\n          <div className=\"modal-content\">\r\n            <div className=\"modal-header\">\r\n              <h5 className=\"modal-title\">Tạo đặt phòng mới</h5>\r\n              <button\r\n                type=\"button\"\r\n                className=\"btn-close\"\r\n                onClick={() => setShowReservationModal(false)}\r\n              ></button>\r\n            </div>\r\n            <div className=\"modal-body\">\r\n              <div className=\"row\">\r\n                {/* Customer Information */}\r\n                <div className=\"col-md-4\">\r\n                  <h6>Thông tin khách hàng</h6>\r\n                  <div className=\"mb-3\">\r\n                    <label className=\"form-label\">Tên khách hàng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className=\"form-control\"\r\n                      value={Auth.name}\r\n                      onChange={(e) =>\r\n                        setReservationCustomer((prev) => ({\r\n                          ...prev,\r\n                          name: e.target.value,\r\n                        }))\r\n                      }\r\n                      disabled\r\n                    />\r\n                  </div>\r\n                  <div className=\"mb-3\">\r\n                    <label className=\"form-label\">Email</label>\r\n                    <input\r\n                      type=\"email\"\r\n                      className=\"form-control\"\r\n                      value={Auth.email}\r\n                      onChange={(e) =>\r\n                        setReservationCustomer((prev) => ({\r\n                          ...prev,\r\n                          email: e.target.value,\r\n                        }))\r\n                      }\r\n                      disabled\r\n                    />\r\n                  </div>\r\n                  <div className=\"mb-3\">\r\n                    <label className=\"form-label\">Số điện thoại</label>\r\n                    <input\r\n                      type=\"tel\"\r\n                      className=\"form-control\"\r\n                      value={Auth.phoneNumber}\r\n                      onChange={(e) =>\r\n                        setReservationCustomer((prev) => ({\r\n                          ...prev,\r\n                          phone: e.target.value,\r\n                        }))\r\n                      }\r\n                      disabled\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Booking Summary */}\r\n                  <div className=\"mt-4\">\r\n                    <h6>Tóm tắt đặt phòng</h6>\r\n                    <div className=\"bg-light p-3 rounded\">\r\n                      <p>\r\n                        <strong>Check-in:</strong> {checkinDate}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Check-out:</strong> {checkoutDate}\r\n                      </p>\r\n                      <p>\r\n                        <strong>Số khách:</strong>{\" \"}\r\n                        {selectedAdults.value + selectedChildren.value}\r\n                      </p>\r\n                      <hr />\r\n                      <p>\r\n                        <strong>Tổng tiền:</strong>{\" \"}\r\n                        {Utils.formatCurrency(calculateTotalPrice())}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Room Selection */}\r\n                <div className=\"col-md-4\">\r\n                  <h6>Chọn phòng</h6>\r\n                  <div style={{ maxHeight: \"520px\", overflowY: \"auto\" }}>\r\n                    {rooms.map((room) => (\r\n                      <div key={room._id} className=\"card mb-2\">\r\n                        <div className=\"card-body p-3\">\r\n                          <h6 className=\"card-title\">{room.name}</h6>\r\n                          <p className=\"card-text small\">\r\n                            {room.roomType?.name} -{\" \"}\r\n                            {Utils.formatCurrency(room.price)}/đêm\r\n                          </p>\r\n                          <p className=\"small text-muted\">\r\n                            Còn trống: {room.availableQuantity} phòng\r\n                          </p>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <label className=\"me-2\">Số lượng:</label>\r\n                            <select\r\n                              className=\"form-select form-select-sm\"\r\n                              style={{ width: \"80px\" }}\r\n                              value={selectedRoomsForReservation[room._id] || 0}\r\n                              onChange={(e) =>\r\n                                handleRoomQuantityChange(\r\n                                  room._id,\r\n                                  parseInt(e.target.value)\r\n                                )\r\n                              }\r\n                            >\r\n                              {Array.from(\r\n                                { length: room.availableQuantity + 1 },\r\n                                (_, i) => (\r\n                                  <option key={i} value={i}>\r\n                                    {i}\r\n                                  </option>\r\n                                )\r\n                              )}\r\n                            </select>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Service Selection */}\r\n                <div className=\"col-md-4\">\r\n                  <h6>Chọn dịch vụ</h6>\r\n                  <div style={{ maxHeight: \"520px\", overflowY: \"auto\" }}>\r\n                    {hotelInfo?.[0]?.services?.map((service) => {\r\n                      const isSelected =\r\n                        selectedServicesForReservation[service._id];\r\n                      const quantity = serviceQuantities[service._id] || 1;\r\n                      const selectedDates =\r\n                        serviceSelectedDates[service._id] || [];\r\n\r\n                      return (\r\n                        <div key={service._id} className=\"card mb-2\">\r\n                          <div className=\"card-body p-3\">\r\n                            <div className=\"form-check\">\r\n                              <input\r\n                                className=\"form-check-input\"\r\n                                type=\"checkbox\"\r\n                                checked={!!isSelected}\r\n                                onChange={() => handleServiceSelection(service)}\r\n                              />\r\n                              <label className=\"form-check-label\">\r\n                                <strong>{service.name}</strong>\r\n                              </label>\r\n                            </div>\r\n                            <p className=\"small text-muted\">\r\n                              {Utils.formatCurrency(service.price)}/\r\n                              {service.type}\r\n                            </p>\r\n\r\n                            {isSelected && (\r\n                              <div className=\"mt-2\">\r\n                                <div className=\"d-flex align-items-center mb-2\">\r\n                                  <label className=\"me-2\">Số lượng:</label>\r\n                                  <div\r\n                                    className=\"input-group input-group-sm\"\r\n                                    style={{ width: \"120px\" }}\r\n                                  >\r\n                                    <button\r\n                                      className=\"btn btn-outline-secondary\"\r\n                                      type=\"button\"\r\n                                      onClick={() =>\r\n                                        handleServiceQuantityChange(\r\n                                          service._id,\r\n                                          quantity - 1\r\n                                        )\r\n                                      }\r\n                                      disabled={quantity <= 1}\r\n                                    >\r\n                                      -\r\n                                    </button>\r\n                                    <input\r\n                                      type=\"text\"\r\n                                      className=\"form-control text-center\"\r\n                                      value={quantity}\r\n                                      readOnly\r\n                                    />\r\n                                    <button\r\n                                      className=\"btn btn-outline-secondary\"\r\n                                      type=\"button\"\r\n                                      onClick={() =>\r\n                                        handleServiceQuantityChange(\r\n                                          service._id,\r\n                                          quantity + 1\r\n                                        )\r\n                                      }\r\n                                    >\r\n                                      +\r\n                                    </button>\r\n                                  </div>\r\n                                </div>\r\n\r\n                                <div className=\"mb-2\">\r\n                                  <label className=\"form-label small\">\r\n                                    Chọn ngày sử dụng:\r\n                                  </label>\r\n                                  <div className=\"d-flex flex-wrap gap-1\">\r\n                                    {getDatesBetween(\r\n                                      new Date(checkinDate),\r\n                                      new Date(checkoutDate)\r\n                                    ).map((date) => {\r\n                                      const dateStr = date.toISOString();\r\n                                      const isDateSelected =\r\n                                        selectedDates.includes(dateStr);\r\n\r\n                                      return (\r\n                                        <button\r\n                                          key={dateStr}\r\n                                          type=\"button\"\r\n                                          className={`btn btn-sm ${\r\n                                            isDateSelected\r\n                                              ? \"btn-primary\"\r\n                                              : \"btn-outline-primary\"\r\n                                          }`}\r\n                                          onClick={() =>\r\n                                            handleDateSelection(\r\n                                              service._id,\r\n                                              date\r\n                                            )\r\n                                          }\r\n                                        >\r\n                                          {date.getDate()}/{date.getMonth() + 1}\r\n                                        </button>\r\n                                      );\r\n                                    })}\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"modal-footer\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"btn btn-secondary\"\r\n                onClick={() => setShowReservationModal(false)}\r\n              >\r\n                Hủy\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                className=\"btn btn-primary\"\r\n                onClick={handleReservationSubmit}\r\n              >\r\n                Tạo đặt phòng\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {showReservationModal && <div className=\"modal-backdrop fade show\"></div>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ManagementBooking;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,OAAOC,KAAK,MAAM,cAAc;AAChC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,cAAc;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;EAC1DC,KAAK,EAAED,CAAC,GAAG,CAAC;EACZE,KAAK,EAAE,GAAGF,CAAC,GAAG,CAAC;AACjB,CAAC,CAAC,CAAC;AAEH,MAAMG,eAAe,GAAGP,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;EAC5DC,KAAK,EAAED,CAAC;EACRE,KAAK,EAAE,GAAGF,CAAC;AACb,CAAC,CAAC,CAAC;;AAEH;AACA,MAAMI,YAAY,GAAG;EACnBC,OAAO,EAAGC,QAAQ,KAAM;IACtB,GAAGA,QAAQ;IACXC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE;MACTC,WAAW,EAAE;IACf;EACF,CAAC;AACH,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,oBAAA;EAC9B,MAAMC,IAAI,GAAG5B,cAAc,CAAE6B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMqC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD,MAAMC,QAAQ,GAAG,IAAIH,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CACxDH,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAEhB,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAACqC,KAAK,CAAC;EACrD,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAACyC,QAAQ,CAAC;;EAE1D;EACA,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAACI,aAAa,CAAC,CAAC,CAAC,CAAC;EACtE,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAACY,eAAe,CAAC,CAAC,CAAC,CAAC;EAE5E,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC;IAC/C2C,WAAW,EAAEN,KAAK;IAClBQ,YAAY,EAAEJ,QAAQ;IACtBY,cAAc,EAAE,CAAC;IACjBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC0D,2BAA2B,EAAEC,8BAA8B,CAAC,GACjE3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,MAAM,CAAC4D,8BAA8B,EAAEC,iCAAiC,CAAC,GACvE7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,MAAM,CAAC8D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACgE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM,CAACkE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnE,QAAQ,CAAC;IAC7DoE,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAG3E,cAAc,CAAC,CAAC;EAEjC,MAAM4E,cAAc,GAAGA,CAAA,KAAM;IAC3BtC,UAAU,CAAC,IAAI,CAAC;IAChBqC,QAAQ,CAAC;MACPE,IAAI,EAAEhF,YAAY,CAACiF,iBAAiB;MACpCC,OAAO,EAAE;QACPC,MAAM,EAAEnD,IAAI,CAACoD,GAAG;QAChBC,SAAS,EAAGC,IAAI,IAAK;UACnBnD,YAAY,CAACmD,IAAI,CAACC,MAAM,CAAC;UACzBlD,UAAU,CAACiD,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACH,GAAG,CAAC;UAC9B3C,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC;QACD+C,QAAQ,EAAEA,CAAA,KAAM;UACd1F,SAAS,CAAC2F,KAAK,CAAC,kCAAkC,CAAC;UACnDhD,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC;QACDiD,OAAO,EAAGC,GAAG,IAAK;UAChBC,OAAO,CAACH,KAAK,CAACE,GAAG,CAAC;UAClB7F,SAAS,CAAC2F,KAAK,CAAC,yCAAyC,CAAC;UAC1DhD,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACdyE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzE,SAAS,CAAC,MAAM;IACdqD,eAAe,CAAC;MACdT,WAAW,EAAEA,WAAW;MACxBE,YAAY,EAAEA,YAAY;MAC1BQ,cAAc,EAAEN,cAAc,CAACrC,KAAK,GAAGuC,gBAAgB,CAACvC,KAAK;MAC7D4C,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,WAAW,EAAEE,YAAY,EAAEE,cAAc,EAAEE,gBAAgB,CAAC,CAAC;EAEjE,MAAMqC,UAAU,GAAIC,SAAS,IAAK;IAChChB,QAAQ,CAAC;MACPE,IAAI,EAAE9E,WAAW,CAAC6F,UAAU;MAC5Bb,OAAO,EAAE;QACP9C,OAAO;QACP4D,KAAK,EAAEtC,YAAY;QACnB2B,SAAS,EAAGY,QAAQ,IAAK;UACvB,IAAIH,SAAS,EAAE;YACb,IAAIlF,KAAK,CAACsF,OAAO,CAACD,QAAQ,CAAC,EAAE;cAC3B1D,QAAQ,CAAC0D,QAAQ,CAAC;YACpB,CAAC,MAAM;cACLL,OAAO,CAACO,IAAI,CAAC,kCAAkC,EAAEF,QAAQ,CAAC;YAC5D;UACF;QACF,CAAC;QACDT,QAAQ,EAAGY,GAAG,IAAK;UACjB,IAAIN,SAAS,EAAE;YACbF,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEW,GAAG,CAAC;UAC9C;QACF,CAAC;QACDV,OAAO,EAAGC,GAAG,IAAK;UAChB,IAAIG,SAAS,EAAE;YACbF,OAAO,CAACH,KAAK,CAAC,eAAe,EAAEE,GAAG,CAAC;UACrC;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACArF,SAAS,CAAC,MAAM;IACd,IAAIwF,SAAS,GAAG,IAAI;IAEpB,IAAI1D,OAAO,IAAIsB,YAAY,CAACR,WAAW,IAAIQ,YAAY,CAACN,YAAY,EAAE;MAClEyC,UAAU,CAACC,SAAS,CAAC;IACzB;IACA,OAAO,MAAM;MACXA,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAAC1D,OAAO,EAAE0C,QAAQ,EAAEpB,YAAY,CAAC,CAAC;EAErC,MAAM2C,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnC,8BAA8B,CAAC,CAAC,CAAC,CAAC;IAClCE,iCAAiC,CAAC,CAAC,CAAC,CAAC;IACrCE,oBAAoB,CAAC,CAAC,CAAC,CAAC;IACxBE,uBAAuB,CAAC,CAAC,CAAC,CAAC;IAC3B7B,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACAgB,eAAe,CAAC;MACdT,WAAW,EAAEA,WAAW;MACxBE,YAAY,EAAEA,YAAY;MAC1BQ,cAAc,EAAEN,cAAc,CAACrC,KAAK,GAAGuC,gBAAgB,CAACvC,KAAK;MAC7D4C,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC,CAAC;;IAEF;IACAwC,UAAU,CAAC,MAAM;MACf3D,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAM4D,wBAAwB,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAK;IACrDvC,8BAA8B,CAAEwC,IAAI,KAAM;MACxC,GAAGA,IAAI;MACP,CAACF,MAAM,GAAGC;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,sBAAsB,GAAIC,OAAO,IAAK;IAC1CxC,iCAAiC,CAAEsC,IAAI,IAAK;MAC1C,MAAMG,UAAU,GAAGH,IAAI,CAACE,OAAO,CAACxB,GAAG,CAAC;MACpC,IAAIyB,UAAU,EAAE;QACd,MAAMC,WAAW,GAAG;UAAE,GAAGJ;QAAK,CAAC;QAC/B,OAAOI,WAAW,CAACF,OAAO,CAACxB,GAAG,CAAC;;QAE/B;QACAd,oBAAoB,CAAEoC,IAAI,IAAK;UAC7B,MAAMK,aAAa,GAAG;YAAE,GAAGL;UAAK,CAAC;UACjC,OAAOK,aAAa,CAACH,OAAO,CAACxB,GAAG,CAAC;UACjC,OAAO2B,aAAa;QACtB,CAAC,CAAC;QACFvC,uBAAuB,CAAEkC,IAAI,IAAK;UAChC,MAAMM,QAAQ,GAAG;YAAE,GAAGN;UAAK,CAAC;UAC5B,OAAOM,QAAQ,CAACJ,OAAO,CAACxB,GAAG,CAAC;UAC5B,OAAO4B,QAAQ;QACjB,CAAC,CAAC;QAEF,OAAOF,WAAW;MACpB,CAAC,MAAM;QACLxC,oBAAoB,CAAEoC,IAAI,KAAM;UAC9B,GAAGA,IAAI;UACP,CAACE,OAAO,CAACxB,GAAG,GAAG;QACjB,CAAC,CAAC,CAAC;QACH,OAAO;UACL,GAAGsB,IAAI;UACP,CAACE,OAAO,CAACxB,GAAG,GAAGwB;QACjB,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,2BAA2B,GAAGA,CAACC,SAAS,EAAET,QAAQ,KAAK;IAC3D,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAClBnC,oBAAoB,CAAEoC,IAAI,KAAM;MAC9B,GAAGA,IAAI;MACP,CAACQ,SAAS,GAAGT;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMU,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIC,WAAW,GAAG,IAAI1E,IAAI,CAACuE,SAAS,CAAC;IACrC,MAAMI,QAAQ,GAAG,IAAI3E,IAAI,CAACwE,OAAO,CAAC;IAElC,OAAOE,WAAW,GAAGC,QAAQ,EAAE;MAC7BF,KAAK,CAACG,IAAI,CAAC,IAAI5E,IAAI,CAAC0E,WAAW,CAAC,CAAC;MACjCA,WAAW,CAACG,OAAO,CAACH,WAAW,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD;IAEA,OAAOL,KAAK;EACd,CAAC;EAED,MAAMM,mBAAmB,GAAGA,CAACV,SAAS,EAAEW,IAAI,KAAK;IAC/CrD,uBAAuB,CAAEkC,IAAI,IAAK;MAChC,MAAMoB,YAAY,GAAGpB,IAAI,CAACQ,SAAS,CAAC,IAAI,EAAE;MAC1C,MAAMa,OAAO,GAAGF,IAAI,CAAC/E,WAAW,CAAC,CAAC;MAElC,IAAIgF,YAAY,CAACE,QAAQ,CAACD,OAAO,CAAC,EAAE;QAClC,OAAO;UACL,GAAGrB,IAAI;UACP,CAACQ,SAAS,GAAGY,YAAY,CAACG,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKH,OAAO;QACvD,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACL,GAAGrB,IAAI;UACP,CAACQ,SAAS,GAAG,CAAC,GAAGY,YAAY,EAAEC,OAAO;QACxC,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C;IACA,MAAMC,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACrE,2BAA2B,CAAC,CAACgE,MAAM,CACpEzB,MAAM,IAAKvC,2BAA2B,CAACuC,MAAM,CAAC,GAAG,CACpD,CAAC;IAED,IAAI4B,eAAe,CAACtH,MAAM,KAAK,CAAC,EAAE;MAChChB,SAAS,CAAC2F,KAAK,CAAC,iCAAiC,CAAC;MAClD;IACF;;IAEA;IACA,IAAI,CAACvC,WAAW,IAAI,CAACE,YAAY,EAAE;MACjCtD,SAAS,CAAC2F,KAAK,CAAC,4CAA4C,CAAC;MAC7D;IACF;IAEA,IAAI,IAAI5C,IAAI,CAACK,WAAW,CAAC,IAAI,IAAIL,IAAI,CAACO,YAAY,CAAC,EAAE;MACnDtD,SAAS,CAAC2F,KAAK,CAAC,yCAAyC,CAAC;MAC1D;IACF;IAEA,IAAI,IAAI5C,IAAI,CAACK,WAAW,CAAC,GAAG,IAAIL,IAAI,CAAC,CAAC,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;MAC3DzI,SAAS,CAAC2F,KAAK,CAAC,kDAAkD,CAAC;MACnE;IACF;;IAEA;IACA,MAAM+C,oBAAoB,GAAG,EAAE;IAC/BJ,eAAe,CAACK,OAAO,CAAEjC,MAAM,IAAK;MAClC,MAAMkC,IAAI,GAAGpG,KAAK,CAACqG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACxD,GAAG,KAAKoB,MAAM,CAAC;MAChD,MAAMqC,gBAAgB,GAAG5E,2BAA2B,CAACuC,MAAM,CAAC;MAE5D,IAAIkC,IAAI,IAAIG,gBAAgB,GAAGH,IAAI,CAACI,iBAAiB,EAAE;QACrDN,oBAAoB,CAACf,IAAI,CACvB,SAASiB,IAAI,CAAC/D,IAAI,aAAa+D,IAAI,CAACI,iBAAiB,6BAA6BD,gBAAgB,QACpG,CAAC;MACH;IACF,CAAC,CAAC;IAEF,IAAIL,oBAAoB,CAAC1H,MAAM,GAAG,CAAC,EAAE;MACnChB,SAAS,CAAC2F,KAAK,CAAC+C,oBAAoB,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC;MAChD;IACF;;IAEA;IACA,MAAMC,kBAAkB,GAAGZ,eAAe,CAACa,MAAM,CAAC,CAACC,KAAK,EAAE1C,MAAM,KAAK;MACnE,MAAMkC,IAAI,GAAGpG,KAAK,CAACqG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACxD,GAAG,KAAKoB,MAAM,CAAC;MAChD,MAAMC,QAAQ,GAAGxC,2BAA2B,CAACuC,MAAM,CAAC;MACpD,OAAO0C,KAAK,IAAIR,IAAI,GAAGA,IAAI,CAACS,QAAQ,GAAG1C,QAAQ,GAAG,CAAC,CAAC;IACtD,CAAC,EAAE,CAAC,CAAC;IAEL,MAAM2C,WAAW,GAAG9F,cAAc,CAACrC,KAAK,GAAGuC,gBAAgB,CAACvC,KAAK;IACjE,IAAImI,WAAW,GAAGJ,kBAAkB,EAAE;MACpClJ,SAAS,CAAC2F,KAAK,CACb,mBAAmB2D,WAAW,0CAA0CJ,kBAAkB,GAC5F,CAAC;MACD;IACF;;IAEA;IACA,MAAMK,gBAAgB,GAAGhB,MAAM,CAACiB,MAAM,CAACnF,8BAA8B,CAAC;IACtE,MAAMoF,uBAAuB,GAAG,EAAE;IAElCF,gBAAgB,CAACZ,OAAO,CAAE7B,OAAO,IAAK;MACpC,MAAM4C,aAAa,GAAGjF,oBAAoB,CAACqC,OAAO,CAACxB,GAAG,CAAC,IAAI,EAAE;MAC7D,MAAMqB,QAAQ,GAAGpC,iBAAiB,CAACuC,OAAO,CAACxB,GAAG,CAAC,IAAI,CAAC;MAEpD,IAAIoE,aAAa,CAAC1I,MAAM,KAAK,CAAC,EAAE;QAC9ByI,uBAAuB,CAAC9B,IAAI,CAC1B,WAAWb,OAAO,CAACjC,IAAI,0CACzB,CAAC;MACH;MAEA,IAAI8B,QAAQ,GAAG,CAAC,EAAE;QAChB8C,uBAAuB,CAAC9B,IAAI,CAC1B,WAAWb,OAAO,CAACjC,IAAI,2BACzB,CAAC;MACH;;MAEA;MACA,MAAM8E,WAAW,GAAG,IAAI5G,IAAI,CAACK,WAAW,CAAC,CAACwG,OAAO,CAAC,CAAC;MACnD,MAAMC,YAAY,GAAG,IAAI9G,IAAI,CAACO,YAAY,CAAC,CAACsG,OAAO,CAAC,CAAC;MAErDF,aAAa,CAACf,OAAO,CAAEV,OAAO,IAAK;QACjC,MAAM6B,WAAW,GAAG,IAAI/G,IAAI,CAACkF,OAAO,CAAC,CAAC2B,OAAO,CAAC,CAAC;QAC/C,IAAIE,WAAW,GAAGH,WAAW,IAAIG,WAAW,IAAID,YAAY,EAAE;UAC5DJ,uBAAuB,CAAC9B,IAAI,CAC1B,WAAWb,OAAO,CAACjC,IAAI,oDACzB,CAAC;QACH;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI4E,uBAAuB,CAACzI,MAAM,GAAG,CAAC,EAAE;MACtChB,SAAS,CAAC2F,KAAK,CAAC8D,uBAAuB,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;MACnD;IACF;;IAEA;IACA,IAAI,CAAC/G,IAAI,CAACoD,GAAG,EAAE;MACbtF,SAAS,CAAC2F,KAAK,CAAC,mCAAmC,CAAC;MACpD;IACF;;IAEA;IACA,IAAI,CAACrD,OAAO,EAAE;MACZtC,SAAS,CAAC2F,KAAK,CAAC,yBAAyB,CAAC;MAC1C;IACF;;IAEA;IACA,MAAMoE,UAAU,GAAGC,mBAAmB,CAAC,CAAC;IACxC,IAAID,UAAU,IAAI,CAAC,EAAE;MACnB/J,SAAS,CAAC2F,KAAK,CAAC,uCAAuC,CAAC;MACxD;IACF;;IAEA;IACA,MAAMsE,MAAM,GAAGC,IAAI,CAACC,IAAI,CACtB,CAAC,IAAIpH,IAAI,CAACO,YAAY,CAAC,GAAG,IAAIP,IAAI,CAACK,WAAW,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CACzE,CAAC;IACD,IAAI6G,MAAM,IAAI,CAAC,EAAE;MACfjK,SAAS,CAAC2F,KAAK,CAAC,sCAAsC,CAAC;MACvD;IACF;;IAEA;IACA,MAAMyE,eAAe,GAAG;MACtB9H,OAAO,EAAEA,OAAO;MAChB+H,WAAW,EAAEjH,WAAW;MACxBkH,YAAY,EAAEhH,YAAY;MAC1ByG,UAAU,EAAEA,UAAU;MACtBQ,UAAU,EAAER,UAAU;MACtBS,WAAW,EAAElC,eAAe,CAACmC,GAAG,CAAE/D,MAAM,KAAM;QAC5CkC,IAAI,EAAE;UACJtD,GAAG,EAAEoB;QACP,CAAC;QACDgE,MAAM,EAAEvG,2BAA2B,CAACuC,MAAM;MAC5C,CAAC,CAAC,CAAC;MACHiE,cAAc,EAAEpC,MAAM,CAACiB,MAAM,CAACnF,8BAA8B,CAAC,CAACoG,GAAG,CAC9D3D,OAAO,KAAM;QACZxB,GAAG,EAAEwB,OAAO,CAACxB,GAAG;QAChBqB,QAAQ,EAAEpC,iBAAiB,CAACuC,OAAO,CAACxB,GAAG,CAAC,IAAI,CAAC;QAC7CoE,aAAa,EAAEjF,oBAAoB,CAACqC,OAAO,CAACxB,GAAG,CAAC,IAAI;MACtD,CAAC,CACH;IACF,CAAC;IAEDQ,OAAO,CAAC8E,GAAG,CAAC,mBAAmB,EAAER,eAAe,CAAC;IAEjD,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM1K,SAAS,CAAC2K,sBAAsB,CAACV,eAAe,CAAC;MACxEtE,OAAO,CAAC8E,GAAG,CAAC,cAAc,EAAEC,QAAQ,CAAC;MACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;QAAA,IAAAC,cAAA,EAAAC,qBAAA;QAC5BnF,OAAO,CAAC8E,GAAG,CAAC,cAAc,EAAEC,QAAQ,CAAC;QACrC,MAAMK,mBAAmB,GAAGL,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAErF,IAAI,cAAAwF,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmC3F,GAAG;QAElE,IAAI,CAAC4F,mBAAmB,EAAE;UACxBlL,SAAS,CAAC2F,KAAK,CAAC,4CAA4C,CAAC;UAC7D;QACF;QACAvB,8BAA8B,CAAC,CAAC,CAAC,CAAC;QAClCE,iCAAiC,CAAC,CAAC,CAAC,CAAC;QACrCE,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBE,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAC3BqB,UAAU,CAAC,IAAI,CAAC;QAChB/F,SAAS,CAACoL,OAAO,CAAC,uBAAuB,CAAC;MAC5C,CAAC,MAAM,IAAI,CAAAP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;QAAA,IAAAM,eAAA,EAAAC,qBAAA;QACnCxF,OAAO,CAAC8E,GAAG,CAAC,cAAc,EAAEC,QAAQ,CAAC;QACrC,MAAMU,aAAa,GAAGV,QAAQ,aAARA,QAAQ,wBAAAQ,eAAA,GAARR,QAAQ,CAAErF,IAAI,cAAA6F,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BhG,GAAG;QAEtD,IAAI,CAACiG,aAAa,EAAE;UAClBvL,SAAS,CAAC2F,KAAK,CAAC,4CAA4C,CAAC;UAC7D;QACF;QACAvB,8BAA8B,CAAC,CAAC,CAAC,CAAC;QAClCE,iCAAiC,CAAC,CAAC,CAAC,CAAC;QACrCE,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBE,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAC3BqB,UAAU,CAAC,IAAI,CAAC;QAChB/F,SAAS,CAACoL,OAAO,CAAC,8BAA8B,CAAC;MACnD,CAAC,MAAM;QAAA,IAAAK,eAAA;QACL,MAAMC,YAAY,GAChB,CAAAb,QAAQ,aAARA,QAAQ,wBAAAY,eAAA,GAARZ,QAAQ,CAAErF,IAAI,cAAAiG,eAAA,uBAAdA,eAAA,CAAgBE,OAAO,KAAI,sCAAsC;QACnE3L,SAAS,CAAC2F,KAAK,CAAC+F,YAAY,CAAC;QAC7B5F,OAAO,CAAC8E,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOlF,KAAK,EAAE;MAAA,IAAAiG,eAAA,EAAAC,oBAAA;MACd/F,OAAO,CAACH,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAM+F,YAAY,GAChB,CAAA/F,KAAK,aAALA,KAAK,wBAAAiG,eAAA,GAALjG,KAAK,CAAEkF,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBpG,IAAI,cAAAqG,oBAAA,uBAArBA,oBAAA,CAAuBF,OAAO,MAC9BhG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgG,OAAO,KACd,kDAAkD;MACpD3L,SAAS,CAAC2F,KAAK,CAAC+F,YAAY,CAAC;IAC/B,CAAC,SAAS;MACR;MACAxH,uBAAuB,CAAC,KAAK,CAAC;;MAE9B;MACAE,8BAA8B,CAAC,CAAC,CAAC,CAAC;MAClCE,iCAAiC,CAAC,CAAC,CAAC,CAAC;MACrCE,oBAAoB,CAAC,CAAC,CAAC,CAAC;MACxBE,uBAAuB,CAAC,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC;EAED,MAAMsF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIZ,KAAK,GAAG,CAAC;IACb,MAAMa,MAAM,GAAGC,IAAI,CAACC,IAAI,CACtB,CAAC,IAAIpH,IAAI,CAACO,YAAY,CAAC,GAAG,IAAIP,IAAI,CAACK,WAAW,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CACzE,CAAC;;IAED;IACAmF,MAAM,CAACC,IAAI,CAACrE,2BAA2B,CAAC,CAACwE,OAAO,CAAEjC,MAAM,IAAK;MAC3D,MAAMkC,IAAI,GAAGpG,KAAK,CAACqG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACxD,GAAG,KAAKoB,MAAM,CAAC;MAChD,IAAIkC,IAAI,IAAIzE,2BAA2B,CAACuC,MAAM,CAAC,GAAG,CAAC,EAAE;QACnD0C,KAAK,IAAIR,IAAI,CAACkD,KAAK,GAAG3H,2BAA2B,CAACuC,MAAM,CAAC,GAAGuD,MAAM;MACpE;IACF,CAAC,CAAC;;IAEF;IACA1B,MAAM,CAACiB,MAAM,CAACnF,8BAA8B,CAAC,CAACsE,OAAO,CAAE7B,OAAO,IAAK;MACjE,MAAMH,QAAQ,GAAGpC,iBAAiB,CAACuC,OAAO,CAACxB,GAAG,CAAC,IAAI,CAAC;MACpD,MAAMoE,aAAa,GAAGjF,oBAAoB,CAACqC,OAAO,CAACxB,GAAG,CAAC,IAAI,EAAE;MAC7D8D,KAAK,IAAItC,OAAO,CAACgF,KAAK,GAAGnF,QAAQ,GAAG+C,aAAa,CAAC1I,MAAM;IAC1D,CAAC,CAAC;IAEF,OAAOoI,KAAK;EACd,CAAC;EAED,oBACExI,OAAA;IAAKmL,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCpL,OAAA,CAACX,aAAa;MAAAgM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBxL,OAAA;MAAAoL,QAAA,EAAI;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAG1BxL,OAAA;MACEmL,SAAS,EAAC,gBAAgB;MAC1BM,KAAK,EAAE;QACLC,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,MAAM;QACf7K,YAAY,EAAE,MAAM;QACpB8K,YAAY,EAAE;MAChB,CAAE;MAAAR,QAAA,gBAEFpL,OAAA;QAAKmL,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrEpL,OAAA;UAAAoL,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BxL,OAAA;UACEmL,SAAS,EAAC,iBAAiB;UAC3BU,OAAO,EAAEA,CAAA,KAAMvI,uBAAuB,CAAC,IAAI,CAAE;UAC7CwI,QAAQ,EAAE,CAACpK,OAAO,IAAIE,KAAK,CAACxB,MAAM,KAAK,CAAE;UAAAgL,QAAA,gBAEzCpL,OAAA;YAAGmL,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,yDAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxL,OAAA;QAAKmL,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBpL,OAAA;UAAKmL,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpL,OAAA;YAAO+L,OAAO,EAAC,aAAa;YAACZ,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxL,OAAA;YACEsE,IAAI,EAAC,MAAM;YACX0H,EAAE,EAAC,aAAa;YAChBb,SAAS,EAAC,cAAc;YACxB5K,KAAK,EAAEiC,WAAY;YACnByJ,GAAG,EAAE/J,KAAM;YACXgK,QAAQ,EAAGC,CAAC,IAAK1J,cAAc,CAAC0J,CAAC,CAACC,MAAM,CAAC7L,KAAK;UAAE;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxL,OAAA;UAAKmL,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpL,OAAA;YAAO+L,OAAO,EAAC,cAAc;YAACZ,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxL,OAAA;YACEsE,IAAI,EAAC,MAAM;YACX0H,EAAE,EAAC,cAAc;YACjBb,SAAS,EAAC,cAAc;YACxB5K,KAAK,EAAEmC,YAAa;YACpBuJ,GAAG,EAAEzJ,WAAW,IAAIN,KAAM;YAC1BgK,QAAQ,EAAGC,CAAC,IAAKxJ,eAAe,CAACwJ,CAAC,CAACC,MAAM,CAAC7L,KAAK;UAAE;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxL,OAAA;UAAKmL,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpL,OAAA;YAAOmL,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/CxL,OAAA,CAACF,MAAM;YACLS,KAAK,EAAEqC,cAAe;YACtBsJ,QAAQ,EAAErJ,iBAAkB;YAC5BwJ,OAAO,EAAEpM,aAAc;YACvBqM,MAAM,EAAE5L,YAAa;YACrB6L,YAAY,EAAE;UAAM;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxL,OAAA;UAAKmL,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpL,OAAA;YAAOmL,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CxL,OAAA,CAACF,MAAM;YACLS,KAAK,EAAEuC,gBAAiB;YACxBoJ,QAAQ,EAAEnJ,mBAAoB;YAC9BsJ,OAAO,EAAE5L,eAAgB;YACzB6L,MAAM,EAAE5L,YAAa;YACrB6L,YAAY,EAAE;UAAM;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxL,OAAA;UAAKmL,SAAS,EAAC,sCAAsC;UAAAC,QAAA,eACnDpL,OAAA;YACEmL,SAAS,EAAC,uBAAuB;YACjCU,OAAO,EAAElG,iBAAkB;YAC3BmG,QAAQ,EAAE9J,WAAW,IAAI,CAACQ,WAAW,IAAI,CAACE,YAAa;YAAA0I,QAAA,EAEtDpJ,WAAW,GAAG,aAAa,GAAG;UAAU;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxL,OAAA;MAAKmL,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCpL,OAAA;QAAKmL,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrEpL,OAAA;UAAImL,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CxL,OAAA;UAAMmL,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GACtCxJ,KAAK,CAACxB,MAAM,EAAC,qBAChB;QAAA;UAAAiL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAEL1J,OAAO,gBACN9B,OAAA;QAAKmL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BpL,OAAA;UACEmL,SAAS,EAAC,6BAA6B;UACvCqB,IAAI,EAAC,QAAQ;UACbf,KAAK,EAAE;YAAEgB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAtB,QAAA,eAEzCpL,OAAA;YAAMmL,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNxL,OAAA;UAAGmL,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,GACJ5J,KAAK,CAACxB,MAAM,GAAG,CAAC,gBAClBJ,OAAA;QAAKmL,SAAS,EAAC,SAAS;QAAAC,QAAA,EACrBxJ,KAAK,CAACiI,GAAG,CAAE7B,IAAI;UAAA,IAAA2E,cAAA;UAAA,oBACd3M,OAAA;YAAoBmL,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAC/CpL,OAAA;cACEmL,SAAS,EAAC,+BAA+B;cACzCM,KAAK,EAAE;gBAAEmB,UAAU,EAAE;cAA6B,CAAE;cAAAxB,QAAA,gBAEpDpL,OAAA;gBAAKmL,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDpL,OAAA;kBAAImL,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EACjDpD,IAAI,CAAC/D;gBAAI;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLxL,OAAA;kBAAGmL,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACjC,EAAAuB,cAAA,GAAA3E,IAAI,CAAC6E,QAAQ,cAAAF,cAAA,uBAAbA,cAAA,CAAe1I,IAAI,KAAI+D,IAAI,CAAC1D;gBAAI;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENxL,OAAA;gBAAKmL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAE7BpL,OAAA;kBAAKmL,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpL,OAAA;oBAAMmL,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAC3CzL,KAAK,CAACmN,cAAc,CAAC9E,IAAI,CAACkD,KAAK;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACPxL,OAAA;oBAAMmL,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eAGNxL,OAAA;kBAAKmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BpL,OAAA;oBAAKmL,SAAS,EAAC,OAAO;oBAAAC,QAAA,eACpBpL,OAAA;sBAAKmL,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,gBAC7DpL,OAAA;wBAAGmL,SAAS,EAAC;sBAAgC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClDxL,OAAA;wBAAAoL,QAAA,gBACEpL,OAAA;0BAAOmL,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,EAAC;wBAEtC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACRxL,OAAA;0BAAAoL,QAAA,GAASpD,IAAI,CAACS,QAAQ,EAAC,kBAAM;wBAAA;0BAAA4C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNxL,OAAA;oBAAKmL,SAAS,EAAC,OAAO;oBAAAC,QAAA,eACpBpL,OAAA;sBAAKmL,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,gBAC7DpL,OAAA;wBAAGmL,SAAS,EAAC;sBAAoC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtDxL,OAAA;wBAAAoL,QAAA,gBACEpL,OAAA;0BAAOmL,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,EAAC;wBAEtC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACRxL,OAAA;0BAAAoL,QAAA,EAASpD,IAAI,CAACI;wBAAiB;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxL,OAAA;kBAAKmL,SAAS,EAAC,SAAS;kBAAAC,QAAA,eACtBpL,OAAA;oBAAKmL,SAAS,EAAC,QAAQ;oBAAAC,QAAA,eACrBpL,OAAA;sBAAKmL,SAAS,EAAC,sEAAsE;sBAAAC,QAAA,gBACnFpL,OAAA;wBAAMmL,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3CxL,OAAA;wBAAMmL,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,GACzC2B,MAAM,CAAC/E,IAAI,CAACjC,QAAQ,CAAC,GACpBgH,MAAM,CAAC/E,IAAI,CAACI,iBAAiB,CAAC,EAAE,GAAG,EAAC,UAExC;sBAAA;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxL,OAAA;gBAAKmL,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChDpD,IAAI,CAACI,iBAAiB,GAAG,CAAC,gBACzBpI,OAAA;kBAAKmL,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDpL,OAAA;oBAAGmL,SAAS,EAAC;kBAA0B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5CxL,OAAA;oBAAAoL,QAAA,EAAO;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,gBAENxL,OAAA;kBAAKmL,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDpL,OAAA;oBAAGmL,SAAS,EAAC;kBAA0B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5CxL,OAAA;oBAAAoL,QAAA,EAAO;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA9EExD,IAAI,CAACtD,GAAG;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+Eb,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENxL,OAAA;QAAKmL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BpL,OAAA;UAAKmL,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBpL,OAAA;YACEmL,SAAS,EAAC,uBAAuB;YACjCM,KAAK,EAAE;cAAEuB,QAAQ,EAAE;YAAO;UAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNxL,OAAA;UAAImL,SAAS,EAAC,YAAY;UAAAC,QAAA,EACvB1J,OAAO,GACJ,iDAAiD,GACjD;QAAuC;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACLxL,OAAA;UAAGmL,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNxL,OAAA;MACEmL,SAAS,EAAE,cAAc9H,oBAAoB,GAAG,MAAM,GAAG,EAAE,EAAG;MAC9DoI,KAAK,EAAE;QAAEwB,OAAO,EAAE5J,oBAAoB,GAAG,OAAO,GAAG;MAAO,CAAE;MAC5D6J,QAAQ,EAAC,IAAI;MAAA9B,QAAA,eAEbpL,OAAA;QAAKmL,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCpL,OAAA;UAAKmL,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpL,OAAA;YAAKmL,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BpL,OAAA;cAAImL,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClDxL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb6G,SAAS,EAAC,WAAW;cACrBU,OAAO,EAAEA,CAAA,KAAMvI,uBAAuB,CAAC,KAAK;YAAE;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACNxL,OAAA;YAAKmL,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBpL,OAAA;cAAKmL,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAElBpL,OAAA;gBAAKmL,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpL,OAAA;kBAAAoL,QAAA,EAAI;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BxL,OAAA;kBAAKmL,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpL,OAAA;oBAAOmL,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpDxL,OAAA;oBACEsE,IAAI,EAAC,MAAM;oBACX6G,SAAS,EAAC,cAAc;oBACxB5K,KAAK,EAAEe,IAAI,CAAC2C,IAAK;oBACjBiI,QAAQ,EAAGC,CAAC,IACVnI,sBAAsB,CAAEgC,IAAI,KAAM;sBAChC,GAAGA,IAAI;sBACP/B,IAAI,EAAEkI,CAAC,CAACC,MAAM,CAAC7L;oBACjB,CAAC,CAAC,CACH;oBACDuL,QAAQ;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxL,OAAA;kBAAKmL,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpL,OAAA;oBAAOmL,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3CxL,OAAA;oBACEsE,IAAI,EAAC,OAAO;oBACZ6G,SAAS,EAAC,cAAc;oBACxB5K,KAAK,EAAEe,IAAI,CAAC4C,KAAM;oBAClBgI,QAAQ,EAAGC,CAAC,IACVnI,sBAAsB,CAAEgC,IAAI,KAAM;sBAChC,GAAGA,IAAI;sBACP9B,KAAK,EAAEiI,CAAC,CAACC,MAAM,CAAC7L;oBAClB,CAAC,CAAC,CACH;oBACDuL,QAAQ;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxL,OAAA;kBAAKmL,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpL,OAAA;oBAAOmL,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnDxL,OAAA;oBACEsE,IAAI,EAAC,KAAK;oBACV6G,SAAS,EAAC,cAAc;oBACxB5K,KAAK,EAAEe,IAAI,CAAC6L,WAAY;oBACxBjB,QAAQ,EAAGC,CAAC,IACVnI,sBAAsB,CAAEgC,IAAI,KAAM;sBAChC,GAAGA,IAAI;sBACP7B,KAAK,EAAEgI,CAAC,CAACC,MAAM,CAAC7L;oBAClB,CAAC,CAAC,CACH;oBACDuL,QAAQ;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNxL,OAAA;kBAAKmL,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpL,OAAA;oBAAAoL,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BxL,OAAA;oBAAKmL,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCpL,OAAA;sBAAAoL,QAAA,gBACEpL,OAAA;wBAAAoL,QAAA,EAAQ;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAChJ,WAAW;oBAAA;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACJxL,OAAA;sBAAAoL,QAAA,gBACEpL,OAAA;wBAAAoL,QAAA,EAAQ;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC9I,YAAY;oBAAA;sBAAA2I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACJxL,OAAA;sBAAAoL,QAAA,gBACEpL,OAAA;wBAAAoL,QAAA,EAAQ;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EAAC,GAAG,EAC7B5I,cAAc,CAACrC,KAAK,GAAGuC,gBAAgB,CAACvC,KAAK;oBAAA;sBAAA8K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACJxL,OAAA;sBAAAqL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNxL,OAAA;sBAAAoL,QAAA,gBACEpL,OAAA;wBAAAoL,QAAA,EAAQ;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EAAC,GAAG,EAC9B7L,KAAK,CAACmN,cAAc,CAAC1D,mBAAmB,CAAC,CAAC,CAAC;oBAAA;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxL,OAAA;gBAAKmL,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpL,OAAA;kBAAAoL,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnBxL,OAAA;kBAAKyL,KAAK,EAAE;oBAAE2B,SAAS,EAAE,OAAO;oBAAEC,SAAS,EAAE;kBAAO,CAAE;kBAAAjC,QAAA,EACnDxJ,KAAK,CAACiI,GAAG,CAAE7B,IAAI;oBAAA,IAAAsF,eAAA;oBAAA,oBACdtN,OAAA;sBAAoBmL,SAAS,EAAC,WAAW;sBAAAC,QAAA,eACvCpL,OAAA;wBAAKmL,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC5BpL,OAAA;0BAAImL,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEpD,IAAI,CAAC/D;wBAAI;0BAAAoH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC3CxL,OAAA;0BAAGmL,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,IAAAkC,eAAA,GAC3BtF,IAAI,CAAC6E,QAAQ,cAAAS,eAAA,uBAAbA,eAAA,CAAerJ,IAAI,EAAC,IAAE,EAAC,GAAG,EAC1BtE,KAAK,CAACmN,cAAc,CAAC9E,IAAI,CAACkD,KAAK,CAAC,EAAC,cACpC;wBAAA;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACJxL,OAAA;0BAAGmL,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,GAAC,qBACnB,EAACpD,IAAI,CAACI,iBAAiB,EAAC,WACrC;wBAAA;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACJxL,OAAA;0BAAKmL,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxCpL,OAAA;4BAAOmL,SAAS,EAAC,MAAM;4BAAAC,QAAA,EAAC;0BAAS;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eACzCxL,OAAA;4BACEmL,SAAS,EAAC,4BAA4B;4BACtCM,KAAK,EAAE;8BAAEgB,KAAK,EAAE;4BAAO,CAAE;4BACzBlM,KAAK,EAAEgD,2BAA2B,CAACyE,IAAI,CAACtD,GAAG,CAAC,IAAI,CAAE;4BAClDwH,QAAQ,EAAGC,CAAC,IACVtG,wBAAwB,CACtBmC,IAAI,CAACtD,GAAG,EACR6I,QAAQ,CAACpB,CAAC,CAACC,MAAM,CAAC7L,KAAK,CACzB,CACD;4BAAA6K,QAAA,EAEAlL,KAAK,CAACC,IAAI,CACT;8BAAEC,MAAM,EAAE4H,IAAI,CAACI,iBAAiB,GAAG;4BAAE,CAAC,EACtC,CAAC/H,CAAC,EAAEC,CAAC,kBACHN,OAAA;8BAAgBO,KAAK,EAAED,CAAE;8BAAA8K,QAAA,EACtB9K;4BAAC,GADSA,CAAC;8BAAA+K,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAEN,CAEZ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAjCExD,IAAI,CAACtD,GAAG;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkCb,CAAC;kBAAA,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxL,OAAA;gBAAKmL,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpL,OAAA;kBAAAoL,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrBxL,OAAA;kBAAKyL,KAAK,EAAE;oBAAE2B,SAAS,EAAE,OAAO;oBAAEC,SAAS,EAAE;kBAAO,CAAE;kBAAAjC,QAAA,EACnD5J,SAAS,aAATA,SAAS,wBAAAJ,WAAA,GAATI,SAAS,CAAG,CAAC,CAAC,cAAAJ,WAAA,wBAAAC,oBAAA,GAAdD,WAAA,CAAgBoM,QAAQ,cAAAnM,oBAAA,uBAAxBA,oBAAA,CAA0BwI,GAAG,CAAE3D,OAAO,IAAK;oBAC1C,MAAMC,UAAU,GACd1C,8BAA8B,CAACyC,OAAO,CAACxB,GAAG,CAAC;oBAC7C,MAAMqB,QAAQ,GAAGpC,iBAAiB,CAACuC,OAAO,CAACxB,GAAG,CAAC,IAAI,CAAC;oBACpD,MAAMoE,aAAa,GACjBjF,oBAAoB,CAACqC,OAAO,CAACxB,GAAG,CAAC,IAAI,EAAE;oBAEzC,oBACE1E,OAAA;sBAAuBmL,SAAS,EAAC,WAAW;sBAAAC,QAAA,eAC1CpL,OAAA;wBAAKmL,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC5BpL,OAAA;0BAAKmL,SAAS,EAAC,YAAY;0BAAAC,QAAA,gBACzBpL,OAAA;4BACEmL,SAAS,EAAC,kBAAkB;4BAC5B7G,IAAI,EAAC,UAAU;4BACfmJ,OAAO,EAAE,CAAC,CAACtH,UAAW;4BACtB+F,QAAQ,EAAEA,CAAA,KAAMjG,sBAAsB,CAACC,OAAO;0BAAE;4BAAAmF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjD,CAAC,eACFxL,OAAA;4BAAOmL,SAAS,EAAC,kBAAkB;4BAAAC,QAAA,eACjCpL,OAAA;8BAAAoL,QAAA,EAASlF,OAAO,CAACjC;4BAAI;8BAAAoH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAS;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNxL,OAAA;0BAAGmL,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,GAC5BzL,KAAK,CAACmN,cAAc,CAAC5G,OAAO,CAACgF,KAAK,CAAC,EAAC,GACrC,EAAChF,OAAO,CAAC5B,IAAI;wBAAA;0BAAA+G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,EAEHrF,UAAU,iBACTnG,OAAA;0BAAKmL,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBpL,OAAA;4BAAKmL,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC7CpL,OAAA;8BAAOmL,SAAS,EAAC,MAAM;8BAAAC,QAAA,EAAC;4BAAS;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eACzCxL,OAAA;8BACEmL,SAAS,EAAC,4BAA4B;8BACtCM,KAAK,EAAE;gCAAEgB,KAAK,EAAE;8BAAQ,CAAE;8BAAArB,QAAA,gBAE1BpL,OAAA;gCACEmL,SAAS,EAAC,2BAA2B;gCACrC7G,IAAI,EAAC,QAAQ;gCACbuH,OAAO,EAAEA,CAAA,KACPtF,2BAA2B,CACzBL,OAAO,CAACxB,GAAG,EACXqB,QAAQ,GAAG,CACb,CACD;gCACD+F,QAAQ,EAAE/F,QAAQ,IAAI,CAAE;gCAAAqF,QAAA,EACzB;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACTxL,OAAA;gCACEsE,IAAI,EAAC,MAAM;gCACX6G,SAAS,EAAC,0BAA0B;gCACpC5K,KAAK,EAAEwF,QAAS;gCAChB2H,QAAQ;8BAAA;gCAAArC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eACFxL,OAAA;gCACEmL,SAAS,EAAC,2BAA2B;gCACrC7G,IAAI,EAAC,QAAQ;gCACbuH,OAAO,EAAEA,CAAA,KACPtF,2BAA2B,CACzBL,OAAO,CAACxB,GAAG,EACXqB,QAAQ,GAAG,CACb,CACD;gCAAAqF,QAAA,EACF;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAENxL,OAAA;4BAAKmL,SAAS,EAAC,MAAM;4BAAAC,QAAA,gBACnBpL,OAAA;8BAAOmL,SAAS,EAAC,kBAAkB;8BAAAC,QAAA,EAAC;4BAEpC;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eACRxL,OAAA;8BAAKmL,SAAS,EAAC,wBAAwB;8BAAAC,QAAA,EACpC3E,eAAe,CACd,IAAItE,IAAI,CAACK,WAAW,CAAC,EACrB,IAAIL,IAAI,CAACO,YAAY,CACvB,CAAC,CAACmH,GAAG,CAAE1C,IAAI,IAAK;gCACd,MAAME,OAAO,GAAGF,IAAI,CAAC/E,WAAW,CAAC,CAAC;gCAClC,MAAMuL,cAAc,GAClB7E,aAAa,CAACxB,QAAQ,CAACD,OAAO,CAAC;gCAEjC,oBACErH,OAAA;kCAEEsE,IAAI,EAAC,QAAQ;kCACb6G,SAAS,EAAE,cACTwC,cAAc,GACV,aAAa,GACb,qBAAqB,EACxB;kCACH9B,OAAO,EAAEA,CAAA,KACP3E,mBAAmB,CACjBhB,OAAO,CAACxB,GAAG,EACXyC,IACF,CACD;kCAAAiE,QAAA,GAEAjE,IAAI,CAACF,OAAO,CAAC,CAAC,EAAC,GAAC,EAACE,IAAI,CAACyG,QAAQ,CAAC,CAAC,GAAG,CAAC;gCAAA,GAdhCvG,OAAO;kCAAAgE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAeN,CAAC;8BAEb,CAAC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC,GAjGEtF,OAAO,CAACxB,GAAG;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkGhB,CAAC;kBAEV,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxL,OAAA;YAAKmL,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BpL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb6G,SAAS,EAAC,mBAAmB;cAC7BU,OAAO,EAAEA,CAAA,KAAMvI,uBAAuB,CAAC,KAAK,CAAE;cAAA8H,QAAA,EAC/C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb6G,SAAS,EAAC,iBAAiB;cAC3BU,OAAO,EAAEpE,uBAAwB;cAAA2D,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELnI,oBAAoB,iBAAIrD,OAAA;MAAKmL,SAAS,EAAC;IAA0B;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtE,CAAC;AAEV,CAAC;AAACrK,EAAA,CAp7BID,iBAAiB;EAAA,QACRxB,cAAc,EA0CVD,cAAc;AAAA;AAAAoO,EAAA,GA3C3B3M,iBAAiB;AAs7BvB,eAAeA,iBAAiB;AAAC,IAAA2M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}