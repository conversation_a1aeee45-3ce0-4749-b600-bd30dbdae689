{"ast": null, "code": "export const FETCH_DASHBOARD_METRICS = \"FETCH_DASHBOARD_METRICS\";\nexport const FETCH_DASHBOARD_METRICS_SUCCESS = \"FETCH_DASHBOARD_METRICS_SUCCESS\";\nexport const FETCH_DASHBOARD_METRICS_FAILURE = \"FETCH_DASHBOARD_METRICS_FAILURE\";\nexport const fetchDashboardMetrics = (period = \"month\") => ({\n  type: FETCH_DASHBOARD_METRICS,\n  payload: {\n    period\n  }\n});\nexport const fetchDashboardMetricsSuccess = data => ({\n  type: FETCH_DASHBOARD_METRICS_SUCCESS,\n  payload: data\n});\nexport const fetchDashboardMetricsFailure = error => ({\n  type: FETCH_DASHBOARD_METRICS_FAILURE,\n  payload: error\n});", "map": {"version": 3, "names": ["FETCH_DASHBOARD_METRICS", "FETCH_DASHBOARD_METRICS_SUCCESS", "FETCH_DASHBOARD_METRICS_FAILURE", "fetchDashboardMetrics", "period", "type", "payload", "fetchDashboardMetricsSuccess", "data", "fetchDashboardMetricsFailure", "error"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/dashboard/actions.js"], "sourcesContent": ["export const FETCH_DASHBOARD_METRICS = \"FETCH_DASHBOARD_METRICS\";\r\nexport const FETCH_DASHBOARD_METRICS_SUCCESS = \"FETCH_DASHBOARD_METRICS_SUCCESS\";\r\nexport const FETCH_DASHBOARD_METRICS_FAILURE = \"FETCH_DASHBOARD_METRICS_FAILURE\";\r\n\r\nexport const fetchDashboardMetrics = (period = \"month\") => ({\r\n  type: FETCH_DASHBOARD_METRICS,\r\n  payload: { period }\r\n});\r\n\r\nexport const fetchDashboardMetricsSuccess = (data) => ({\r\n  type: FETCH_DASHBOARD_METRICS_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const fetchDashboardMetricsFailure = (error) => ({\r\n  type: FETCH_DASHBOARD_METRICS_FAILURE,\r\n  payload: error\r\n}); "], "mappings": "AAAA,OAAO,MAAMA,uBAAuB,GAAG,yBAAyB;AAChE,OAAO,MAAMC,+BAA+B,GAAG,iCAAiC;AAChF,OAAO,MAAMC,+BAA+B,GAAG,iCAAiC;AAEhF,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,MAAM,GAAG,OAAO,MAAM;EAC1DC,IAAI,EAAEL,uBAAuB;EAC7BM,OAAO,EAAE;IAAEF;EAAO;AACpB,CAAC,CAAC;AAEF,OAAO,MAAMG,4BAA4B,GAAIC,IAAI,KAAM;EACrDH,IAAI,EAAEJ,+BAA+B;EACrCK,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,4BAA4B,GAAIC,KAAK,KAAM;EACtDL,IAAI,EAAEH,+BAA+B;EACrCI,OAAO,EAAEI;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}