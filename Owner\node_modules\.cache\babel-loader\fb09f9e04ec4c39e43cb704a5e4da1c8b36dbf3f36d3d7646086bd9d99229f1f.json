{"ast": null, "code": "import { FETCH_DASHBOARD_METRICS, FETCH_DASHBOARD_METRICS_SUCCESS, FETCH_DASHBOARD_METRICS_FAILURE } from \"./actions\";\nconst initialState = {\n  loading: false,\n  error: null,\n  data: {\n    totalRevenue: 0,\n    revpar: 0,\n    adr: 0,\n    profit: 0,\n    occupancyRate: 0,\n    averageRating: 0,\n    returnRate: 0,\n    revenueData: [],\n    customerSegmentData: {\n      labels: [],\n      datasets: [{\n        data: [],\n        backgroundColor: []\n      }]\n    },\n    recentBookings: []\n  }\n};\nconst dashboardReducer = (state = initialState, action) => {\n  switch (action.type) {\n    case FETCH_DASHBOARD_METRICS:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case FETCH_DASHBOARD_METRICS_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        data: action.payload,\n        error: null\n      };\n    case FETCH_DASHBOARD_METRICS_FAILURE:\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport default dashboardReducer;", "map": {"version": 3, "names": ["FETCH_DASHBOARD_METRICS", "FETCH_DASHBOARD_METRICS_SUCCESS", "FETCH_DASHBOARD_METRICS_FAILURE", "initialState", "loading", "error", "data", "totalRevenue", "revpar", "adr", "profit", "occupancyRate", "averageRating", "returnRate", "revenueData", "customerSegmentData", "labels", "datasets", "backgroundColor", "recentBookings", "dashboardReducer", "state", "action", "type", "payload"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/dashboard/reducer.js"], "sourcesContent": ["import {\r\n  FETCH_DASHBOARD_METRICS,\r\n  FETCH_DASHBOARD_METRICS_SUCCESS,\r\n  FETCH_DASHBOARD_METRICS_FAILURE,\r\n} from \"./actions\";\r\n\r\nconst initialState = {\r\n  loading: false,\r\n  error: null,\r\n  data: {\r\n    totalRevenue: 0,\r\n    revpar: 0,\r\n    adr: 0,\r\n    profit: 0,\r\n    occupancyRate: 0,\r\n    averageRating: 0,\r\n    returnRate: 0,\r\n    revenueData: [],\r\n    customerSegmentData: {\r\n      labels: [],\r\n      datasets: [{ data: [], backgroundColor: [] }]\r\n    },\r\n    recentBookings: []\r\n  }\r\n};\r\n\r\nconst dashboardReducer = (state = initialState, action) => {\r\n  switch (action.type) {\r\n    case FETCH_DASHBOARD_METRICS:\r\n      return {\r\n        ...state,\r\n        loading: true,\r\n        error: null\r\n      };\r\n    \r\n    case FETCH_DASHBOARD_METRICS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        data: action.payload,\r\n        error: null\r\n      };\r\n    \r\n    case FETCH_DASHBOARD_METRICS_FAILURE:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: action.payload\r\n      };\r\n    \r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default dashboardReducer; "], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,+BAA+B,EAC/BC,+BAA+B,QAC1B,WAAW;AAElB,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;IACJC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,EAAE;IACfC,mBAAmB,EAAE;MACnBC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;QAAEX,IAAI,EAAE,EAAE;QAAEY,eAAe,EAAE;MAAG,CAAC;IAC9C,CAAC;IACDC,cAAc,EAAE;EAClB;AACF,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,GAAGlB,YAAY,EAAEmB,MAAM,KAAK;EACzD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKvB,uBAAuB;MAC1B,OAAO;QACL,GAAGqB,KAAK;QACRjB,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKJ,+BAA+B;MAClC,OAAO;QACL,GAAGoB,KAAK;QACRjB,OAAO,EAAE,KAAK;QACdE,IAAI,EAAEgB,MAAM,CAACE,OAAO;QACpBnB,KAAK,EAAE;MACT,CAAC;IAEH,KAAKH,+BAA+B;MAClC,OAAO;QACL,GAAGmB,KAAK;QACRjB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEiB,MAAM,CAACE;MAChB,CAAC;IAEH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;AAED,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}