{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\WaitPendingPage.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Container, Button } from 'react-bootstrap';\nimport { FaClock } from 'react-icons/fa';\nimport '../css/WaitPendingPage.css';\nimport * as Routers from \"../utils/Routes\";\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WaitPendingPage = () => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"pending-container1\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pending-content1 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"clock-icon1\",\n        children: /*#__PURE__*/_jsxDEV(FaClock, {\n          size: 250\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"pending-title1\",\n        style: {\n          fontSize: 60\n        },\n        children: \"Pending Approval\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"pending-subtitle1\",\n        style: {\n          fontSize: 40\n        },\n        children: \"Your hotel host account is under review\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pending-details1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label1\",\n            children: \"Status: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value1\",\n            style: {\n              color: 'orange'\n            },\n            children: \"Waiting For Pending From Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label1\",\n            children: \"Estimated time: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value1\",\n            children: \"24-48 hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pending-actions1\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          style: {\n            width: '140px'\n          },\n          onClick: () => {\n            navigate(Routers.LoginHotelPage);\n          },\n          children: \"Login Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          style: {\n            width: '140px'\n          },\n          children: \"Contact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_s(WaitPendingPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = WaitPendingPage;\nexport default WaitPendingPage;\nvar _c;\n$RefreshReg$(_c, \"WaitPendingPage\");", "map": {"version": 3, "names": ["React", "Container", "<PERSON><PERSON>", "FaClock", "Routers", "useNavigate", "jsxDEV", "_jsxDEV", "WaitPendingPage", "_s", "navigate", "fluid", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "variant", "width", "onClick", "LoginHotelPage", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/WaitPendingPage.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Container, Button } from 'react-bootstrap';\r\nimport { FaClock } from 'react-icons/fa';\r\nimport '../css/WaitPendingPage.css';\r\nimport * as Routers from \"../utils/Routes\";\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst WaitPendingPage = () => {\r\n    const navigate= useNavigate();\r\n    return (\r\n    <Container fluid className=\"pending-container1\">\r\n      <div className=\"pending-content1 text-center\">\r\n        <div className=\"clock-icon1\">\r\n          <FaClock size={250} />\r\n        </div>\r\n        \r\n        <h1 className=\"pending-title1\" style={{fontSize: 60}}>Pending Approval</h1>\r\n        <p className=\"pending-subtitle1\" style={{fontSize: 40}}>Your hotel host account is under review</p>\r\n        \r\n        <div className=\"pending-details1\">\r\n          <div className=\"detail-item1\">\r\n            <span className=\"detail-label1\">Status: </span>\r\n            <span className=\"detail-value1\" style={{color: 'orange'}}>Waiting For Pending From Admin</span>\r\n          </div>\r\n          \r\n          <div className=\"detail-item1\">\r\n            <span className=\"detail-label1\">Estimated time: </span>\r\n            <span className=\"detail-value1\">24-48 hours</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"pending-actions1\">\r\n          <Button variant=\"primary\" style={{width: '140px'}}\r\n            onClick={() => {\r\n                navigate(Routers.LoginHotelPage)\r\n            }}\r\n          >\r\n            Login Page\r\n          </Button>\r\n          <Button variant=\"secondary\" style={{width: '140px'}}>\r\n            Contact\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default WaitPendingPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,MAAM,QAAQ,iBAAiB;AACnD,SAASC,OAAO,QAAQ,gBAAgB;AACxC,OAAO,4BAA4B;AACnC,OAAO,KAAKC,OAAO,MAAM,iBAAiB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAEL,WAAW,CAAC,CAAC;EAC7B,oBACAE,OAAA,CAACN,SAAS;IAACU,KAAK;IAACC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eAC7CN,OAAA;MAAKK,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CN,OAAA;QAAKK,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BN,OAAA,CAACJ,OAAO;UAACW,IAAI,EAAE;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAENX,OAAA;QAAIK,SAAS,EAAC,gBAAgB;QAACO,KAAK,EAAE;UAACC,QAAQ,EAAE;QAAE,CAAE;QAAAP,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3EX,OAAA;QAAGK,SAAS,EAAC,mBAAmB;QAACO,KAAK,EAAE;UAACC,QAAQ,EAAE;QAAE,CAAE;QAAAP,QAAA,EAAC;MAAuC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEnGX,OAAA;QAAKK,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BN,OAAA;UAAKK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BN,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAACO,KAAK,EAAE;cAACE,KAAK,EAAE;YAAQ,CAAE;YAAAR,QAAA,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAENX,OAAA;UAAKK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BN,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvDX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKK,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BN,OAAA,CAACL,MAAM;UAACoB,OAAO,EAAC,SAAS;UAACH,KAAK,EAAE;YAACI,KAAK,EAAE;UAAO,CAAE;UAChDC,OAAO,EAAEA,CAAA,KAAM;YACXd,QAAQ,CAACN,OAAO,CAACqB,cAAc,CAAC;UACpC,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTX,OAAA,CAACL,MAAM;UAACoB,OAAO,EAAC,WAAW;UAACH,KAAK,EAAE;YAACI,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAErD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACT,EAAA,CAvCID,eAAe;EAAA,QACDH,WAAW;AAAA;AAAAqB,EAAA,GADzBlB,eAAe;AAyCrB,eAAeA,eAAe;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}