import { useState, useEffect } from "react";
import { Line, Bar, Pie, Doughnut } from "react-chartjs-2";
import { useDispatch, useSelector } from "react-redux";
import AdminDashboardActions from "../../redux/adminDashboard/actions";

const DashboardPage = ({setActiveTab}) => {
  const dispatch = useDispatch();
  const { data: dashboardData, loading, error } = useSelector(state => state.AdminDashboard);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Fetch dashboard data on component mount and when period changes
  useEffect(() => {
    dispatch({
      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,
      payload: {
        params: { period: selectedPeriod },
        onSuccess: (data) => {
          console.log('Dashboard data loaded successfully:', data);
        },
        onFailed: (error) => {
          console.error('Failed to load dashboard data:', error);
        }
      }
    });
  }, [dispatch, selectedPeriod]);

  // Handle period change
  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
  };

  // Format revenue for display
  const formatRevenue = (revenue) => {
    if (revenue >= 1000000) {
      return (revenue / 1000000).toFixed(1) + 'M';
    } else if (revenue >= 1000) {
      return (revenue / 1000).toFixed(1) + 'K';
    }
    return revenue?.toLocaleString() || '0';
  };

  // Loading state
  if (loading) {
    return (
      <div className="dashboard-content">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="dashboard-content">
        <div className="alert alert-danger" role="alert">
          <h4 className="alert-heading">Lỗi!</h4>
          <p>{error}</p>
          <button
            className="btn btn-outline-danger"
            onClick={() => handlePeriodChange(selectedPeriod)}
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }



  // Lấy màu cho trạng thái
  const getStatusColor = (status) => {
    switch (status) {
      case "Đã thanh toán":
      case "Hoạt động":
        return "success";
      case "Đang xử lý":
      case "Đang xem xét":
      case "Đang chờ":
        return "warning";
      case "Tạm khóa":
      case "Chưa xử lý":
        return "danger";
      default:
        return "secondary";
    }
  };

  // Lấy màu cho mức độ nghiêm trọng
  const getSeverityColor = (severity) => {
    switch (severity) {
      case "Cao":
        return "danger";
      case "Trung bình":
        return "warning";
      case "Thấp":
        return "info";
      default:
        return "secondary";
    }
  };
  return (
    <div className="dashboard-content">
      <div className="page-header">
        <h1>Tổng quan hệ thống</h1>
        <div className="page-actions">
          <div className="date-filter">
            <select
              className="form-select"
              value={selectedPeriod}
              onChange={(e) => handlePeriodChange(e.target.value)}
            >
              <option value="day">Hôm nay</option>
              <option value="week">Tuần này</option>
              <option value="month">Tháng này</option>
              <option value="year">Năm nay</option>
            </select>
          </div>
          <button
            className="btn btn-outline-secondary me-2"
            onClick={() => handlePeriodChange(selectedPeriod)}
            disabled={loading}
          >
            <i className="bi bi-arrow-clockwise"></i> Làm mới
          </button>
          <button className="btn btn-primary">
            <i className="bi bi-download"></i> Xuất báo cáo
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-cards">
        <div className="stat-card">
          <div className="stat-card-content">
            <h3>{dashboardData.totalHotels || 0}</h3>
            <p>Tổng số khách sạn</p>
          </div>
          <div className="stat-card-icon hotels">
            <i className="bi bi-building"></i>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card-content">
            <h3>{dashboardData.activeHotels || 0}</h3>
            <p>Khách sạn hoạt động</p>
          </div>
          <div className="stat-card-icon active">
            <i className="bi bi-check-circle"></i>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card-content">
            <h3>{dashboardData.pendingApprovals || 0}</h3>
            <p>Chờ phê duyệt</p>
          </div>
          <div className="stat-card-icon pending">
            <i className="bi bi-hourglass-split"></i>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card-content">
            <h3>{dashboardData.totalCustomers || 0}</h3>
            <p>Tổng số khách hàng</p>
          </div>
          <div className="stat-card-icon customers">
            <i className="bi bi-people"></i>
          </div>
        </div>
      </div>

      {/* Revenue Chart */}
      <div className="chart-container">
        <div className="chart-header">
          <h2>Doanh thu hệ thống</h2>
          <div className="chart-actions">
            <div className="btn-group">
              <button className="btn btn-sm btn-outline-secondary">Ngày</button>
              <button className="btn btn-sm btn-outline-secondary">Tuần</button>
              <button className="btn btn-sm btn-primary">Tháng</button>
              <button className="btn btn-sm btn-outline-secondary">Năm</button>
            </div>
          </div>
        </div>
        <div className="chart-body">
          <Line
            data={dashboardData.revenueData || { labels: [], datasets: [] }}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: "top",
                },
              },
              scales: {
                y: {
                  beginAtZero: false,
                  grid: {
                    drawBorder: false,
                  },
                  ticks: {
                    callback: (value) => formatRevenue(value),
                  },
                },
                x: {
                  grid: {
                    display: false,
                  },
                },
              },
            }}
          />
        </div>
      </div>

      {/* Distribution Charts */}
      <div className="charts-row">
        <div className="chart-container half">
          <div className="chart-header">
            <h2>Phân bố khách sạn theo khu vực</h2>
          </div>
          <div className="chart-body">
            <Doughnut
              data={dashboardData.hotelDistributionData || { labels: [], datasets: [{ data: [], backgroundColor: [] }] }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: "bottom",
                  },
                },
                cutout: "70%",
              }}
            />
          </div>
        </div>
        <div className="chart-container half">
          <div className="chart-header">
            <h2>Phân loại khách sạn</h2>
          </div>
          <div className="chart-body">
            <Pie
              data={dashboardData.hotelCategoryData || { labels: [], datasets: [{ data: [], backgroundColor: [] }] }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: "bottom",
                  },
                },
              }}
            />
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="recent-activities">
        <div className="activity-container">
          <div className="activity-header">
            <h2>Yêu cầu phê duyệt gần đây</h2>
            <a
              href="#"
              onClick={() => setActiveTab("approvals")}
              className="view-all"
            >
              Xem tất cả
            </a>
          </div>
          <div className="activity-body">
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Tên khách sạn</th>
                    <th>Chủ sở hữu</th>
                    <th>Địa điểm</th>
                    <th>Ngày gửi</th>
                    <th>Trạng thái</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {(dashboardData.recentApprovals || []).slice(0, 3).map((approval) => (
                    <tr key={approval.id}>
                      <td>{approval.id}</td>
                      <td>{approval.hotelName}</td>
                      <td>{approval.owner}</td>
                      <td>{approval.location}</td>
                      <td>{approval.submittedDate}</td>
                      <td>
                        <span
                          className={`badge bg-${getStatusColor(
                            approval.status
                          )}`}
                        >
                          {approval.status}
                        </span>
                      </td>
                      <td>
                        <div className="action-buttons">
                          <button className="btn btn-sm btn-primary">
                            <i className="bi bi-eye"></i>
                          </button>
                          <button className="btn btn-sm btn-success">
                            <i className="bi bi-check-lg"></i>
                          </button>
                          <button className="btn btn-sm btn-danger">
                            <i className="bi bi-x-lg"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div className="activity-container">
          <div className="activity-header">
            <h2>Báo cáo vi phạm gần đây</h2>
            <a
              href="#"
              onClick={() => setActiveTab("reports")}
              className="view-all"
            >
              Xem tất cả
            </a>
          </div>
          <div className="activity-body">
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Khách hàng</th>
                    <th>Khách sạn</th>
                    <th>Loại báo cáo</th>
                    <th>Mức độ</th>
                    <th>Trạng thái</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {(dashboardData.recentReports || []).slice(0, 3).map((report) => (
                    <tr key={report.id}>
                      <td>{report.id}</td>
                      <td>{report.customerName}</td>
                      <td>{report.hotelName}</td>
                      <td>{report.reportType}</td>
                      <td>
                        <span
                          className={`badge bg-${getSeverityColor(
                            report.severity
                          )}`}
                        >
                          {report.severity}
                        </span>
                      </td>
                      <td>
                        <span
                          className={`badge bg-${getStatusColor(
                            report.status
                          )}`}
                        >
                          {report.status}
                        </span>
                      </td>
                      <td>
                        <div className="action-buttons">
                          <button className="btn btn-sm btn-primary">
                            <i className="bi bi-eye"></i>
                          </button>
                          <button className="btn btn-sm btn-warning">
                            <i className="bi bi-pencil"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
