{"ast": null, "code": "import { all } from 'redux-saga/effects';\nimport AuthSaga from './auth/saga';\nimport HotelSaga from './hotel/saga';\nimport HotelservicesSaga from './Hotelservices/saga';\nimport ReportFeedbackSaga from \"./reportedFeedback/saga\";\nimport ReservationSaga from \"./reservation/saga\";\nimport FeedbackSaga from \"./feedback/saga\";\nimport MonthlyPaymentSaga from \"./monthlyPayment/saga\";\nimport MessageSaga from \"./message/saga\";\nimport DashboardSaga from \"./dashboard/saga\";\nimport RoomSaga from \"./room/saga\";\nexport default function* rootSaga() {\n  yield all([AuthSaga(), FeedbackSaga(), HotelSaga(), HotelservicesSaga(), ReportFeedbackSaga(), ReservationSaga(), MonthlyPaymentSaga(), MessageSaga(), DashboardSaga(), RoomSaga()]);\n}", "map": {"version": 3, "names": ["all", "Auth<PERSON>aga", "HotelSaga", "HotelservicesSaga", "ReportFeedbackSaga", "ReservationSaga", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MonthlyPaymentSaga", "MessageSaga", "DashboardSaga", "RoomSaga", "rootSaga"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/root-saga.js"], "sourcesContent": ["import 'regenerator-runtime/runtime';\r\nimport {all} from 'redux-saga/effects';\r\nimport AuthSaga from './auth/saga'; \r\nimport HotelSaga from './hotel/saga'; \r\nimport HotelservicesSaga from './Hotelservices/saga'; \r\nimport ReportFeedbackSaga from \"./reportedFeedback/saga\";\r\nimport ReservationSaga from \"./reservation/saga\";\r\nimport FeedbackSaga from \"./feedback/saga\";\r\nimport MonthlyPaymentSaga from \"./monthlyPayment/saga\";\r\nimport MessageSaga from \"./message/saga\";\r\nimport DashboardSaga from \"./dashboard/saga\";\r\nimport RoomSaga from \"./room/saga\";\r\n\r\nexport default function* rootSaga() {\r\n  yield all([\r\n    AuthSaga(),\r\n    FeedbackSaga(),\r\n    HotelSaga(),\r\n    HotelservicesSaga(),\r\n    ReportFeedbackSaga(),\r\n    ReservationSaga(),\r\n    MonthlyPaymentSaga(),\r\n    MessageSaga(),\r\n    DashboardSaga(),\r\n    RoomSaga(),\r\n  ]);\r\n}\r\n"], "mappings": "AACA,SAAQA,GAAG,QAAO,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,QAAQ,MAAM,aAAa;AAElC,eAAe,UAAUC,QAAQA,CAAA,EAAG;EAClC,MAAMX,GAAG,CAAC,CACRC,QAAQ,CAAC,CAAC,EACVK,YAAY,CAAC,CAAC,EACdJ,SAAS,CAAC,CAAC,EACXC,iBAAiB,CAAC,CAAC,EACnBC,kBAAkB,CAAC,CAAC,EACpBC,eAAe,CAAC,CAAC,EACjBE,kBAAkB,CAAC,CAAC,EACpBC,WAAW,CAAC,CAAC,EACbC,aAAa,CAAC,CAAC,EACfC,QAAQ,CAAC,CAAC,CACX,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}