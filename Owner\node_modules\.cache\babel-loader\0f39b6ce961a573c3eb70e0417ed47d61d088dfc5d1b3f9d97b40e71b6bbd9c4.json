{"ast": null, "code": "import { call, put, takeLatest } from \"redux-saga/effects\";\nimport { FETCH_DASHBOARD_METRICS, fetchDashboardMetricsSuccess, fetchDashboardMetricsFailure } from \"./actions\";\nimport DashboardFactories from \"./factories\";\nfunction* fetchDashboardMetricsSaga(action) {\n  try {\n    const {\n      period\n    } = action.payload;\n    const response = yield call(DashboardFactories.fetchDashboardMetrics, {\n      period\n    });\n    if (response.data.error === false) {\n      yield put(fetchDashboardMetricsSuccess(response.data.data));\n    } else {\n      yield put(fetchDashboardMetricsFailure(response.data.message));\n    }\n  } catch (error) {\n    yield put(fetchDashboardMetricsFailure(error.message));\n  }\n}\nexport default function* dashboardSaga() {\n  yield takeLatest(FETCH_DASHBOARD_METRICS, fetchDashboardMetricsSaga);\n}", "map": {"version": 3, "names": ["call", "put", "take<PERSON><PERSON>t", "FETCH_DASHBOARD_METRICS", "fetchDashboardMetricsSuccess", "fetchDashboardMetricsFailure", "DashboardFactories", "fetchDashboardMetricsSaga", "action", "period", "payload", "response", "fetchDashboardMetrics", "data", "error", "message", "dashboardSaga"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/dashboard/saga.js"], "sourcesContent": ["import { call, put, takeLatest } from \"redux-saga/effects\";\r\nimport {\r\n  FETCH_DASHBOARD_METRICS,\r\n  fetchDashboardMetricsSuccess,\r\n  fetchDashboardMetricsFailure,\r\n} from \"./actions\";\r\nimport DashboardFactories from \"./factories\";\r\n\r\nfunction* fetchDashboardMetricsSaga(action) {\r\n  try {\r\n    const { period } = action.payload;\r\n    const response = yield call(DashboardFactories.fetchDashboardMetrics, { period });\r\n    \r\n    if (response.data.error === false) {\r\n      yield put(fetchDashboardMetricsSuccess(response.data.data));\r\n    } else {\r\n      yield put(fetchDashboardMetricsFailure(response.data.message));\r\n    }\r\n  } catch (error) {\r\n    yield put(fetchDashboardMetricsFailure(error.message));\r\n  }\r\n}\r\n\r\nexport default function* dashboardSaga() {\r\n  yield takeLatest(FETCH_DASHBOARD_METRICS, fetchDashboardMetricsSaga);\r\n} "], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAQ,oBAAoB;AAC1D,SACEC,uBAAuB,EACvBC,4BAA4B,EAC5BC,4BAA4B,QACvB,WAAW;AAClB,OAAOC,kBAAkB,MAAM,aAAa;AAE5C,UAAUC,yBAAyBA,CAACC,MAAM,EAAE;EAC1C,IAAI;IACF,MAAM;MAAEC;IAAO,CAAC,GAAGD,MAAM,CAACE,OAAO;IACjC,MAAMC,QAAQ,GAAG,MAAMX,IAAI,CAACM,kBAAkB,CAACM,qBAAqB,EAAE;MAAEH;IAAO,CAAC,CAAC;IAEjF,IAAIE,QAAQ,CAACE,IAAI,CAACC,KAAK,KAAK,KAAK,EAAE;MACjC,MAAMb,GAAG,CAACG,4BAA4B,CAACO,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAAC;IAC7D,CAAC,MAAM;MACL,MAAMZ,GAAG,CAACI,4BAA4B,CAACM,QAAQ,CAACE,IAAI,CAACE,OAAO,CAAC,CAAC;IAChE;EACF,CAAC,CAAC,OAAOD,KAAK,EAAE;IACd,MAAMb,GAAG,CAACI,4BAA4B,CAACS,KAAK,CAACC,OAAO,CAAC,CAAC;EACxD;AACF;AAEA,eAAe,UAAUC,aAAaA,CAAA,EAAG;EACvC,MAAMd,UAAU,CAACC,uBAAuB,EAAEI,yBAAyB,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}