{"ast": null, "code": "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport { useState, useCallback } from 'react';\nvar _excluded = [\"defaultInputValue\", \"defaultMenuIsOpen\", \"defaultValue\", \"inputValue\", \"menuIsOpen\", \"onChange\", \"onInputChange\", \"onMenuClose\", \"onMenuOpen\", \"value\"];\nfunction useStateManager(_ref) {\n  var _ref$defaultInputValu = _ref.defaultInputValue,\n    defaultInputValue = _ref$defaultInputValu === void 0 ? '' : _ref$defaultInputValu,\n    _ref$defaultMenuIsOpe = _ref.defaultMenuIsOpen,\n    defaultMenuIsOpen = _ref$defaultMenuIsOpe === void 0 ? false : _ref$defaultMenuIsOpe,\n    _ref$defaultValue = _ref.defaultValue,\n    defaultValue = _ref$defaultValue === void 0 ? null : _ref$defaultValue,\n    propsInputValue = _ref.inputValue,\n    propsMenuIsOpen = _ref.menuIsOpen,\n    propsOnChange = _ref.onChange,\n    propsOnInputChange = _ref.onInputChange,\n    propsOnMenuClose = _ref.onMenuClose,\n    propsOnMenuOpen = _ref.onMenuOpen,\n    propsValue = _ref.value,\n    restSelectProps = _objectWithoutProperties(_ref, _excluded);\n  var _useState = useState(propsInputValue !== undefined ? propsInputValue : defaultInputValue),\n    _useState2 = _slicedToArray(_useState, 2),\n    stateInputValue = _useState2[0],\n    setStateInputValue = _useState2[1];\n  var _useState3 = useState(propsMenuIsOpen !== undefined ? propsMenuIsOpen : defaultMenuIsOpen),\n    _useState4 = _slicedToArray(_useState3, 2),\n    stateMenuIsOpen = _useState4[0],\n    setStateMenuIsOpen = _useState4[1];\n  var _useState5 = useState(propsValue !== undefined ? propsValue : defaultValue),\n    _useState6 = _slicedToArray(_useState5, 2),\n    stateValue = _useState6[0],\n    setStateValue = _useState6[1];\n  var onChange = useCallback(function (value, actionMeta) {\n    if (typeof propsOnChange === 'function') {\n      propsOnChange(value, actionMeta);\n    }\n    setStateValue(value);\n  }, [propsOnChange]);\n  var onInputChange = useCallback(function (value, actionMeta) {\n    var newValue;\n    if (typeof propsOnInputChange === 'function') {\n      newValue = propsOnInputChange(value, actionMeta);\n    }\n    setStateInputValue(newValue !== undefined ? newValue : value);\n  }, [propsOnInputChange]);\n  var onMenuOpen = useCallback(function () {\n    if (typeof propsOnMenuOpen === 'function') {\n      propsOnMenuOpen();\n    }\n    setStateMenuIsOpen(true);\n  }, [propsOnMenuOpen]);\n  var onMenuClose = useCallback(function () {\n    if (typeof propsOnMenuClose === 'function') {\n      propsOnMenuClose();\n    }\n    setStateMenuIsOpen(false);\n  }, [propsOnMenuClose]);\n  var inputValue = propsInputValue !== undefined ? propsInputValue : stateInputValue;\n  var menuIsOpen = propsMenuIsOpen !== undefined ? propsMenuIsOpen : stateMenuIsOpen;\n  var value = propsValue !== undefined ? propsValue : stateValue;\n  return _objectSpread(_objectSpread({}, restSelectProps), {}, {\n    inputValue: inputValue,\n    menuIsOpen: menuIsOpen,\n    onChange: onChange,\n    onInputChange: onInputChange,\n    onMenuClose: onMenuClose,\n    onMenuOpen: onMenuOpen,\n    value: value\n  });\n}\nexport { useStateManager as u };", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "_objectWithoutProperties", "useState", "useCallback", "_excluded", "useStateManager", "_ref", "_ref$defaultInputValu", "defaultInputValue", "_ref$defaultMenuIsOpe", "defaultMenuIsOpen", "_ref$defaultValue", "defaultValue", "propsInputValue", "inputValue", "propsMenuIsOpen", "menuIsOpen", "props<PERSON>n<PERSON><PERSON><PERSON>", "onChange", "propsOnInputChange", "onInputChange", "propsOnMenuClose", "onMenuClose", "propsOnMenuOpen", "onMenuOpen", "props<PERSON><PERSON><PERSON>", "value", "restSelectProps", "_useState", "undefined", "_useState2", "stateInputValue", "setStateInputValue", "_useState3", "_useState4", "stateMenuIsOpen", "setStateMenuIsOpen", "_useState5", "_useState6", "stateValue", "setStateValue", "actionMeta", "newValue", "u"], "sources": ["E:/WDP301_UROOM/Owner/node_modules/react-select/dist/useStateManager-7e1e8489.esm.js"], "sourcesContent": ["import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport { useState, useCallback } from 'react';\n\nvar _excluded = [\"defaultInputValue\", \"defaultMenuIsOpen\", \"defaultValue\", \"inputValue\", \"menuIsOpen\", \"onChange\", \"onInputChange\", \"onMenuClose\", \"onMenuOpen\", \"value\"];\nfunction useStateManager(_ref) {\n  var _ref$defaultInputValu = _ref.defaultInputValue,\n    defaultInputValue = _ref$defaultInputValu === void 0 ? '' : _ref$defaultInputValu,\n    _ref$defaultMenuIsOpe = _ref.defaultMenuIsOpen,\n    defaultMenuIsOpen = _ref$defaultMenuIsOpe === void 0 ? false : _ref$defaultMenuIsOpe,\n    _ref$defaultValue = _ref.defaultValue,\n    defaultValue = _ref$defaultValue === void 0 ? null : _ref$defaultValue,\n    propsInputValue = _ref.inputValue,\n    propsMenuIsOpen = _ref.menuIsOpen,\n    propsOnChange = _ref.onChange,\n    propsOnInputChange = _ref.onInputChange,\n    propsOnMenuClose = _ref.onMenuClose,\n    propsOnMenuOpen = _ref.onMenuOpen,\n    propsValue = _ref.value,\n    restSelectProps = _objectWithoutProperties(_ref, _excluded);\n  var _useState = useState(propsInputValue !== undefined ? propsInputValue : defaultInputValue),\n    _useState2 = _slicedToArray(_useState, 2),\n    stateInputValue = _useState2[0],\n    setStateInputValue = _useState2[1];\n  var _useState3 = useState(propsMenuIsOpen !== undefined ? propsMenuIsOpen : defaultMenuIsOpen),\n    _useState4 = _slicedToArray(_useState3, 2),\n    stateMenuIsOpen = _useState4[0],\n    setStateMenuIsOpen = _useState4[1];\n  var _useState5 = useState(propsValue !== undefined ? propsValue : defaultValue),\n    _useState6 = _slicedToArray(_useState5, 2),\n    stateValue = _useState6[0],\n    setStateValue = _useState6[1];\n  var onChange = useCallback(function (value, actionMeta) {\n    if (typeof propsOnChange === 'function') {\n      propsOnChange(value, actionMeta);\n    }\n    setStateValue(value);\n  }, [propsOnChange]);\n  var onInputChange = useCallback(function (value, actionMeta) {\n    var newValue;\n    if (typeof propsOnInputChange === 'function') {\n      newValue = propsOnInputChange(value, actionMeta);\n    }\n    setStateInputValue(newValue !== undefined ? newValue : value);\n  }, [propsOnInputChange]);\n  var onMenuOpen = useCallback(function () {\n    if (typeof propsOnMenuOpen === 'function') {\n      propsOnMenuOpen();\n    }\n    setStateMenuIsOpen(true);\n  }, [propsOnMenuOpen]);\n  var onMenuClose = useCallback(function () {\n    if (typeof propsOnMenuClose === 'function') {\n      propsOnMenuClose();\n    }\n    setStateMenuIsOpen(false);\n  }, [propsOnMenuClose]);\n  var inputValue = propsInputValue !== undefined ? propsInputValue : stateInputValue;\n  var menuIsOpen = propsMenuIsOpen !== undefined ? propsMenuIsOpen : stateMenuIsOpen;\n  var value = propsValue !== undefined ? propsValue : stateValue;\n  return _objectSpread(_objectSpread({}, restSelectProps), {}, {\n    inputValue: inputValue,\n    menuIsOpen: menuIsOpen,\n    onChange: onChange,\n    onInputChange: onInputChange,\n    onMenuClose: onMenuClose,\n    onMenuOpen: onMenuOpen,\n    value: value\n  });\n}\n\nexport { useStateManager as u };\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,SAASC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAE7C,IAAIC,SAAS,GAAG,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC;AACzK,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAIC,qBAAqB,GAAGD,IAAI,CAACE,iBAAiB;IAChDA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IACjFE,qBAAqB,GAAGH,IAAI,CAACI,iBAAiB;IAC9CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACpFE,iBAAiB,GAAGL,IAAI,CAACM,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACtEE,eAAe,GAAGP,IAAI,CAACQ,UAAU;IACjCC,eAAe,GAAGT,IAAI,CAACU,UAAU;IACjCC,aAAa,GAAGX,IAAI,CAACY,QAAQ;IAC7BC,kBAAkB,GAAGb,IAAI,CAACc,aAAa;IACvCC,gBAAgB,GAAGf,IAAI,CAACgB,WAAW;IACnCC,eAAe,GAAGjB,IAAI,CAACkB,UAAU;IACjCC,UAAU,GAAGnB,IAAI,CAACoB,KAAK;IACvBC,eAAe,GAAG1B,wBAAwB,CAACK,IAAI,EAAEF,SAAS,CAAC;EAC7D,IAAIwB,SAAS,GAAG1B,QAAQ,CAACW,eAAe,KAAKgB,SAAS,GAAGhB,eAAe,GAAGL,iBAAiB,CAAC;IAC3FsB,UAAU,GAAG9B,cAAc,CAAC4B,SAAS,EAAE,CAAC,CAAC;IACzCG,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACpC,IAAIG,UAAU,GAAG/B,QAAQ,CAACa,eAAe,KAAKc,SAAS,GAAGd,eAAe,GAAGL,iBAAiB,CAAC;IAC5FwB,UAAU,GAAGlC,cAAc,CAACiC,UAAU,EAAE,CAAC,CAAC;IAC1CE,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACpC,IAAIG,UAAU,GAAGnC,QAAQ,CAACuB,UAAU,KAAKI,SAAS,GAAGJ,UAAU,GAAGb,YAAY,CAAC;IAC7E0B,UAAU,GAAGtC,cAAc,CAACqC,UAAU,EAAE,CAAC,CAAC;IAC1CE,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC/B,IAAIpB,QAAQ,GAAGf,WAAW,CAAC,UAAUuB,KAAK,EAAEe,UAAU,EAAE;IACtD,IAAI,OAAOxB,aAAa,KAAK,UAAU,EAAE;MACvCA,aAAa,CAACS,KAAK,EAAEe,UAAU,CAAC;IAClC;IACAD,aAAa,CAACd,KAAK,CAAC;EACtB,CAAC,EAAE,CAACT,aAAa,CAAC,CAAC;EACnB,IAAIG,aAAa,GAAGjB,WAAW,CAAC,UAAUuB,KAAK,EAAEe,UAAU,EAAE;IAC3D,IAAIC,QAAQ;IACZ,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;MAC5CuB,QAAQ,GAAGvB,kBAAkB,CAACO,KAAK,EAAEe,UAAU,CAAC;IAClD;IACAT,kBAAkB,CAACU,QAAQ,KAAKb,SAAS,GAAGa,QAAQ,GAAGhB,KAAK,CAAC;EAC/D,CAAC,EAAE,CAACP,kBAAkB,CAAC,CAAC;EACxB,IAAIK,UAAU,GAAGrB,WAAW,CAAC,YAAY;IACvC,IAAI,OAAOoB,eAAe,KAAK,UAAU,EAAE;MACzCA,eAAe,CAAC,CAAC;IACnB;IACAa,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC,EAAE,CAACb,eAAe,CAAC,CAAC;EACrB,IAAID,WAAW,GAAGnB,WAAW,CAAC,YAAY;IACxC,IAAI,OAAOkB,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAe,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC,EAAE,CAACf,gBAAgB,CAAC,CAAC;EACtB,IAAIP,UAAU,GAAGD,eAAe,KAAKgB,SAAS,GAAGhB,eAAe,GAAGkB,eAAe;EAClF,IAAIf,UAAU,GAAGD,eAAe,KAAKc,SAAS,GAAGd,eAAe,GAAGoB,eAAe;EAClF,IAAIT,KAAK,GAAGD,UAAU,KAAKI,SAAS,GAAGJ,UAAU,GAAGc,UAAU;EAC9D,OAAOxC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3Db,UAAU,EAAEA,UAAU;IACtBE,UAAU,EAAEA,UAAU;IACtBE,QAAQ,EAAEA,QAAQ;IAClBE,aAAa,EAAEA,aAAa;IAC5BE,WAAW,EAAEA,WAAW;IACxBE,UAAU,EAAEA,UAAU;IACtBE,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AAEA,SAASrB,eAAe,IAAIsC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}