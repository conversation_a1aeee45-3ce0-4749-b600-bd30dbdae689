{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"workUnitAsyncStorageInstance\", {\n  enumerable: true,\n  get: function () {\n    return workUnitAsyncStorageInstance;\n  }\n});\nconst _asynclocalstorage = require(\"./async-local-storage\");\nconst workUnitAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();", "map": {"version": 3, "names": ["workUnitAsyncStorageInstance", "_asynclocalstorage", "createAsyncLocalStorage"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\server\\app-render\\work-unit-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { WorkUnitAsyncStorage } from './work-unit-async-storage.external'\n\nexport const workUnitAsyncStorageInstance: WorkUnitAsyncStorage =\n  createAsyncLocalStorage()\n"], "mappings": ";;;;;+BAGa;;;WAAAA,4BAAA;;;mCAH2B;AAGjC,MAAMA,4BAAA,GACX,IAAAC,kBAAA,CAAAC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}