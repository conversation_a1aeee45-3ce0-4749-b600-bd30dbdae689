{"ast": null, "code": "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport { jsx, keyframes, css as css$2 } from '@emotion/react';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport _typeof from '@babel/runtime/helpers/esm/typeof';\nimport _taggedTemplateLiteral from '@babel/runtime/helpers/esm/taggedTemplateLiteral';\nimport _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport { useContext, useRef, useState, useMemo, useCallback, createContext } from 'react';\nimport { createPortal } from 'react-dom';\nimport { autoUpdate } from '@floating-ui/dom';\nimport useLayoutEffect from 'use-isomorphic-layout-effect';\nvar _excluded$4 = [\"className\", \"clearValue\", \"cx\", \"getStyles\", \"getClassNames\", \"getValue\", \"hasValue\", \"isMulti\", \"isRtl\", \"options\", \"selectOption\", \"selectProps\", \"setValue\", \"theme\"];\n// ==============================\n// NO OP\n// ==============================\n\nvar noop = function noop() {};\n\n// ==============================\n// Class Name Prefixer\n// ==============================\n\n/**\n String representation of component state for styling with class names.\n\n Expects an array of strings OR a string/object pair:\n - className(['comp', 'comp-arg', 'comp-arg-2'])\n   @returns 'react-select__comp react-select__comp-arg react-select__comp-arg-2'\n - className('comp', { some: true, state: false })\n   @returns 'react-select__comp react-select__comp--some'\n*/\nfunction applyPrefixToName(prefix, name) {\n  if (!name) {\n    return prefix;\n  } else if (name[0] === '-') {\n    return prefix + name;\n  } else {\n    return prefix + '__' + name;\n  }\n}\nfunction classNames(prefix, state) {\n  for (var _len = arguments.length, classNameList = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    classNameList[_key - 2] = arguments[_key];\n  }\n  var arr = [].concat(classNameList);\n  if (state && prefix) {\n    for (var key in state) {\n      if (state.hasOwnProperty(key) && state[key]) {\n        arr.push(\"\".concat(applyPrefixToName(prefix, key)));\n      }\n    }\n  }\n  return arr.filter(function (i) {\n    return i;\n  }).map(function (i) {\n    return String(i).trim();\n  }).join(' ');\n}\n// ==============================\n// Clean Value\n// ==============================\n\nvar cleanValue = function cleanValue(value) {\n  if (isArray(value)) return value.filter(Boolean);\n  if (_typeof(value) === 'object' && value !== null) return [value];\n  return [];\n};\n\n// ==============================\n// Clean Common Props\n// ==============================\n\nvar cleanCommonProps = function cleanCommonProps(props) {\n  //className\n  props.className;\n  props.clearValue;\n  props.cx;\n  props.getStyles;\n  props.getClassNames;\n  props.getValue;\n  props.hasValue;\n  props.isMulti;\n  props.isRtl;\n  props.options;\n  props.selectOption;\n  props.selectProps;\n  props.setValue;\n  props.theme;\n  var innerProps = _objectWithoutProperties(props, _excluded$4);\n  return _objectSpread({}, innerProps);\n};\n\n// ==============================\n// Get Style Props\n// ==============================\n\nvar getStyleProps = function getStyleProps(props, name, classNamesState) {\n  var cx = props.cx,\n    getStyles = props.getStyles,\n    getClassNames = props.getClassNames,\n    className = props.className;\n  return {\n    css: getStyles(name, props),\n    className: cx(classNamesState !== null && classNamesState !== void 0 ? classNamesState : {}, getClassNames(name, props), className)\n  };\n};\n\n// ==============================\n// Handle Input Change\n// ==============================\n\nfunction handleInputChange(inputValue, actionMeta, onInputChange) {\n  if (onInputChange) {\n    var _newValue = onInputChange(inputValue, actionMeta);\n    if (typeof _newValue === 'string') return _newValue;\n  }\n  return inputValue;\n}\n\n// ==============================\n// Scroll Helpers\n// ==============================\n\nfunction isDocumentElement(el) {\n  return [document.documentElement, document.body, window].indexOf(el) > -1;\n}\n\n// Normalized Scroll Top\n// ------------------------------\n\nfunction normalizedHeight(el) {\n  if (isDocumentElement(el)) {\n    return window.innerHeight;\n  }\n  return el.clientHeight;\n}\n\n// Normalized scrollTo & scrollTop\n// ------------------------------\n\nfunction getScrollTop(el) {\n  if (isDocumentElement(el)) {\n    return window.pageYOffset;\n  }\n  return el.scrollTop;\n}\nfunction scrollTo(el, top) {\n  // with a scroll distance, we perform scroll on the element\n  if (isDocumentElement(el)) {\n    window.scrollTo(0, top);\n    return;\n  }\n  el.scrollTop = top;\n}\n\n// Get Scroll Parent\n// ------------------------------\n\nfunction getScrollParent(element) {\n  var style = getComputedStyle(element);\n  var excludeStaticParent = style.position === 'absolute';\n  var overflowRx = /(auto|scroll)/;\n  if (style.position === 'fixed') return document.documentElement;\n  for (var parent = element; parent = parent.parentElement;) {\n    style = getComputedStyle(parent);\n    if (excludeStaticParent && style.position === 'static') {\n      continue;\n    }\n    if (overflowRx.test(style.overflow + style.overflowY + style.overflowX)) {\n      return parent;\n    }\n  }\n  return document.documentElement;\n}\n\n// Animated Scroll To\n// ------------------------------\n\n/**\n  @param t: time (elapsed)\n  @param b: initial value\n  @param c: amount of change\n  @param d: duration\n*/\nfunction easeOutCubic(t, b, c, d) {\n  return c * ((t = t / d - 1) * t * t + 1) + b;\n}\nfunction animatedScrollTo(element, to) {\n  var duration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 200;\n  var callback = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : noop;\n  var start = getScrollTop(element);\n  var change = to - start;\n  var increment = 10;\n  var currentTime = 0;\n  function animateScroll() {\n    currentTime += increment;\n    var val = easeOutCubic(currentTime, start, change, duration);\n    scrollTo(element, val);\n    if (currentTime < duration) {\n      window.requestAnimationFrame(animateScroll);\n    } else {\n      callback(element);\n    }\n  }\n  animateScroll();\n}\n\n// Scroll Into View\n// ------------------------------\n\nfunction scrollIntoView(menuEl, focusedEl) {\n  var menuRect = menuEl.getBoundingClientRect();\n  var focusedRect = focusedEl.getBoundingClientRect();\n  var overScroll = focusedEl.offsetHeight / 3;\n  if (focusedRect.bottom + overScroll > menuRect.bottom) {\n    scrollTo(menuEl, Math.min(focusedEl.offsetTop + focusedEl.clientHeight - menuEl.offsetHeight + overScroll, menuEl.scrollHeight));\n  } else if (focusedRect.top - overScroll < menuRect.top) {\n    scrollTo(menuEl, Math.max(focusedEl.offsetTop - overScroll, 0));\n  }\n}\n\n// ==============================\n// Get bounding client object\n// ==============================\n\n// cannot get keys using array notation with DOMRect\nfunction getBoundingClientObj(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    bottom: rect.bottom,\n    height: rect.height,\n    left: rect.left,\n    right: rect.right,\n    top: rect.top,\n    width: rect.width\n  };\n}\n\n// ==============================\n// Touch Capability Detector\n// ==============================\n\nfunction isTouchCapable() {\n  try {\n    document.createEvent('TouchEvent');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// ==============================\n// Mobile Device Detector\n// ==============================\n\nfunction isMobileDevice() {\n  try {\n    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n  } catch (e) {\n    return false;\n  }\n}\n\n// ==============================\n// Passive Event Detector\n// ==============================\n\n// https://github.com/rafgraph/detect-it/blob/main/src/index.ts#L19-L36\nvar passiveOptionAccessed = false;\nvar options = {\n  get passive() {\n    return passiveOptionAccessed = true;\n  }\n};\n// check for SSR\nvar w = typeof window !== 'undefined' ? window : {};\nif (w.addEventListener && w.removeEventListener) {\n  w.addEventListener('p', noop, options);\n  w.removeEventListener('p', noop, false);\n}\nvar supportsPassiveEvents = passiveOptionAccessed;\nfunction notNullish(item) {\n  return item != null;\n}\nfunction isArray(arg) {\n  return Array.isArray(arg);\n}\nfunction valueTernary(isMulti, multiValue, singleValue) {\n  return isMulti ? multiValue : singleValue;\n}\nfunction singleValueAsValue(singleValue) {\n  return singleValue;\n}\nfunction multiValueAsValue(multiValue) {\n  return multiValue;\n}\nvar removeProps = function removeProps(propsObj) {\n  for (var _len2 = arguments.length, properties = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    properties[_key2 - 1] = arguments[_key2];\n  }\n  var propsMap = Object.entries(propsObj).filter(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 1),\n      key = _ref2[0];\n    return !properties.includes(key);\n  });\n  return propsMap.reduce(function (newProps, _ref3) {\n    var _ref4 = _slicedToArray(_ref3, 2),\n      key = _ref4[0],\n      val = _ref4[1];\n    newProps[key] = val;\n    return newProps;\n  }, {});\n};\nvar _excluded$3 = [\"children\", \"innerProps\"],\n  _excluded2$1 = [\"children\", \"innerProps\"];\nfunction getMenuPlacement(_ref) {\n  var preferredMaxHeight = _ref.maxHeight,\n    menuEl = _ref.menuEl,\n    minHeight = _ref.minHeight,\n    preferredPlacement = _ref.placement,\n    shouldScroll = _ref.shouldScroll,\n    isFixedPosition = _ref.isFixedPosition,\n    controlHeight = _ref.controlHeight;\n  var scrollParent = getScrollParent(menuEl);\n  var defaultState = {\n    placement: 'bottom',\n    maxHeight: preferredMaxHeight\n  };\n\n  // something went wrong, return default state\n  if (!menuEl || !menuEl.offsetParent) return defaultState;\n\n  // we can't trust `scrollParent.scrollHeight` --> it may increase when\n  // the menu is rendered\n  var _scrollParent$getBoun = scrollParent.getBoundingClientRect(),\n    scrollHeight = _scrollParent$getBoun.height;\n  var _menuEl$getBoundingCl = menuEl.getBoundingClientRect(),\n    menuBottom = _menuEl$getBoundingCl.bottom,\n    menuHeight = _menuEl$getBoundingCl.height,\n    menuTop = _menuEl$getBoundingCl.top;\n  var _menuEl$offsetParent$ = menuEl.offsetParent.getBoundingClientRect(),\n    containerTop = _menuEl$offsetParent$.top;\n  var viewHeight = isFixedPosition ? window.innerHeight : normalizedHeight(scrollParent);\n  var scrollTop = getScrollTop(scrollParent);\n  var marginBottom = parseInt(getComputedStyle(menuEl).marginBottom, 10);\n  var marginTop = parseInt(getComputedStyle(menuEl).marginTop, 10);\n  var viewSpaceAbove = containerTop - marginTop;\n  var viewSpaceBelow = viewHeight - menuTop;\n  var scrollSpaceAbove = viewSpaceAbove + scrollTop;\n  var scrollSpaceBelow = scrollHeight - scrollTop - menuTop;\n  var scrollDown = menuBottom - viewHeight + scrollTop + marginBottom;\n  var scrollUp = scrollTop + menuTop - marginTop;\n  var scrollDuration = 160;\n  switch (preferredPlacement) {\n    case 'auto':\n    case 'bottom':\n      // 1: the menu will fit, do nothing\n      if (viewSpaceBelow >= menuHeight) {\n        return {\n          placement: 'bottom',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 2: the menu will fit, if scrolled\n      if (scrollSpaceBelow >= menuHeight && !isFixedPosition) {\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollDown, scrollDuration);\n        }\n        return {\n          placement: 'bottom',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 3: the menu will fit, if constrained\n      if (!isFixedPosition && scrollSpaceBelow >= minHeight || isFixedPosition && viewSpaceBelow >= minHeight) {\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollDown, scrollDuration);\n        }\n\n        // we want to provide as much of the menu as possible to the user,\n        // so give them whatever is available below rather than the minHeight.\n        var constrainedHeight = isFixedPosition ? viewSpaceBelow - marginBottom : scrollSpaceBelow - marginBottom;\n        return {\n          placement: 'bottom',\n          maxHeight: constrainedHeight\n        };\n      }\n\n      // 4. Forked beviour when there isn't enough space below\n\n      // AUTO: flip the menu, render above\n      if (preferredPlacement === 'auto' || isFixedPosition) {\n        // may need to be constrained after flipping\n        var _constrainedHeight = preferredMaxHeight;\n        var spaceAbove = isFixedPosition ? viewSpaceAbove : scrollSpaceAbove;\n        if (spaceAbove >= minHeight) {\n          _constrainedHeight = Math.min(spaceAbove - marginBottom - controlHeight, preferredMaxHeight);\n        }\n        return {\n          placement: 'top',\n          maxHeight: _constrainedHeight\n        };\n      }\n\n      // BOTTOM: allow browser to increase scrollable area and immediately set scroll\n      if (preferredPlacement === 'bottom') {\n        if (shouldScroll) {\n          scrollTo(scrollParent, scrollDown);\n        }\n        return {\n          placement: 'bottom',\n          maxHeight: preferredMaxHeight\n        };\n      }\n      break;\n    case 'top':\n      // 1: the menu will fit, do nothing\n      if (viewSpaceAbove >= menuHeight) {\n        return {\n          placement: 'top',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 2: the menu will fit, if scrolled\n      if (scrollSpaceAbove >= menuHeight && !isFixedPosition) {\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollUp, scrollDuration);\n        }\n        return {\n          placement: 'top',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 3: the menu will fit, if constrained\n      if (!isFixedPosition && scrollSpaceAbove >= minHeight || isFixedPosition && viewSpaceAbove >= minHeight) {\n        var _constrainedHeight2 = preferredMaxHeight;\n\n        // we want to provide as much of the menu as possible to the user,\n        // so give them whatever is available below rather than the minHeight.\n        if (!isFixedPosition && scrollSpaceAbove >= minHeight || isFixedPosition && viewSpaceAbove >= minHeight) {\n          _constrainedHeight2 = isFixedPosition ? viewSpaceAbove - marginTop : scrollSpaceAbove - marginTop;\n        }\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollUp, scrollDuration);\n        }\n        return {\n          placement: 'top',\n          maxHeight: _constrainedHeight2\n        };\n      }\n\n      // 4. not enough space, the browser WILL NOT increase scrollable area when\n      // absolutely positioned element rendered above the viewport (only below).\n      // Flip the menu, render below\n      return {\n        placement: 'bottom',\n        maxHeight: preferredMaxHeight\n      };\n    default:\n      throw new Error(\"Invalid placement provided \\\"\".concat(preferredPlacement, \"\\\".\"));\n  }\n  return defaultState;\n}\n\n// Menu Component\n// ------------------------------\n\nfunction alignToControl(placement) {\n  var placementToCSSProp = {\n    bottom: 'top',\n    top: 'bottom'\n  };\n  return placement ? placementToCSSProp[placement] : 'bottom';\n}\nvar coercePlacement = function coercePlacement(p) {\n  return p === 'auto' ? 'bottom' : p;\n};\nvar menuCSS = function menuCSS(_ref2, unstyled) {\n  var _objectSpread2;\n  var placement = _ref2.placement,\n    _ref2$theme = _ref2.theme,\n    borderRadius = _ref2$theme.borderRadius,\n    spacing = _ref2$theme.spacing,\n    colors = _ref2$theme.colors;\n  return _objectSpread((_objectSpread2 = {\n    label: 'menu'\n  }, _defineProperty(_objectSpread2, alignToControl(placement), '100%'), _defineProperty(_objectSpread2, \"position\", 'absolute'), _defineProperty(_objectSpread2, \"width\", '100%'), _defineProperty(_objectSpread2, \"zIndex\", 1), _objectSpread2), unstyled ? {} : {\n    backgroundColor: colors.neutral0,\n    borderRadius: borderRadius,\n    boxShadow: '0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)',\n    marginBottom: spacing.menuGutter,\n    marginTop: spacing.menuGutter\n  });\n};\nvar PortalPlacementContext = /*#__PURE__*/createContext(null);\n\n// NOTE: internal only\nvar MenuPlacer = function MenuPlacer(props) {\n  var children = props.children,\n    minMenuHeight = props.minMenuHeight,\n    maxMenuHeight = props.maxMenuHeight,\n    menuPlacement = props.menuPlacement,\n    menuPosition = props.menuPosition,\n    menuShouldScrollIntoView = props.menuShouldScrollIntoView,\n    theme = props.theme;\n  var _ref3 = useContext(PortalPlacementContext) || {},\n    setPortalPlacement = _ref3.setPortalPlacement;\n  var ref = useRef(null);\n  var _useState = useState(maxMenuHeight),\n    _useState2 = _slicedToArray(_useState, 2),\n    maxHeight = _useState2[0],\n    setMaxHeight = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    placement = _useState4[0],\n    setPlacement = _useState4[1];\n  var controlHeight = theme.spacing.controlHeight;\n  useLayoutEffect(function () {\n    var menuEl = ref.current;\n    if (!menuEl) return;\n\n    // DO NOT scroll if position is fixed\n    var isFixedPosition = menuPosition === 'fixed';\n    var shouldScroll = menuShouldScrollIntoView && !isFixedPosition;\n    var state = getMenuPlacement({\n      maxHeight: maxMenuHeight,\n      menuEl: menuEl,\n      minHeight: minMenuHeight,\n      placement: menuPlacement,\n      shouldScroll: shouldScroll,\n      isFixedPosition: isFixedPosition,\n      controlHeight: controlHeight\n    });\n    setMaxHeight(state.maxHeight);\n    setPlacement(state.placement);\n    setPortalPlacement === null || setPortalPlacement === void 0 ? void 0 : setPortalPlacement(state.placement);\n  }, [maxMenuHeight, menuPlacement, menuPosition, menuShouldScrollIntoView, minMenuHeight, setPortalPlacement, controlHeight]);\n  return children({\n    ref: ref,\n    placerProps: _objectSpread(_objectSpread({}, props), {}, {\n      placement: placement || coercePlacement(menuPlacement),\n      maxHeight: maxHeight\n    })\n  });\n};\nvar Menu = function Menu(props) {\n  var children = props.children,\n    innerRef = props.innerRef,\n    innerProps = props.innerProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'menu', {\n    menu: true\n  }), {\n    ref: innerRef\n  }, innerProps), children);\n};\nvar Menu$1 = Menu;\n\n// ==============================\n// Menu List\n// ==============================\n\nvar menuListCSS = function menuListCSS(_ref4, unstyled) {\n  var maxHeight = _ref4.maxHeight,\n    baseUnit = _ref4.theme.spacing.baseUnit;\n  return _objectSpread({\n    maxHeight: maxHeight,\n    overflowY: 'auto',\n    position: 'relative',\n    // required for offset[Height, Top] > keyboard scroll\n    WebkitOverflowScrolling: 'touch'\n  }, unstyled ? {} : {\n    paddingBottom: baseUnit,\n    paddingTop: baseUnit\n  });\n};\nvar MenuList = function MenuList(props) {\n  var children = props.children,\n    innerProps = props.innerProps,\n    innerRef = props.innerRef,\n    isMulti = props.isMulti;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'menuList', {\n    'menu-list': true,\n    'menu-list--is-multi': isMulti\n  }), {\n    ref: innerRef\n  }, innerProps), children);\n};\n\n// ==============================\n// Menu Notices\n// ==============================\n\nvar noticeCSS = function noticeCSS(_ref5, unstyled) {\n  var _ref5$theme = _ref5.theme,\n    baseUnit = _ref5$theme.spacing.baseUnit,\n    colors = _ref5$theme.colors;\n  return _objectSpread({\n    textAlign: 'center'\n  }, unstyled ? {} : {\n    color: colors.neutral40,\n    padding: \"\".concat(baseUnit * 2, \"px \").concat(baseUnit * 3, \"px\")\n  });\n};\nvar noOptionsMessageCSS = noticeCSS;\nvar loadingMessageCSS = noticeCSS;\nvar NoOptionsMessage = function NoOptionsMessage(_ref6) {\n  var _ref6$children = _ref6.children,\n    children = _ref6$children === void 0 ? 'No options' : _ref6$children,\n    innerProps = _ref6.innerProps,\n    restProps = _objectWithoutProperties(_ref6, _excluded$3);\n  return jsx(\"div\", _extends({}, getStyleProps(_objectSpread(_objectSpread({}, restProps), {}, {\n    children: children,\n    innerProps: innerProps\n  }), 'noOptionsMessage', {\n    'menu-notice': true,\n    'menu-notice--no-options': true\n  }), innerProps), children);\n};\nvar LoadingMessage = function LoadingMessage(_ref7) {\n  var _ref7$children = _ref7.children,\n    children = _ref7$children === void 0 ? 'Loading...' : _ref7$children,\n    innerProps = _ref7.innerProps,\n    restProps = _objectWithoutProperties(_ref7, _excluded2$1);\n  return jsx(\"div\", _extends({}, getStyleProps(_objectSpread(_objectSpread({}, restProps), {}, {\n    children: children,\n    innerProps: innerProps\n  }), 'loadingMessage', {\n    'menu-notice': true,\n    'menu-notice--loading': true\n  }), innerProps), children);\n};\n\n// ==============================\n// Menu Portal\n// ==============================\n\nvar menuPortalCSS = function menuPortalCSS(_ref8) {\n  var rect = _ref8.rect,\n    offset = _ref8.offset,\n    position = _ref8.position;\n  return {\n    left: rect.left,\n    position: position,\n    top: offset,\n    width: rect.width,\n    zIndex: 1\n  };\n};\nvar MenuPortal = function MenuPortal(props) {\n  var appendTo = props.appendTo,\n    children = props.children,\n    controlElement = props.controlElement,\n    innerProps = props.innerProps,\n    menuPlacement = props.menuPlacement,\n    menuPosition = props.menuPosition;\n  var menuPortalRef = useRef(null);\n  var cleanupRef = useRef(null);\n  var _useState5 = useState(coercePlacement(menuPlacement)),\n    _useState6 = _slicedToArray(_useState5, 2),\n    placement = _useState6[0],\n    setPortalPlacement = _useState6[1];\n  var portalPlacementContext = useMemo(function () {\n    return {\n      setPortalPlacement: setPortalPlacement\n    };\n  }, []);\n  var _useState7 = useState(null),\n    _useState8 = _slicedToArray(_useState7, 2),\n    computedPosition = _useState8[0],\n    setComputedPosition = _useState8[1];\n  var updateComputedPosition = useCallback(function () {\n    if (!controlElement) return;\n    var rect = getBoundingClientObj(controlElement);\n    var scrollDistance = menuPosition === 'fixed' ? 0 : window.pageYOffset;\n    var offset = rect[placement] + scrollDistance;\n    if (offset !== (computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.offset) || rect.left !== (computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.left) || rect.width !== (computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.width)) {\n      setComputedPosition({\n        offset: offset,\n        rect: rect\n      });\n    }\n  }, [controlElement, menuPosition, placement, computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.offset, computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.left, computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.width]);\n  useLayoutEffect(function () {\n    updateComputedPosition();\n  }, [updateComputedPosition]);\n  var runAutoUpdate = useCallback(function () {\n    if (typeof cleanupRef.current === 'function') {\n      cleanupRef.current();\n      cleanupRef.current = null;\n    }\n    if (controlElement && menuPortalRef.current) {\n      cleanupRef.current = autoUpdate(controlElement, menuPortalRef.current, updateComputedPosition, {\n        elementResize: 'ResizeObserver' in window\n      });\n    }\n  }, [controlElement, updateComputedPosition]);\n  useLayoutEffect(function () {\n    runAutoUpdate();\n  }, [runAutoUpdate]);\n  var setMenuPortalElement = useCallback(function (menuPortalElement) {\n    menuPortalRef.current = menuPortalElement;\n    runAutoUpdate();\n  }, [runAutoUpdate]);\n\n  // bail early if required elements aren't present\n  if (!appendTo && menuPosition !== 'fixed' || !computedPosition) return null;\n\n  // same wrapper element whether fixed or portalled\n  var menuWrapper = jsx(\"div\", _extends({\n    ref: setMenuPortalElement\n  }, getStyleProps(_objectSpread(_objectSpread({}, props), {}, {\n    offset: computedPosition.offset,\n    position: menuPosition,\n    rect: computedPosition.rect\n  }), 'menuPortal', {\n    'menu-portal': true\n  }), innerProps), children);\n  return jsx(PortalPlacementContext.Provider, {\n    value: portalPlacementContext\n  }, appendTo ? /*#__PURE__*/createPortal(menuWrapper, appendTo) : menuWrapper);\n};\n\n// ==============================\n// Root Container\n// ==============================\n\nvar containerCSS = function containerCSS(_ref) {\n  var isDisabled = _ref.isDisabled,\n    isRtl = _ref.isRtl;\n  return {\n    label: 'container',\n    direction: isRtl ? 'rtl' : undefined,\n    pointerEvents: isDisabled ? 'none' : undefined,\n    // cancel mouse events when disabled\n    position: 'relative'\n  };\n};\nvar SelectContainer = function SelectContainer(props) {\n  var children = props.children,\n    innerProps = props.innerProps,\n    isDisabled = props.isDisabled,\n    isRtl = props.isRtl;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'container', {\n    '--is-disabled': isDisabled,\n    '--is-rtl': isRtl\n  }), innerProps), children);\n};\n\n// ==============================\n// Value Container\n// ==============================\n\nvar valueContainerCSS = function valueContainerCSS(_ref2, unstyled) {\n  var spacing = _ref2.theme.spacing,\n    isMulti = _ref2.isMulti,\n    hasValue = _ref2.hasValue,\n    controlShouldRenderValue = _ref2.selectProps.controlShouldRenderValue;\n  return _objectSpread({\n    alignItems: 'center',\n    display: isMulti && hasValue && controlShouldRenderValue ? 'flex' : 'grid',\n    flex: 1,\n    flexWrap: 'wrap',\n    WebkitOverflowScrolling: 'touch',\n    position: 'relative',\n    overflow: 'hidden'\n  }, unstyled ? {} : {\n    padding: \"\".concat(spacing.baseUnit / 2, \"px \").concat(spacing.baseUnit * 2, \"px\")\n  });\n};\nvar ValueContainer = function ValueContainer(props) {\n  var children = props.children,\n    innerProps = props.innerProps,\n    isMulti = props.isMulti,\n    hasValue = props.hasValue;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'valueContainer', {\n    'value-container': true,\n    'value-container--is-multi': isMulti,\n    'value-container--has-value': hasValue\n  }), innerProps), children);\n};\n\n// ==============================\n// Indicator Container\n// ==============================\n\nvar indicatorsContainerCSS = function indicatorsContainerCSS() {\n  return {\n    alignItems: 'center',\n    alignSelf: 'stretch',\n    display: 'flex',\n    flexShrink: 0\n  };\n};\nvar IndicatorsContainer = function IndicatorsContainer(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'indicatorsContainer', {\n    indicators: true\n  }), innerProps), children);\n};\nvar _templateObject;\nvar _excluded$2 = [\"size\"],\n  _excluded2 = [\"innerProps\", \"isRtl\", \"size\"];\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__() {\n  return \"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).\";\n}\n\n// ==============================\n// Dropdown & Clear Icons\n// ==============================\nvar _ref2 = process.env.NODE_ENV === \"production\" ? {\n  name: \"8mmkcg\",\n  styles: \"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0\"\n} : {\n  name: \"tj5bde-Svg\",\n  styles: \"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0;label:Svg;\",\n  map: \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\",\n  toString: _EMOTION_STRINGIFIED_CSS_ERROR__\n};\nvar Svg = function Svg(_ref) {\n  var size = _ref.size,\n    props = _objectWithoutProperties(_ref, _excluded$2);\n  return jsx(\"svg\", _extends({\n    height: size,\n    width: size,\n    viewBox: \"0 0 20 20\",\n    \"aria-hidden\": \"true\",\n    focusable: \"false\",\n    css: _ref2\n  }, props));\n};\nvar CrossIcon = function CrossIcon(props) {\n  return jsx(Svg, _extends({\n    size: 20\n  }, props), jsx(\"path\", {\n    d: \"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z\"\n  }));\n};\nvar DownChevron = function DownChevron(props) {\n  return jsx(Svg, _extends({\n    size: 20\n  }, props), jsx(\"path\", {\n    d: \"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z\"\n  }));\n};\n\n// ==============================\n// Dropdown & Clear Buttons\n// ==============================\n\nvar baseCSS = function baseCSS(_ref3, unstyled) {\n  var isFocused = _ref3.isFocused,\n    _ref3$theme = _ref3.theme,\n    baseUnit = _ref3$theme.spacing.baseUnit,\n    colors = _ref3$theme.colors;\n  return _objectSpread({\n    label: 'indicatorContainer',\n    display: 'flex',\n    transition: 'color 150ms'\n  }, unstyled ? {} : {\n    color: isFocused ? colors.neutral60 : colors.neutral20,\n    padding: baseUnit * 2,\n    ':hover': {\n      color: isFocused ? colors.neutral80 : colors.neutral40\n    }\n  });\n};\nvar dropdownIndicatorCSS = baseCSS;\nvar DropdownIndicator = function DropdownIndicator(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'dropdownIndicator', {\n    indicator: true,\n    'dropdown-indicator': true\n  }), innerProps), children || jsx(DownChevron, null));\n};\nvar clearIndicatorCSS = baseCSS;\nvar ClearIndicator = function ClearIndicator(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'clearIndicator', {\n    indicator: true,\n    'clear-indicator': true\n  }), innerProps), children || jsx(CrossIcon, null));\n};\n\n// ==============================\n// Separator\n// ==============================\n\nvar indicatorSeparatorCSS = function indicatorSeparatorCSS(_ref4, unstyled) {\n  var isDisabled = _ref4.isDisabled,\n    _ref4$theme = _ref4.theme,\n    baseUnit = _ref4$theme.spacing.baseUnit,\n    colors = _ref4$theme.colors;\n  return _objectSpread({\n    label: 'indicatorSeparator',\n    alignSelf: 'stretch',\n    width: 1\n  }, unstyled ? {} : {\n    backgroundColor: isDisabled ? colors.neutral10 : colors.neutral20,\n    marginBottom: baseUnit * 2,\n    marginTop: baseUnit * 2\n  });\n};\nvar IndicatorSeparator = function IndicatorSeparator(props) {\n  var innerProps = props.innerProps;\n  return jsx(\"span\", _extends({}, innerProps, getStyleProps(props, 'indicatorSeparator', {\n    'indicator-separator': true\n  })));\n};\n\n// ==============================\n// Loading\n// ==============================\n\nvar loadingDotAnimations = keyframes(_templateObject || (_templateObject = _taggedTemplateLiteral([\"\\n  0%, 80%, 100% { opacity: 0; }\\n  40% { opacity: 1; }\\n\"])));\nvar loadingIndicatorCSS = function loadingIndicatorCSS(_ref5, unstyled) {\n  var isFocused = _ref5.isFocused,\n    size = _ref5.size,\n    _ref5$theme = _ref5.theme,\n    colors = _ref5$theme.colors,\n    baseUnit = _ref5$theme.spacing.baseUnit;\n  return _objectSpread({\n    label: 'loadingIndicator',\n    display: 'flex',\n    transition: 'color 150ms',\n    alignSelf: 'center',\n    fontSize: size,\n    lineHeight: 1,\n    marginRight: size,\n    textAlign: 'center',\n    verticalAlign: 'middle'\n  }, unstyled ? {} : {\n    color: isFocused ? colors.neutral60 : colors.neutral20,\n    padding: baseUnit * 2\n  });\n};\nvar LoadingDot = function LoadingDot(_ref6) {\n  var delay = _ref6.delay,\n    offset = _ref6.offset;\n  return jsx(\"span\", {\n    css: /*#__PURE__*/css$2({\n      animation: \"\".concat(loadingDotAnimations, \" 1s ease-in-out \").concat(delay, \"ms infinite;\"),\n      backgroundColor: 'currentColor',\n      borderRadius: '1em',\n      display: 'inline-block',\n      marginLeft: offset ? '1em' : undefined,\n      height: '1em',\n      verticalAlign: 'top',\n      width: '1em'\n    }, process.env.NODE_ENV === \"production\" ? \"\" : \";label:LoadingDot;\", process.env.NODE_ENV === \"production\" ? \"\" : \"\")\n  });\n};\nvar LoadingIndicator = function LoadingIndicator(_ref7) {\n  var innerProps = _ref7.innerProps,\n    isRtl = _ref7.isRtl,\n    _ref7$size = _ref7.size,\n    size = _ref7$size === void 0 ? 4 : _ref7$size,\n    restProps = _objectWithoutProperties(_ref7, _excluded2);\n  return jsx(\"div\", _extends({}, getStyleProps(_objectSpread(_objectSpread({}, restProps), {}, {\n    innerProps: innerProps,\n    isRtl: isRtl,\n    size: size\n  }), 'loadingIndicator', {\n    indicator: true,\n    'loading-indicator': true\n  }), innerProps), jsx(LoadingDot, {\n    delay: 0,\n    offset: isRtl\n  }), jsx(LoadingDot, {\n    delay: 160,\n    offset: true\n  }), jsx(LoadingDot, {\n    delay: 320,\n    offset: !isRtl\n  }));\n};\nvar css$1 = function css(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    isFocused = _ref.isFocused,\n    _ref$theme = _ref.theme,\n    colors = _ref$theme.colors,\n    borderRadius = _ref$theme.borderRadius,\n    spacing = _ref$theme.spacing;\n  return _objectSpread({\n    label: 'control',\n    alignItems: 'center',\n    cursor: 'default',\n    display: 'flex',\n    flexWrap: 'wrap',\n    justifyContent: 'space-between',\n    minHeight: spacing.controlHeight,\n    outline: '0 !important',\n    position: 'relative',\n    transition: 'all 100ms'\n  }, unstyled ? {} : {\n    backgroundColor: isDisabled ? colors.neutral5 : colors.neutral0,\n    borderColor: isDisabled ? colors.neutral10 : isFocused ? colors.primary : colors.neutral20,\n    borderRadius: borderRadius,\n    borderStyle: 'solid',\n    borderWidth: 1,\n    boxShadow: isFocused ? \"0 0 0 1px \".concat(colors.primary) : undefined,\n    '&:hover': {\n      borderColor: isFocused ? colors.primary : colors.neutral30\n    }\n  });\n};\nvar Control = function Control(props) {\n  var children = props.children,\n    isDisabled = props.isDisabled,\n    isFocused = props.isFocused,\n    innerRef = props.innerRef,\n    innerProps = props.innerProps,\n    menuIsOpen = props.menuIsOpen;\n  return jsx(\"div\", _extends({\n    ref: innerRef\n  }, getStyleProps(props, 'control', {\n    control: true,\n    'control--is-disabled': isDisabled,\n    'control--is-focused': isFocused,\n    'control--menu-is-open': menuIsOpen\n  }), innerProps, {\n    \"aria-disabled\": isDisabled || undefined\n  }), children);\n};\nvar Control$1 = Control;\nvar _excluded$1 = [\"data\"];\nvar groupCSS = function groupCSS(_ref, unstyled) {\n  var spacing = _ref.theme.spacing;\n  return unstyled ? {} : {\n    paddingBottom: spacing.baseUnit * 2,\n    paddingTop: spacing.baseUnit * 2\n  };\n};\nvar Group = function Group(props) {\n  var children = props.children,\n    cx = props.cx,\n    getStyles = props.getStyles,\n    getClassNames = props.getClassNames,\n    Heading = props.Heading,\n    headingProps = props.headingProps,\n    innerProps = props.innerProps,\n    label = props.label,\n    theme = props.theme,\n    selectProps = props.selectProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'group', {\n    group: true\n  }), innerProps), jsx(Heading, _extends({}, headingProps, {\n    selectProps: selectProps,\n    theme: theme,\n    getStyles: getStyles,\n    getClassNames: getClassNames,\n    cx: cx\n  }), label), jsx(\"div\", null, children));\n};\nvar groupHeadingCSS = function groupHeadingCSS(_ref2, unstyled) {\n  var _ref2$theme = _ref2.theme,\n    colors = _ref2$theme.colors,\n    spacing = _ref2$theme.spacing;\n  return _objectSpread({\n    label: 'group',\n    cursor: 'default',\n    display: 'block'\n  }, unstyled ? {} : {\n    color: colors.neutral40,\n    fontSize: '75%',\n    fontWeight: 500,\n    marginBottom: '0.25em',\n    paddingLeft: spacing.baseUnit * 3,\n    paddingRight: spacing.baseUnit * 3,\n    textTransform: 'uppercase'\n  });\n};\nvar GroupHeading = function GroupHeading(props) {\n  var _cleanCommonProps = cleanCommonProps(props);\n  _cleanCommonProps.data;\n  var innerProps = _objectWithoutProperties(_cleanCommonProps, _excluded$1);\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'groupHeading', {\n    'group-heading': true\n  }), innerProps));\n};\nvar Group$1 = Group;\nvar _excluded = [\"innerRef\", \"isDisabled\", \"isHidden\", \"inputClassName\"];\nvar inputCSS = function inputCSS(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    value = _ref.value,\n    _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return _objectSpread(_objectSpread({\n    visibility: isDisabled ? 'hidden' : 'visible',\n    // force css to recompute when value change due to @emotion bug.\n    // We can remove it whenever the bug is fixed.\n    transform: value ? 'translateZ(0)' : ''\n  }, containerStyle), unstyled ? {} : {\n    margin: spacing.baseUnit / 2,\n    paddingBottom: spacing.baseUnit / 2,\n    paddingTop: spacing.baseUnit / 2,\n    color: colors.neutral80\n  });\n};\nvar spacingStyle = {\n  gridArea: '1 / 2',\n  font: 'inherit',\n  minWidth: '2px',\n  border: 0,\n  margin: 0,\n  outline: 0,\n  padding: 0\n};\nvar containerStyle = {\n  flex: '1 1 auto',\n  display: 'inline-grid',\n  gridArea: '1 / 1 / 2 / 3',\n  gridTemplateColumns: '0 min-content',\n  '&:after': _objectSpread({\n    content: 'attr(data-value) \" \"',\n    visibility: 'hidden',\n    whiteSpace: 'pre'\n  }, spacingStyle)\n};\nvar inputStyle = function inputStyle(isHidden) {\n  return _objectSpread({\n    label: 'input',\n    color: 'inherit',\n    background: 0,\n    opacity: isHidden ? 0 : 1,\n    width: '100%'\n  }, spacingStyle);\n};\nvar Input = function Input(props) {\n  var cx = props.cx,\n    value = props.value;\n  var _cleanCommonProps = cleanCommonProps(props),\n    innerRef = _cleanCommonProps.innerRef,\n    isDisabled = _cleanCommonProps.isDisabled,\n    isHidden = _cleanCommonProps.isHidden,\n    inputClassName = _cleanCommonProps.inputClassName,\n    innerProps = _objectWithoutProperties(_cleanCommonProps, _excluded);\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'input', {\n    'input-container': true\n  }), {\n    \"data-value\": value || ''\n  }), jsx(\"input\", _extends({\n    className: cx({\n      input: true\n    }, inputClassName),\n    ref: innerRef,\n    style: inputStyle(isHidden),\n    disabled: isDisabled\n  }, innerProps)));\n};\nvar Input$1 = Input;\nvar multiValueCSS = function multiValueCSS(_ref, unstyled) {\n  var _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    borderRadius = _ref$theme.borderRadius,\n    colors = _ref$theme.colors;\n  return _objectSpread({\n    label: 'multiValue',\n    display: 'flex',\n    minWidth: 0\n  }, unstyled ? {} : {\n    backgroundColor: colors.neutral10,\n    borderRadius: borderRadius / 2,\n    margin: spacing.baseUnit / 2\n  });\n};\nvar multiValueLabelCSS = function multiValueLabelCSS(_ref2, unstyled) {\n  var _ref2$theme = _ref2.theme,\n    borderRadius = _ref2$theme.borderRadius,\n    colors = _ref2$theme.colors,\n    cropWithEllipsis = _ref2.cropWithEllipsis;\n  return _objectSpread({\n    overflow: 'hidden',\n    textOverflow: cropWithEllipsis || cropWithEllipsis === undefined ? 'ellipsis' : undefined,\n    whiteSpace: 'nowrap'\n  }, unstyled ? {} : {\n    borderRadius: borderRadius / 2,\n    color: colors.neutral80,\n    fontSize: '85%',\n    padding: 3,\n    paddingLeft: 6\n  });\n};\nvar multiValueRemoveCSS = function multiValueRemoveCSS(_ref3, unstyled) {\n  var _ref3$theme = _ref3.theme,\n    spacing = _ref3$theme.spacing,\n    borderRadius = _ref3$theme.borderRadius,\n    colors = _ref3$theme.colors,\n    isFocused = _ref3.isFocused;\n  return _objectSpread({\n    alignItems: 'center',\n    display: 'flex'\n  }, unstyled ? {} : {\n    borderRadius: borderRadius / 2,\n    backgroundColor: isFocused ? colors.dangerLight : undefined,\n    paddingLeft: spacing.baseUnit,\n    paddingRight: spacing.baseUnit,\n    ':hover': {\n      backgroundColor: colors.dangerLight,\n      color: colors.danger\n    }\n  });\n};\nvar MultiValueGeneric = function MultiValueGeneric(_ref4) {\n  var children = _ref4.children,\n    innerProps = _ref4.innerProps;\n  return jsx(\"div\", innerProps, children);\n};\nvar MultiValueContainer = MultiValueGeneric;\nvar MultiValueLabel = MultiValueGeneric;\nfunction MultiValueRemove(_ref5) {\n  var children = _ref5.children,\n    innerProps = _ref5.innerProps;\n  return jsx(\"div\", _extends({\n    role: \"button\"\n  }, innerProps), children || jsx(CrossIcon, {\n    size: 14\n  }));\n}\nvar MultiValue = function MultiValue(props) {\n  var children = props.children,\n    components = props.components,\n    data = props.data,\n    innerProps = props.innerProps,\n    isDisabled = props.isDisabled,\n    removeProps = props.removeProps,\n    selectProps = props.selectProps;\n  var Container = components.Container,\n    Label = components.Label,\n    Remove = components.Remove;\n  return jsx(Container, {\n    data: data,\n    innerProps: _objectSpread(_objectSpread({}, getStyleProps(props, 'multiValue', {\n      'multi-value': true,\n      'multi-value--is-disabled': isDisabled\n    })), innerProps),\n    selectProps: selectProps\n  }, jsx(Label, {\n    data: data,\n    innerProps: _objectSpread({}, getStyleProps(props, 'multiValueLabel', {\n      'multi-value__label': true\n    })),\n    selectProps: selectProps\n  }, children), jsx(Remove, {\n    data: data,\n    innerProps: _objectSpread(_objectSpread({}, getStyleProps(props, 'multiValueRemove', {\n      'multi-value__remove': true\n    })), {}, {\n      'aria-label': \"Remove \".concat(children || 'option')\n    }, removeProps),\n    selectProps: selectProps\n  }));\n};\nvar MultiValue$1 = MultiValue;\nvar optionCSS = function optionCSS(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    isFocused = _ref.isFocused,\n    isSelected = _ref.isSelected,\n    _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return _objectSpread({\n    label: 'option',\n    cursor: 'default',\n    display: 'block',\n    fontSize: 'inherit',\n    width: '100%',\n    userSelect: 'none',\n    WebkitTapHighlightColor: 'rgba(0, 0, 0, 0)'\n  }, unstyled ? {} : {\n    backgroundColor: isSelected ? colors.primary : isFocused ? colors.primary25 : 'transparent',\n    color: isDisabled ? colors.neutral20 : isSelected ? colors.neutral0 : 'inherit',\n    padding: \"\".concat(spacing.baseUnit * 2, \"px \").concat(spacing.baseUnit * 3, \"px\"),\n    // provide some affordance on touch devices\n    ':active': {\n      backgroundColor: !isDisabled ? isSelected ? colors.primary : colors.primary50 : undefined\n    }\n  });\n};\nvar Option = function Option(props) {\n  var children = props.children,\n    isDisabled = props.isDisabled,\n    isFocused = props.isFocused,\n    isSelected = props.isSelected,\n    innerRef = props.innerRef,\n    innerProps = props.innerProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'option', {\n    option: true,\n    'option--is-disabled': isDisabled,\n    'option--is-focused': isFocused,\n    'option--is-selected': isSelected\n  }), {\n    ref: innerRef,\n    \"aria-disabled\": isDisabled\n  }, innerProps), children);\n};\nvar Option$1 = Option;\nvar placeholderCSS = function placeholderCSS(_ref, unstyled) {\n  var _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return _objectSpread({\n    label: 'placeholder',\n    gridArea: '1 / 1 / 2 / 3'\n  }, unstyled ? {} : {\n    color: colors.neutral50,\n    marginLeft: spacing.baseUnit / 2,\n    marginRight: spacing.baseUnit / 2\n  });\n};\nvar Placeholder = function Placeholder(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'placeholder', {\n    placeholder: true\n  }), innerProps), children);\n};\nvar Placeholder$1 = Placeholder;\nvar css = function css(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return _objectSpread({\n    label: 'singleValue',\n    gridArea: '1 / 1 / 2 / 3',\n    maxWidth: '100%',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap'\n  }, unstyled ? {} : {\n    color: isDisabled ? colors.neutral40 : colors.neutral80,\n    marginLeft: spacing.baseUnit / 2,\n    marginRight: spacing.baseUnit / 2\n  });\n};\nvar SingleValue = function SingleValue(props) {\n  var children = props.children,\n    isDisabled = props.isDisabled,\n    innerProps = props.innerProps;\n  return jsx(\"div\", _extends({}, getStyleProps(props, 'singleValue', {\n    'single-value': true,\n    'single-value--is-disabled': isDisabled\n  }), innerProps), children);\n};\nvar SingleValue$1 = SingleValue;\nvar components = {\n  ClearIndicator: ClearIndicator,\n  Control: Control$1,\n  DropdownIndicator: DropdownIndicator,\n  DownChevron: DownChevron,\n  CrossIcon: CrossIcon,\n  Group: Group$1,\n  GroupHeading: GroupHeading,\n  IndicatorsContainer: IndicatorsContainer,\n  IndicatorSeparator: IndicatorSeparator,\n  Input: Input$1,\n  LoadingIndicator: LoadingIndicator,\n  Menu: Menu$1,\n  MenuList: MenuList,\n  MenuPortal: MenuPortal,\n  LoadingMessage: LoadingMessage,\n  NoOptionsMessage: NoOptionsMessage,\n  MultiValue: MultiValue$1,\n  MultiValueContainer: MultiValueContainer,\n  MultiValueLabel: MultiValueLabel,\n  MultiValueRemove: MultiValueRemove,\n  Option: Option$1,\n  Placeholder: Placeholder$1,\n  SelectContainer: SelectContainer,\n  SingleValue: SingleValue$1,\n  ValueContainer: ValueContainer\n};\nvar defaultComponents = function defaultComponents(props) {\n  return _objectSpread(_objectSpread({}, components), props.components);\n};\nexport { isMobileDevice as A, multiValueAsValue as B, singleValueAsValue as C, valueTernary as D, classNames as E, defaultComponents as F, isDocumentElement as G, cleanValue as H, scrollIntoView as I, noop as J, notNullish as K, handleInputChange as L, MenuPlacer as M, clearIndicatorCSS as a, containerCSS as b, components as c, css$1 as d, dropdownIndicatorCSS as e, groupHeadingCSS as f, groupCSS as g, indicatorSeparatorCSS as h, indicatorsContainerCSS as i, inputCSS as j, loadingMessageCSS as k, loadingIndicatorCSS as l, menuCSS as m, menuListCSS as n, menuPortalCSS as o, multiValueCSS as p, multiValueLabelCSS as q, removeProps as r, supportsPassiveEvents as s, multiValueRemoveCSS as t, noOptionsMessageCSS as u, optionCSS as v, placeholderCSS as w, css as x, valueContainerCSS as y, isTouchCapable as z };", "map": {"version": 3, "names": ["_objectSpread"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\react-select\\dist\\indicators.tsx"], "sourcesContent": ["/** @jsx jsx */\nimport { JSX, ReactNode } from 'react';\nimport { jsx, keyframes } from '@emotion/react';\n\nimport {\n  CommonPropsAndClassName,\n  CSSObjectWithLabel,\n  GroupBase,\n} from '../types';\nimport { getStyleProps } from '../utils';\n\n// ==============================\n// Dropdown & Clear Icons\n// ==============================\n\nconst Svg = ({\n  size,\n  ...props\n}: JSX.IntrinsicElements['svg'] & { size: number }) => (\n  <svg\n    height={size}\n    width={size}\n    viewBox=\"0 0 20 20\"\n    aria-hidden=\"true\"\n    focusable=\"false\"\n    css={{\n      display: 'inline-block',\n      fill: 'currentColor',\n      lineHeight: 1,\n      stroke: 'currentColor',\n      strokeWidth: 0,\n    }}\n    {...props}\n  />\n);\n\nexport type CrossIconProps = JSX.IntrinsicElements['svg'] & { size?: number };\nexport const CrossIcon = (props: CrossIconProps) => (\n  <Svg size={20} {...props}>\n    <path d=\"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z\" />\n  </Svg>\n);\nexport type DownChevronProps = JSX.IntrinsicElements['svg'] & { size?: number };\nexport const DownChevron = (props: DownChevronProps) => (\n  <Svg size={20} {...props}>\n    <path d=\"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z\" />\n  </Svg>\n);\n\n// ==============================\n// Dropdown & Clear Buttons\n// ==============================\n\nexport interface DropdownIndicatorProps<\n  Option = unknown,\n  IsMulti extends boolean = boolean,\n  Group extends GroupBase<Option> = GroupBase<Option>\n> extends CommonPropsAndClassName<Option, IsMulti, Group> {\n  /** The children to be rendered inside the indicator. */\n  children?: ReactNode;\n  /** Props that will be passed on to the children. */\n  innerProps: JSX.IntrinsicElements['div'];\n  /** The focused state of the select. */\n  isFocused: boolean;\n  isDisabled: boolean;\n}\n\nconst baseCSS = <\n  Option,\n  IsMulti extends boolean,\n  Group extends GroupBase<Option>\n>(\n  {\n    isFocused,\n    theme: {\n      spacing: { baseUnit },\n      colors,\n    },\n  }:\n    | DropdownIndicatorProps<Option, IsMulti, Group>\n    | ClearIndicatorProps<Option, IsMulti, Group>,\n  unstyled: boolean\n): CSSObjectWithLabel => ({\n  label: 'indicatorContainer',\n  display: 'flex',\n  transition: 'color 150ms',\n  ...(unstyled\n    ? {}\n    : {\n        color: isFocused ? colors.neutral60 : colors.neutral20,\n        padding: baseUnit * 2,\n        ':hover': {\n          color: isFocused ? colors.neutral80 : colors.neutral40,\n        },\n      }),\n});\n\nexport const dropdownIndicatorCSS = baseCSS;\nexport const DropdownIndicator = <\n  Option,\n  IsMulti extends boolean,\n  Group extends GroupBase<Option>\n>(\n  props: DropdownIndicatorProps<Option, IsMulti, Group>\n) => {\n  const { children, innerProps } = props;\n  return (\n    <div\n      {...getStyleProps(props, 'dropdownIndicator', {\n        indicator: true,\n        'dropdown-indicator': true,\n      })}\n      {...innerProps}\n    >\n      {children || <DownChevron />}\n    </div>\n  );\n};\n\nexport interface ClearIndicatorProps<\n  Option = unknown,\n  IsMulti extends boolean = boolean,\n  Group extends GroupBase<Option> = GroupBase<Option>\n> extends CommonPropsAndClassName<Option, IsMulti, Group> {\n  /** The children to be rendered inside the indicator. */\n  children?: ReactNode;\n  /** Props that will be passed on to the children. */\n  innerProps: JSX.IntrinsicElements['div'];\n  /** The focused state of the select. */\n  isFocused: boolean;\n}\n\nexport const clearIndicatorCSS = baseCSS;\nexport const ClearIndicator = <\n  Option,\n  IsMulti extends boolean,\n  Group extends GroupBase<Option>\n>(\n  props: ClearIndicatorProps<Option, IsMulti, Group>\n) => {\n  const { children, innerProps } = props;\n  return (\n    <div\n      {...getStyleProps(props, 'clearIndicator', {\n        indicator: true,\n        'clear-indicator': true,\n      })}\n      {...innerProps}\n    >\n      {children || <CrossIcon />}\n    </div>\n  );\n};\n\n// ==============================\n// Separator\n// ==============================\n\nexport interface IndicatorSeparatorProps<\n  Option = unknown,\n  IsMulti extends boolean = boolean,\n  Group extends GroupBase<Option> = GroupBase<Option>\n> extends CommonPropsAndClassName<Option, IsMulti, Group> {\n  isDisabled: boolean;\n  isFocused: boolean;\n  innerProps?: JSX.IntrinsicElements['span'];\n}\n\nexport const indicatorSeparatorCSS = <\n  Option,\n  IsMulti extends boolean,\n  Group extends GroupBase<Option>\n>(\n  {\n    isDisabled,\n    theme: {\n      spacing: { baseUnit },\n      colors,\n    },\n  }: IndicatorSeparatorProps<Option, IsMulti, Group>,\n  unstyled: boolean\n): CSSObjectWithLabel => ({\n  label: 'indicatorSeparator',\n  alignSelf: 'stretch',\n  width: 1,\n  ...(unstyled\n    ? {}\n    : {\n        backgroundColor: isDisabled ? colors.neutral10 : colors.neutral20,\n        marginBottom: baseUnit * 2,\n        marginTop: baseUnit * 2,\n      }),\n});\n\nexport const IndicatorSeparator = <\n  Option,\n  IsMulti extends boolean,\n  Group extends GroupBase<Option>\n>(\n  props: IndicatorSeparatorProps<Option, IsMulti, Group>\n) => {\n  const { innerProps } = props;\n  return (\n    <span\n      {...innerProps}\n      {...getStyleProps(props, 'indicatorSeparator', {\n        'indicator-separator': true,\n      })}\n    />\n  );\n};\n\n// ==============================\n// Loading\n// ==============================\n\nconst loadingDotAnimations = keyframes`\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n`;\n\nexport const loadingIndicatorCSS = <\n  Option,\n  IsMulti extends boolean,\n  Group extends GroupBase<Option>\n>(\n  {\n    isFocused,\n    size,\n    theme: {\n      colors,\n      spacing: { baseUnit },\n    },\n  }: LoadingIndicatorProps<Option, IsMulti, Group>,\n  unstyled: boolean\n): CSSObjectWithLabel => ({\n  label: 'loadingIndicator',\n  display: 'flex',\n  transition: 'color 150ms',\n  alignSelf: 'center',\n  fontSize: size,\n  lineHeight: 1,\n  marginRight: size,\n  textAlign: 'center',\n  verticalAlign: 'middle',\n  ...(unstyled\n    ? {}\n    : {\n        color: isFocused ? colors.neutral60 : colors.neutral20,\n        padding: baseUnit * 2,\n      }),\n});\n\ninterface LoadingDotProps {\n  delay: number;\n  offset: boolean;\n}\nconst LoadingDot = ({ delay, offset }: LoadingDotProps) => (\n  <span\n    css={{\n      animation: `${loadingDotAnimations} 1s ease-in-out ${delay}ms infinite;`,\n      backgroundColor: 'currentColor',\n      borderRadius: '1em',\n      display: 'inline-block',\n      marginLeft: offset ? '1em' : undefined,\n      height: '1em',\n      verticalAlign: 'top',\n      width: '1em',\n    }}\n  />\n);\n\nexport interface LoadingIndicatorProps<\n  Option = unknown,\n  IsMulti extends boolean = boolean,\n  Group extends GroupBase<Option> = GroupBase<Option>\n> extends CommonPropsAndClassName<Option, IsMulti, Group> {\n  /** Props that will be passed on to the children. */\n  innerProps: JSX.IntrinsicElements['div'];\n  /** The focused state of the select. */\n  isFocused: boolean;\n  isDisabled: boolean;\n  /** Set size of the container. */\n  size: number;\n}\nexport const LoadingIndicator = <\n  Option,\n  IsMulti extends boolean,\n  Group extends GroupBase<Option>\n>({\n  innerProps,\n  isRtl,\n  size = 4,\n  ...restProps\n}: LoadingIndicatorProps<Option, IsMulti, Group>) => {\n  return (\n    <div\n      {...getStyleProps(\n        { ...restProps, innerProps, isRtl, size },\n        'loadingIndicator',\n        {\n          indicator: true,\n          'loading-indicator': true,\n        }\n      )}\n      {...innerProps}\n    >\n      <LoadingDot delay={0} offset={isRtl} />\n      <LoadingDot delay={160} offset />\n      <LoadingDot delay={320} offset={!isRtl} />\n    </div>\n  );\n};\n"], "mappings": "AAmQI,OAAAA,aAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}