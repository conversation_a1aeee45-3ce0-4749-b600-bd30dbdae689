{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\login_register\\\\VerifyCodeRegisterPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { Container, Form, Button, Card, Spinner } from \"react-bootstrap\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport Banner from \"../../../images/banner.jpg\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport { useLocation } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport AuthActions from \"../../../redux/auth/actions\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VerifyCodeRegisterPage = () => {\n  _s();\n  var _location$state;\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [verificationCode, setVerificationCode] = useState([\"\", \"\", \"\", \"\", \"\", \"\"]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isResending, setIsResending] = useState(false);\n  const inputRefs = useRef([]);\n  const location = useLocation();\n  const userEmail = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.email) || '';\n  useEffect(() => {\n    var _location$state2;\n    inputRefs.current = inputRefs.current.slice(0, 6);\n    if ((_location$state2 = location.state) !== null && _location$state2 !== void 0 && _location$state2.message) {\n      showToast.warning(location.state.message);\n    }\n  }, [location]);\n  const handleChange = (index, value) => {\n    // Only allow numbers\n    if (value && !/^\\d*$/.test(value)) return;\n\n    // Update the code array\n    const newCode = [...verificationCode];\n    newCode[index] = value.slice(0, 1); // Only take the first character\n    setVerificationCode(newCode);\n\n    // Auto-focus next input if current input is filled\n    if (value && index < 5) {\n      var _inputRefs$current;\n      (_inputRefs$current = inputRefs.current[index + 1]) === null || _inputRefs$current === void 0 ? void 0 : _inputRefs$current.focus();\n    }\n  };\n  const handleKeyDown = (index, e) => {\n    // Handle backspace\n    if (e.key === \"Backspace\") {\n      if (!verificationCode[index] && index > 0) {\n        var _inputRefs$current2;\n        // If current input is empty and backspace is pressed, focus previous input\n        (_inputRefs$current2 = inputRefs.current[index - 1]) === null || _inputRefs$current2 === void 0 ? void 0 : _inputRefs$current2.focus();\n      }\n    }\n  };\n  const handlePaste = e => {\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData(\"text\").trim();\n\n    // Check if pasted content is a number and has appropriate length\n    if (/^\\d+$/.test(pastedData)) {\n      const digits = pastedData.split(\"\").slice(0, 6);\n      const newCode = [...verificationCode];\n      digits.forEach((digit, index) => {\n        if (index < 6) newCode[index] = digit;\n      });\n      setVerificationCode(newCode);\n\n      // Focus the next empty input or the last input if all are filled\n      const nextEmptyIndex = newCode.findIndex(val => !val);\n      if (nextEmptyIndex !== -1) {\n        var _inputRefs$current$ne;\n        (_inputRefs$current$ne = inputRefs.current[nextEmptyIndex]) === null || _inputRefs$current$ne === void 0 ? void 0 : _inputRefs$current$ne.focus();\n      } else if (newCode[5]) {\n        var _inputRefs$current$;\n        (_inputRefs$current$ = inputRefs.current[5]) === null || _inputRefs$current$ === void 0 ? void 0 : _inputRefs$current$.focus();\n      }\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const code = verificationCode.join(\"\");\n    if (code.length !== 6) {\n      showToast.error(\"Vui lòng nhập đầy đủ 6 chữ số của mã xác thực\");\n      return;\n    }\n    setIsLoading(true);\n    dispatch({\n      type: AuthActions.VERIFY_EMAIL,\n      payload: {\n        data: {\n          code\n        },\n        onSuccess: data => {\n          setIsLoading(false);\n          navigate(Routers.LoginHotelPage, {\n            state: {\n              message: \"Tài khoản của bạn đã được xác thực. Bạn có thể đăng nhập ngay bây giờ.\"\n            }\n          });\n        },\n        onFailed: msg => {\n          setIsLoading(false);\n          showToast.error(msg);\n        },\n        onError: error => {\n          setIsLoading(false);\n          showToast.error(\"Lỗi khi xác thực email\");\n        }\n      }\n    });\n  };\n  const handleResendCode = () => {\n    if (!userEmail) {\n      showToast.error(\"Email bị thiếu. Vui lòng quay lại trang đăng ký.\");\n      return;\n    }\n    setIsResending(true);\n    dispatch({\n      type: AuthActions.RESEND_VERIFICATION,\n      payload: {\n        data: {\n          email: userEmail\n        },\n        onSuccess: data => {\n          setIsResending(false);\n          showToast.success(\"Mã xác thực mới đã được gửi đến email của bạn\");\n        },\n        onFailed: msg => {\n          setIsResending(false);\n          showToast.error(msg);\n        },\n        onError: error => {\n          setIsResending(false);\n          showToast.error(\"Không thể gửi lại mã xác thực\");\n        }\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 d-flex align-items-center justify-content-center py-5\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      className: \"position-relative\",\n      children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mx-auto shadow\",\n        style: {\n          maxWidth: \"800px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          className: \"p-4 p-md-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-center mb-2\",\n            children: \"X\\xE1c Th\\u1EF1c M\\xE3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted\",\n              children: \"M\\xE3 \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c g\\u1EEDi \\u0111\\u1EBFn email c\\u1EE7a b\\u1EA1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted\",\n              children: \"Vui l\\xF2ng ki\\u1EC3m tra email \\u0111\\u1EC3 nh\\u1EADn m\\xE3 x\\xE1c th\\u1EF1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                style: {\n                  fontWeight: 500\n                },\n                children: \"M\\xE3 X\\xE1c Th\\u1EF1c\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between gap-2 mt-3\",\n                children: verificationCode.map((digit, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"position-relative\",\n                  style: {\n                    flex: \"1\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Form.Control, {\n                    ref: el => inputRefs.current[index] = el,\n                    type: \"text\",\n                    value: digit,\n                    onChange: e => handleChange(index, e.target.value),\n                    onKeyDown: e => handleKeyDown(index, e),\n                    onPaste: index === 0 ? handlePaste : undefined,\n                    className: \"text-center py-2\",\n                    style: {\n                      width: \"100px\",\n                      height: \"48px\",\n                      borderColor: index === 0 && !digit ? \"#0d6efd\" : \"#dee2e6\",\n                      borderWidth: index === 0 && !digit ? \"2px\" : \"1px\"\n                    },\n                    maxLength: 1,\n                    autoFocus: index === 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-muted\",\n                children: \"Kh\\xF4ng nh\\u1EADn \\u0111\\u01B0\\u1EE3c m\\xE3? \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"link\",\n                className: \"text-decoration-none p-0\",\n                style: {\n                  cursor: \"pointer\"\n                },\n                onClick: handleResendCode,\n                disabled: isResending,\n                children: isResending ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this), \"\\u0110ang g\\u1EEDi...\"]\n                }, void 0, true) : \"Gửi lại mã\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              type: \"submit\",\n              className: \"w-100 py-2 mt-2\",\n              disabled: verificationCode.some(digit => !digit) || isLoading,\n              children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                  as: \"span\",\n                  animation: \"border\",\n                  size: \"sm\",\n                  role: \"status\",\n                  \"aria-hidden\": \"true\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this), \"\\u0110ang x\\xE1c th\\u1EF1c...\"]\n              }, void 0, true) : \"Xác Thực Mã\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(VerifyCodeRegisterPage, \"MgsZkkYujJKqKxgsxFHmZK2V8EE=\", false, function () {\n  return [useNavigate, useDispatch, useLocation];\n});\n_c = VerifyCodeRegisterPage;\nexport default VerifyCodeRegisterPage;\nvar _c;\n$RefreshReg$(_c, \"VerifyCodeRegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Container", "Form", "<PERSON><PERSON>", "Card", "Spinner", "Routers", "useNavigate", "Banner", "showToast", "ToastProvider", "useLocation", "useDispatch", "AuthActions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VerifyCodeRegisterPage", "_s", "_location$state", "navigate", "dispatch", "verificationCode", "setVerificationCode", "isLoading", "setIsLoading", "isResending", "setIsResending", "inputRefs", "location", "userEmail", "state", "email", "_location$state2", "current", "slice", "message", "warning", "handleChange", "index", "value", "test", "newCode", "_inputRefs$current", "focus", "handleKeyDown", "e", "key", "_inputRefs$current2", "handlePaste", "preventDefault", "pastedData", "clipboardData", "getData", "trim", "digits", "split", "for<PERSON>ach", "digit", "nextEmptyIndex", "findIndex", "val", "_inputRefs$current$ne", "_inputRefs$current$", "handleSubmit", "code", "join", "length", "error", "type", "VERIFY_EMAIL", "payload", "data", "onSuccess", "LoginHotelPage", "onFailed", "msg", "onError", "handleResendCode", "RESEND_VERIFICATION", "success", "className", "style", "backgroundImage", "backgroundSize", "backgroundPosition", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "Body", "onSubmit", "Group", "Label", "fontWeight", "map", "flex", "Control", "ref", "el", "onChange", "target", "onKeyDown", "onPaste", "undefined", "width", "height", "borderColor", "borderWidth", "max<PERSON><PERSON><PERSON>", "autoFocus", "variant", "cursor", "onClick", "disabled", "as", "animation", "size", "role", "some", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/login_register/VerifyCodeRegisterPage.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { Con<PERSON>er, Form, But<PERSON>, Card, Spinner } from \"react-bootstrap\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport AuthActions from \"../../../redux/auth/actions\";\r\n\r\nconst VerifyCodeRegisterPage = () => {\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const [verificationCode, setVerificationCode] = useState([\"\", \"\", \"\", \"\", \"\", \"\"]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isResending, setIsResending] = useState(false);\r\n  const inputRefs = useRef([]);\r\n  const location = useLocation();\r\n  const userEmail = location.state?.email || '';\r\n\r\n  useEffect(() => {\r\n    inputRefs.current = inputRefs.current.slice(0, 6);\r\n    if (location.state?.message) {\r\n      showToast.warning(location.state.message);\r\n    }\r\n  }, [location]);\r\n\r\n  const handleChange = (index, value) => {\r\n    // Only allow numbers\r\n    if (value && !/^\\d*$/.test(value)) return;\r\n\r\n    // Update the code array\r\n    const newCode = [...verificationCode];\r\n    newCode[index] = value.slice(0, 1); // Only take the first character\r\n    setVerificationCode(newCode);\r\n\r\n    // Auto-focus next input if current input is filled\r\n    if (value && index < 5) {\r\n      inputRefs.current[index + 1]?.focus();\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (index, e) => {\r\n    // Handle backspace\r\n    if (e.key === \"Backspace\") {\r\n      if (!verificationCode[index] && index > 0) {\r\n        // If current input is empty and backspace is pressed, focus previous input\r\n        inputRefs.current[index - 1]?.focus();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePaste = (e) => {\r\n    e.preventDefault();\r\n    const pastedData = e.clipboardData.getData(\"text\").trim();\r\n\r\n    // Check if pasted content is a number and has appropriate length\r\n    if (/^\\d+$/.test(pastedData)) {\r\n      const digits = pastedData.split(\"\").slice(0, 6);\r\n      const newCode = [...verificationCode];\r\n\r\n      digits.forEach((digit, index) => {\r\n        if (index < 6) newCode[index] = digit;\r\n      });\r\n\r\n      setVerificationCode(newCode);\r\n\r\n      // Focus the next empty input or the last input if all are filled\r\n      const nextEmptyIndex = newCode.findIndex((val) => !val);\r\n      if (nextEmptyIndex !== -1) {\r\n        inputRefs.current[nextEmptyIndex]?.focus();\r\n      } else if (newCode[5]) {\r\n        inputRefs.current[5]?.focus();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    const code = verificationCode.join(\"\");\r\n    \r\n    if (code.length !== 6) {\r\n      showToast.error(\"Vui lòng nhập đầy đủ 6 chữ số của mã xác thực\");\r\n      return;\r\n    }\r\n    \r\n    setIsLoading(true);\r\n    \r\n    dispatch({\r\n      type: AuthActions.VERIFY_EMAIL,\r\n      payload: {\r\n        data: { code },\r\n        onSuccess: (data) => {\r\n          setIsLoading(false);\r\n          navigate(Routers.LoginHotelPage, { \r\n            state: { message: \"Tài khoản của bạn đã được xác thực. Bạn có thể đăng nhập ngay bây giờ.\" }\r\n          });\r\n        },\r\n        onFailed: (msg) => {\r\n          setIsLoading(false);\r\n          showToast.error(msg);\r\n        },\r\n        onError: (error) => {\r\n          setIsLoading(false);\r\n          showToast.error(\"Lỗi khi xác thực email\");\r\n        },\r\n      },\r\n    });\r\n  };\r\n  \r\n  const handleResendCode = () => {\r\n    if (!userEmail) {\r\n      showToast.error(\"Email bị thiếu. Vui lòng quay lại trang đăng ký.\");\r\n      return;\r\n    }\r\n    \r\n    setIsResending(true);\r\n    \r\n    dispatch({\r\n      type: AuthActions.RESEND_VERIFICATION,\r\n      payload: {\r\n        data: { email: userEmail },\r\n        onSuccess: (data) => {\r\n          setIsResending(false);\r\n          showToast.success(\"Mã xác thực mới đã được gửi đến email của bạn\");\r\n        },\r\n        onFailed: (msg) => {\r\n          setIsResending(false);\r\n          showToast.error(msg);\r\n        },\r\n        onError: (error) => {\r\n          setIsResending(false);\r\n          showToast.error(\"Không thể gửi lại mã xác thực\");\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"min-vh-100 d-flex align-items-center justify-content-center py-5\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Container className=\"position-relative\">\r\n        <ToastProvider />\r\n        <Card className=\"mx-auto shadow\" style={{ maxWidth: \"800px\" }}>\r\n          <Card.Body className=\"p-4 p-md-5\">\r\n            <h2 className=\"text-center mb-2\">Xác Thực Mã</h2>\r\n            <div className=\"text-center mb-4\">\r\n              <span className=\"text-muted\">Mã đã được gửi đến email của bạn</span>\r\n              <br />\r\n              <span className=\"text-muted\">\r\n                Vui lòng kiểm tra email để nhận mã xác thực\r\n              </span>\r\n            </div>\r\n\r\n            <Form onSubmit={handleSubmit}>\r\n              <Form.Group className=\"mb-4\">\r\n                <Form.Label style={{ fontWeight: 500 }}>\r\n                  Mã Xác Thực\r\n                </Form.Label>\r\n                <div className=\"d-flex justify-content-between gap-2 mt-3\">\r\n                  {verificationCode.map((digit, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"position-relative\"\r\n                      style={{ flex: \"1\" }}\r\n                    >\r\n                      <Form.Control\r\n                        ref={(el) => (inputRefs.current[index] = el)}\r\n                        type=\"text\"\r\n                        value={digit}\r\n                        onChange={(e) => handleChange(index, e.target.value)}\r\n                        onKeyDown={(e) => handleKeyDown(index, e)}\r\n                        onPaste={index === 0 ? handlePaste : undefined}\r\n                        className=\"text-center py-2\"\r\n                        style={{\r\n                          width: \"100px\",\r\n                          height: \"48px\",\r\n                          borderColor:\r\n                            index === 0 && !digit ? \"#0d6efd\" : \"#dee2e6\",\r\n                          borderWidth: index === 0 && !digit ? \"2px\" : \"1px\",\r\n                        }}\r\n                        maxLength={1}\r\n                        autoFocus={index === 0}\r\n                      />\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </Form.Group>\r\n\r\n              <div className=\"text-center mb-3\">\r\n                <span className=\"text-muted\">Không nhận được mã? </span>\r\n                <Button\r\n                  variant=\"link\"\r\n                  className=\"text-decoration-none p-0\"\r\n                  style={{ cursor: \"pointer\" }}\r\n                  onClick={handleResendCode}\r\n                  disabled={isResending}\r\n                >\r\n                  {isResending ? (\r\n                    <>\r\n                      <Spinner\r\n                        as=\"span\"\r\n                        animation=\"border\"\r\n                        size=\"sm\"\r\n                        role=\"status\"\r\n                        aria-hidden=\"true\"\r\n                        className=\"me-1\"\r\n                      />\r\n                      Đang gửi...\r\n                    </>\r\n                  ) : (\r\n                    \"Gửi lại mã\"\r\n                  )}\r\n                </Button>\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"primary\"\r\n                type=\"submit\"\r\n                className=\"w-100 py-2 mt-2\"\r\n                disabled={verificationCode.some((digit) => !digit) || isLoading}\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <Spinner\r\n                      as=\"span\"\r\n                      animation=\"border\"\r\n                      size=\"sm\"\r\n                      role=\"status\"\r\n                      aria-hidden=\"true\"\r\n                      className=\"me-2\"\r\n                    />\r\n                    Đang xác thực...\r\n                  </>\r\n                ) : (\r\n                  \"Xác Thực Mã\"\r\n                )}\r\n              </Button>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </Container>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VerifyCodeRegisterPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;AACxE,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,WAAW,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACnC,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAClF,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM+B,SAAS,GAAG9B,MAAM,CAAC,EAAE,CAAC;EAC5B,MAAM+B,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,SAAS,GAAG,EAAAX,eAAA,GAAAU,QAAQ,CAACE,KAAK,cAAAZ,eAAA,uBAAdA,eAAA,CAAgBa,KAAK,KAAI,EAAE;EAE7CjC,SAAS,CAAC,MAAM;IAAA,IAAAkC,gBAAA;IACdL,SAAS,CAACM,OAAO,GAAGN,SAAS,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjD,KAAAF,gBAAA,GAAIJ,QAAQ,CAACE,KAAK,cAAAE,gBAAA,eAAdA,gBAAA,CAAgBG,OAAO,EAAE;MAC3B5B,SAAS,CAAC6B,OAAO,CAACR,QAAQ,CAACE,KAAK,CAACK,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMS,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrC;IACA,IAAIA,KAAK,IAAI,CAAC,OAAO,CAACC,IAAI,CAACD,KAAK,CAAC,EAAE;;IAEnC;IACA,MAAME,OAAO,GAAG,CAAC,GAAGpB,gBAAgB,CAAC;IACrCoB,OAAO,CAACH,KAAK,CAAC,GAAGC,KAAK,CAACL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpCZ,mBAAmB,CAACmB,OAAO,CAAC;;IAE5B;IACA,IAAIF,KAAK,IAAID,KAAK,GAAG,CAAC,EAAE;MAAA,IAAAI,kBAAA;MACtB,CAAAA,kBAAA,GAAAf,SAAS,CAACM,OAAO,CAACK,KAAK,GAAG,CAAC,CAAC,cAAAI,kBAAA,uBAA5BA,kBAAA,CAA8BC,KAAK,CAAC,CAAC;IACvC;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACN,KAAK,EAAEO,CAAC,KAAK;IAClC;IACA,IAAIA,CAAC,CAACC,GAAG,KAAK,WAAW,EAAE;MACzB,IAAI,CAACzB,gBAAgB,CAACiB,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAAA,IAAAS,mBAAA;QACzC;QACA,CAAAA,mBAAA,GAAApB,SAAS,CAACM,OAAO,CAACK,KAAK,GAAG,CAAC,CAAC,cAAAS,mBAAA,uBAA5BA,mBAAA,CAA8BJ,KAAK,CAAC,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMK,WAAW,GAAIH,CAAC,IAAK;IACzBA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMC,UAAU,GAAGL,CAAC,CAACM,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC;;IAEzD;IACA,IAAI,OAAO,CAACb,IAAI,CAACU,UAAU,CAAC,EAAE;MAC5B,MAAMI,MAAM,GAAGJ,UAAU,CAACK,KAAK,CAAC,EAAE,CAAC,CAACrB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/C,MAAMO,OAAO,GAAG,CAAC,GAAGpB,gBAAgB,CAAC;MAErCiC,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,EAAEnB,KAAK,KAAK;QAC/B,IAAIA,KAAK,GAAG,CAAC,EAAEG,OAAO,CAACH,KAAK,CAAC,GAAGmB,KAAK;MACvC,CAAC,CAAC;MAEFnC,mBAAmB,CAACmB,OAAO,CAAC;;MAE5B;MACA,MAAMiB,cAAc,GAAGjB,OAAO,CAACkB,SAAS,CAAEC,GAAG,IAAK,CAACA,GAAG,CAAC;MACvD,IAAIF,cAAc,KAAK,CAAC,CAAC,EAAE;QAAA,IAAAG,qBAAA;QACzB,CAAAA,qBAAA,GAAAlC,SAAS,CAACM,OAAO,CAACyB,cAAc,CAAC,cAAAG,qBAAA,uBAAjCA,qBAAA,CAAmClB,KAAK,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAIF,OAAO,CAAC,CAAC,CAAC,EAAE;QAAA,IAAAqB,mBAAA;QACrB,CAAAA,mBAAA,GAAAnC,SAAS,CAACM,OAAO,CAAC,CAAC,CAAC,cAAA6B,mBAAA,uBAApBA,mBAAA,CAAsBnB,KAAK,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMoB,YAAY,GAAIlB,CAAC,IAAK;IAC1BA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMe,IAAI,GAAG3C,gBAAgB,CAAC4C,IAAI,CAAC,EAAE,CAAC;IAEtC,IAAID,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;MACrB3D,SAAS,CAAC4D,KAAK,CAAC,+CAA+C,CAAC;MAChE;IACF;IAEA3C,YAAY,CAAC,IAAI,CAAC;IAElBJ,QAAQ,CAAC;MACPgD,IAAI,EAAEzD,WAAW,CAAC0D,YAAY;MAC9BC,OAAO,EAAE;QACPC,IAAI,EAAE;UAAEP;QAAK,CAAC;QACdQ,SAAS,EAAGD,IAAI,IAAK;UACnB/C,YAAY,CAAC,KAAK,CAAC;UACnBL,QAAQ,CAACf,OAAO,CAACqE,cAAc,EAAE;YAC/B3C,KAAK,EAAE;cAAEK,OAAO,EAAE;YAAyE;UAC7F,CAAC,CAAC;QACJ,CAAC;QACDuC,QAAQ,EAAGC,GAAG,IAAK;UACjBnD,YAAY,CAAC,KAAK,CAAC;UACnBjB,SAAS,CAAC4D,KAAK,CAACQ,GAAG,CAAC;QACtB,CAAC;QACDC,OAAO,EAAGT,KAAK,IAAK;UAClB3C,YAAY,CAAC,KAAK,CAAC;UACnBjB,SAAS,CAAC4D,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAChD,SAAS,EAAE;MACdtB,SAAS,CAAC4D,KAAK,CAAC,kDAAkD,CAAC;MACnE;IACF;IAEAzC,cAAc,CAAC,IAAI,CAAC;IAEpBN,QAAQ,CAAC;MACPgD,IAAI,EAAEzD,WAAW,CAACmE,mBAAmB;MACrCR,OAAO,EAAE;QACPC,IAAI,EAAE;UAAExC,KAAK,EAAEF;QAAU,CAAC;QAC1B2C,SAAS,EAAGD,IAAI,IAAK;UACnB7C,cAAc,CAAC,KAAK,CAAC;UACrBnB,SAAS,CAACwE,OAAO,CAAC,+CAA+C,CAAC;QACpE,CAAC;QACDL,QAAQ,EAAGC,GAAG,IAAK;UACjBjD,cAAc,CAAC,KAAK,CAAC;UACrBnB,SAAS,CAAC4D,KAAK,CAACQ,GAAG,CAAC;QACtB,CAAC;QACDC,OAAO,EAAGT,KAAK,IAAK;UAClBzC,cAAc,CAAC,KAAK,CAAC;UACrBnB,SAAS,CAAC4D,KAAK,CAAC,+BAA+B,CAAC;QAClD;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtD,OAAA;IACEmE,SAAS,EAAC,kEAAkE;IAC5EC,KAAK,EAAE;MACLC,eAAe,EAAE,OAAO5E,MAAM,GAAG;MACjC6E,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAC,QAAA,eAEFxE,OAAA,CAACd,SAAS;MAACiF,SAAS,EAAC,mBAAmB;MAAAK,QAAA,gBACtCxE,OAAA,CAACL,aAAa;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjB5E,OAAA,CAACX,IAAI;QAAC8E,SAAS,EAAC,gBAAgB;QAACC,KAAK,EAAE;UAAES,QAAQ,EAAE;QAAQ,CAAE;QAAAL,QAAA,eAC5DxE,OAAA,CAACX,IAAI,CAACyF,IAAI;UAACX,SAAS,EAAC,YAAY;UAAAK,QAAA,gBAC/BxE,OAAA;YAAImE,SAAS,EAAC,kBAAkB;YAAAK,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD5E,OAAA;YAAKmE,SAAS,EAAC,kBAAkB;YAAAK,QAAA,gBAC/BxE,OAAA;cAAMmE,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpE5E,OAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5E,OAAA;cAAMmE,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN5E,OAAA,CAACb,IAAI;YAAC4F,QAAQ,EAAE7B,YAAa;YAAAsB,QAAA,gBAC3BxE,OAAA,CAACb,IAAI,CAAC6F,KAAK;cAACb,SAAS,EAAC,MAAM;cAAAK,QAAA,gBAC1BxE,OAAA,CAACb,IAAI,CAAC8F,KAAK;gBAACb,KAAK,EAAE;kBAAEc,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAExC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5E,OAAA;gBAAKmE,SAAS,EAAC,2CAA2C;gBAAAK,QAAA,EACvDhE,gBAAgB,CAAC2E,GAAG,CAAC,CAACvC,KAAK,EAAEnB,KAAK,kBACjCzB,OAAA;kBAEEmE,SAAS,EAAC,mBAAmB;kBAC7BC,KAAK,EAAE;oBAAEgB,IAAI,EAAE;kBAAI,CAAE;kBAAAZ,QAAA,eAErBxE,OAAA,CAACb,IAAI,CAACkG,OAAO;oBACXC,GAAG,EAAGC,EAAE,IAAMzE,SAAS,CAACM,OAAO,CAACK,KAAK,CAAC,GAAG8D,EAAI;oBAC7ChC,IAAI,EAAC,MAAM;oBACX7B,KAAK,EAAEkB,KAAM;oBACb4C,QAAQ,EAAGxD,CAAC,IAAKR,YAAY,CAACC,KAAK,EAAEO,CAAC,CAACyD,MAAM,CAAC/D,KAAK,CAAE;oBACrDgE,SAAS,EAAG1D,CAAC,IAAKD,aAAa,CAACN,KAAK,EAAEO,CAAC,CAAE;oBAC1C2D,OAAO,EAAElE,KAAK,KAAK,CAAC,GAAGU,WAAW,GAAGyD,SAAU;oBAC/CzB,SAAS,EAAC,kBAAkB;oBAC5BC,KAAK,EAAE;sBACLyB,KAAK,EAAE,OAAO;sBACdC,MAAM,EAAE,MAAM;sBACdC,WAAW,EACTtE,KAAK,KAAK,CAAC,IAAI,CAACmB,KAAK,GAAG,SAAS,GAAG,SAAS;sBAC/CoD,WAAW,EAAEvE,KAAK,KAAK,CAAC,IAAI,CAACmB,KAAK,GAAG,KAAK,GAAG;oBAC/C,CAAE;oBACFqD,SAAS,EAAE,CAAE;oBACbC,SAAS,EAAEzE,KAAK,KAAK;kBAAE;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC,GArBGnD,KAAK;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsBP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAEb5E,OAAA;cAAKmE,SAAS,EAAC,kBAAkB;cAAAK,QAAA,gBAC/BxE,OAAA;gBAAMmE,SAAS,EAAC,YAAY;gBAAAK,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxD5E,OAAA,CAACZ,MAAM;gBACL+G,OAAO,EAAC,MAAM;gBACdhC,SAAS,EAAC,0BAA0B;gBACpCC,KAAK,EAAE;kBAAEgC,MAAM,EAAE;gBAAU,CAAE;gBAC7BC,OAAO,EAAErC,gBAAiB;gBAC1BsC,QAAQ,EAAE1F,WAAY;gBAAA4D,QAAA,EAErB5D,WAAW,gBACVZ,OAAA,CAAAE,SAAA;kBAAAsE,QAAA,gBACExE,OAAA,CAACV,OAAO;oBACNiH,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClBvC,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,yBAEJ;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5E,OAAA,CAACZ,MAAM;cACL+G,OAAO,EAAC,SAAS;cACjB5C,IAAI,EAAC,QAAQ;cACbY,SAAS,EAAC,iBAAiB;cAC3BmC,QAAQ,EAAE9F,gBAAgB,CAACmG,IAAI,CAAE/D,KAAK,IAAK,CAACA,KAAK,CAAC,IAAIlC,SAAU;cAAA8D,QAAA,EAE/D9D,SAAS,gBACRV,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,gBACExE,OAAA,CAACV,OAAO;kBACNiH,EAAE,EAAC,MAAM;kBACTC,SAAS,EAAC,QAAQ;kBAClBC,IAAI,EAAC,IAAI;kBACTC,IAAI,EAAC,QAAQ;kBACb,eAAY,MAAM;kBAClBvC,SAAS,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,iCAEJ;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACxE,EAAA,CAhPID,sBAAsB;EAAA,QACTX,WAAW,EACXK,WAAW,EAKXD,WAAW;AAAA;AAAAgH,EAAA,GAPxBzG,sBAAsB;AAkP5B,eAAeA,sBAAsB;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}