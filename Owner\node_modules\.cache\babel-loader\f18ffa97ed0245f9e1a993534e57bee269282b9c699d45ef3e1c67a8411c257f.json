{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\login_register\\\\ForgetPasswordHotelPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport { Container, Form, Button, Card } from 'react-bootstrap';\nimport { FaArrowLeft } from 'react-icons/fa';\nimport * as Routers from \"../../../utils/Routes\";\nimport Banner from '../../../images/banner.jpg';\nimport { useNavigate } from 'react-router-dom';\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport { useDispatch } from \"react-redux\";\nimport AuthActions from \"../../../redux/auth/actions\";\nimport Factories from '@redux/auth/factories';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ForgetPasswordHotelPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    phone: '',\n    email: '',\n    password: '',\n    rememberMe: false\n  });\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      const response = await Factories.forgetPassword({\n        email: formData.email\n      });\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n        setIsLoading(false);\n        navigate(Routers.VerifyCodeHotelPage, {\n          state: {\n            status: \"FORGET_PASSWORD\",\n            message: \"Mã xác thực đã được gửi đến email của bạn, vui lòng xác thực tại đây!\",\n            email: formData.email\n          }\n        });\n      }\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      setIsLoading(false);\n      // Ưu tiên lấy MsgNo nếu có, sau đó đến message, cuối cùng là mặc định\n      const msg = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.MsgNo) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Gửi email thất bại\";\n      console.error(\"Lỗi khi gửi email:\", error);\n      showToast.error(msg);\n    }\n  };\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 d-flex align-items-center justify-content-center py-5\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: 'cover',\n      backgroundPosition: 'center'\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      className: \"position-relative\",\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        className: \"mx-auto shadow\",\n        style: {\n          maxWidth: '800px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          className: \"p-4 p-md-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-center mb-2\",\n            children: \"Qu\\xEAn M\\u1EADt Kh\\u1EA9u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted\",\n              children: \"B\\u1EA1n \\u0111\\xE3 nh\\u1EDB m\\u1EADt kh\\u1EA9u? \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: Routers.LoginHotelPage,\n              className: \"text-decoration-none\",\n              children: \"\\u0110\\u0103ng nh\\u1EADp t\\u1EA1i \\u0111\\xE2y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                style: {\n                  fontWeight: 500\n                },\n                children: \"\\u0110\\u1ECBa Ch\\u1EC9 Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"email\",\n                placeholder: \"Nh\\u1EADp \\u0111\\u1ECBa ch\\u1EC9 email c\\u1EE7a b\\u1EA1n\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                className: \"w-100 py-2 mb-4\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              type: \"submit\",\n              className: \"w-100 py-2\",\n              disabled: isLoading,\n              children: isLoading ? \"Đang gửi...\" : \"Đặt Lại Mật Khẩu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(ForgetPasswordHotelPage, \"CRGwQf2wdYMoAwgNcbiIj4to6VA=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = ForgetPasswordHotelPage;\nexport default ForgetPasswordHotelPage;\nvar _c;\n$RefreshReg$(_c, \"ForgetPasswordHotelPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Form", "<PERSON><PERSON>", "Card", "FaArrowLeft", "Routers", "Banner", "useNavigate", "showToast", "ToastProvider", "useDispatch", "AuthActions", "Factories", "jsxDEV", "_jsxDEV", "ForgetPasswordHotelPage", "_s", "navigate", "dispatch", "showPassword", "setShowPassword", "isLoading", "setIsLoading", "formData", "setFormData", "username", "phone", "email", "password", "rememberMe", "handleChange", "e", "name", "value", "type", "checked", "target", "handleSubmit", "preventDefault", "response", "forgetPassword", "status", "VerifyCodeHotelPage", "state", "message", "error", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "msg", "data", "MsgNo", "console", "togglePasswordVisibility", "className", "style", "backgroundImage", "backgroundSize", "backgroundPosition", "children", "max<PERSON><PERSON><PERSON>", "Body", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "LoginHotelPage", "onSubmit", "Group", "Label", "fontWeight", "Control", "placeholder", "onChange", "required", "variant", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/login_register/ForgetPasswordHotelPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport { Container, Form, Button, Card } from 'react-bootstrap';\r\nimport { FaArrowLeft } from 'react-icons/fa';\r\nimport * as Routers from \"../../../utils/Routes\"\r\nimport Banner from '../../../images/banner.jpg';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport AuthActions from \"../../../redux/auth/actions\";\r\nimport Factories from '@redux/auth/factories';\r\n\r\nconst ForgetPasswordHotelPage = () => {\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    username: '',\r\n    phone: '',\r\n    email: '',\r\n    password: '',\r\n    rememberMe: false\r\n  });\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await Factories.forgetPassword({\r\n        email: formData.email,\r\n      });\r\n      if (response?.status === 200) {\r\n        setIsLoading(false);\r\n        navigate(Routers.VerifyCodeHotelPage, {\r\n          state: {\r\n            status: \"FORGET_PASSWORD\",\r\n            message: \"Mã xác thực đã được gửi đến email của bạn, vui lòng xác thực tại đây!\",\r\n            email: formData.email\r\n          },\r\n        });\r\n      }\r\n    \r\n    } catch (error) {\r\n      setIsLoading(false);\r\n      // Ưu tiên lấy MsgNo nếu có, sau đó đến message, cuối cùng là mặc định\r\n      const msg =\r\n          error.response?.data?.MsgNo ||\r\n          error.response?.data?.message ||\r\n          \"Gửi email thất bại\";\r\n      console.error(\"Lỗi khi gửi email:\", error);\r\n      showToast.error(msg);\r\n    }\r\n  };\r\n\r\n  const togglePasswordVisibility = () => {\r\n    setShowPassword(!showPassword);\r\n  };\r\n\r\n  return (\r\n    <div \r\n      className=\"min-vh-100 d-flex align-items-center justify-content-center py-5\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: 'cover',\r\n        backgroundPosition: 'center'\r\n      }}\r\n    >\r\n      <Container className=\"position-relative\">\r\n        <Card className=\"mx-auto shadow\" style={{ maxWidth: '800px' }}>\r\n          <Card.Body className=\"p-4 p-md-5\">\r\n            <h2 className=\"text-center mb-2\">Quên Mật Khẩu</h2>\r\n            <div className=\"text-center\">\r\n                <span className=\"text-muted\">Bạn đã nhớ mật khẩu? </span>\r\n                <a href={Routers.LoginHotelPage} className=\"text-decoration-none\">Đăng nhập tại đây</a>\r\n              </div>\r\n            <Form onSubmit={handleSubmit}>\r\n              <Form.Group className=\"mb-4\">\r\n                <Form.Label style={{fontWeight: 500}}>Địa Chỉ Email</Form.Label>\r\n                <Form.Control\r\n                  type=\"email\"\r\n                  placeholder=\"Nhập địa chỉ email của bạn\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  className=\"w-100 py-2 mb-4\"\r\n                  required\r\n                />\r\n              </Form.Group>\r\n\r\n              <Button \r\n                variant=\"primary\" \r\n                type=\"submit\" \r\n                className=\"w-100 py-2\"\r\n                disabled={isLoading}\r\n              >\r\n                {isLoading ? \"Đang gửi...\" : \"Đặt Lại Mật Khẩu\"}\r\n              </Button>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n        <ToastProvider/>\r\n      </Container>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ForgetPasswordHotelPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,sCAAsC;AAC7C,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,QAAQ,iBAAiB;AAC/D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,SAAS,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC;IACvC0B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/CZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACS,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBhB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM3B,SAAS,CAAC4B,cAAc,CAAC;QAC9Cb,KAAK,EAAEJ,QAAQ,CAACI;MAClB,CAAC,CAAC;MACF,IAAI,CAAAY,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;QAC5BnB,YAAY,CAAC,KAAK,CAAC;QACnBL,QAAQ,CAACZ,OAAO,CAACqC,mBAAmB,EAAE;UACpCC,KAAK,EAAE;YACLF,MAAM,EAAE,iBAAiB;YACzBG,OAAO,EAAE,uEAAuE;YAChFjB,KAAK,EAAEJ,QAAQ,CAACI;UAClB;QACF,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd3B,YAAY,CAAC,KAAK,CAAC;MACnB;MACA,MAAM4B,GAAG,GACL,EAAAJ,eAAA,GAAAD,KAAK,CAACN,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBK,IAAI,cAAAJ,oBAAA,uBAApBA,oBAAA,CAAsBK,KAAK,OAAAJ,gBAAA,GAC3BH,KAAK,CAACN,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBG,IAAI,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsBL,OAAO,KAC7B,oBAAoB;MACxBS,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CrC,SAAS,CAACqC,KAAK,CAACK,GAAG,CAAC;IACtB;EACF,CAAC;EAED,MAAMI,wBAAwB,GAAGA,CAAA,KAAM;IACrClC,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,oBACEL,OAAA;IACEyC,SAAS,EAAC,kEAAkE;IAC5EC,KAAK,EAAE;MACLC,eAAe,EAAE,OAAOnD,MAAM,GAAG;MACjCoD,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAC,QAAA,eAEF9C,OAAA,CAACd,SAAS;MAACuD,SAAS,EAAC,mBAAmB;MAAAK,QAAA,gBACtC9C,OAAA,CAACX,IAAI;QAACoD,SAAS,EAAC,gBAAgB;QAACC,KAAK,EAAE;UAAEK,QAAQ,EAAE;QAAQ,CAAE;QAAAD,QAAA,eAC5D9C,OAAA,CAACX,IAAI,CAAC2D,IAAI;UAACP,SAAS,EAAC,YAAY;UAAAK,QAAA,gBAC/B9C,OAAA;YAAIyC,SAAS,EAAC,kBAAkB;YAAAK,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDpD,OAAA;YAAKyC,SAAS,EAAC,aAAa;YAAAK,QAAA,gBACxB9C,OAAA;cAAMyC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDpD,OAAA;cAAGqD,IAAI,EAAE9D,OAAO,CAAC+D,cAAe;cAACb,SAAS,EAAC,sBAAsB;cAAAK,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACRpD,OAAA,CAACb,IAAI;YAACoE,QAAQ,EAAEhC,YAAa;YAAAuB,QAAA,gBAC3B9C,OAAA,CAACb,IAAI,CAACqE,KAAK;cAACf,SAAS,EAAC,MAAM;cAAAK,QAAA,gBAC1B9C,OAAA,CAACb,IAAI,CAACsE,KAAK;gBAACf,KAAK,EAAE;kBAACgB,UAAU,EAAE;gBAAG,CAAE;gBAAAZ,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChEpD,OAAA,CAACb,IAAI,CAACwE,OAAO;gBACXvC,IAAI,EAAC,OAAO;gBACZwC,WAAW,EAAC,0DAA4B;gBACxC1C,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAEV,QAAQ,CAACI,KAAM;gBACtBgD,QAAQ,EAAE7C,YAAa;gBACvByB,SAAS,EAAC,iBAAiB;gBAC3BqB,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAEbpD,OAAA,CAACZ,MAAM;cACL2E,OAAO,EAAC,SAAS;cACjB3C,IAAI,EAAC,QAAQ;cACbqB,SAAS,EAAC,YAAY;cACtBuB,QAAQ,EAAEzD,SAAU;cAAAuC,QAAA,EAEnBvC,SAAS,GAAG,aAAa,GAAG;YAAkB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACPpD,OAAA,CAACL,aAAa;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAClD,EAAA,CArGID,uBAAuB;EAAA,QACVR,WAAW,EACXG,WAAW;AAAA;AAAAqE,EAAA,GAFxBhE,uBAAuB;AAuG7B,eAAeA,uBAAuB;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}