{"ast": null, "code": "const BannedPage = '/banned';\nconst ErrorPage = '/error';\nconst WaitPendingPage = '/waitpending';\n\n// Hotel Host\nconst Transaction = '/Transaction';\nconst BookingSchedule = '/BookingSchedule';\nconst TransactionDetail = '/TransactionDetail';\nconst HomeHotel = '/';\nconst BookingRegistration = '/BookingRegistration';\nconst BookingPropertyName = '/BookingPropertyName';\nconst BookingPropertyLocation = '/BookingPropertyLocation';\nconst BookingPropertyFacility = '/BookingPropertyFacility';\nconst BookingPropertyCheckInOut = '/BookingPropertyCheckInOut';\nconst BookingPropertyDescription = '/BookingPropertyDescription';\nconst BookingPropertyChecklist = '/BookingPropertyChecklist';\nconst LoginHotelPage = '/login_hotelhost';\nconst RegisterHotelPage = '/register_hotelhost';\nconst ForgetPasswordHotelPage = '/forgetpassword_hotelhost';\nconst VerifyCodeHotelPage = '/verifycode_hotelhost';\nconst ResetPasswordHotelPage = '/resetpassword_hotelhost';\nconst MyAccountHotelPage = '/myaccount_hotelhost';\nconst ListFeedbackHotelPage = '/feedback_hotelhost';\nconst ReportedFeedbackHotel = '/reportedfeedback_hotelhost';\nconst DocumentUpload = '/documentupload_hotelhost';\nconst DataAnalysisAI = '/data';\nconst CreateRoom = '/CreateRoom';\nconst RoomNamingForm = '/RoomNamingForm';\nconst PricingSetupForm = '/PricingSetupForm';\nconst RoomImageForm = '/RoomImageForm';\nconst RoomListingPage = '/RoomListingPage';\nconst AdditionalServicesPage = '/AdditionalServicesPage';\nconst Room = '/Room';\nconst HotelManagement = '/HotelManagement';\nconst VerifyCodeRegisterPage = '/verify_regiter';\nconst CreateService = '/CreateService';\nexport {\n// Common\nCreateService, BannedPage, ErrorPage, WaitPendingPage,\n// Hotel Host\nTransaction, BookingSchedule, TransactionDetail, HomeHotel, BookingRegistration, BookingPropertyName, BookingPropertyLocation, BookingPropertyFacility, BookingPropertyCheckInOut, BookingPropertyDescription, BookingPropertyChecklist, HotelManagement, LoginHotelPage, VerifyCodeRegisterPage, RegisterHotelPage, ForgetPasswordHotelPage, VerifyCodeHotelPage, ResetPasswordHotelPage, MyAccountHotelPage, ListFeedbackHotelPage, ReportedFeedbackHotel, DocumentUpload, DataAnalysisAI,\n// Newly Added\nCreateRoom, RoomNamingForm, PricingSetupForm, RoomImageForm, RoomListingPage, AdditionalServicesPage, Room };", "map": {"version": 3, "names": ["BannedPage", "ErrorPage", "WaitPendingPage", "Transaction", "BookingSchedule", "TransactionDetail", "HomeHotel", "BookingRegistration", "BookingPropertyName", "BookingPropertyLocation", "BookingPropertyFacility", "BookingPropertyCheckInOut", "BookingPropertyDescription", "BookingPropertyChecklist", "LoginHotelPage", "RegisterHotelPage", "ForgetPasswordHotelPage", "VerifyCodeHotelPage", "ResetPasswordHotelPage", "MyAccountHotelPage", "ListFeedbackHotelPage", "ReportedFeedbackHotel", "DocumentUpload", "DataAnalysisAI", "CreateRoom", "RoomNamingForm", "PricingSetupForm", "RoomImageForm", "RoomListingPage", "AdditionalServicesPage", "Room", "HotelManagement", "VerifyCodeRegisterPage", "CreateService"], "sources": ["E:/WDP301_UROOM/Owner/src/utils/Routes.js"], "sourcesContent": ["const BannedPage = '/banned';\r\nconst ErrorPage = '/error';\r\nconst WaitPendingPage = '/waitpending';\r\n\r\n// Hotel Host\r\nconst Transaction = '/Transaction';\r\nconst BookingSchedule = '/BookingSchedule';\r\nconst TransactionDetail = '/TransactionDetail';\r\nconst HomeHotel = '/';\r\nconst BookingRegistration = '/BookingRegistration';\r\nconst BookingPropertyName = '/BookingPropertyName';\r\nconst BookingPropertyLocation = '/BookingPropertyLocation';\r\nconst BookingPropertyFacility = '/BookingPropertyFacility';\r\nconst BookingPropertyCheckInOut = '/BookingPropertyCheckInOut';\r\nconst BookingPropertyDescription = '/BookingPropertyDescription';\r\nconst BookingPropertyChecklist = '/BookingPropertyChecklist';\r\n\r\nconst LoginHotelPage = '/login_hotelhost';\r\nconst RegisterHotelPage = '/register_hotelhost';\r\nconst ForgetPasswordHotelPage = '/forgetpassword_hotelhost';\r\nconst VerifyCodeHotelPage = '/verifycode_hotelhost';\r\nconst ResetPasswordHotelPage = '/resetpassword_hotelhost';\r\nconst MyAccountHotelPage = '/myaccount_hotelhost';\r\nconst ListFeedbackHotelPage = '/feedback_hotelhost';\r\nconst ReportedFeedbackHotel = '/reportedfeedback_hotelhost';\r\nconst DocumentUpload = '/documentupload_hotelhost';\r\nconst DataAnalysisAI = '/data';\r\nconst CreateRoom = '/CreateRoom';\r\nconst RoomNamingForm = '/RoomNamingForm';\r\nconst PricingSetupForm = '/PricingSetupForm';\r\nconst RoomImageForm = '/RoomImageForm';\r\nconst RoomListingPage = '/RoomListingPage';\r\nconst AdditionalServicesPage = '/AdditionalServicesPage';\r\nconst Room= '/Room';\r\nconst HotelManagement= '/HotelManagement';\r\nconst VerifyCodeRegisterPage= '/verify_regiter';\r\nconst CreateService = '/CreateService';\r\n\r\nexport {\r\n  // Common\r\n  CreateService,\r\n  BannedPage,\r\n  ErrorPage,\r\n  WaitPendingPage,\r\n  // Hotel Host\r\n  Transaction,\r\n  BookingSchedule,\r\n  TransactionDetail,\r\n  HomeHotel,\r\n  BookingRegistration,\r\n  BookingPropertyName,\r\n  BookingPropertyLocation,\r\n  BookingPropertyFacility,\r\n  BookingPropertyCheckInOut,\r\n  BookingPropertyDescription,\r\n  BookingPropertyChecklist,\r\n  HotelManagement,\r\n  \r\n  LoginHotelPage,\r\n  VerifyCodeRegisterPage,\r\n  RegisterHotelPage,\r\n  ForgetPasswordHotelPage,\r\n  VerifyCodeHotelPage,\r\n  ResetPasswordHotelPage,\r\n  MyAccountHotelPage,\r\n  ListFeedbackHotelPage,\r\n  ReportedFeedbackHotel,\r\n  DocumentUpload,\r\n  DataAnalysisAI,\r\n\r\n  // Newly Added\r\n  CreateRoom,\r\n  RoomNamingForm,\r\n  PricingSetupForm,\r\n  RoomImageForm,\r\n  RoomListingPage,\r\n  AdditionalServicesPage,\r\n  Room,\r\n};"], "mappings": "AAAA,MAAMA,UAAU,GAAG,SAAS;AAC5B,MAAMC,SAAS,GAAG,QAAQ;AAC1B,MAAMC,eAAe,GAAG,cAAc;;AAEtC;AACA,MAAMC,WAAW,GAAG,cAAc;AAClC,MAAMC,eAAe,GAAG,kBAAkB;AAC1C,MAAMC,iBAAiB,GAAG,oBAAoB;AAC9C,MAAMC,SAAS,GAAG,GAAG;AACrB,MAAMC,mBAAmB,GAAG,sBAAsB;AAClD,MAAMC,mBAAmB,GAAG,sBAAsB;AAClD,MAAMC,uBAAuB,GAAG,0BAA0B;AAC1D,MAAMC,uBAAuB,GAAG,0BAA0B;AAC1D,MAAMC,yBAAyB,GAAG,4BAA4B;AAC9D,MAAMC,0BAA0B,GAAG,6BAA6B;AAChE,MAAMC,wBAAwB,GAAG,2BAA2B;AAE5D,MAAMC,cAAc,GAAG,kBAAkB;AACzC,MAAMC,iBAAiB,GAAG,qBAAqB;AAC/C,MAAMC,uBAAuB,GAAG,2BAA2B;AAC3D,MAAMC,mBAAmB,GAAG,uBAAuB;AACnD,MAAMC,sBAAsB,GAAG,0BAA0B;AACzD,MAAMC,kBAAkB,GAAG,sBAAsB;AACjD,MAAMC,qBAAqB,GAAG,qBAAqB;AACnD,MAAMC,qBAAqB,GAAG,6BAA6B;AAC3D,MAAMC,cAAc,GAAG,2BAA2B;AAClD,MAAMC,cAAc,GAAG,OAAO;AAC9B,MAAMC,UAAU,GAAG,aAAa;AAChC,MAAMC,cAAc,GAAG,iBAAiB;AACxC,MAAMC,gBAAgB,GAAG,mBAAmB;AAC5C,MAAMC,aAAa,GAAG,gBAAgB;AACtC,MAAMC,eAAe,GAAG,kBAAkB;AAC1C,MAAMC,sBAAsB,GAAG,yBAAyB;AACxD,MAAMC,IAAI,GAAE,OAAO;AACnB,MAAMC,eAAe,GAAE,kBAAkB;AACzC,MAAMC,sBAAsB,GAAE,iBAAiB;AAC/C,MAAMC,aAAa,GAAG,gBAAgB;AAEtC;AACE;AACAA,aAAa,EACbjC,UAAU,EACVC,SAAS,EACTC,eAAe;AACf;AACAC,WAAW,EACXC,eAAe,EACfC,iBAAiB,EACjBC,SAAS,EACTC,mBAAmB,EACnBC,mBAAmB,EACnBC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,wBAAwB,EACxBkB,eAAe,EAEfjB,cAAc,EACdkB,sBAAsB,EACtBjB,iBAAiB,EACjBC,uBAAuB,EACvBC,mBAAmB,EACnBC,sBAAsB,EACtBC,kBAAkB,EAClBC,qBAAqB,EACrBC,qBAAqB,EACrBC,cAAc,EACdC,cAAc;AAEd;AACAC,UAAU,EACVC,cAAc,EACdC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,sBAAsB,EACtBC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}