{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Admin\\\\src\\\\pages\\\\dashboard\\\\DashboardPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { Line, Bar, Pie, Doughnut } from \"react-chartjs-2\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement);\nconst DashboardPage = () => {\n  _s();\n  var _dashboardData$revenu, _dashboardData$revenu2, _dashboardData$hotelD, _dashboardData$hotelD2, _dashboardData$hotelC, _dashboardData$hotelC2;\n  const dispatch = useDispatch();\n  const {\n    data: dashboardData,\n    loading,\n    error\n  } = useSelector(state => state.AdminDashboard);\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n\n  // Fetch dashboard data on component mount and when period changes\n  useEffect(() => {\n    dispatch({\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\n      payload: {\n        params: {\n          period: selectedPeriod\n        },\n        onSuccess: data => {\n          console.log('Dashboard data loaded successfully:', data);\n        },\n        onFailed: error => {\n          console.error('Failed to load dashboard data:', error);\n        }\n      }\n    });\n  }, [dispatch, selectedPeriod]);\n\n  // Handle period change\n  const handlePeriodChange = period => {\n    setSelectedPeriod(period);\n  };\n\n  // Chart empty state component\n  const ChartEmptyState = ({\n    icon,\n    message\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex align-items-center justify-content-center h-100 text-muted\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `bi ${icon} fs-1 d-block mb-2`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n\n  // Format revenue for display\n  const formatRevenue = revenue => {\n    if (revenue >= 1000000) {\n      return (revenue / 1000000).toFixed(1) + 'M';\n    } else if (revenue >= 1000) {\n      return (revenue / 1000).toFixed(1) + 'K';\n    }\n    return (revenue === null || revenue === void 0 ? void 0 : revenue.toLocaleString()) || '0';\n  };\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          height: '400px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"alert-heading\",\n          children: \"L\\u1ED7i!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-danger\",\n          onClick: () => handlePeriodChange(selectedPeriod),\n          children: \"Th\\u1EED l\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"T\\u1ED5ng quan h\\u1EC7 th\\u1ED1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-filter\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select\",\n            value: selectedPeriod,\n            onChange: e => handlePeriodChange(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"day\",\n              children: \"H\\xF4m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"week\",\n              children: \"Tu\\u1EA7n n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"month\",\n              children: \"Th\\xE1ng n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"year\",\n              children: \"N\\u0103m nay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-secondary me-2\",\n          onClick: () => handlePeriodChange(selectedPeriod),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-arrow-clockwise\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), \" L\\xE0m m\\u1EDBi\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), \" Xu\\u1EA5t b\\xE1o c\\xE1o\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon hotels\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-building\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.activeHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xE1ch s\\u1EA1n ho\\u1EA1t \\u0111\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon active\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-check-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalCustomers || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon customers\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-people\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalOwners || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u1EE7 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon owners\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-person-badge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Doanh thu h\\u1EC7 th\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"btn-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Ng\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"Tu\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-primary\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-sm btn-outline-secondary\",\n              children: \"N\\u0103m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-body\",\n        children: ((_dashboardData$revenu = dashboardData.revenueData) === null || _dashboardData$revenu === void 0 ? void 0 : (_dashboardData$revenu2 = _dashboardData$revenu.labels) === null || _dashboardData$revenu2 === void 0 ? void 0 : _dashboardData$revenu2.length) > 0 ? /*#__PURE__*/_jsxDEV(Line, {\n          data: dashboardData.revenueData,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                position: \"top\"\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: false,\n                grid: {\n                  drawBorder: false\n                },\n                ticks: {\n                  callback: value => formatRevenue(value)\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n          icon: \"bi-graph-up\",\n          message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u doanh thu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n b\\u1ED1 kh\\xE1ch s\\u1EA1n theo khu v\\u1EF1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelD = dashboardData.hotelDistributionData) === null || _dashboardData$hotelD === void 0 ? void 0 : (_dashboardData$hotelD2 = _dashboardData$hotelD.labels) === null || _dashboardData$hotelD2 === void 0 ? void 0 : _dashboardData$hotelD2.length) > 0 ? /*#__PURE__*/_jsxDEV(Doughnut, {\n            data: dashboardData.hotelDistributionData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\",\n                  labels: {\n                    generateLabels: function (chart) {\n                      const data = chart.data;\n                      if (data.labels.length && data.datasets.length) {\n                        const dataset = data.datasets[0];\n                        const total = dataset.data.reduce((sum, value) => sum + value, 0);\n                        return data.labels.map((label, i) => {\n                          var _dataset$borderColor;\n                          const value = dataset.data[i];\n                          const percentage = (value / total * 100).toFixed(1);\n                          return {\n                            text: `${label}: ${value} (${percentage}%)`,\n                            fillStyle: dataset.backgroundColor[i],\n                            strokeStyle: ((_dataset$borderColor = dataset.borderColor) === null || _dataset$borderColor === void 0 ? void 0 : _dataset$borderColor[i]) || '#fff',\n                            lineWidth: 2,\n                            hidden: false,\n                            index: i\n                          };\n                        });\n                      }\n                      return [];\n                    }\n                  }\n                },\n                tooltip: {\n                  callbacks: {\n                    label: function (context) {\n                      const label = context.label || '';\n                      const value = context.parsed;\n                      const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\n                      const percentage = (value / total * 100).toFixed(1);\n                      return `${label}: ${value} khách sạn (${percentage}%)`;\n                    }\n                  }\n                }\n              },\n              cutout: \"70%\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n b\\u1ED1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelC = dashboardData.hotelCategoryData) === null || _dashboardData$hotelC === void 0 ? void 0 : (_dashboardData$hotelC2 = _dashboardData$hotelC.labels) === null || _dashboardData$hotelC2 === void 0 ? void 0 : _dashboardData$hotelC2.length) > 0 ? /*#__PURE__*/_jsxDEV(Pie, {\n            data: dashboardData.hotelCategoryData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\",\n                  labels: {\n                    generateLabels: function (chart) {\n                      const data = chart.data;\n                      if (data.labels.length && data.datasets.length) {\n                        const dataset = data.datasets[0];\n                        const total = dataset.data.reduce((sum, value) => sum + value, 0);\n                        return data.labels.map((label, i) => {\n                          var _dataset$borderColor2;\n                          const value = dataset.data[i];\n                          const percentage = (value / total * 100).toFixed(1);\n                          return {\n                            text: `${label}: ${value} (${percentage}%)`,\n                            fillStyle: dataset.backgroundColor[i],\n                            strokeStyle: ((_dataset$borderColor2 = dataset.borderColor) === null || _dataset$borderColor2 === void 0 ? void 0 : _dataset$borderColor2[i]) || '#fff',\n                            lineWidth: 2,\n                            hidden: false,\n                            index: i\n                          };\n                        });\n                      }\n                      return [];\n                    }\n                  }\n                },\n                tooltip: {\n                  callbacks: {\n                    label: function (context) {\n                      const label = context.label || '';\n                      const value = context.parsed;\n                      const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\n                      const percentage = (value / total * 100).toFixed(1);\n                      return `${label}: ${value} khách sạn (${percentage}%)`;\n                    }\n                  }\n                }\n              },\n              onHover: (_, activeElements, chart) => {\n                if (activeElements.length > 0) {\n                  const dataIndex = activeElements[0].index;\n                  const dataset = chart.data.datasets[0];\n                  const total = dataset.data.reduce((sum, val) => sum + val, 0);\n                  const value = dataset.data[dataIndex];\n                  const percentage = (value / total * 100).toFixed(1);\n                  const label = chart.data.labels[dataIndex];\n\n                  // Update center text\n                  chart.options.plugins.centerText = {\n                    display: true,\n                    text: `${percentage}%`,\n                    subtext: label,\n                    value: value\n                  };\n                  chart.update('none');\n                } else {\n                  // Reset center text\n                  chart.options.plugins.centerText = {\n                    display: true,\n                    text: 'Click',\n                    subtext: 'để xem chi tiết',\n                    value: ''\n                  };\n                  chart.update('none');\n                }\n              }\n            },\n            plugins: [{\n              id: 'centerTextPie',\n              beforeDraw: chart => {\n                const {\n                  ctx,\n                  width,\n                  height\n                } = chart;\n                const centerText = chart.options.plugins.centerText || {\n                  display: true,\n                  text: 'Click',\n                  subtext: 'để xem chi tiết',\n                  value: ''\n                };\n                if (centerText.display) {\n                  ctx.save();\n                  ctx.textAlign = 'center';\n                  ctx.textBaseline = 'middle';\n                  const centerX = width / 2;\n                  const centerY = height / 2;\n\n                  // Main percentage text\n                  ctx.font = 'bold 20px Arial';\n                  ctx.fillStyle = '#333';\n                  ctx.fillText(centerText.text, centerX, centerY - 8);\n\n                  // Subtext (label)\n                  ctx.font = '12px Arial';\n                  ctx.fillStyle = '#666';\n                  ctx.fillText(centerText.subtext, centerX, centerY + 12);\n\n                  // Value\n                  if (centerText.value) {\n                    ctx.font = '10px Arial';\n                    ctx.fillStyle = '#999';\n                    ctx.fillText(`${centerText.value} khách sạn`, centerX, centerY + 28);\n                  }\n                  ctx.restore();\n                }\n              }\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart-fill\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n lo\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detailed-analysis mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container mb-4 card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-geo-alt me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch chi ti\\u1EBFt theo khu v\\u1EF1c\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Khu v\\u1EF1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.locationBreakdown || []).length > 0 ? (dashboardData.locationBreakdown || []).map((location, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: location.region\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: location.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: location.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: location.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${location.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 456,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 455,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [location.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 461,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: location.activePercentage >= 80 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 29\n                    }, this) : location.activePercentage >= 60 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-geo fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch khu v\\u1EF1c\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container mb-4 card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-star me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch theo ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ph\\xE2n lo\\u1EA1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\xE1nh gi\\xE1 TB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EA5t l\\u01B0\\u1EE3ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.categoryBreakdown || []).length > 0 ? (dashboardData.categoryBreakdown || []).map((category, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: category.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: category.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: category.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: category.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: `bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`,\n                        style: {\n                          fontSize: '12px'\n                        }\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 530,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"ms-1\",\n                        children: [\"(\", category.avgRating, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 536,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${category.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 542,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [category.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 547,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: category.avgRating >= 4.5 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"Xu\\u1EA5t s\\u1EAFc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 4.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-info\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 3.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"7\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-star fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch ph\\xE2n lo\\u1EA1i\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"pBlSUJ1rKJpORKZOoh/qh4nmKVQ=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "Line", "Bar", "Pie", "Doughnut", "useDispatch", "useSelector", "AdminDashboardActions", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "jsxDEV", "_jsxDEV", "register", "DashboardPage", "_s", "_dashboardData$revenu", "_dashboardData$revenu2", "_dashboardData$hotelD", "_dashboardData$hotelD2", "_dashboardData$hotelC", "_dashboardData$hotelC2", "dispatch", "data", "dashboardData", "loading", "error", "state", "AdminDashboard", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "type", "FETCH_ADMIN_DASHBOARD_METRICS", "payload", "params", "period", "onSuccess", "console", "log", "onFailed", "handlePeriodChange", "ChartEmptyState", "icon", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatRevenue", "revenue", "toFixed", "toLocaleString", "style", "height", "role", "onClick", "value", "onChange", "e", "target", "disabled", "totalHotels", "activeHotels", "totalCustomers", "totalOwners", "revenueData", "labels", "length", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "scales", "y", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "x", "display", "hotelDistributionData", "generateLabels", "chart", "datasets", "dataset", "total", "reduce", "sum", "map", "label", "i", "_dataset$borderColor", "percentage", "text", "fillStyle", "backgroundColor", "strokeStyle", "borderColor", "lineWidth", "hidden", "index", "tooltip", "callbacks", "context", "parsed", "val", "cutout", "hotelCategoryData", "_dataset$borderColor2", "onHover", "_", "activeElements", "dataIndex", "centerText", "subtext", "update", "id", "beforeDraw", "ctx", "width", "save", "textAlign", "textBaseline", "centerX", "centerY", "font", "fillText", "restore", "locationBreakdown", "location", "region", "active", "pending", "activePercentage", "colSpan", "categoryBreakdown", "category", "Array", "Math", "floor", "avgRating", "fontSize", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/pages/dashboard/DashboardPage.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { <PERSON>, Bar, Pie, Doughnut } from \"react-chartjs-2\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  ArcElement,\r\n} from 'chart.js';\r\n\r\n// Register Chart.js components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  ArcElement\r\n);\r\n\r\nconst DashboardPage = () => {\r\n  const dispatch = useDispatch();\r\n  const { data: dashboardData, loading, error } = useSelector(state => state.AdminDashboard);\r\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\r\n\r\n  // Fetch dashboard data on component mount and when period changes\r\n  useEffect(() => {\r\n    dispatch({\r\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\r\n      payload: {\r\n        params: { period: selectedPeriod },\r\n        onSuccess: (data) => {\r\n          console.log('Dashboard data loaded successfully:', data);\r\n        },\r\n        onFailed: (error) => {\r\n          console.error('Failed to load dashboard data:', error);\r\n        }\r\n      }\r\n    });\r\n  }, [dispatch, selectedPeriod]);\r\n\r\n  // Handle period change\r\n  const handlePeriodChange = (period) => {\r\n    setSelectedPeriod(period);\r\n  };\r\n\r\n  // Chart empty state component\r\n  const ChartEmptyState = ({ icon, message }) => (\r\n    <div className=\"d-flex align-items-center justify-content-center h-100 text-muted\">\r\n      <div className=\"text-center\">\r\n        <i className={`bi ${icon} fs-1 d-block mb-2`}></i>\r\n        <p>{message}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Format revenue for display\r\n  const formatRevenue = (revenue) => {\r\n    if (revenue >= 1000000) {\r\n      return (revenue / 1000000).toFixed(1) + 'M';\r\n    } else if (revenue >= 1000) {\r\n      return (revenue / 1000).toFixed(1) + 'K';\r\n    }\r\n    return revenue?.toLocaleString() || '0';\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n          <div className=\"spinner-border text-primary\" role=\"status\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"alert alert-danger\" role=\"alert\">\r\n          <h4 className=\"alert-heading\">Lỗi!</h4>\r\n          <p>{error}</p>\r\n          <button\r\n            className=\"btn btn-outline-danger\"\r\n            onClick={() => handlePeriodChange(selectedPeriod)}\r\n          >\r\n            Thử lại\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"dashboard-content\">\r\n      <div className=\"page-header\">\r\n        <h1>Tổng quan hệ thống</h1>\r\n        <div className=\"page-actions\">\r\n          <div className=\"date-filter\">\r\n            <select\r\n              className=\"form-select\"\r\n              value={selectedPeriod}\r\n              onChange={(e) => handlePeriodChange(e.target.value)}\r\n            >\r\n              <option value=\"day\">Hôm nay</option>\r\n              <option value=\"week\">Tuần này</option>\r\n              <option value=\"month\">Tháng này</option>\r\n              <option value=\"year\">Năm nay</option>\r\n            </select>\r\n          </div>\r\n          <button\r\n            className=\"btn btn-outline-secondary me-2\"\r\n            onClick={() => handlePeriodChange(selectedPeriod)}\r\n            disabled={loading}\r\n          >\r\n            <i className=\"bi bi-arrow-clockwise\"></i> Làm mới\r\n          </button>\r\n          <button className=\"btn btn-primary\">\r\n            <i className=\"bi bi-download\"></i> Xuất báo cáo\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"stats-cards\">\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalHotels || 0}</h3>\r\n            <p>Tổng số khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon hotels\">\r\n            <i className=\"bi bi-building\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.activeHotels || 0}</h3>\r\n            <p>Khách sạn hoạt động</p>\r\n          </div>\r\n          <div className=\"stat-card-icon active\">\r\n            <i className=\"bi bi-check-circle\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalCustomers || 0}</h3>\r\n            <p>Tổng số khách hàng</p>\r\n          </div>\r\n          <div className=\"stat-card-icon customers\">\r\n            <i className=\"bi bi-people\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalOwners || 0}</h3>\r\n            <p>Chủ khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon owners\">\r\n            <i className=\"bi bi-person-badge\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Revenue Chart */}\r\n      <div className=\"chart-container\">\r\n        <div className=\"chart-header\">\r\n          <h2>Doanh thu hệ thống</h2>\r\n          <div className=\"chart-actions\">\r\n            <div className=\"btn-group\">\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Ngày</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Tuần</button>\r\n              <button className=\"btn btn-sm btn-primary\">Tháng</button>\r\n              <button className=\"btn btn-sm btn-outline-secondary\">Năm</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-body\">\r\n          {dashboardData.revenueData?.labels?.length > 0 ? (\r\n            <Line\r\n              data={dashboardData.revenueData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"top\",\r\n                  },\r\n                },\r\n                scales: {\r\n                  y: {\r\n                    beginAtZero: false,\r\n                    grid: {\r\n                      drawBorder: false,\r\n                    },\r\n                    ticks: {\r\n                      callback: (value) => formatRevenue(value),\r\n                    },\r\n                  },\r\n                  x: {\r\n                    grid: {\r\n                      display: false,\r\n                    },\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n          ) : (\r\n            <ChartEmptyState icon=\"bi-graph-up\" message=\"Chưa có dữ liệu doanh thu\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Distribution Charts */}\r\n      <div className=\"charts-row\">\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân bố khách sạn theo khu vực</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelDistributionData?.labels?.length > 0 ? (\r\n              <Doughnut\r\n                data={dashboardData.hotelDistributionData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                      labels: {\r\n                        generateLabels: function(chart) {\r\n                          const data = chart.data;\r\n                          if (data.labels.length && data.datasets.length) {\r\n                            const dataset = data.datasets[0];\r\n                            const total = dataset.data.reduce((sum, value) => sum + value, 0);\r\n                            return data.labels.map((label, i) => {\r\n                              const value = dataset.data[i];\r\n                              const percentage = ((value / total) * 100).toFixed(1);\r\n                              return {\r\n                                text: `${label}: ${value} (${percentage}%)`,\r\n                                fillStyle: dataset.backgroundColor[i],\r\n                                strokeStyle: dataset.borderColor?.[i] || '#fff',\r\n                                lineWidth: 2,\r\n                                hidden: false,\r\n                                index: i\r\n                              };\r\n                            });\r\n                          }\r\n                          return [];\r\n                        }\r\n                      }\r\n                    },\r\n                    tooltip: {\r\n                      callbacks: {\r\n                        label: function(context) {\r\n                          const label = context.label || '';\r\n                          const value = context.parsed;\r\n                          const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\r\n                          const percentage = ((value / total) * 100).toFixed(1);\r\n                          return `${label}: ${value} khách sạn (${percentage}%)`;\r\n                        }\r\n                      }\r\n                    }\r\n                  },\r\n                  cutout: \"70%\",\r\n                }}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart\" message=\"Chưa có dữ liệu phân bố\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân loại khách sạn</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelCategoryData?.labels?.length > 0 ? (\r\n              <Pie\r\n                data={dashboardData.hotelCategoryData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                      labels: {\r\n                        generateLabels: function(chart) {\r\n                          const data = chart.data;\r\n                          if (data.labels.length && data.datasets.length) {\r\n                            const dataset = data.datasets[0];\r\n                            const total = dataset.data.reduce((sum, value) => sum + value, 0);\r\n                            return data.labels.map((label, i) => {\r\n                              const value = dataset.data[i];\r\n                              const percentage = ((value / total) * 100).toFixed(1);\r\n                              return {\r\n                                text: `${label}: ${value} (${percentage}%)`,\r\n                                fillStyle: dataset.backgroundColor[i],\r\n                                strokeStyle: dataset.borderColor?.[i] || '#fff',\r\n                                lineWidth: 2,\r\n                                hidden: false,\r\n                                index: i\r\n                              };\r\n                            });\r\n                          }\r\n                          return [];\r\n                        }\r\n                      }\r\n                    },\r\n                    tooltip: {\r\n                      callbacks: {\r\n                        label: function(context) {\r\n                          const label = context.label || '';\r\n                          const value = context.parsed;\r\n                          const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\r\n                          const percentage = ((value / total) * 100).toFixed(1);\r\n                          return `${label}: ${value} khách sạn (${percentage}%)`;\r\n                        }\r\n                      }\r\n                    }\r\n                  },\r\n                  onHover: (_, activeElements, chart) => {\r\n                    if (activeElements.length > 0) {\r\n                      const dataIndex = activeElements[0].index;\r\n                      const dataset = chart.data.datasets[0];\r\n                      const total = dataset.data.reduce((sum, val) => sum + val, 0);\r\n                      const value = dataset.data[dataIndex];\r\n                      const percentage = ((value / total) * 100).toFixed(1);\r\n                      const label = chart.data.labels[dataIndex];\r\n\r\n                      // Update center text\r\n                      chart.options.plugins.centerText = {\r\n                        display: true,\r\n                        text: `${percentage}%`,\r\n                        subtext: label,\r\n                        value: value\r\n                      };\r\n                      chart.update('none');\r\n                    } else {\r\n                      // Reset center text\r\n                      chart.options.plugins.centerText = {\r\n                        display: true,\r\n                        text: 'Click',\r\n                        subtext: 'để xem chi tiết',\r\n                        value: ''\r\n                      };\r\n                      chart.update('none');\r\n                    }\r\n                  },\r\n                }}\r\n                plugins={[{\r\n                  id: 'centerTextPie',\r\n                  beforeDraw: (chart) => {\r\n                    const { ctx, width, height } = chart;\r\n                    const centerText = chart.options.plugins.centerText || {\r\n                      display: true,\r\n                      text: 'Click',\r\n                      subtext: 'để xem chi tiết',\r\n                      value: ''\r\n                    };\r\n\r\n                    if (centerText.display) {\r\n                      ctx.save();\r\n                      ctx.textAlign = 'center';\r\n                      ctx.textBaseline = 'middle';\r\n\r\n                      const centerX = width / 2;\r\n                      const centerY = height / 2;\r\n\r\n                      // Main percentage text\r\n                      ctx.font = 'bold 20px Arial';\r\n                      ctx.fillStyle = '#333';\r\n                      ctx.fillText(centerText.text, centerX, centerY - 8);\r\n\r\n                      // Subtext (label)\r\n                      ctx.font = '12px Arial';\r\n                      ctx.fillStyle = '#666';\r\n                      ctx.fillText(centerText.subtext, centerX, centerY + 12);\r\n\r\n                      // Value\r\n                      if (centerText.value) {\r\n                        ctx.font = '10px Arial';\r\n                        ctx.fillStyle = '#999';\r\n                        ctx.fillText(`${centerText.value} khách sạn`, centerX, centerY + 28);\r\n                      }\r\n\r\n                      ctx.restore();\r\n                    }\r\n                  }\r\n                }]}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart-fill\" message=\"Chưa có dữ liệu phân loại\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detailed Analysis */}\r\n      <div className=\"detailed-analysis mt-4\">\r\n        {/* Location Breakdown */}\r\n        <div className=\"analysis-container mb-4 card\">\r\n          <div className=\"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\">\r\n            <h2 className=\"mb-0\">\r\n              <i className=\"bi bi-geo-alt me-2\"></i>\r\n              Phân tích chi tiết theo khu vực\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body card-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Khu vực</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Trạng thái</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.locationBreakdown || []).length > 0 ? (\r\n                    (dashboardData.locationBreakdown || []).map((location, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{location.region}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{location.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{location.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{location.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${location.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{location.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {location.activePercentage >= 80 ? (\r\n                            <span className=\"badge bg-success\">Tốt</span>\r\n                          ) : location.activePercentage >= 60 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"6\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-geo fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích khu vực\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category Breakdown */}\r\n        <div className=\"analysis-container mb-4 card\">\r\n          <div className=\"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\">\r\n            <h2 className=\"mb-0\">\r\n              <i className=\"bi bi-star me-2\"></i>\r\n              Phân tích theo phân loại khách sạn\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body card-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Phân loại</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Đánh giá TB</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Chất lượng</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.categoryBreakdown || []).length > 0 ? (\r\n                    (dashboardData.categoryBreakdown || []).map((category, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{category.category}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{category.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{category.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{category.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            {[...Array(5)].map((_, i) => (\r\n                              <i\r\n                                key={i}\r\n                                className={`bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`}\r\n                                style={{ fontSize: '12px' }}\r\n                              ></i>\r\n                            ))}\r\n                            <small className=\"ms-1\">({category.avgRating})</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${category.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{category.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {category.avgRating >= 4.5 ? (\r\n                            <span className=\"badge bg-success\">Xuất sắc</span>\r\n                          ) : category.avgRating >= 4.0 ? (\r\n                            <span className=\"badge bg-info\">Tốt</span>\r\n                          ) : category.avgRating >= 3.0 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"7\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-star fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích phân loại\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAV,OAAO,CAACW,QAAQ,CACdV,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAC1B,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB,IAAI,EAAEC,aAAa;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAG3B,WAAW,CAAC4B,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;EAC1F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,OAAO,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd6B,QAAQ,CAAC;MACPS,IAAI,EAAE/B,qBAAqB,CAACgC,6BAA6B;MACzDC,OAAO,EAAE;QACPC,MAAM,EAAE;UAAEC,MAAM,EAAEN;QAAe,CAAC;QAClCO,SAAS,EAAGb,IAAI,IAAK;UACnBc,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEf,IAAI,CAAC;QAC1D,CAAC;QACDgB,QAAQ,EAAGb,KAAK,IAAK;UACnBW,OAAO,CAACX,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,QAAQ,EAAEO,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMW,kBAAkB,GAAIL,MAAM,IAAK;IACrCL,iBAAiB,CAACK,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMM,eAAe,GAAGA,CAAC;IAAEC,IAAI;IAAEC;EAAQ,CAAC,kBACxC/B,OAAA;IAAKgC,SAAS,EAAC,mEAAmE;IAAAC,QAAA,eAChFjC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjC,OAAA;QAAGgC,SAAS,EAAE,MAAMF,IAAI;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDrC,OAAA;QAAAiC,QAAA,EAAIF;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMC,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAIA,OAAO,IAAI,OAAO,EAAE;MACtB,OAAO,CAACA,OAAO,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC7C,CAAC,MAAM,IAAID,OAAO,IAAI,IAAI,EAAE;MAC1B,OAAO,CAACA,OAAO,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC1C;IACA,OAAO,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,cAAc,CAAC,CAAC,KAAI,GAAG;EACzC,CAAC;;EAED;EACA,IAAI5B,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKgC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCjC,OAAA;QAAKgC,SAAS,EAAC,kDAAkD;QAACU,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAV,QAAA,eAC3FjC,OAAA;UAAKgC,SAAS,EAAC,6BAA6B;UAACY,IAAI,EAAC,QAAQ;UAAAX,QAAA,eACxDjC,OAAA;YAAMgC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIvB,KAAK,EAAE;IACT,oBACEd,OAAA;MAAKgC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCjC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAACY,IAAI,EAAC,OAAO;QAAAX,QAAA,gBAC9CjC,OAAA;UAAIgC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCrC,OAAA;UAAAiC,QAAA,EAAInB;QAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdrC,OAAA;UACEgC,SAAS,EAAC,wBAAwB;UAClCa,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAACX,cAAc,CAAE;UAAAgB,QAAA,EACnD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAKA,oBACErC,OAAA;IAAKgC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCjC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjC,OAAA;QAAAiC,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BrC,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjC,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BjC,OAAA;YACEgC,SAAS,EAAC,aAAa;YACvBc,KAAK,EAAE7B,cAAe;YACtB8B,QAAQ,EAAGC,CAAC,IAAKpB,kBAAkB,CAACoB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAb,QAAA,gBAEpDjC,OAAA;cAAQ8C,KAAK,EAAC,KAAK;cAAAb,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCrC,OAAA;cAAQ8C,KAAK,EAAC,MAAM;cAAAb,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCrC,OAAA;cAAQ8C,KAAK,EAAC,OAAO;cAAAb,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCrC,OAAA;cAAQ8C,KAAK,EAAC,MAAM;cAAAb,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrC,OAAA;UACEgC,SAAS,EAAC,gCAAgC;UAC1Ca,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAACX,cAAc,CAAE;UAClDiC,QAAQ,EAAErC,OAAQ;UAAAoB,QAAA,gBAElBjC,OAAA;YAAGgC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAC3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA;UAAQgC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBACjCjC,OAAA;YAAGgC,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,4BACpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAACuC,WAAW,IAAI;UAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCrC,OAAA;YAAAiC,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCjC,OAAA;YAAGgC,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAACwC,YAAY,IAAI;UAAC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CrC,OAAA;YAAAiC,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCjC,OAAA;YAAGgC,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAACyC,cAAc,IAAI;UAAC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5CrC,OAAA;YAAAiC,QAAA,EAAG;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCjC,OAAA;YAAGgC,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAAiC,QAAA,EAAKrB,aAAa,CAAC0C,WAAW,IAAI;UAAC;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCrC,OAAA;YAAAiC,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCjC,OAAA;YAAGgC,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BjC,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjC,OAAA;UAAAiC,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAQgC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClErC,OAAA;cAAQgC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClErC,OAAA;cAAQgC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzDrC,OAAA;cAAQgC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxB,EAAA7B,qBAAA,GAAAQ,aAAa,CAAC2C,WAAW,cAAAnD,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BoD,MAAM,cAAAnD,sBAAA,uBAAjCA,sBAAA,CAAmCoD,MAAM,IAAG,CAAC,gBAC5CzD,OAAA,CAAClB,IAAI;UACH6B,IAAI,EAAEC,aAAa,CAAC2C,WAAY;UAChCG,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACPC,MAAM,EAAE;gBACNC,QAAQ,EAAE;cACZ;YACF,CAAC;YACDC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,KAAK;gBAClBC,IAAI,EAAE;kBACJC,UAAU,EAAE;gBACd,CAAC;gBACDC,KAAK,EAAE;kBACLC,QAAQ,EAAGxB,KAAK,IAAKR,aAAa,CAACQ,KAAK;gBAC1C;cACF,CAAC;cACDyB,CAAC,EAAE;gBACDJ,IAAI,EAAE;kBACJK,OAAO,EAAE;gBACX;cACF;YACF;UACF;QAAE;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFrC,OAAA,CAAC6B,eAAe;UAACC,IAAI,EAAC,aAAa;UAACC,OAAO,EAAC;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC1E;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBjC,OAAA;QAAKgC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCjC,OAAA;UAAKgC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BjC,OAAA;YAAAiC,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAA3B,qBAAA,GAAAM,aAAa,CAAC6D,qBAAqB,cAAAnE,qBAAA,wBAAAC,sBAAA,GAAnCD,qBAAA,CAAqCkD,MAAM,cAAAjD,sBAAA,uBAA3CA,sBAAA,CAA6CkD,MAAM,IAAG,CAAC,gBACtDzD,OAAA,CAACf,QAAQ;YACP0B,IAAI,EAAEC,aAAa,CAAC6D,qBAAsB;YAC1Cf,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE,QAAQ;kBAClBP,MAAM,EAAE;oBACNkB,cAAc,EAAE,SAAAA,CAASC,KAAK,EAAE;sBAC9B,MAAMhE,IAAI,GAAGgE,KAAK,CAAChE,IAAI;sBACvB,IAAIA,IAAI,CAAC6C,MAAM,CAACC,MAAM,IAAI9C,IAAI,CAACiE,QAAQ,CAACnB,MAAM,EAAE;wBAC9C,MAAMoB,OAAO,GAAGlE,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAAC;wBAChC,MAAME,KAAK,GAAGD,OAAO,CAAClE,IAAI,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAElC,KAAK,KAAKkC,GAAG,GAAGlC,KAAK,EAAE,CAAC,CAAC;wBACjE,OAAOnC,IAAI,CAAC6C,MAAM,CAACyB,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;0BAAA,IAAAC,oBAAA;0BACnC,MAAMtC,KAAK,GAAG+B,OAAO,CAAClE,IAAI,CAACwE,CAAC,CAAC;0BAC7B,MAAME,UAAU,GAAG,CAAEvC,KAAK,GAAGgC,KAAK,GAAI,GAAG,EAAEtC,OAAO,CAAC,CAAC,CAAC;0BACrD,OAAO;4BACL8C,IAAI,EAAE,GAAGJ,KAAK,KAAKpC,KAAK,KAAKuC,UAAU,IAAI;4BAC3CE,SAAS,EAAEV,OAAO,CAACW,eAAe,CAACL,CAAC,CAAC;4BACrCM,WAAW,EAAE,EAAAL,oBAAA,GAAAP,OAAO,CAACa,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAsBD,CAAC,CAAC,KAAI,MAAM;4BAC/CQ,SAAS,EAAE,CAAC;4BACZC,MAAM,EAAE,KAAK;4BACbC,KAAK,EAAEV;0BACT,CAAC;wBACH,CAAC,CAAC;sBACJ;sBACA,OAAO,EAAE;oBACX;kBACF;gBACF,CAAC;gBACDW,OAAO,EAAE;kBACPC,SAAS,EAAE;oBACTb,KAAK,EAAE,SAAAA,CAASc,OAAO,EAAE;sBACvB,MAAMd,KAAK,GAAGc,OAAO,CAACd,KAAK,IAAI,EAAE;sBACjC,MAAMpC,KAAK,GAAGkD,OAAO,CAACC,MAAM;sBAC5B,MAAMnB,KAAK,GAAGkB,OAAO,CAACnB,OAAO,CAAClE,IAAI,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAEkB,GAAG,KAAKlB,GAAG,GAAGkB,GAAG,EAAE,CAAC,CAAC;sBACrE,MAAMb,UAAU,GAAG,CAAEvC,KAAK,GAAGgC,KAAK,GAAI,GAAG,EAAEtC,OAAO,CAAC,CAAC,CAAC;sBACrD,OAAO,GAAG0C,KAAK,KAAKpC,KAAK,eAAeuC,UAAU,IAAI;oBACxD;kBACF;gBACF;cACF,CAAC;cACDc,MAAM,EAAE;YACV;UAAE;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFrC,OAAA,CAAC6B,eAAe;YAACC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC;UAAyB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACzE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKgC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCjC,OAAA;UAAKgC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BjC,OAAA;YAAAiC,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAAzB,qBAAA,GAAAI,aAAa,CAACwF,iBAAiB,cAAA5F,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCgD,MAAM,cAAA/C,sBAAA,uBAAvCA,sBAAA,CAAyCgD,MAAM,IAAG,CAAC,gBAClDzD,OAAA,CAAChB,GAAG;YACF2B,IAAI,EAAEC,aAAa,CAACwF,iBAAkB;YACtC1C,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE,QAAQ;kBAClBP,MAAM,EAAE;oBACNkB,cAAc,EAAE,SAAAA,CAASC,KAAK,EAAE;sBAC9B,MAAMhE,IAAI,GAAGgE,KAAK,CAAChE,IAAI;sBACvB,IAAIA,IAAI,CAAC6C,MAAM,CAACC,MAAM,IAAI9C,IAAI,CAACiE,QAAQ,CAACnB,MAAM,EAAE;wBAC9C,MAAMoB,OAAO,GAAGlE,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAAC;wBAChC,MAAME,KAAK,GAAGD,OAAO,CAAClE,IAAI,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAElC,KAAK,KAAKkC,GAAG,GAAGlC,KAAK,EAAE,CAAC,CAAC;wBACjE,OAAOnC,IAAI,CAAC6C,MAAM,CAACyB,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;0BAAA,IAAAkB,qBAAA;0BACnC,MAAMvD,KAAK,GAAG+B,OAAO,CAAClE,IAAI,CAACwE,CAAC,CAAC;0BAC7B,MAAME,UAAU,GAAG,CAAEvC,KAAK,GAAGgC,KAAK,GAAI,GAAG,EAAEtC,OAAO,CAAC,CAAC,CAAC;0BACrD,OAAO;4BACL8C,IAAI,EAAE,GAAGJ,KAAK,KAAKpC,KAAK,KAAKuC,UAAU,IAAI;4BAC3CE,SAAS,EAAEV,OAAO,CAACW,eAAe,CAACL,CAAC,CAAC;4BACrCM,WAAW,EAAE,EAAAY,qBAAA,GAAAxB,OAAO,CAACa,WAAW,cAAAW,qBAAA,uBAAnBA,qBAAA,CAAsBlB,CAAC,CAAC,KAAI,MAAM;4BAC/CQ,SAAS,EAAE,CAAC;4BACZC,MAAM,EAAE,KAAK;4BACbC,KAAK,EAAEV;0BACT,CAAC;wBACH,CAAC,CAAC;sBACJ;sBACA,OAAO,EAAE;oBACX;kBACF;gBACF,CAAC;gBACDW,OAAO,EAAE;kBACPC,SAAS,EAAE;oBACTb,KAAK,EAAE,SAAAA,CAASc,OAAO,EAAE;sBACvB,MAAMd,KAAK,GAAGc,OAAO,CAACd,KAAK,IAAI,EAAE;sBACjC,MAAMpC,KAAK,GAAGkD,OAAO,CAACC,MAAM;sBAC5B,MAAMnB,KAAK,GAAGkB,OAAO,CAACnB,OAAO,CAAClE,IAAI,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAEkB,GAAG,KAAKlB,GAAG,GAAGkB,GAAG,EAAE,CAAC,CAAC;sBACrE,MAAMb,UAAU,GAAG,CAAEvC,KAAK,GAAGgC,KAAK,GAAI,GAAG,EAAEtC,OAAO,CAAC,CAAC,CAAC;sBACrD,OAAO,GAAG0C,KAAK,KAAKpC,KAAK,eAAeuC,UAAU,IAAI;oBACxD;kBACF;gBACF;cACF,CAAC;cACDiB,OAAO,EAAEA,CAACC,CAAC,EAAEC,cAAc,EAAE7B,KAAK,KAAK;gBACrC,IAAI6B,cAAc,CAAC/C,MAAM,GAAG,CAAC,EAAE;kBAC7B,MAAMgD,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC,CAACX,KAAK;kBACzC,MAAMhB,OAAO,GAAGF,KAAK,CAAChE,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAAC;kBACtC,MAAME,KAAK,GAAGD,OAAO,CAAClE,IAAI,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAEkB,GAAG,KAAKlB,GAAG,GAAGkB,GAAG,EAAE,CAAC,CAAC;kBAC7D,MAAMpD,KAAK,GAAG+B,OAAO,CAAClE,IAAI,CAAC8F,SAAS,CAAC;kBACrC,MAAMpB,UAAU,GAAG,CAAEvC,KAAK,GAAGgC,KAAK,GAAI,GAAG,EAAEtC,OAAO,CAAC,CAAC,CAAC;kBACrD,MAAM0C,KAAK,GAAGP,KAAK,CAAChE,IAAI,CAAC6C,MAAM,CAACiD,SAAS,CAAC;;kBAE1C;kBACA9B,KAAK,CAACjB,OAAO,CAACG,OAAO,CAAC6C,UAAU,GAAG;oBACjClC,OAAO,EAAE,IAAI;oBACbc,IAAI,EAAE,GAAGD,UAAU,GAAG;oBACtBsB,OAAO,EAAEzB,KAAK;oBACdpC,KAAK,EAAEA;kBACT,CAAC;kBACD6B,KAAK,CAACiC,MAAM,CAAC,MAAM,CAAC;gBACtB,CAAC,MAAM;kBACL;kBACAjC,KAAK,CAACjB,OAAO,CAACG,OAAO,CAAC6C,UAAU,GAAG;oBACjClC,OAAO,EAAE,IAAI;oBACbc,IAAI,EAAE,OAAO;oBACbqB,OAAO,EAAE,iBAAiB;oBAC1B7D,KAAK,EAAE;kBACT,CAAC;kBACD6B,KAAK,CAACiC,MAAM,CAAC,MAAM,CAAC;gBACtB;cACF;YACF,CAAE;YACF/C,OAAO,EAAE,CAAC;cACRgD,EAAE,EAAE,eAAe;cACnBC,UAAU,EAAGnC,KAAK,IAAK;gBACrB,MAAM;kBAAEoC,GAAG;kBAAEC,KAAK;kBAAErE;gBAAO,CAAC,GAAGgC,KAAK;gBACpC,MAAM+B,UAAU,GAAG/B,KAAK,CAACjB,OAAO,CAACG,OAAO,CAAC6C,UAAU,IAAI;kBACrDlC,OAAO,EAAE,IAAI;kBACbc,IAAI,EAAE,OAAO;kBACbqB,OAAO,EAAE,iBAAiB;kBAC1B7D,KAAK,EAAE;gBACT,CAAC;gBAED,IAAI4D,UAAU,CAAClC,OAAO,EAAE;kBACtBuC,GAAG,CAACE,IAAI,CAAC,CAAC;kBACVF,GAAG,CAACG,SAAS,GAAG,QAAQ;kBACxBH,GAAG,CAACI,YAAY,GAAG,QAAQ;kBAE3B,MAAMC,OAAO,GAAGJ,KAAK,GAAG,CAAC;kBACzB,MAAMK,OAAO,GAAG1E,MAAM,GAAG,CAAC;;kBAE1B;kBACAoE,GAAG,CAACO,IAAI,GAAG,iBAAiB;kBAC5BP,GAAG,CAACxB,SAAS,GAAG,MAAM;kBACtBwB,GAAG,CAACQ,QAAQ,CAACb,UAAU,CAACpB,IAAI,EAAE8B,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC;;kBAEnD;kBACAN,GAAG,CAACO,IAAI,GAAG,YAAY;kBACvBP,GAAG,CAACxB,SAAS,GAAG,MAAM;kBACtBwB,GAAG,CAACQ,QAAQ,CAACb,UAAU,CAACC,OAAO,EAAES,OAAO,EAAEC,OAAO,GAAG,EAAE,CAAC;;kBAEvD;kBACA,IAAIX,UAAU,CAAC5D,KAAK,EAAE;oBACpBiE,GAAG,CAACO,IAAI,GAAG,YAAY;oBACvBP,GAAG,CAACxB,SAAS,GAAG,MAAM;oBACtBwB,GAAG,CAACQ,QAAQ,CAAC,GAAGb,UAAU,CAAC5D,KAAK,YAAY,EAAEsE,OAAO,EAAEC,OAAO,GAAG,EAAE,CAAC;kBACtE;kBAEAN,GAAG,CAACS,OAAO,CAAC,CAAC;gBACf;cACF;YACF,CAAC;UAAE;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEFrC,OAAA,CAAC6B,eAAe;YAACC,IAAI,EAAC,mBAAmB;YAACC,OAAO,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAChF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCjC,OAAA;QAAKgC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CjC,OAAA;UAAKgC,SAAS,EAAC,qFAAqF;UAAAC,QAAA,eAClGjC,OAAA;YAAIgC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBjC,OAAA;cAAGgC,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mDAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCjC,OAAA;YAAKgC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BjC,OAAA;cAAOgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCjC,OAAA;gBAAAiC,QAAA,eACEjC,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrC,OAAA;gBAAAiC,QAAA,EACG,CAACrB,aAAa,CAAC6G,iBAAiB,IAAI,EAAE,EAAEhE,MAAM,GAAG,CAAC,GACjD,CAAC7C,aAAa,CAAC6G,iBAAiB,IAAI,EAAE,EAAExC,GAAG,CAAC,CAACyC,QAAQ,EAAE7B,KAAK,kBAC1D7F,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAAiC,QAAA,EAASyF,QAAQ,CAACC;oBAAM;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEyF,QAAQ,CAAC5C;oBAAK;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEyF,QAAQ,CAACE;oBAAM;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEyF,QAAQ,CAACG;oBAAO;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCjC,OAAA;wBAAKgC,SAAS,EAAC,eAAe;wBAACU,KAAK,EAAE;0BAAEsE,KAAK,EAAE,MAAM;0BAAErE,MAAM,EAAE;wBAAM,CAAE;wBAAAV,QAAA,eACrEjC,OAAA;0BACEgC,SAAS,EAAC,yBAAyB;0BACnCU,KAAK,EAAE;4BAAEsE,KAAK,EAAE,GAAGU,QAAQ,CAACI,gBAAgB;0BAAI;wBAAE;0BAAA5F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNrC,OAAA;wBAAAiC,QAAA,GAAQyF,QAAQ,CAACI,gBAAgB,EAAC,GAAC;sBAAA;wBAAA5F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,EACGyF,QAAQ,CAACI,gBAAgB,IAAI,EAAE,gBAC9B9H,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAC3CqF,QAAQ,CAACI,gBAAgB,IAAI,EAAE,gBACjC9H,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpDrC,OAAA;sBAAMgC,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAhCEwD,KAAK;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACL,CAAC,gBAEFrC,OAAA;kBAAAiC,QAAA,eACEjC,OAAA;oBAAI+H,OAAO,EAAC,GAAG;oBAAC/F,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrDjC,OAAA;sBAAGgC,SAAS,EAAC;oBAA6B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,kEAEjD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAKgC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CjC,OAAA;UAAKgC,SAAS,EAAC,qFAAqF;UAAAC,QAAA,eAClGjC,OAAA;YAAIgC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBjC,OAAA;cAAGgC,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,4DAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCjC,OAAA;YAAKgC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BjC,OAAA;cAAOgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCjC,OAAA;gBAAAiC,QAAA,eACEjC,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBrC,OAAA;oBAAAiC,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrC,OAAA;gBAAAiC,QAAA,EACG,CAACrB,aAAa,CAACoH,iBAAiB,IAAI,EAAE,EAAEvE,MAAM,GAAG,CAAC,GACjD,CAAC7C,aAAa,CAACoH,iBAAiB,IAAI,EAAE,EAAE/C,GAAG,CAAC,CAACgD,QAAQ,EAAEpC,KAAK,kBAC1D7F,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAAiC,QAAA,EAASgG,QAAQ,CAACA;oBAAQ;sBAAA/F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgG,QAAQ,CAACnD;oBAAK;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgG,QAAQ,CAACL;oBAAM;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEgG,QAAQ,CAACJ;oBAAO;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACvC,CAAC,GAAGiG,KAAK,CAAC,CAAC,CAAC,CAAC,CAACjD,GAAG,CAAC,CAACsB,CAAC,EAAEpB,CAAC,kBACtBnF,OAAA;wBAEEgC,SAAS,EAAE,MAAMmD,CAAC,GAAGgD,IAAI,CAACC,KAAK,CAACH,QAAQ,CAACI,SAAS,IAAI,CAAC,CAAC,GAAG,cAAc,GAAG,SAAS,oBAAqB;wBAC1G3F,KAAK,EAAE;0BAAE4F,QAAQ,EAAE;wBAAO;sBAAE,GAFvBnD,CAAC;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGJ,CACL,CAAC,eACFrC,OAAA;wBAAOgC,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,GAAC,EAACgG,QAAQ,CAACI,SAAS,EAAC,GAAC;sBAAA;wBAAAnG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,eACEjC,OAAA;sBAAKgC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCjC,OAAA;wBAAKgC,SAAS,EAAC,eAAe;wBAACU,KAAK,EAAE;0BAAEsE,KAAK,EAAE,MAAM;0BAAErE,MAAM,EAAE;wBAAM,CAAE;wBAAAV,QAAA,eACrEjC,OAAA;0BACEgC,SAAS,EAAC,yBAAyB;0BACnCU,KAAK,EAAE;4BAAEsE,KAAK,EAAE,GAAGiB,QAAQ,CAACH,gBAAgB;0BAAI;wBAAE;0BAAA5F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNrC,OAAA;wBAAAiC,QAAA,GAAQgG,QAAQ,CAACH,gBAAgB,EAAC,GAAC;sBAAA;wBAAA5F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrC,OAAA;oBAAAiC,QAAA,EACGgG,QAAQ,CAACI,SAAS,IAAI,GAAG,gBACxBrI,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAChD4F,QAAQ,CAACI,SAAS,IAAI,GAAG,gBAC3BrI,OAAA;sBAAMgC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GACxC4F,QAAQ,CAACI,SAAS,IAAI,GAAG,gBAC3BrI,OAAA;sBAAMgC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpDrC,OAAA;sBAAMgC,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA9CEwD,KAAK;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+CV,CACL,CAAC,gBAEFrC,OAAA;kBAAAiC,QAAA,eACEjC,OAAA;oBAAI+H,OAAO,EAAC,GAAG;oBAAC/F,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrDjC,OAAA;sBAAGgC,SAAS,EAAC;oBAA8B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uEAElD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CAxiBID,aAAa;EAAA,QACAhB,WAAW,EACoBC,WAAW;AAAA;AAAAoJ,EAAA,GAFvDrI,aAAa;AA0iBnB,eAAeA,aAAa;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}