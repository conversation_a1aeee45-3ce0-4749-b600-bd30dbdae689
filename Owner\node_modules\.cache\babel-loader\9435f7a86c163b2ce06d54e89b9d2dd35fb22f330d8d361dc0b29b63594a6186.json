{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Owner\\\\src\\\\pages\\\\hotel_host\\\\create_hotel\\\\BookingPropertyLocation.jsx\",\n  _s = $RefreshSig$();\nimport * as Routers from \"../../../utils/Routes\";\nimport React, { useState, useEffect } from \"react\";\nimport { Navbar, Container, Button, Form, Card, ProgressBar, Row, Col } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { ArrowLeft } from \"lucide-react\";\nimport { cityOptionSelect, districtsByCity, wardsByDistrict } from \"@utils/data\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAppDispatch, useAppSelector } from \"@redux/store\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BookingLocation() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const createHotel = useAppSelector(state => state.Hotel.createHotel);\n  console.log(cityOptionSelect, districtsByCity, wardsByDistrict);\n  // Initialize state with values from Redux store or empty strings\n  const [selectedCity, setSelectedCity] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.city) || \"\");\n  const [selectedDistrict, setSelectedDistrict] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.district) || \"\");\n  const [selectedWard, setSelectedWard] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.ward) || \"\");\n  const [specificAddress, setSpecificAddress] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.specificAddress) || \"\");\n  const [generalAddress, setGeneralAddress] = useState((createHotel === null || createHotel === void 0 ? void 0 : createHotel.address) || \"\");\n  const [availableDistricts, setAvailableDistricts] = useState([]);\n  const [availableWards, setAvailableWards] = useState([]);\n\n  // Initialize districts and wards when component mounts with existing data\n  useEffect(() => {\n    if (selectedCity) {\n      const districts = districtsByCity[selectedCity] || [];\n      setAvailableDistricts(districts);\n      if (selectedDistrict) {\n        const wards = wardsByDistrict[selectedDistrict] || [];\n        setAvailableWards(wards);\n      }\n    }\n  }, []); // Run only on mount\n\n  // Update districts when city changes\n  useEffect(() => {\n    if (selectedCity) {\n      const districts = districtsByCity[selectedCity] || [];\n      setAvailableDistricts(districts);\n\n      // Only reset district and ward if the current district is not valid for the new city\n      const isDistrictValid = districts.some(d => d.value === selectedDistrict);\n      if (!isDistrictValid) {\n        setSelectedDistrict(\"\");\n        setSelectedWard(\"\");\n        setAvailableWards([]);\n      }\n    } else {\n      setAvailableDistricts([]);\n      setSelectedDistrict(\"\");\n      setSelectedWard(\"\");\n      setAvailableWards([]);\n    }\n  }, [selectedCity]);\n\n  // Update wards when district changes\n  useEffect(() => {\n    if (selectedDistrict) {\n      const wards = wardsByDistrict[selectedDistrict] || [];\n      setAvailableWards(wards);\n\n      // Only reset ward if the current ward is not valid for the new district\n      const isWardValid = wards.some(w => w.value === selectedWard);\n      if (!isWardValid) {\n        setSelectedWard(\"\");\n      }\n    } else {\n      setAvailableWards([]);\n      setSelectedWard(\"\");\n    }\n  }, [selectedDistrict]);\n\n  // Update general address when location changes\n  useEffect(() => {\n    const addressParts = [];\n    if (specificAddress !== null && specificAddress !== void 0 && specificAddress.trim()) addressParts.push(specificAddress.trim());\n    if (selectedWard) {\n      addressParts.push(`${selectedWard}`);\n    }\n    if (selectedDistrict) {\n      addressParts.push(`${selectedDistrict}`);\n    }\n    if (selectedCity) addressParts.push(`${selectedCity}`);\n    setGeneralAddress(addressParts.join(\", \"));\n  }, [specificAddress, selectedWard, selectedDistrict, selectedCity]);\n  const handleContinue = () => {\n    // Validate required fields\n    if (!selectedCity) {\n      showToast.warning(\"Vui lòng chọn thành phố\");\n      return;\n    }\n    if (!selectedDistrict) {\n      showToast.warning(\"Vui lòng chọn quận/huyện\");\n      return;\n    }\n    if (!selectedWard) {\n      showToast.warning(\"Vui lòng chọn phường/xã\");\n      return;\n    }\n    if (!(specificAddress !== null && specificAddress !== void 0 && specificAddress.trim())) {\n      showToast.warning(\"Vui lòng nhập địa chỉ cụ thể\");\n      return;\n    }\n\n    // Dispatch action to save data\n    dispatch({\n      type: HotelActions.SAVE_HOTEL_ADDRESS_CREATE,\n      payload: {\n        specificAddress: specificAddress.trim(),\n        address: generalAddress,\n        city: selectedCity,\n        district: selectedDistrict,\n        ward: selectedWard\n      }\n    });\n\n    // Navigate to next step\n    navigate(Routers.BookingPropertyFacility);\n  };\n  const handleBack = () => {\n    // Save current data before going back\n    dispatch({\n      type: HotelActions.SAVE_HOTEL_ADDRESS_CREATE,\n      payload: {\n        specificAddress: (specificAddress === null || specificAddress === void 0 ? void 0 : specificAddress.trim()) || \"\",\n        address: generalAddress,\n        city: selectedCity,\n        district: selectedDistrict,\n        ward: selectedWard\n      }\n    });\n\n    // Navigate back (you can replace with actual back route)\n    navigate(Routers.BookingPropertyName);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"booking-app\",\n    children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navbar, {\n      style: {\n        backgroundColor: \"#003580\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n          href: \"#home\",\n          className: \"text-white fw-bold\",\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            style: {\n              fontSize: 30\n            },\n            children: [\"UR\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#f8e71c\"\n              },\n              children: \"OO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), \"M\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-label mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Th\\xF4ng tin c\\u01A1 b\\u1EA3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n          style: {\n            height: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 20\n          }, 1, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"primary\",\n            now: 20\n          }, 2, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"secondary\",\n            now: 20\n          }, 3, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"secondary\",\n            now: 20\n          }, 4, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            variant: \"secondary\",\n            now: 20\n          }, 5, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"main-content py-4\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 7,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"main-heading\",\n              children: \"Ch\\u1ED7 ngh\\u1EC9 c\\u1EE7a Qu\\xFD v\\u1ECB \\u1EDF \\u0111\\xE2u?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"property-form-card\",\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"fw-bold\",\n                  children: [\"Th\\xE0nh ph\\u1ED1 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-danger\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  className: \"form-input\",\n                  value: selectedCity,\n                  onChange: e => setSelectedCity(e.target.value),\n                  isInvalid: !selectedCity,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Ch\\u1ECDn th\\xE0nh ph\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 21\n                  }, this), cityOptionSelect.map(city => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: city.value,\n                    children: city.label\n                  }, city.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"fw-bold\",\n                  children: [\"Qu\\u1EADn/Huy\\u1EC7n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-danger\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  className: \"form-input\",\n                  value: selectedDistrict,\n                  onChange: e => setSelectedDistrict(e.target.value),\n                  disabled: !selectedCity,\n                  isInvalid: selectedCity && !selectedDistrict,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Ch\\u1ECDn qu\\u1EADn/huy\\u1EC7n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), availableDistricts.map(district => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: district.value,\n                    children: district.label\n                  }, district.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), !selectedCity && /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"Vui l\\xF2ng ch\\u1ECDn th\\xE0nh ph\\u1ED1 tr\\u01B0\\u1EDBc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"fw-bold\",\n                  children: [\"Ph\\u01B0\\u1EDDng/X\\xE3 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-danger\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  className: \"form-input\",\n                  value: selectedWard,\n                  onChange: e => setSelectedWard(e.target.value),\n                  disabled: !selectedDistrict,\n                  isInvalid: selectedDistrict && !selectedWard,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Ch\\u1ECDn ph\\u01B0\\u1EDDng/x\\xE3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this), availableWards.map(ward => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: ward.value,\n                    children: ward.label\n                  }, ward.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), !selectedDistrict && /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"Vui l\\xF2ng ch\\u1ECDn qu\\u1EADn/huy\\u1EC7n tr\\u01B0\\u1EDBc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"fw-bold\",\n                  children: [\"\\u0110\\u1ECBa ch\\u1EC9 c\\u1EE5 th\\u1EC3 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-danger\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  placeholder: \"Nh\\u1EADp s\\u1ED1 nh\\xE0, t\\xEAn \\u0111\\u01B0\\u1EDDng...\",\n                  className: \"form-input\",\n                  value: specificAddress,\n                  onChange: e => setSpecificAddress(e.target.value),\n                  isInvalid: !(specificAddress !== null && specificAddress !== void 0 && specificAddress.trim())\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"V\\xED d\\u1EE5: 123 \\u0110\\u01B0\\u1EDDng Nguy\\u1EC5n V\\u0103n A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"fw-bold\",\n                  children: \"\\u0110\\u1ECBa ch\\u1EC9 t\\u1ED5ng qu\\xE1t\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  placeholder: \"\\u0110\\u1ECBa ch\\u1EC9 s\\u1EBD \\u0111\\u01B0\\u1EE3c t\\u1EA1o t\\u1EF1 \\u0111\\u1ED9ng\",\n                  className: \"form-input\",\n                  value: generalAddress,\n                  disabled: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"\\u0110\\u1ECBa ch\\u1EC9 n\\xE0y s\\u1EBD hi\\u1EC3n th\\u1ECB cho kh\\xE1ch h\\xE0ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"navigation-buttons mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              className: \"back-button\",\n              onClick: handleBack,\n              title: \"Quay l\\u1EA1i\",\n              children: /*#__PURE__*/_jsxDEV(ArrowLeft, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              className: \"continue-button\",\n              onClick: handleContinue,\n              disabled: !selectedCity || !selectedDistrict || !selectedWard || !(specificAddress !== null && specificAddress !== void 0 && specificAddress.trim()),\n              children: \"Ti\\u1EBFp t\\u1EE5c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-cards\",\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-icon thumbs-up\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      role: \"img\",\n                      \"aria-label\": \"thumbs up\",\n                      children: \"\\uD83D\\uDC4D\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"info-title\",\n                      children: \"T\\xF4i n\\xEAn ch\\xFA \\xFD \\u0111i\\u1EC1u g\\xEC khi \\u0111i\\u1EC1n th\\xF4ng tin \\u0111\\u1ECBa ch\\u1EC9?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"info-list mt-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"Ch\\u1ECDn th\\xE0nh ph\\u1ED1, qu\\u1EADn, ph\\u01B0\\u1EDDng \\u0111\\xFAng\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"Tr\\xE1nh s\\u1EED d\\u1EE5ng ch\\u1EEF vi\\u1EBFt t\\u1EAFt\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"\\u0110\\xFAng v\\u1EDBi th\\u1EF1c t\\u1EBF tr\\xEAn Google Maps\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 356,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"\\u0110i\\u1EC1n \\u0111\\u1EA7y \\u0111\\u1EE7 s\\u1ED1 nh\\xE0 v\\xE0 t\\xEAn \\u0111\\u01B0\\u1EDDng\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-icon lightbulb\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      role: \"img\",\n                      \"aria-label\": \"lightbulb\",\n                      children: \"\\uD83D\\uDCA1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"info-title\",\n                      children: \"T\\u1EA1i sao t\\xF4i c\\u1EA7n \\u0111i\\u1EC1n \\u0111\\xFAng \\u0111\\u1ECBa ch\\u1EC9 cho ch\\u1ED7 ngh\\u1EC9 c\\u1EE7a m\\xECnh?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"info-text mt-3\",\n                      children: \"\\u0110\\u1ECBa ch\\u1EC9 ch\\xEDnh x\\xE1c gi\\xFAp kh\\xE1ch h\\xE0ng d\\u1EC5 d\\xE0ng t\\xECm th\\u1EA5y ch\\u1ED7 ngh\\u1EC9 c\\u1EE7a b\\u1EA1n. Th\\xF4ng tin n\\xE0y s\\u1EBD hi\\u1EC3n th\\u1ECB tr\\xEAn b\\u1EA3n \\u0111\\u1ED3 v\\xE0 trong k\\u1EBFt qu\\u1EA3 t\\xECm ki\\u1EBFm, gi\\xFAp t\\u0103ng \\u0111\\u1ED9 tin c\\u1EADy v\\xE0 kh\\u1EA3 n\\u0103ng \\u0111\\u1EB7t ph\\xF2ng t\\u1EEB kh\\xE1ch h\\xE0ng.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .booking-app {\n          min-height: 100vh;\n          background-color: #f8f9fa;\n        }\n\n        .progress-section {\n          margin: 0 auto;\n        }\n\n        .progress-label {\n          font-size: 14px;\n          color: #333;\n        }\n\n        .main-content {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .main-heading {\n          font-size: 28px;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 20px;\n        }\n\n        .property-form-card {\n          background-color: #fff;\n          border-radius: 8px;\n          padding: 24px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          border: 1px solid #e9ecef;\n        }\n\n        .form-input {\n          height: 45px;\n          border: 1px solid #ced4da;\n          border-radius: 6px;\n          font-size: 16px;\n          transition: border-color 0.15s ease-in-out,\n            box-shadow 0.15s ease-in-out;\n        }\n\n        .form-input:focus {\n          border-color: #0071c2;\n          box-shadow: 0 0 0 0.2rem rgba(0, 113, 194, 0.25);\n        }\n\n        .form-input:disabled {\n          background-color: #f8f9fa;\n          opacity: 0.7;\n        }\n\n        .form-input.is-invalid {\n          border-color: #dc3545;\n        }\n\n        .navigation-buttons {\n          display: flex;\n          gap: 12px;\n        }\n\n        .back-button {\n          width: 50px;\n          height: 45px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-color: #0071c2;\n          color: #0071c2;\n          border-radius: 6px;\n        }\n\n        .back-button:hover {\n          background-color: #0071c2;\n          color: white;\n        }\n\n        .continue-button {\n          flex: 1;\n          height: 45px;\n          background-color: #0071c2;\n          border: none;\n          font-weight: 600;\n          border-radius: 6px;\n          transition: background-color 0.15s ease-in-out;\n        }\n\n        .continue-button:hover:not(:disabled) {\n          background-color: #005999;\n        }\n\n        .continue-button:disabled {\n          background-color: #6c757d;\n          border-color: #6c757d;\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .info-card {\n          background-color: #fff;\n          border-radius: 8px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          border: 1px solid #e9ecef;\n          transition: transform 0.2s ease-in-out;\n        }\n\n        .info-card:hover {\n          transform: translateY(-2px);\n        }\n\n        .info-icon {\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 50%;\n          font-size: 20px;\n          background-color: #f8f9fa;\n          margin-right: 16px;\n          flex-shrink: 0;\n        }\n\n        .info-content {\n          flex: 1;\n        }\n\n        .info-title {\n          font-size: 16px;\n          font-weight: 600;\n          margin-bottom: 0;\n          color: #333;\n        }\n\n        .info-list {\n          padding-left: 20px;\n          margin-bottom: 0;\n        }\n\n        .info-list li {\n          margin-bottom: 8px;\n          color: #666;\n        }\n\n        .info-text {\n          font-size: 14px;\n          color: #666;\n          margin-bottom: 0;\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .main-heading {\n            font-size: 24px;\n          }\n\n          .property-form-card {\n            padding: 16px;\n          }\n\n          .navigation-buttons {\n            flex-direction: column;\n          }\n\n          .back-button {\n            width: 100%;\n            order: 2;\n          }\n\n          .continue-button {\n            order: 1;\n            margin-bottom: 12px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n}\n_s(BookingLocation, \"x3hWwxOpJ0j5nxC4Ho17pY/qu4c=\", false, function () {\n  return [useNavigate, useAppDispatch, useAppSelector];\n});\n_c = BookingLocation;\nvar _c;\n$RefreshReg$(_c, \"BookingLocation\");", "map": {"version": 3, "names": ["Routers", "React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "Container", "<PERSON><PERSON>", "Form", "Card", "ProgressBar", "Row", "Col", "ArrowLeft", "cityOptionSelect", "districtsByCity", "wardsByDistrict", "useNavigate", "useAppDispatch", "useAppSelector", "showToast", "ToastProvider", "HotelActions", "jsxDEV", "_jsxDEV", "BookingLocation", "_s", "navigate", "dispatch", "createHotel", "state", "Hotel", "console", "log", "selectedCity", "setSelectedCity", "city", "selectedDistrict", "setSelectedDistrict", "district", "<PERSON><PERSON><PERSON>", "setSelectedWard", "ward", "specificAddress", "setSpecificAddress", "<PERSON><PERSON><PERSON><PERSON>", "setGeneralAdd<PERSON>", "address", "availableDistricts", "setAvailableDistricts", "availableWards", "setAvailableWards", "districts", "wards", "isDistrictValid", "some", "d", "value", "isWard<PERSON>alid", "w", "addressParts", "trim", "push", "join", "handleContinue", "warning", "type", "SAVE_HOTEL_ADDRESS_CREATE", "payload", "BookingPropertyFacility", "handleBack", "BookingPropertyName", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundColor", "Brand", "href", "fontSize", "color", "height", "variant", "now", "md", "Group", "Label", "Select", "onChange", "e", "target", "isInvalid", "map", "label", "disabled", "Text", "Control", "placeholder", "onClick", "title", "size", "Body", "role", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/pages/hotel_host/create_hotel/BookingPropertyLocation.jsx"], "sourcesContent": ["import * as Routers from \"../../../utils/Routes\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Navbar,\r\n  Container,\r\n  Button,\r\n  Form,\r\n  Card,\r\n  ProgressBar,\r\n  Row,\r\n  Col,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { ArrowLeft } from \"lucide-react\";\r\nimport {\r\n  cityOptionSelect,\r\n  districtsByCity,\r\n  wardsByDistrict,\r\n} from \"@utils/data\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useAppDispatch, useAppSelector } from \"@redux/store\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\n\r\nexport default function BookingLocation() {\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const createHotel = useAppSelector((state) => state.Hotel.createHotel);\r\n  console.log(cityOptionSelect, districtsByCity, wardsByDistrict);\r\n  // Initialize state with values from Redux store or empty strings\r\n  const [selectedCity, setSelectedCity] = useState(createHotel?.city || \"\");\r\n  const [selectedDistrict, setSelectedDistrict] = useState(\r\n    createHotel?.district || \"\"\r\n  );\r\n  const [selectedWard, setSelectedWard] = useState(createHotel?.ward || \"\");\r\n  const [specificAddress, setSpecificAddress] = useState(\r\n    createHotel?.specificAddress || \"\"\r\n  );\r\n  const [generalAddress, setGeneralAddress] = useState(\r\n    createHotel?.address || \"\"\r\n  );\r\n\r\n  const [availableDistricts, setAvailableDistricts] = useState([]);\r\n  const [availableWards, setAvailableWards] = useState([]);\r\n\r\n  // Initialize districts and wards when component mounts with existing data\r\n  useEffect(() => {\r\n    if (selectedCity) {\r\n      const districts = districtsByCity[selectedCity] || [];\r\n      setAvailableDistricts(districts);\r\n\r\n      if (selectedDistrict) {\r\n        const wards = wardsByDistrict[selectedDistrict] || [];\r\n        setAvailableWards(wards);\r\n      }\r\n    }\r\n  }, []); // Run only on mount\r\n\r\n  // Update districts when city changes\r\n  useEffect(() => {\r\n    if (selectedCity) {\r\n      const districts = districtsByCity[selectedCity] || [];\r\n      setAvailableDistricts(districts);\r\n\r\n      // Only reset district and ward if the current district is not valid for the new city\r\n      const isDistrictValid = districts.some(\r\n        (d) => d.value === selectedDistrict\r\n      );\r\n      if (!isDistrictValid) {\r\n        setSelectedDistrict(\"\");\r\n        setSelectedWard(\"\");\r\n        setAvailableWards([]);\r\n      }\r\n    } else {\r\n      setAvailableDistricts([]);\r\n      setSelectedDistrict(\"\");\r\n      setSelectedWard(\"\");\r\n      setAvailableWards([]);\r\n    }\r\n  }, [selectedCity]);\r\n\r\n  // Update wards when district changes\r\n  useEffect(() => {\r\n    if (selectedDistrict) {\r\n      const wards = wardsByDistrict[selectedDistrict] || [];\r\n      setAvailableWards(wards);\r\n\r\n      // Only reset ward if the current ward is not valid for the new district\r\n      const isWardValid = wards.some((w) => w.value === selectedWard);\r\n      if (!isWardValid) {\r\n        setSelectedWard(\"\");\r\n      }\r\n    } else {\r\n      setAvailableWards([]);\r\n      setSelectedWard(\"\");\r\n    }\r\n  }, [selectedDistrict]);\r\n\r\n  // Update general address when location changes\r\n  useEffect(() => {\r\n    const addressParts = [];\r\n    if (specificAddress?.trim()) addressParts.push(specificAddress.trim());\r\n\r\n    if (selectedWard) {\r\n      addressParts.push(`${selectedWard}`);\r\n    }\r\n\r\n    if (selectedDistrict) {\r\n      addressParts.push(`${selectedDistrict}`);\r\n    }\r\n\r\n    if (selectedCity) addressParts.push(`${selectedCity}`);\r\n\r\n    setGeneralAddress(addressParts.join(\", \"));\r\n  }, [specificAddress, selectedWard, selectedDistrict, selectedCity]);\r\n\r\n  const handleContinue = () => {\r\n    // Validate required fields\r\n    if (!selectedCity) {\r\n      showToast.warning(\"Vui lòng chọn thành phố\");\r\n      return;\r\n    }\r\n    if (!selectedDistrict) {\r\n      showToast.warning(\"Vui lòng chọn quận/huyện\");\r\n      return;\r\n    }\r\n    if (!selectedWard) {\r\n      showToast.warning(\"Vui lòng chọn phường/xã\");\r\n      return;\r\n    }\r\n    if (!specificAddress?.trim()) {\r\n      showToast.warning(\"Vui lòng nhập địa chỉ cụ thể\");\r\n      return;\r\n    }\r\n\r\n    // Dispatch action to save data\r\n    dispatch({\r\n      type: HotelActions.SAVE_HOTEL_ADDRESS_CREATE,\r\n      payload: {\r\n        specificAddress: specificAddress.trim(),\r\n        address: generalAddress,\r\n        city: selectedCity,\r\n        district: selectedDistrict,\r\n        ward: selectedWard,\r\n      },\r\n    });\r\n\r\n    // Navigate to next step\r\n    navigate(Routers.BookingPropertyFacility);\r\n  };\r\n\r\n  const handleBack = () => {\r\n    // Save current data before going back\r\n    dispatch({\r\n      type: HotelActions.SAVE_HOTEL_ADDRESS_CREATE,\r\n      payload: {\r\n        specificAddress: specificAddress?.trim() || \"\",\r\n        address: generalAddress,\r\n        city: selectedCity,\r\n        district: selectedDistrict,\r\n        ward: selectedWard,\r\n      },\r\n    });\r\n\r\n    // Navigate back (you can replace with actual back route)\r\n    navigate(Routers.BookingPropertyName);\r\n  };\r\n\r\n  return (\r\n    <div className=\"booking-app\">\r\n      <ToastProvider />\r\n\r\n      {/* Navigation Bar */}\r\n      <Navbar style={{ backgroundColor: \"#003580\" }}>\r\n        <Container>\r\n          <Navbar.Brand href=\"#home\" className=\"text-white fw-bold\">\r\n            <b style={{ fontSize: 30 }}>\r\n              UR<span style={{ color: \"#f8e71c\" }}>OO</span>M\r\n            </b>\r\n          </Navbar.Brand>\r\n        </Container>\r\n      </Navbar>\r\n\r\n      {/* Progress Bar */}\r\n      <Container className=\"mt-4\">\r\n        <div className=\"progress-section\">\r\n          <div className=\"progress-label mb-2\">\r\n            <h5>Thông tin cơ bản</h5>\r\n          </div>\r\n          <ProgressBar style={{ height: \"20px\" }}>\r\n            <ProgressBar variant=\"primary\" now={20} key={1} />\r\n            <ProgressBar variant=\"primary\" now={20} key={2} />\r\n            <ProgressBar variant=\"secondary\" now={20} key={3} />\r\n            <ProgressBar variant=\"secondary\" now={20} key={4} />\r\n            <ProgressBar variant=\"secondary\" now={20} key={5} />\r\n          </ProgressBar>\r\n        </div>\r\n      </Container>\r\n\r\n      {/* Main Content */}\r\n      <Container className=\"main-content py-4\">\r\n        <Row>\r\n          <Col md={7}>\r\n            <div className=\"mb-4\">\r\n              <h1 className=\"main-heading\">Chỗ nghỉ của Quý vị ở đâu?</h1>\r\n            </div>\r\n\r\n            {/* Property Form */}\r\n            <div className=\"property-form-card\">\r\n              <Form>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"fw-bold\">\r\n                    Thành phố <span className=\"text-danger\">*</span>\r\n                  </Form.Label>\r\n                  <Form.Select\r\n                    className=\"form-input\"\r\n                    value={selectedCity}\r\n                    onChange={(e) => setSelectedCity(e.target.value)}\r\n                    isInvalid={!selectedCity}\r\n                  >\r\n                    <option value=\"\">Chọn thành phố</option>\r\n                    {cityOptionSelect.map((city) => (\r\n                      <option key={city.value} value={city.value}>\r\n                        {city.label}\r\n                      </option>\r\n                    ))}\r\n                  </Form.Select>\r\n                </Form.Group>\r\n\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"fw-bold\">\r\n                    Quận/Huyện <span className=\"text-danger\">*</span>\r\n                  </Form.Label>\r\n                  <Form.Select\r\n                    className=\"form-input\"\r\n                    value={selectedDistrict}\r\n                    onChange={(e) => setSelectedDistrict(e.target.value)}\r\n                    disabled={!selectedCity}\r\n                    isInvalid={selectedCity && !selectedDistrict}\r\n                  >\r\n                    <option value=\"\">Chọn quận/huyện</option>\r\n                    {availableDistricts.map((district) => (\r\n                      <option key={district.value} value={district.value}>\r\n                        {district.label}\r\n                      </option>\r\n                    ))}\r\n                  </Form.Select>\r\n                  {!selectedCity && (\r\n                    <Form.Text className=\"text-muted\">\r\n                      Vui lòng chọn thành phố trước\r\n                    </Form.Text>\r\n                  )}\r\n                </Form.Group>\r\n\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"fw-bold\">\r\n                    Phường/Xã <span className=\"text-danger\">*</span>\r\n                  </Form.Label>\r\n                  <Form.Select\r\n                    className=\"form-input\"\r\n                    value={selectedWard}\r\n                    onChange={(e) => setSelectedWard(e.target.value)}\r\n                    disabled={!selectedDistrict}\r\n                    isInvalid={selectedDistrict && !selectedWard}\r\n                  >\r\n                    <option value=\"\">Chọn phường/xã</option>\r\n                    {availableWards.map((ward) => (\r\n                      <option key={ward.value} value={ward.value}>\r\n                        {ward.label}\r\n                      </option>\r\n                    ))}\r\n                  </Form.Select>\r\n                  {!selectedDistrict && (\r\n                    <Form.Text className=\"text-muted\">\r\n                      Vui lòng chọn quận/huyện trước\r\n                    </Form.Text>\r\n                  )}\r\n                </Form.Group>\r\n\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"fw-bold\">\r\n                    Địa chỉ cụ thể <span className=\"text-danger\">*</span>\r\n                  </Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    placeholder=\"Nhập số nhà, tên đường...\"\r\n                    className=\"form-input\"\r\n                    value={specificAddress}\r\n                    onChange={(e) => setSpecificAddress(e.target.value)}\r\n                    isInvalid={!specificAddress?.trim()}\r\n                  />\r\n                  <Form.Text className=\"text-muted\">\r\n                    Ví dụ: 123 Đường Nguyễn Văn A\r\n                  </Form.Text>\r\n                </Form.Group>\r\n\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"fw-bold\">Địa chỉ tổng quát</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    placeholder=\"Địa chỉ sẽ được tạo tự động\"\r\n                    className=\"form-input\"\r\n                    value={generalAddress}\r\n                    disabled\r\n                  />\r\n                  <Form.Text className=\"text-muted\">\r\n                    Địa chỉ này sẽ hiển thị cho khách hàng\r\n                  </Form.Text>\r\n                </Form.Group>\r\n              </Form>\r\n            </div>\r\n\r\n            <div className=\"navigation-buttons mt-4\">\r\n              <Button\r\n                variant=\"outline-primary\"\r\n                className=\"back-button\"\r\n                onClick={handleBack}\r\n                title=\"Quay lại\"\r\n              >\r\n                <ArrowLeft size={20} />\r\n              </Button>\r\n              <Button\r\n                variant=\"primary\"\r\n                className=\"continue-button\"\r\n                onClick={handleContinue}\r\n                disabled={\r\n                  !selectedCity ||\r\n                  !selectedDistrict ||\r\n                  !selectedWard ||\r\n                  !specificAddress?.trim()\r\n                }\r\n              >\r\n                Tiếp tục\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n\r\n          <Col md={5}>\r\n            <div className=\"info-cards\">\r\n              {/* First Info Card */}\r\n              <Card className=\"info-card mb-4\">\r\n                <Card.Body>\r\n                  <div className=\"d-flex align-items-start\">\r\n                    <div className=\"info-icon thumbs-up\">\r\n                      <span role=\"img\" aria-label=\"thumbs up\">\r\n                        👍\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"info-content\">\r\n                      <h5 className=\"info-title\">\r\n                        Tôi nên chú ý điều gì khi điền thông tin địa chỉ?\r\n                      </h5>\r\n                      <ul className=\"info-list mt-3\">\r\n                        <li>Chọn thành phố, quận, phường đúng</li>\r\n                        <li>Tránh sử dụng chữ viết tắt</li>\r\n                        <li>Đúng với thực tế trên Google Maps</li>\r\n                        <li>Điền đầy đủ số nhà và tên đường</li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n\r\n              {/* Second Info Card */}\r\n              <Card className=\"info-card\">\r\n                <Card.Body>\r\n                  <div className=\"d-flex align-items-start\">\r\n                    <div className=\"info-icon lightbulb\">\r\n                      <span role=\"img\" aria-label=\"lightbulb\">\r\n                        💡\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"info-content\">\r\n                      <h5 className=\"info-title\">\r\n                        Tại sao tôi cần điền đúng địa chỉ cho chỗ nghỉ của mình?\r\n                      </h5>\r\n                      <p className=\"info-text mt-3\">\r\n                        Địa chỉ chính xác giúp khách hàng dễ dàng tìm thấy chỗ\r\n                        nghỉ của bạn. Thông tin này sẽ hiển thị trên bản đồ và\r\n                        trong kết quả tìm kiếm, giúp tăng độ tin cậy và khả năng\r\n                        đặt phòng từ khách hàng.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n\r\n      <style jsx>{`\r\n        .booking-app {\r\n          min-height: 100vh;\r\n          background-color: #f8f9fa;\r\n        }\r\n\r\n        .progress-section {\r\n          margin: 0 auto;\r\n        }\r\n\r\n        .progress-label {\r\n          font-size: 14px;\r\n          color: #333;\r\n        }\r\n\r\n        .main-content {\r\n          max-width: 1200px;\r\n          margin: 0 auto;\r\n        }\r\n\r\n        .main-heading {\r\n          font-size: 28px;\r\n          font-weight: bold;\r\n          color: #333;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .property-form-card {\r\n          background-color: #fff;\r\n          border-radius: 8px;\r\n          padding: 24px;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n          border: 1px solid #e9ecef;\r\n        }\r\n\r\n        .form-input {\r\n          height: 45px;\r\n          border: 1px solid #ced4da;\r\n          border-radius: 6px;\r\n          font-size: 16px;\r\n          transition: border-color 0.15s ease-in-out,\r\n            box-shadow 0.15s ease-in-out;\r\n        }\r\n\r\n        .form-input:focus {\r\n          border-color: #0071c2;\r\n          box-shadow: 0 0 0 0.2rem rgba(0, 113, 194, 0.25);\r\n        }\r\n\r\n        .form-input:disabled {\r\n          background-color: #f8f9fa;\r\n          opacity: 0.7;\r\n        }\r\n\r\n        .form-input.is-invalid {\r\n          border-color: #dc3545;\r\n        }\r\n\r\n        .navigation-buttons {\r\n          display: flex;\r\n          gap: 12px;\r\n        }\r\n\r\n        .back-button {\r\n          width: 50px;\r\n          height: 45px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-color: #0071c2;\r\n          color: #0071c2;\r\n          border-radius: 6px;\r\n        }\r\n\r\n        .back-button:hover {\r\n          background-color: #0071c2;\r\n          color: white;\r\n        }\r\n\r\n        .continue-button {\r\n          flex: 1;\r\n          height: 45px;\r\n          background-color: #0071c2;\r\n          border: none;\r\n          font-weight: 600;\r\n          border-radius: 6px;\r\n          transition: background-color 0.15s ease-in-out;\r\n        }\r\n\r\n        .continue-button:hover:not(:disabled) {\r\n          background-color: #005999;\r\n        }\r\n\r\n        .continue-button:disabled {\r\n          background-color: #6c757d;\r\n          border-color: #6c757d;\r\n          opacity: 0.6;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .info-card {\r\n          background-color: #fff;\r\n          border-radius: 8px;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n          border: 1px solid #e9ecef;\r\n          transition: transform 0.2s ease-in-out;\r\n        }\r\n\r\n        .info-card:hover {\r\n          transform: translateY(-2px);\r\n        }\r\n\r\n        .info-icon {\r\n          width: 40px;\r\n          height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 50%;\r\n          font-size: 20px;\r\n          background-color: #f8f9fa;\r\n          margin-right: 16px;\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .info-content {\r\n          flex: 1;\r\n        }\r\n\r\n        .info-title {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          margin-bottom: 0;\r\n          color: #333;\r\n        }\r\n\r\n        .info-list {\r\n          padding-left: 20px;\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .info-list li {\r\n          margin-bottom: 8px;\r\n          color: #666;\r\n        }\r\n\r\n        .info-text {\r\n          font-size: 14px;\r\n          color: #666;\r\n          margin-bottom: 0;\r\n          line-height: 1.6;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .main-heading {\r\n            font-size: 24px;\r\n          }\r\n\r\n          .property-form-card {\r\n            padding: 16px;\r\n          }\r\n\r\n          .navigation-buttons {\r\n            flex-direction: column;\r\n          }\r\n\r\n          .back-button {\r\n            width: 100%;\r\n            order: 2;\r\n          }\r\n\r\n          .continue-button {\r\n            order: 1;\r\n            margin-bottom: 12px;\r\n          }\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAO,KAAKA,OAAO,MAAM,uBAAuB;AAChD,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,GAAG,EACHC,GAAG,QACE,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,SAAS,QAAQ,cAAc;AACxC,SACEC,gBAAgB,EAChBC,eAAe,EACfC,eAAe,QACV,aAAa;AACpB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,eAAe,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,cAAc,CAAC,CAAC;EACjC,MAAMW,WAAW,GAAGV,cAAc,CAAEW,KAAK,IAAKA,KAAK,CAACC,KAAK,CAACF,WAAW,CAAC;EACtEG,OAAO,CAACC,GAAG,CAACnB,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,CAAC;EAC/D;EACA,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,CAAA0B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEO,IAAI,KAAI,EAAE,CAAC;EACzE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CACtD,CAAA0B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,QAAQ,KAAI,EAC3B,CAAC;EACD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,CAAA0B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEa,IAAI,KAAI,EAAE,CAAC;EACzE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CACpD,CAAA0B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,eAAe,KAAI,EAClC,CAAC;EACD,MAAM,CAACE,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAClD,CAAA0B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkB,OAAO,KAAI,EAC1B,CAAC;EAED,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACAC,SAAS,CAAC,MAAM;IACd,IAAI8B,YAAY,EAAE;MAChB,MAAMkB,SAAS,GAAGrC,eAAe,CAACmB,YAAY,CAAC,IAAI,EAAE;MACrDe,qBAAqB,CAACG,SAAS,CAAC;MAEhC,IAAIf,gBAAgB,EAAE;QACpB,MAAMgB,KAAK,GAAGrC,eAAe,CAACqB,gBAAgB,CAAC,IAAI,EAAE;QACrDc,iBAAiB,CAACE,KAAK,CAAC;MAC1B;IACF;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAjD,SAAS,CAAC,MAAM;IACd,IAAI8B,YAAY,EAAE;MAChB,MAAMkB,SAAS,GAAGrC,eAAe,CAACmB,YAAY,CAAC,IAAI,EAAE;MACrDe,qBAAqB,CAACG,SAAS,CAAC;;MAEhC;MACA,MAAME,eAAe,GAAGF,SAAS,CAACG,IAAI,CACnCC,CAAC,IAAKA,CAAC,CAACC,KAAK,KAAKpB,gBACrB,CAAC;MACD,IAAI,CAACiB,eAAe,EAAE;QACpBhB,mBAAmB,CAAC,EAAE,CAAC;QACvBG,eAAe,CAAC,EAAE,CAAC;QACnBU,iBAAiB,CAAC,EAAE,CAAC;MACvB;IACF,CAAC,MAAM;MACLF,qBAAqB,CAAC,EAAE,CAAC;MACzBX,mBAAmB,CAAC,EAAE,CAAC;MACvBG,eAAe,CAAC,EAAE,CAAC;MACnBU,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC,EAAE,CAACjB,YAAY,CAAC,CAAC;;EAElB;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIiC,gBAAgB,EAAE;MACpB,MAAMgB,KAAK,GAAGrC,eAAe,CAACqB,gBAAgB,CAAC,IAAI,EAAE;MACrDc,iBAAiB,CAACE,KAAK,CAAC;;MAExB;MACA,MAAMK,WAAW,GAAGL,KAAK,CAACE,IAAI,CAAEI,CAAC,IAAKA,CAAC,CAACF,KAAK,KAAKjB,YAAY,CAAC;MAC/D,IAAI,CAACkB,WAAW,EAAE;QAChBjB,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC,MAAM;MACLU,iBAAiB,CAAC,EAAE,CAAC;MACrBV,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC,EAAE,CAACJ,gBAAgB,CAAC,CAAC;;EAEtB;EACAjC,SAAS,CAAC,MAAM;IACd,MAAMwD,YAAY,GAAG,EAAE;IACvB,IAAIjB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEkB,IAAI,CAAC,CAAC,EAAED,YAAY,CAACE,IAAI,CAACnB,eAAe,CAACkB,IAAI,CAAC,CAAC,CAAC;IAEtE,IAAIrB,YAAY,EAAE;MAChBoB,YAAY,CAACE,IAAI,CAAC,GAAGtB,YAAY,EAAE,CAAC;IACtC;IAEA,IAAIH,gBAAgB,EAAE;MACpBuB,YAAY,CAACE,IAAI,CAAC,GAAGzB,gBAAgB,EAAE,CAAC;IAC1C;IAEA,IAAIH,YAAY,EAAE0B,YAAY,CAACE,IAAI,CAAC,GAAG5B,YAAY,EAAE,CAAC;IAEtDY,iBAAiB,CAACc,YAAY,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5C,CAAC,EAAE,CAACpB,eAAe,EAAEH,YAAY,EAAEH,gBAAgB,EAAEH,YAAY,CAAC,CAAC;EAEnE,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAI,CAAC9B,YAAY,EAAE;MACjBd,SAAS,CAAC6C,OAAO,CAAC,yBAAyB,CAAC;MAC5C;IACF;IACA,IAAI,CAAC5B,gBAAgB,EAAE;MACrBjB,SAAS,CAAC6C,OAAO,CAAC,0BAA0B,CAAC;MAC7C;IACF;IACA,IAAI,CAACzB,YAAY,EAAE;MACjBpB,SAAS,CAAC6C,OAAO,CAAC,yBAAyB,CAAC;MAC5C;IACF;IACA,IAAI,EAACtB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEkB,IAAI,CAAC,CAAC,GAAE;MAC5BzC,SAAS,CAAC6C,OAAO,CAAC,8BAA8B,CAAC;MACjD;IACF;;IAEA;IACArC,QAAQ,CAAC;MACPsC,IAAI,EAAE5C,YAAY,CAAC6C,yBAAyB;MAC5CC,OAAO,EAAE;QACPzB,eAAe,EAAEA,eAAe,CAACkB,IAAI,CAAC,CAAC;QACvCd,OAAO,EAAEF,cAAc;QACvBT,IAAI,EAAEF,YAAY;QAClBK,QAAQ,EAAEF,gBAAgB;QAC1BK,IAAI,EAAEF;MACR;IACF,CAAC,CAAC;;IAEF;IACAb,QAAQ,CAAC1B,OAAO,CAACoE,uBAAuB,CAAC;EAC3C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA1C,QAAQ,CAAC;MACPsC,IAAI,EAAE5C,YAAY,CAAC6C,yBAAyB;MAC5CC,OAAO,EAAE;QACPzB,eAAe,EAAE,CAAAA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkB,IAAI,CAAC,CAAC,KAAI,EAAE;QAC9Cd,OAAO,EAAEF,cAAc;QACvBT,IAAI,EAAEF,YAAY;QAClBK,QAAQ,EAAEF,gBAAgB;QAC1BK,IAAI,EAAEF;MACR;IACF,CAAC,CAAC;;IAEF;IACAb,QAAQ,CAAC1B,OAAO,CAACsE,mBAAmB,CAAC;EACvC,CAAC;EAED,oBACE/C,OAAA;IAAKgD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BjD,OAAA,CAACH,aAAa;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGjBrD,OAAA,CAACnB,MAAM;MAACyE,KAAK,EAAE;QAAEC,eAAe,EAAE;MAAU,CAAE;MAAAN,QAAA,eAC5CjD,OAAA,CAAClB,SAAS;QAAAmE,QAAA,eACRjD,OAAA,CAACnB,MAAM,CAAC2E,KAAK;UAACC,IAAI,EAAC,OAAO;UAACT,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACvDjD,OAAA;YAAGsD,KAAK,EAAE;cAAEI,QAAQ,EAAE;YAAG,CAAE;YAAAT,QAAA,GAAC,IACxB,eAAAjD,OAAA;cAAMsD,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAV,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGTrD,OAAA,CAAClB,SAAS;MAACkE,SAAS,EAAC,MAAM;MAAAC,QAAA,eACzBjD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BjD,OAAA;UAAKgD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCjD,OAAA;YAAAiD,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNrD,OAAA,CAACd,WAAW;UAACoE,KAAK,EAAE;YAAEM,MAAM,EAAE;UAAO,CAAE;UAAAX,QAAA,gBACrCjD,OAAA,CAACd,WAAW;YAAC2E,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDrD,OAAA,CAACd,WAAW;YAAC2E,OAAO,EAAC,SAAS;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDrD,OAAA,CAACd,WAAW;YAAC2E,OAAO,EAAC,WAAW;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpDrD,OAAA,CAACd,WAAW;YAAC2E,OAAO,EAAC,WAAW;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpDrD,OAAA,CAACd,WAAW;YAAC2E,OAAO,EAAC,WAAW;YAACC,GAAG,EAAE;UAAG,GAAM,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZrD,OAAA,CAAClB,SAAS;MAACkE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACtCjD,OAAA,CAACb,GAAG;QAAA8D,QAAA,gBACFjD,OAAA,CAACZ,GAAG;UAAC2E,EAAE,EAAE,CAAE;UAAAd,QAAA,gBACTjD,OAAA;YAAKgD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBjD,OAAA;cAAIgD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAGNrD,OAAA;YAAKgD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCjD,OAAA,CAAChB,IAAI;cAAAiE,QAAA,gBACHjD,OAAA,CAAChB,IAAI,CAACgF,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BjD,OAAA,CAAChB,IAAI,CAACiF,KAAK;kBAACjB,SAAS,EAAC,SAAS;kBAAAC,QAAA,GAAC,oBACpB,eAAAjD,OAAA;oBAAMgD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbrD,OAAA,CAAChB,IAAI,CAACkF,MAAM;kBACVlB,SAAS,EAAC,YAAY;kBACtBf,KAAK,EAAEvB,YAAa;kBACpByD,QAAQ,EAAGC,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;kBACjDqC,SAAS,EAAE,CAAC5D,YAAa;kBAAAuC,QAAA,gBAEzBjD,OAAA;oBAAQiC,KAAK,EAAC,EAAE;oBAAAgB,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvC/D,gBAAgB,CAACiF,GAAG,CAAE3D,IAAI,iBACzBZ,OAAA;oBAAyBiC,KAAK,EAAErB,IAAI,CAACqB,KAAM;oBAAAgB,QAAA,EACxCrC,IAAI,CAAC4D;kBAAK,GADA5D,IAAI,CAACqB,KAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEbrD,OAAA,CAAChB,IAAI,CAACgF,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BjD,OAAA,CAAChB,IAAI,CAACiF,KAAK;kBAACjB,SAAS,EAAC,SAAS;kBAAAC,QAAA,GAAC,uBACnB,eAAAjD,OAAA;oBAAMgD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACbrD,OAAA,CAAChB,IAAI,CAACkF,MAAM;kBACVlB,SAAS,EAAC,YAAY;kBACtBf,KAAK,EAAEpB,gBAAiB;kBACxBsD,QAAQ,EAAGC,CAAC,IAAKtD,mBAAmB,CAACsD,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;kBACrDwC,QAAQ,EAAE,CAAC/D,YAAa;kBACxB4D,SAAS,EAAE5D,YAAY,IAAI,CAACG,gBAAiB;kBAAAoC,QAAA,gBAE7CjD,OAAA;oBAAQiC,KAAK,EAAC,EAAE;oBAAAgB,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACxC7B,kBAAkB,CAAC+C,GAAG,CAAExD,QAAQ,iBAC/Bf,OAAA;oBAA6BiC,KAAK,EAAElB,QAAQ,CAACkB,KAAM;oBAAAgB,QAAA,EAChDlC,QAAQ,CAACyD;kBAAK,GADJzD,QAAQ,CAACkB,KAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,EACb,CAAC3C,YAAY,iBACZV,OAAA,CAAChB,IAAI,CAAC0F,IAAI;kBAAC1B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAEbrD,OAAA,CAAChB,IAAI,CAACgF,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BjD,OAAA,CAAChB,IAAI,CAACiF,KAAK;kBAACjB,SAAS,EAAC,SAAS;kBAAAC,QAAA,GAAC,yBACpB,eAAAjD,OAAA;oBAAMgD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbrD,OAAA,CAAChB,IAAI,CAACkF,MAAM;kBACVlB,SAAS,EAAC,YAAY;kBACtBf,KAAK,EAAEjB,YAAa;kBACpBmD,QAAQ,EAAGC,CAAC,IAAKnD,eAAe,CAACmD,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;kBACjDwC,QAAQ,EAAE,CAAC5D,gBAAiB;kBAC5ByD,SAAS,EAAEzD,gBAAgB,IAAI,CAACG,YAAa;kBAAAiC,QAAA,gBAE7CjD,OAAA;oBAAQiC,KAAK,EAAC,EAAE;oBAAAgB,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvC3B,cAAc,CAAC6C,GAAG,CAAErD,IAAI,iBACvBlB,OAAA;oBAAyBiC,KAAK,EAAEf,IAAI,CAACe,KAAM;oBAAAgB,QAAA,EACxC/B,IAAI,CAACsD;kBAAK,GADAtD,IAAI,CAACe,KAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,EACb,CAACxC,gBAAgB,iBAChBb,OAAA,CAAChB,IAAI,CAAC0F,IAAI;kBAAC1B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAEbrD,OAAA,CAAChB,IAAI,CAACgF,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BjD,OAAA,CAAChB,IAAI,CAACiF,KAAK;kBAACjB,SAAS,EAAC,SAAS;kBAAAC,QAAA,GAAC,0CACf,eAAAjD,OAAA;oBAAMgD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACbrD,OAAA,CAAChB,IAAI,CAAC2F,OAAO;kBACXjC,IAAI,EAAC,MAAM;kBACXkC,WAAW,EAAC,0DAA2B;kBACvC5B,SAAS,EAAC,YAAY;kBACtBf,KAAK,EAAEd,eAAgB;kBACvBgD,QAAQ,EAAGC,CAAC,IAAKhD,kBAAkB,CAACgD,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;kBACpDqC,SAAS,EAAE,EAACnD,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEkB,IAAI,CAAC,CAAC;gBAAC;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACFrD,OAAA,CAAChB,IAAI,CAAC0F,IAAI;kBAAC1B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEbrD,OAAA,CAAChB,IAAI,CAACgF,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BjD,OAAA,CAAChB,IAAI,CAACiF,KAAK;kBAACjB,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9DrD,OAAA,CAAChB,IAAI,CAAC2F,OAAO;kBACXjC,IAAI,EAAC,MAAM;kBACXkC,WAAW,EAAC,oFAA6B;kBACzC5B,SAAS,EAAC,YAAY;kBACtBf,KAAK,EAAEZ,cAAe;kBACtBoD,QAAQ;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACFrD,OAAA,CAAChB,IAAI,CAAC0F,IAAI;kBAAC1B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENrD,OAAA;YAAKgD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCjD,OAAA,CAACjB,MAAM;cACL8E,OAAO,EAAC,iBAAiB;cACzBb,SAAS,EAAC,aAAa;cACvB6B,OAAO,EAAE/B,UAAW;cACpBgC,KAAK,EAAC,eAAU;cAAA7B,QAAA,eAEhBjD,OAAA,CAACX,SAAS;gBAAC0F,IAAI,EAAE;cAAG;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTrD,OAAA,CAACjB,MAAM;cACL8E,OAAO,EAAC,SAAS;cACjBb,SAAS,EAAC,iBAAiB;cAC3B6B,OAAO,EAAErC,cAAe;cACxBiC,QAAQ,EACN,CAAC/D,YAAY,IACb,CAACG,gBAAgB,IACjB,CAACG,YAAY,IACb,EAACG,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEkB,IAAI,CAAC,CAAC,CACzB;cAAAY,QAAA,EACF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrD,OAAA,CAACZ,GAAG;UAAC2E,EAAE,EAAE,CAAE;UAAAd,QAAA,eACTjD,OAAA;YAAKgD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEzBjD,OAAA,CAACf,IAAI;cAAC+D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC9BjD,OAAA,CAACf,IAAI,CAAC+F,IAAI;gBAAA/B,QAAA,eACRjD,OAAA;kBAAKgD,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCjD,OAAA;oBAAKgD,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClCjD,OAAA;sBAAMiF,IAAI,EAAC,KAAK;sBAAC,cAAW,WAAW;sBAAAhC,QAAA,EAAC;oBAExC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNrD,OAAA;oBAAKgD,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BjD,OAAA;sBAAIgD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAE3B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrD,OAAA;sBAAIgD,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC5BjD,OAAA;wBAAAiD,QAAA,EAAI;sBAAiC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1CrD,OAAA;wBAAAiD,QAAA,EAAI;sBAA0B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnCrD,OAAA;wBAAAiD,QAAA,EAAI;sBAAiC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1CrD,OAAA;wBAAAiD,QAAA,EAAI;sBAA+B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGPrD,OAAA,CAACf,IAAI;cAAC+D,SAAS,EAAC,WAAW;cAAAC,QAAA,eACzBjD,OAAA,CAACf,IAAI,CAAC+F,IAAI;gBAAA/B,QAAA,eACRjD,OAAA;kBAAKgD,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCjD,OAAA;oBAAKgD,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClCjD,OAAA;sBAAMiF,IAAI,EAAC,KAAK;sBAAC,cAAW,WAAW;sBAAAhC,QAAA,EAAC;oBAExC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNrD,OAAA;oBAAKgD,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BjD,OAAA;sBAAIgD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAE3B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrD,OAAA;sBAAGgD,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,EAAC;oBAK9B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEZrD,OAAA;MAAOkF,GAAG;MAAAjC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACnD,EAAA,CAliBuBD,eAAe;EAAA,QACpBR,WAAW,EACXC,cAAc,EACXC,cAAc;AAAA;AAAAwF,EAAA,GAHZlF,eAAe;AAAA,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}