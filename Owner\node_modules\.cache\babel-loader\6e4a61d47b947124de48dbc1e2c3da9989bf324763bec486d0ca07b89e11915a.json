{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  getDraftModeProviderForCacheScope: null,\n  getExpectedRequestStore: null,\n  getHmrRefreshHash: null,\n  getPrerenderResumeDataCache: null,\n  getRenderResumeDataCache: null,\n  throwForMissingRequestStore: null,\n  workUnitAsyncStorage: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  getDraftModeProviderForCacheScope: function () {\n    return getDraftModeProviderForCacheScope;\n  },\n  getExpectedRequestStore: function () {\n    return getExpectedRequestStore;\n  },\n  getHmrRefreshHash: function () {\n    return getHmrRefreshHash;\n  },\n  getPrerenderResumeDataCache: function () {\n    return getPrerenderResumeDataCache;\n  },\n  getRenderResumeDataCache: function () {\n    return getRenderResumeDataCache;\n  },\n  throwForMissingRequestStore: function () {\n    return throwForMissingRequestStore;\n  },\n  workUnitAsyncStorage: function () {\n    return _workunitasyncstorageinstance.workUnitAsyncStorageInstance;\n  }\n});\nconst _workunitasyncstorageinstance = require(\"./work-unit-async-storage-instance\");\nconst _approuterheaders = require(\"../../client/components/app-router-headers\");\nfunction getExpectedRequestStore(callingExpression) {\n  const workUnitStore = _workunitasyncstorageinstance.workUnitAsyncStorageInstance.getStore();\n  if (!workUnitStore) {\n    throwForMissingRequestStore(callingExpression);\n  }\n  switch (workUnitStore.type) {\n    case 'request':\n      return workUnitStore;\n    case 'prerender':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // This should not happen because we should have checked it already.\n      throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside a prerender. This is a bug in Next.js.`), \"__NEXT_ERROR_CODE\", {\n        value: \"E401\",\n        enumerable: false,\n        configurable: true\n      });\n    case 'cache':\n      throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n        value: \"E37\",\n        enumerable: false,\n        configurable: true\n      });\n    case 'unstable-cache':\n      throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n        value: \"E69\",\n        enumerable: false,\n        configurable: true\n      });\n    default:\n      const _exhaustiveCheck = workUnitStore;\n      return _exhaustiveCheck;\n  }\n}\nfunction throwForMissingRequestStore(callingExpression) {\n  throw Object.defineProperty(new Error(`\\`${callingExpression}\\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`), \"__NEXT_ERROR_CODE\", {\n    value: \"E251\",\n    enumerable: false,\n    configurable: true\n  });\n}\nfunction getPrerenderResumeDataCache(workUnitStore) {\n  if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-ppr') {\n    return workUnitStore.prerenderResumeDataCache;\n  }\n  return null;\n}\nfunction getRenderResumeDataCache(workUnitStore) {\n  if (workUnitStore.type !== 'prerender-legacy' && workUnitStore.type !== 'cache' && workUnitStore.type !== 'unstable-cache') {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.renderResumeDataCache;\n    }\n    // We return the mutable resume data cache here as an immutable version of\n    // the cache as it can also be used for reading.\n    return workUnitStore.prerenderResumeDataCache;\n  }\n  return null;\n}\nfunction getHmrRefreshHash(workStore, workUnitStore) {\n  var _workUnitStore_cookies_get;\n  if (!workStore.dev) {\n    return undefined;\n  }\n  return workUnitStore.type === 'cache' || workUnitStore.type === 'prerender' ? workUnitStore.hmrRefreshHash : workUnitStore.type === 'request' ? (_workUnitStore_cookies_get = workUnitStore.cookies.get(_approuterheaders.NEXT_HMR_REFRESH_HASH_COOKIE)) == null ? void 0 : _workUnitStore_cookies_get.value : undefined;\n}\nfunction getDraftModeProviderForCacheScope(workStore, workUnitStore) {\n  if (workStore.isDraftMode) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n      case 'request':\n        return workUnitStore.draftMode;\n      default:\n        return undefined;\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["getDraftModeProviderForCacheScope", "getExpectedRequestStore", "getHmrRefreshHash", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "throwForMissingRequestStore", "workUnitAsyncStorage", "_workunitasyncstorageinstance", "workUnitAsyncStorageInstance", "callingExpression", "workUnitStore", "getStore", "type", "Object", "defineProperty", "Error", "_exhaustiveCheck", "prerenderResumeDataCache", "renderResumeDataCache", "workStore", "_workUnitStore_cookies_get", "dev", "undefined", "hmrRefreshHash", "cookies", "get", "_approuterheaders", "NEXT_HMR_REFRESH_HASH_COOKIE", "value", "isDraftMode", "draftMode"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\server\\app-render\\work-unit-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\nimport type { ResponseCookies } from '../web/spec-extension/cookies'\nimport type { ReadonlyHeaders } from '../web/spec-extension/adapters/headers'\nimport type { ReadonlyRequestCookies } from '../web/spec-extension/adapters/request-cookies'\nimport type { CacheSignal } from './cache-signal'\nimport type { DynamicTrackingState } from './dynamic-rendering'\n\n// Share the instance module in the next-shared layer\nimport { workUnitAsyncStorageInstance } from './work-unit-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type {\n  RenderResumeDataCache,\n  PrerenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { Params } from '../request/params'\nimport type { ImplicitTags } from '../lib/implicit-tags'\nimport type { WorkStore } from './work-async-storage.external'\nimport { NEXT_HMR_REFRESH_HASH_COOKIE } from '../../client/components/app-router-headers'\n\nexport type WorkUnitPhase = 'action' | 'render' | 'after'\n\nexport interface CommonWorkUnitStore {\n  /** NOTE: Will be mutated as phases change */\n  phase: WorkUnitPhase\n  readonly implicitTags: ImplicitTags\n}\n\nexport interface RequestStore extends CommonWorkUnitStore {\n  type: 'request'\n\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL.\n   */\n  readonly url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    readonly pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    readonly search: string\n  }\n\n  readonly headers: ReadonlyHeaders\n  // This is mutable because we need to reassign it when transitioning from the action phase to the render phase.\n  // The cookie object itself is deliberately read only and thus can't be updated.\n  cookies: ReadonlyRequestCookies\n  readonly mutableCookies: ResponseCookies\n  readonly userspaceMutableCookies: ResponseCookies\n  readonly draftMode: DraftModeProvider\n  readonly isHmrRefresh?: boolean\n  readonly serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  readonly rootParams: Params\n\n  /**\n   * The resume data cache for this request. This will be a immutable cache.\n   */\n  renderResumeDataCache: RenderResumeDataCache | null\n\n  // DEV-only\n  usedDynamic?: boolean\n  prerenderPhase?: boolean\n}\n\n/**\n * The Prerender store is for tracking information related to prerenders.\n *\n * It can be used for both RSC and SSR prerendering and should be scoped as close\n * to the individual `renderTo...` API call as possible. To keep the type simple\n * we don't distinguish between RSC and SSR prerendering explicitly but instead\n * use conditional object properties to infer which mode we are in. For instance cache tracking\n * only needs to happen during the RSC prerender when we are prospectively prerendering\n * to fill all caches.\n */\nexport interface PrerenderStoreModern extends CommonWorkUnitStore {\n  type: 'prerender'\n\n  /**\n   * This signal is aborted when the React render is complete. (i.e. it is the same signal passed to react)\n   */\n  readonly renderSignal: AbortSignal\n  /**\n   * This is the AbortController which represents the boundary between Prerender and dynamic. In some renders it is\n   * the same as the controller for the renderSignal but in others it is a separate controller. It should be aborted\n   * whenever the we are no longer in the prerender phase of rendering. Typically this is after one task or when you call\n   * a sync API which requires the prerender to end immediately\n   */\n  readonly controller: AbortController\n\n  /**\n   * when not null this signal is used to track cache reads during prerendering and\n   * to await all cache reads completing before aborting the prerender.\n   */\n  readonly cacheSignal: null | CacheSignal\n\n  /**\n   * During some prerenders we want to track dynamic access.\n   */\n  readonly dynamicTracking: null | DynamicTrackingState\n\n  readonly rootParams: Params\n\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache | null\n\n  // DEV ONLY\n  // When used this flag informs certain APIs to skip logging because we're\n  // not part of the primary render path and are just prerendering to produce\n  // validation results\n  validating?: boolean\n\n  /**\n   * The HMR refresh hash is only provided in dev mode. It is needed for the dev\n   * warmup render to ensure that the cache keys will be identical for the\n   * subsequent dynamic render.\n   */\n  readonly hmrRefreshHash: string | undefined\n}\n\nexport interface PrerenderStorePPR extends CommonWorkUnitStore {\n  type: 'prerender-ppr'\n  readonly rootParams: Params\n  readonly dynamicTracking: null | DynamicTrackingState\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache\n}\n\nexport interface PrerenderStoreLegacy extends CommonWorkUnitStore {\n  type: 'prerender-legacy'\n  readonly rootParams: Params\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n}\n\nexport type PrerenderStore =\n  | PrerenderStoreLegacy\n  | PrerenderStorePPR\n  | PrerenderStoreModern\n\nexport interface CommonCacheStore\n  extends Omit<CommonWorkUnitStore, 'implicitTags'> {\n  /**\n   * A cache work unit store might not always have an outer work unit store,\n   * from which implicit tags could be inherited.\n   */\n  readonly implicitTags: ImplicitTags | undefined\n}\n\nexport interface UseCacheStore extends CommonCacheStore {\n  type: 'cache'\n  // Collected revalidate times and tags for this cache entry during the cache render.\n  revalidate: number // implicit revalidate time from inner caches / fetches\n  expire: number // server expiration time\n  stale: number // client expiration time\n  explicitRevalidate: undefined | number // explicit revalidate time from cacheLife() calls\n  explicitExpire: undefined | number // server expiration time\n  explicitStale: undefined | number // client expiration time\n  tags: null | string[]\n  readonly hmrRefreshHash: string | undefined\n  readonly isHmrRefresh: boolean\n  readonly serverComponentsHmrCache: ServerComponentsHmrCache | undefined\n  readonly forceRevalidate: boolean\n  // Draft mode is only available if the outer work unit store is a request\n  // store and draft mode is enabled.\n  readonly draftMode: DraftModeProvider | undefined\n}\n\nexport interface UnstableCacheStore extends CommonCacheStore {\n  type: 'unstable-cache'\n  // Draft mode is only available if the outer work unit store is a request\n  // store and draft mode is enabled.\n  readonly draftMode: DraftModeProvider | undefined\n}\n\n/**\n * The Cache store is for tracking information inside a \"use cache\" or unstable_cache context.\n * Inside this context we should never expose any request or page specific information.\n */\nexport type CacheStore = UseCacheStore | UnstableCacheStore\n\nexport type WorkUnitStore = RequestStore | CacheStore | PrerenderStore\n\nexport type WorkUnitAsyncStorage = AsyncLocalStorage<WorkUnitStore>\n\nexport { workUnitAsyncStorageInstance as workUnitAsyncStorage }\n\nexport function getExpectedRequestStore(\n  callingExpression: string\n): RequestStore {\n  const workUnitStore = workUnitAsyncStorageInstance.getStore()\n\n  if (!workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'request':\n      return workUnitStore\n\n    case 'prerender':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // This should not happen because we should have checked it already.\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside a prerender. This is a bug in Next.js.`\n      )\n\n    case 'cache':\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`\n      )\n\n    case 'unstable-cache':\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n      )\n\n    default:\n      const _exhaustiveCheck: never = workUnitStore\n      return _exhaustiveCheck\n  }\n}\n\nexport function throwForMissingRequestStore(callingExpression: string): never {\n  throw new Error(\n    `\\`${callingExpression}\\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`\n  )\n}\n\nexport function getPrerenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): PrerenderResumeDataCache | null {\n  if (\n    workUnitStore.type === 'prerender' ||\n    workUnitStore.type === 'prerender-ppr'\n  ) {\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n\nexport function getRenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): RenderResumeDataCache | null {\n  if (\n    workUnitStore.type !== 'prerender-legacy' &&\n    workUnitStore.type !== 'cache' &&\n    workUnitStore.type !== 'unstable-cache'\n  ) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.renderResumeDataCache\n    }\n\n    // We return the mutable resume data cache here as an immutable version of\n    // the cache as it can also be used for reading.\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n\nexport function getHmrRefreshHash(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore\n): string | undefined {\n  if (!workStore.dev) {\n    return undefined\n  }\n\n  return workUnitStore.type === 'cache' || workUnitStore.type === 'prerender'\n    ? workUnitStore.hmrRefreshHash\n    : workUnitStore.type === 'request'\n      ? workUnitStore.cookies.get(NEXT_HMR_REFRESH_HASH_COOKIE)?.value\n      : undefined\n}\n\n/**\n * Returns a draft mode provider only if draft mode is enabled.\n */\nexport function getDraftModeProviderForCacheScope(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore\n): DraftModeProvider | undefined {\n  if (workStore.isDraftMode) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n      case 'request':\n        return workUnitStore.draftMode\n      default:\n        return undefined\n    }\n  }\n\n  return undefined\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;EAiTgBA,iCAAiC,WAAAA,CAAA;WAAjCA,iCAAA;;EA9FAC,uBAAuB,WAAAA,CAAA;WAAvBA,uBAAA;;EA4EAC,iBAAiB,WAAAA,CAAA;WAAjBA,iBAAA;;EAjCAC,2BAA2B,WAAAA,CAAA;WAA3BA,2BAAA;;EAaAC,wBAAwB,WAAAA,CAAA;WAAxBA,wBAAA;;EAnBAC,2BAA2B,WAAAA,CAAA;WAA3BA,2BAAA;;EAvCyBC,oBAAoB,WAAAA,CAAA;WAApDC,6BAAA,CAAAC,4BAA4B;;;8CAxMQ;kCASA;AAiMtC,SAASP,wBACdQ,iBAAyB;EAEzB,MAAMC,aAAA,GAAgBH,6BAAA,CAAAC,4BAA4B,CAACG,QAAQ;EAE3D,IAAI,CAACD,aAAA,EAAe;IAClBL,2BAAA,CAA4BI,iBAAA;EAC9B;EAEA,QAAQC,aAAA,CAAcE,IAAI;IACxB,KAAK;MACH,OAAOF,aAAA;IAET,KAAK;IACL,KAAK;IACL,KAAK;MACH;MACA,MAAMG,MAAA,CAAAC,cAEL,CAFK,IAAIC,KAAA,CACR,KAAKN,iBAAA,mEAAoF,GADrF;eAAA;oBAAA;sBAAA;MAEN;IAEF,KAAK;MACH,MAAMI,MAAA,CAAAC,cAEL,CAFK,IAAIC,KAAA,CACR,KAAKN,iBAAA,6JAA8K,GAD/K;eAAA;oBAAA;sBAAA;MAEN;IAEF,KAAK;MACH,MAAMI,MAAA,CAAAC,cAEL,CAFK,IAAIC,KAAA,CACR,KAAKN,iBAAA,wKAAyL,GAD1L;eAAA;oBAAA;sBAAA;MAEN;IAEF;MACE,MAAMO,gBAAA,GAA0BN,aAAA;MAChC,OAAOM,gBAAA;EACX;AACF;AAEO,SAASX,4BAA4BI,iBAAyB;EACnE,MAAMI,MAAA,CAAAC,cAEL,CAFK,IAAIC,KAAA,CACR,KAAKN,iBAAA,mHAAoI,GADrI;WAAA;gBAAA;kBAAA;EAEN;AACF;AAEO,SAASN,4BACdO,aAA4B;EAE5B,IACEA,aAAA,CAAcE,IAAI,KAAK,eACvBF,aAAA,CAAcE,IAAI,KAAK,iBACvB;IACA,OAAOF,aAAA,CAAcO,wBAAwB;EAC/C;EAEA,OAAO;AACT;AAEO,SAASb,yBACdM,aAA4B;EAE5B,IACEA,aAAA,CAAcE,IAAI,KAAK,sBACvBF,aAAA,CAAcE,IAAI,KAAK,WACvBF,aAAA,CAAcE,IAAI,KAAK,kBACvB;IACA,IAAIF,aAAA,CAAcE,IAAI,KAAK,WAAW;MACpC,OAAOF,aAAA,CAAcQ,qBAAqB;IAC5C;IAEA;IACA;IACA,OAAOR,aAAA,CAAcO,wBAAwB;EAC/C;EAEA,OAAO;AACT;AAEO,SAASf,kBACdiB,SAAoB,EACpBT,aAA4B;MAStBU,0BAAA;EAPN,IAAI,CAACD,SAAA,CAAUE,GAAG,EAAE;IAClB,OAAOC,SAAA;EACT;EAEA,OAAOZ,aAAA,CAAcE,IAAI,KAAK,WAAWF,aAAA,CAAcE,IAAI,KAAK,cAC5DF,aAAA,CAAca,cAAc,GAC5Bb,aAAA,CAAcE,IAAI,KAAK,aACrBQ,0BAAA,GAAAV,aAAA,CAAcc,OAAO,CAACC,GAAG,CAACC,iBAAA,CAAAC,4BAA4B,sBAAtDP,0BAAA,CAAyDQ,KAAK,GAC9DN,SAAA;AACR;AAKO,SAAStB,kCACdmB,SAAoB,EACpBT,aAA4B;EAE5B,IAAIS,SAAA,CAAUU,WAAW,EAAE;IACzB,QAAQnB,aAAA,CAAcE,IAAI;MACxB,KAAK;MACL,KAAK;MACL,KAAK;QACH,OAAOF,aAAA,CAAcoB,SAAS;MAChC;QACE,OAAOR,SAAA;IACX;EACF;EAEA,OAAOA,SAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}