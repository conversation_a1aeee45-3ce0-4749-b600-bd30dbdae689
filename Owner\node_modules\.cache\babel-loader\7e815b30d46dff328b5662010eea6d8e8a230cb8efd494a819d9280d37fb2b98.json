{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  bindSnapshot: null,\n  createAsyncLocalStorage: null,\n  createSnapshot: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  bindSnapshot: function () {\n    return bindSnapshot;\n  },\n  createAsyncLocalStorage: function () {\n    return createAsyncLocalStorage;\n  },\n  createSnapshot: function () {\n    return createSnapshot;\n  }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n  value: \"E504\",\n  enumerable: false,\n  configurable: true\n});\nclass FakeAsyncLocalStorage {\n  disable() {\n    throw sharedAsyncLocalStorageNotAvailableError;\n  }\n  getStore() {\n    // This fake implementation of AsyncLocalStorage always returns `undefined`.\n    return undefined;\n  }\n  run() {\n    throw sharedAsyncLocalStorageNotAvailableError;\n  }\n  exit() {\n    throw sharedAsyncLocalStorageNotAvailableError;\n  }\n  enterWith() {\n    throw sharedAsyncLocalStorageNotAvailableError;\n  }\n  static bind(fn) {\n    return fn;\n  }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n  if (maybeGlobalAsyncLocalStorage) {\n    return new maybeGlobalAsyncLocalStorage();\n  }\n  return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.bind(fn);\n  }\n  return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.snapshot();\n  }\n  return function (fn, ...args) {\n    return fn(...args);\n  };\n}", "map": {"version": 3, "names": ["bindSnapshot", "createAsyncLocalStorage", "createSnapshot", "sharedAsyncLocalStorageNotAvailableError", "Object", "defineProperty", "Error", "FakeAsyncLocalStorage", "disable", "getStore", "undefined", "run", "exit", "enterWith", "bind", "fn", "maybeGlobalAsyncLocalStorage", "globalThis", "AsyncLocalStorage", "snapshot", "args"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\server\\app-render\\async-local-storage.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\nconst sharedAsyncLocalStorageNotAvailableError = new Error(\n  'Invariant: AsyncLocalStorage accessed in runtime where it is not available'\n)\n\nclass FakeAsyncLocalStorage<Store extends {}>\n  implements AsyncLocalStorage<Store>\n{\n  disable(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  getStore(): Store | undefined {\n    // This fake implementation of AsyncLocalStorage always returns `undefined`.\n    return undefined\n  }\n\n  run<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  exit<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  enterWith(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  static bind<T>(fn: T): T {\n    return fn\n  }\n}\n\nconst maybeGlobalAsyncLocalStorage =\n  typeof globalThis !== 'undefined' && (globalThis as any).AsyncLocalStorage\n\nexport function createAsyncLocalStorage<\n  Store extends {},\n>(): AsyncLocalStorage<Store> {\n  if (maybeGlobalAsyncLocalStorage) {\n    return new maybeGlobalAsyncLocalStorage()\n  }\n  return new FakeAsyncLocalStorage()\n}\n\nexport function bindSnapshot<T>(fn: T): T {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.bind(fn)\n  }\n  return FakeAsyncLocalStorage.bind(fn)\n}\n\nexport function createSnapshot(): <R, TArgs extends any[]>(\n  fn: (...args: TArgs) => R,\n  ...args: TArgs\n) => R {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.snapshot()\n  }\n  return function (fn: any, ...args: any[]) {\n    return fn(...args)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;EA+CgBA,YAAY,WAAAA,CAAA;WAAZA,YAAA;;EATAC,uBAAuB,WAAAA,CAAA;WAAvBA,uBAAA;;EAgBAC,cAAc,WAAAA,CAAA;WAAdA,cAAA;;;AApDhB,MAAMC,wCAAA,GAA2CC,MAAA,CAAAC,cAEhD,CAFgD,IAAIC,KAAA,CACnD,+EAD+C;SAAA;cAAA;gBAAA;AAEjD;AAEA,MAAMC,qBAAA;EAGJC,QAAA,EAAgB;IACd,MAAML,wCAAA;EACR;EAEAM,SAAA,EAA8B;IAC5B;IACA,OAAOC,SAAA;EACT;EAEAC,IAAA,EAAY;IACV,MAAMR,wCAAA;EACR;EAEAS,KAAA,EAAa;IACX,MAAMT,wCAAA;EACR;EAEAU,UAAA,EAAkB;IAChB,MAAMV,wCAAA;EACR;EAEA,OAAOW,KAAQC,EAAK,EAAK;IACvB,OAAOA,EAAA;EACT;AACF;AAEA,MAAMC,4BAAA,GACJ,OAAOC,UAAA,KAAe,eAAeA,UAAC,CAAmBC,iBAAiB;AAErE,SAASjB,wBAAA;EAGd,IAAIe,4BAAA,EAA8B;IAChC,OAAO,IAAIA,4BAAA;EACb;EACA,OAAO,IAAIT,qBAAA;AACb;AAEO,SAASP,aAAgBe,EAAK;EACnC,IAAIC,4BAAA,EAA8B;IAChC,OAAOA,4BAAA,CAA6BF,IAAI,CAACC,EAAA;EAC3C;EACA,OAAOR,qBAAA,CAAsBO,IAAI,CAACC,EAAA;AACpC;AAEO,SAASb,eAAA;EAId,IAAIc,4BAAA,EAA8B;IAChC,OAAOA,4BAAA,CAA6BG,QAAQ;EAC9C;EACA,OAAO,UAAUJ,EAAO,EAAE,GAAGK,IAAW;IACtC,OAAOL,EAAA,IAAMK,IAAA;EACf;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}