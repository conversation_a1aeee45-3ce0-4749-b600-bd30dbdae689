{"ast": null, "code": "import BankInfoActions from \"./actions\";\nconst initState = {\n  bankInfo: null,\n  hasBankInfo: false,\n  showForm: true\n};\nconst Reducer = (state = initState, action) => {\n  console.log('BankInfo Reducer - Action:', action.type, action.payload); // Debug log\n\n  switch (action.type) {\n    case BankInfoActions.SAVE_BANK_INFO:\n      console.log('Saving bank info:', action.payload);\n      return {\n        ...state,\n        bankInfo: action.payload,\n        hasBankInfo: true,\n        showForm: false\n      };\n    case BankInfoActions.UPDATE_BANK_INFO:\n      console.log('Updating bank info:', action.payload);\n      return {\n        ...state,\n        bankInfo: {\n          ...state.bankInfo,\n          ...action.payload\n        },\n        showForm: false\n      };\n    case BankInfoActions.DELETE_BANK_INFO:\n      console.log('Deleting bank info');\n      return {\n        ...state,\n        bankInfo: null,\n        hasBankInfo: false,\n        showForm: true\n      };\n    case BankInfoActions.SET_SHOW_FORM:\n      console.log('Setting show form:', action.payload);\n      return {\n        ...state,\n        showForm: action.payload\n      };\n    default:\n      return state;\n  }\n};\n_c = Reducer;\nexport default Reducer;\nvar _c;\n$RefreshReg$(_c, \"Reducer\");", "map": {"version": 3, "names": ["BankInfoActions", "initState", "bankInfo", "hasBankInfo", "showForm", "Reducer", "state", "action", "console", "log", "type", "payload", "SAVE_BANK_INFO", "UPDATE_BANK_INFO", "DELETE_BANK_INFO", "SET_SHOW_FORM", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/bankInfo/reducer.js"], "sourcesContent": ["import BankInfoActions from \"./actions\";\r\n\r\nconst initState = {\r\n  bankInfo: null,\r\n  hasBankInfo: false,\r\n  showForm: true,\r\n};\r\n\r\nconst Reducer = (state = initState, action) => {\r\n  console.log('BankInfo Reducer - Action:', action.type, action.payload); // Debug log\r\n  \r\n  switch (action.type) {\r\n    case BankInfoActions.SAVE_BANK_INFO:\r\n      console.log('Saving bank info:', action.payload);\r\n      return {\r\n        ...state,\r\n        bankInfo: action.payload,\r\n        hasBankInfo: true,\r\n        showForm: false,\r\n      };\r\n\r\n    case BankInfoActions.UPDATE_BANK_INFO:\r\n      console.log('Updating bank info:', action.payload);\r\n      return {\r\n        ...state,\r\n        bankInfo: { ...state.bankInfo, ...action.payload },\r\n        showForm: false,\r\n      };\r\n\r\n    case BankInfoActions.DELETE_BANK_INFO:\r\n      console.log('Deleting bank info');\r\n      return {\r\n        ...state,\r\n        bankInfo: null,\r\n        hasBankInfo: false,\r\n        showForm: true,\r\n      };\r\n\r\n    case BankInfoActions.SET_SHOW_FORM:\r\n      console.log('Setting show form:', action.payload);\r\n      return {\r\n        ...state,\r\n        showForm: action.payload,\r\n      };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default Reducer;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,WAAW;AAEvC,MAAMC,SAAS,GAAG;EAChBC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,KAAK;EAClBC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,OAAO,GAAGA,CAACC,KAAK,GAAGL,SAAS,EAAEM,MAAM,KAAK;EAC7CC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,MAAM,CAACG,IAAI,EAAEH,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC;;EAExE,QAAQJ,MAAM,CAACG,IAAI;IACjB,KAAKV,eAAe,CAACY,cAAc;MACjCJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,MAAM,CAACI,OAAO,CAAC;MAChD,OAAO;QACL,GAAGL,KAAK;QACRJ,QAAQ,EAAEK,MAAM,CAACI,OAAO;QACxBR,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE;MACZ,CAAC;IAEH,KAAKJ,eAAe,CAACa,gBAAgB;MACnCL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,MAAM,CAACI,OAAO,CAAC;MAClD,OAAO;QACL,GAAGL,KAAK;QACRJ,QAAQ,EAAE;UAAE,GAAGI,KAAK,CAACJ,QAAQ;UAAE,GAAGK,MAAM,CAACI;QAAQ,CAAC;QAClDP,QAAQ,EAAE;MACZ,CAAC;IAEH,KAAKJ,eAAe,CAACc,gBAAgB;MACnCN,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjC,OAAO;QACL,GAAGH,KAAK;QACRJ,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE;MACZ,CAAC;IAEH,KAAKJ,eAAe,CAACe,aAAa;MAChCP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,MAAM,CAACI,OAAO,CAAC;MACjD,OAAO;QACL,GAAGL,KAAK;QACRF,QAAQ,EAAEG,MAAM,CAACI;MACnB,CAAC;IAEH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAACU,EAAA,GAxCIX,OAAO;AA0Cb,eAAeA,OAAO;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}