{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  // Fetch rooms by hotel ID for owner\n  fetchRoomByHotelId: async hotelId => {\n    try {\n      const response = await api.get(ApiConstants.ROOMS_BY_HOTEL_ID(hotelId));\n      return response.data;\n    } catch (error) {\n      console.error(\"Error fetching rooms by hotel ID:\", error);\n      throw error;\n    }\n  },\n  // Create new room\n  createRoom: async roomData => {\n    try {\n      const response = await api.post(\"/room/create-room\", roomData);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error creating room:\", error);\n      throw error;\n    }\n  },\n  // Update existing room\n  updateRoom: async (roomId, roomData) => {\n    try {\n      const response = await api.put(`/room/update-room/${roomId}`, roomData);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error updating room:\", error);\n      throw error;\n    }\n  },\n  // Change room status\n  changeRoomStatus: async (roomId, statusActive) => {\n    try {\n      const response = await api.put(`/room/change-status-room/${roomId}`, {\n        statusActive\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error changing room status:\", error);\n      throw error;\n    }\n  },\n  // Upload room images\n  uploadRoomImages: async imageFiles => {\n    try {\n      const formData = new FormData();\n      imageFiles.forEach(file => {\n        formData.append('images', file);\n      });\n      const response = await api.post(\"/room/upload_images\", formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error uploading room images:\", error);\n      throw error;\n    }\n  },\n  // Delete room images\n  deleteRoomImages: async imageIds => {\n    try {\n      const response = await api.delete(\"/room/delete_images\", {\n        data: {\n          imageIds\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error deleting room images:\", error);\n      throw error;\n    }\n  },\n  // Get room details by ID\n  getRoomById: async roomId => {\n    try {\n      const response = await api.get(`/room/rooms_detail/${roomId}`);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error fetching room details:\", error);\n      throw error;\n    }\n  },\n  fetch_room: (hotelId, query) => {\n    const params = {\n      checkInDate: '',\n      checkOutDate: ''\n    };\n    if (query.checkoutDate) {\n      params.checkOutDate = query.checkoutDate;\n    }\n    if (query.checkinDate) {\n      params.checkInDate = query.checkinDate;\n    }\n    const url = ApiConstants.FETCH_ROOM.replace(\":hotelId\", hotelId);\n    return api.get(url, {\n      params\n    });\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetchRoomByHotelId", "hotelId", "response", "get", "ROOMS_BY_HOTEL_ID", "data", "error", "console", "createRoom", "roomData", "post", "updateRoom", "roomId", "put", "changeRoomStatus", "statusActive", "uploadRoomImages", "imageFiles", "formData", "FormData", "for<PERSON>ach", "file", "append", "headers", "deleteRoomImages", "imageIds", "delete", "getRoomById", "fetch_room", "query", "params", "checkInDate", "checkOutDate", "checkoutDate", "checkinDate", "url", "FETCH_ROOM", "replace"], "sources": ["E:/WDP301_UROOM/Owner/src/redux/room/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  // Fetch rooms by hotel ID for owner\r\n  fetchRoomByHotelId: async (hotelId) => {\r\n    try {\r\n      const response = await api.get(ApiConstants.ROOMS_BY_HOTEL_ID(hotelId));\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error fetching rooms by hotel ID:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create new room\r\n  createRoom: async (roomData) => {\r\n    try {\r\n      const response = await api.post(\"/room/create-room\", roomData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating room:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update existing room\r\n  updateRoom: async (roomId, roomData) => {\r\n    try {\r\n      const response = await api.put(`/room/update-room/${roomId}`, roomData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error updating room:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Change room status\r\n  changeRoomStatus: async (roomId, statusActive) => {\r\n    try {\r\n      const response = await api.put(`/room/change-status-room/${roomId}`, {\r\n        statusActive\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error changing room status:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Upload room images\r\n  uploadRoomImages: async (imageFiles) => {\r\n    try {\r\n      const formData = new FormData();\r\n      imageFiles.forEach((file) => {\r\n        formData.append('images', file);\r\n      });\r\n\r\n      const response = await api.post(\"/room/upload_images\", formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error uploading room images:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete room images\r\n  deleteRoomImages: async (imageIds) => {\r\n    try {\r\n      const response = await api.delete(\"/room/delete_images\", {\r\n        data: { imageIds }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error deleting room images:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get room details by ID\r\n  getRoomById: async (roomId) => {\r\n    try {\r\n      const response = await api.get(`/room/rooms_detail/${roomId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error fetching room details:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n   fetch_room: (hotelId, query) => {\r\n\r\n    const params = {\r\n      checkInDate: '',\r\n      checkOutDate: '',\r\n    };\r\n    if(query.checkoutDate){\r\n      params.checkOutDate= query.checkoutDate\r\n    }\r\n    if(query.checkinDate){\r\n      params.checkInDate= query.checkinDate\r\n    }\r\n    const url = ApiConstants.FETCH_ROOM.replace(\":hotelId\", hotelId);\r\n    return api.get(url, {params});  \r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChB;EACAC,kBAAkB,EAAE,MAAOC,OAAO,IAAK;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAACN,YAAY,CAACO,iBAAiB,CAACH,OAAO,CAAC,CAAC;MACvE,OAAOC,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAE,UAAU,EAAE,MAAOC,QAAQ,IAAK;IAC9B,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMJ,GAAG,CAACY,IAAI,CAAC,mBAAmB,EAAED,QAAQ,CAAC;MAC9D,OAAOP,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAK,UAAU,EAAE,MAAAA,CAAOC,MAAM,EAAEH,QAAQ,KAAK;IACtC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMJ,GAAG,CAACe,GAAG,CAAC,qBAAqBD,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACvE,OAAOP,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAQ,gBAAgB,EAAE,MAAAA,CAAOF,MAAM,EAAEG,YAAY,KAAK;IAChD,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMJ,GAAG,CAACe,GAAG,CAAC,4BAA4BD,MAAM,EAAE,EAAE;QACnEG;MACF,CAAC,CAAC;MACF,OAAOb,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAU,gBAAgB,EAAE,MAAOC,UAAU,IAAK;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BF,UAAU,CAACG,OAAO,CAAEC,IAAI,IAAK;QAC3BH,QAAQ,CAACI,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;MACjC,CAAC,CAAC;MAEF,MAAMnB,QAAQ,GAAG,MAAMJ,GAAG,CAACY,IAAI,CAAC,qBAAqB,EAAEQ,QAAQ,EAAE;QAC/DK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOrB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAkB,gBAAgB,EAAE,MAAOC,QAAQ,IAAK;IACpC,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMJ,GAAG,CAAC4B,MAAM,CAAC,qBAAqB,EAAE;QACvDrB,IAAI,EAAE;UAAEoB;QAAS;MACnB,CAAC,CAAC;MACF,OAAOvB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAqB,WAAW,EAAE,MAAOf,MAAM,IAAK;IAC7B,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,sBAAsBS,MAAM,EAAE,CAAC;MAC9D,OAAOV,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAEAsB,UAAU,EAAEA,CAAC3B,OAAO,EAAE4B,KAAK,KAAK;IAE/B,MAAMC,MAAM,GAAG;MACbC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE;IAChB,CAAC;IACD,IAAGH,KAAK,CAACI,YAAY,EAAC;MACpBH,MAAM,CAACE,YAAY,GAAEH,KAAK,CAACI,YAAY;IACzC;IACA,IAAGJ,KAAK,CAACK,WAAW,EAAC;MACnBJ,MAAM,CAACC,WAAW,GAAEF,KAAK,CAACK,WAAW;IACvC;IACA,MAAMC,GAAG,GAAGtC,YAAY,CAACuC,UAAU,CAACC,OAAO,CAAC,UAAU,EAAEpC,OAAO,CAAC;IAChE,OAAOH,GAAG,CAACK,GAAG,CAACgC,GAAG,EAAE;MAACL;IAAM,CAAC,CAAC;EAC/B;AACF,CAAC;AAED,eAAe/B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}