{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n0 && (module.exports = {\n  ACTION_HEADER: null,\n  FLIGHT_HEADERS: null,\n  NEXT_DID_POSTPONE_HEADER: null,\n  NEXT_HMR_REFRESH_HASH_COOKIE: null,\n  NEXT_HMR_REFRESH_HEADER: null,\n  NEXT_IS_PRERENDER_HEADER: null,\n  NEXT_REWRITTEN_PATH_HEADER: null,\n  NEXT_REWRITTEN_QUERY_HEADER: null,\n  NEXT_ROUTER_PREFETCH_HEADER: null,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: null,\n  NEXT_ROUTER_STALE_TIME_HEADER: null,\n  NEXT_ROUTER_STATE_TREE_HEADER: null,\n  NEXT_RSC_UNION_QUERY: null,\n  NEXT_URL: null,\n  RSC_CONTENT_TYPE_HEADER: null,\n  RSC_HEADER: null\n});\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  ACTION_HEADER: function () {\n    return ACTION_HEADER;\n  },\n  FLIGHT_HEADERS: function () {\n    return FLIGHT_HEADERS;\n  },\n  NEXT_DID_POSTPONE_HEADER: function () {\n    return NEXT_DID_POSTPONE_HEADER;\n  },\n  NEXT_HMR_REFRESH_HASH_COOKIE: function () {\n    return NEXT_HMR_REFRESH_HASH_COOKIE;\n  },\n  NEXT_HMR_REFRESH_HEADER: function () {\n    return NEXT_HMR_REFRESH_HEADER;\n  },\n  NEXT_IS_PRERENDER_HEADER: function () {\n    return NEXT_IS_PRERENDER_HEADER;\n  },\n  NEXT_REWRITTEN_PATH_HEADER: function () {\n    return NEXT_REWRITTEN_PATH_HEADER;\n  },\n  NEXT_REWRITTEN_QUERY_HEADER: function () {\n    return NEXT_REWRITTEN_QUERY_HEADER;\n  },\n  NEXT_ROUTER_PREFETCH_HEADER: function () {\n    return NEXT_ROUTER_PREFETCH_HEADER;\n  },\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: function () {\n    return NEXT_ROUTER_SEGMENT_PREFETCH_HEADER;\n  },\n  NEXT_ROUTER_STALE_TIME_HEADER: function () {\n    return NEXT_ROUTER_STALE_TIME_HEADER;\n  },\n  NEXT_ROUTER_STATE_TREE_HEADER: function () {\n    return NEXT_ROUTER_STATE_TREE_HEADER;\n  },\n  NEXT_RSC_UNION_QUERY: function () {\n    return NEXT_RSC_UNION_QUERY;\n  },\n  NEXT_URL: function () {\n    return NEXT_URL;\n  },\n  RSC_CONTENT_TYPE_HEADER: function () {\n    return RSC_CONTENT_TYPE_HEADER;\n  },\n  RSC_HEADER: function () {\n    return RSC_HEADER;\n  }\n});\nconst RSC_HEADER = 'RSC';\nconst ACTION_HEADER = 'Next-Action';\nconst NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree';\nconst NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch';\nconst NEXT_ROUTER_SEGMENT_PREFETCH_HEADER = 'Next-Router-Segment-Prefetch';\nconst NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh';\nconst NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__';\nconst NEXT_URL = 'Next-Url';\nconst RSC_CONTENT_TYPE_HEADER = 'text/x-component';\nconst FLIGHT_HEADERS = [RSC_HEADER, NEXT_ROUTER_STATE_TREE_HEADER, NEXT_ROUTER_PREFETCH_HEADER, NEXT_HMR_REFRESH_HEADER, NEXT_ROUTER_SEGMENT_PREFETCH_HEADER];\nconst NEXT_RSC_UNION_QUERY = '_rsc';\nconst NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time';\nconst NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed';\nconst NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path';\nconst NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query';\nconst NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}", "map": {"version": 3, "names": ["ACTION_HEADER", "FLIGHT_HEADERS", "NEXT_DID_POSTPONE_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_HMR_REFRESH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER"], "sources": ["E:\\WDP301_UROOM\\Owner\\node_modules\\next\\src\\client\\components\\app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EACaA,aAAa,WAAAA,CAAA;WAAbA,aAAA;;EAiBAC,cAAc,WAAAA,CAAA;WAAdA,cAAA;;EAWAC,wBAAwB,WAAAA,CAAA;WAAxBA,wBAAA;;EAfAC,4BAA4B,WAAAA,CAAA;WAA5BA,4BAAA;;EADAC,uBAAuB,WAAAA,CAAA;WAAvBA,uBAAA;;EAmBAC,wBAAwB,WAAAA,CAAA;WAAxBA,wBAAA;;EAFAC,0BAA0B,WAAAA,CAAA;WAA1BA,0BAAA;;EACAC,2BAA2B,WAAAA,CAAA;WAA3BA,2BAAA;;EAzBAC,2BAA2B,WAAAA,CAAA;WAA3BA,2BAAA;;EAKAC,mCAAmC,WAAAA,CAAA;WAAnCA,mCAAA;;EAiBAC,6BAA6B,WAAAA,CAAA;WAA7BA,6BAAA;;EAvBAC,6BAA6B,WAAAA,CAAA;WAA7BA,6BAAA;;EAqBAC,oBAAoB,WAAAA,CAAA;WAApBA,oBAAA;;EAXAC,QAAQ,WAAAA,CAAA;WAARA,QAAA;;EACAC,uBAAuB,WAAAA,CAAA;WAAvBA,uBAAA;;EAhBAC,UAAU,WAAAA,CAAA;WAAVA,UAAA;;;AAAN,MAAMA,UAAA,GAAa;AACnB,MAAMf,aAAA,GAAgB;AAItB,MAAMW,6BAAA,GAAgC;AACtC,MAAMH,2BAAA,GAA8B;AAKpC,MAAMC,mCAAA,GACX;AACK,MAAML,uBAAA,GAA0B;AAChC,MAAMD,4BAAA,GAA+B;AACrC,MAAMU,QAAA,GAAW;AACjB,MAAMC,uBAAA,GAA0B;AAEhC,MAAMb,cAAA,GAAiB,CAC5Bc,UAAA,EACAJ,6BAAA,EACAH,2BAAA,EACAJ,uBAAA,EACAK,mCAAA,CACD;AAEM,MAAMG,oBAAA,GAAuB;AAE7B,MAAMF,6BAAA,GAAgC;AACtC,MAAMR,wBAAA,GAA2B;AACjC,MAAMI,0BAAA,GAA6B;AACnC,MAAMC,2BAAA,GAA8B;AACpC,MAAMF,wBAAA,GAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}