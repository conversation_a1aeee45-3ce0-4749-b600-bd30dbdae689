{"ast": null, "code": "const ApiConstants = {\n  //AUTH:\n  LOGIN_OWNER: \"/auth/login_owner\",\n  REGISTER_OWNER: \"/auth/register_owner\",\n  VERIFY_EMAIL: \"/auth/verify-email\",\n  RESEND_VERIFICATION: \"/auth/resend-verification\",\n  CHANGE_PASSWORD: \"/auth/changePassword_customer\",\n  UPDATE_PROFILE: \"/auth/updateProfile_customer\",\n  UPDATE_AVATAR: \"/auth/update_avatar\",\n  FORGOT_PASSWORD: \"/auth/forgot_password\",\n  RESET_PASSWORD: \"/auth/reset_password\",\n  VERIFY_FORGOT_PASSWORD: \"/auth/verify_forgot_password\",\n  //FEEDBACK:\n  FEEDBACK_HOTEL: \"/feedback/get-feedback-hotel/:hotelId\",\n  FETCH_FEEDBACK_BY_ID: \"/feedback/getFeedbackById/:feedbackId\",\n  ///REPORTFEEDBACK\n  REPORT_FEEDBACK: \"reportFeedback/create_report_feedback_owner\",\n  FETCH_REPORTS_BY_USERID: \"reportFeedback/my-reports\",\n  DELETE_REPORTED_FEEDBACK: \"reportFeedback/delete_report_feedback/:reportId\",\n  //HOTEL\n  CREATE_HOTEL: \"/hotel/create-hotel\",\n  FETCH_OWNER_HOTEL: \"/hotel/owner-hotels\",\n  UPDATE_HOTEL: \"/hotel/update-hotel\",\n  CREATE_HOTEL_SERVICE: \"/hotel/add-service\",\n  UPDATE_HOTEL_SERVICE: \"/hotelservices/update-service\",\n  UPDATE_HOTEL_SERVICE_STATUS: \"/hotel/updateStatusService/:hotelId/status\",\n  CHANGE_STATUS_HOTEL: \"/hotel/changeStatus-hotel\",\n  //RESERVATION\n  RESERVATIONS: \"/payment/reservations\",\n  //MONTHLYPAYMENT\n  MONTHLY_PAYMENTS: \"/monthly-payment/all\",\n  //DASHBOARD\n  DASHBOARD_METRICS: \"/dashboard-owner/metrics\",\n  //chat\n  FETCH_CHAT_MESSAGE: \"/chat/chat-history/:receiverId\",\n  FETCH_CHAT_ALL_USERS: \"/chat/chat-users\",\n  //Image_Hotel:\n  UPLOAD_HOTEL_IMAGE: \"/hotel/upload_images\",\n  DELETE_HOTEL_IMAGE: \"/hotel/delete_images\",\n  //Room:\n  FETCH_ROOM: \"/room/rooms_information/:hotelId\",\n  ROOMS_BY_HOTEL_ID: hotelId => `/room/list_room/${hotelId}`,\n  CREATE_BOOKING_OFFLINE: \"/payment/create-booking-offline\"\n};\nexport default ApiConstants;", "map": {"version": 3, "names": ["ApiConstants", "LOGIN_OWNER", "REGISTER_OWNER", "VERIFY_EMAIL", "RESEND_VERIFICATION", "CHANGE_PASSWORD", "UPDATE_PROFILE", "UPDATE_AVATAR", "FORGOT_PASSWORD", "RESET_PASSWORD", "VERIFY_FORGOT_PASSWORD", "FEEDBACK_HOTEL", "FETCH_FEEDBACK_BY_ID", "REPORT_FEEDBACK", "FETCH_REPORTS_BY_USERID", "DELETE_REPORTED_FEEDBACK", "CREATE_HOTEL", "FETCH_OWNER_HOTEL", "UPDATE_HOTEL", "CREATE_HOTEL_SERVICE", "UPDATE_HOTEL_SERVICE", "UPDATE_HOTEL_SERVICE_STATUS", "CHANGE_STATUS_HOTEL", "RESERVATIONS", "MONTHLY_PAYMENTS", "DASHBOARD_METRICS", "FETCH_CHAT_MESSAGE", "FETCH_CHAT_ALL_USERS", "UPLOAD_HOTEL_IMAGE", "DELETE_HOTEL_IMAGE", "FETCH_ROOM", "ROOMS_BY_HOTEL_ID", "hotelId", "CREATE_BOOKING_OFFLINE"], "sources": ["E:/WDP301_UROOM/Owner/src/adapter/ApiConstants.js"], "sourcesContent": ["const ApiConstants = {\r\n  //AUTH:\r\n  LOGIN_OWNER: \"/auth/login_owner\",\r\n  REGISTER_OWNER: \"/auth/register_owner\",\r\n  VERIFY_EMAIL: \"/auth/verify-email\",\r\n  RESEND_VERIFICATION: \"/auth/resend-verification\",\r\n  CHANGE_PASSWORD: \"/auth/changePassword_customer\",\r\n  UPDATE_PROFILE: \"/auth/updateProfile_customer\",\r\n  UPDATE_AVATAR: \"/auth/update_avatar\",\r\n  FORGOT_PASSWORD: \"/auth/forgot_password\",\r\n  RESET_PASSWORD: \"/auth/reset_password\",\r\n  VERIFY_FORGOT_PASSWORD: \"/auth/verify_forgot_password\",\r\n  //FEEDBACK:\r\n  FEEDBACK_HOTEL: \"/feedback/get-feedback-hotel/:hotelId\",\r\n  FETCH_FEEDBACK_BY_ID: \"/feedback/getFeedbackById/:feedbackId\",\r\n  ///REPORTFEEDBACK\r\n  REPORT_FEEDBACK: \"reportFeedback/create_report_feedback_owner\",\r\n  FETCH_REPORTS_BY_USERID: \"reportFeedback/my-reports\",\r\n  DELETE_REPORTED_FEEDBACK: \"reportFeedback/delete_report_feedback/:reportId\",\r\n  //HOTEL\r\n  CREATE_HOTEL: \"/hotel/create-hotel\",\r\n  FETCH_OWNER_HOTEL: \"/hotel/owner-hotels\",\r\n  UPDATE_HOTEL: \"/hotel/update-hotel\",\r\n  CREATE_HOTEL_SERVICE: \"/hotel/add-service\",\r\n  UPDATE_HOTEL_SERVICE: \"/hotelservices/update-service\",\r\n  UPDATE_HOTEL_SERVICE_STATUS: \"/hotel/updateStatusService/:hotelId/status\",\r\n  CHANGE_STATUS_HOTEL: \"/hotel/changeStatus-hotel\",\r\n  //RESERVATION\r\n  RESERVATIONS: \"/payment/reservations\",\r\n  //MONTHLYPAYMENT\r\n  MONTHLY_PAYMENTS: \"/monthly-payment/all\",\r\n  //DASHBOARD\r\n  DASHBOARD_METRICS: \"/dashboard-owner/metrics\",\r\n\r\n  //chat\r\n  FETCH_CHAT_MESSAGE: \"/chat/chat-history/:receiverId\",\r\n  FETCH_CHAT_ALL_USERS: \"/chat/chat-users\",\r\n\r\n  //Image_Hotel:\r\n  UPLOAD_HOTEL_IMAGE: \"/hotel/upload_images\",\r\n  DELETE_HOTEL_IMAGE: \"/hotel/delete_images\",\r\n\r\n  //Room:\r\n  FETCH_ROOM: \"/room/rooms_information/:hotelId\",\r\n  ROOMS_BY_HOTEL_ID: (hotelId) => `/room/list_room/${hotelId}`,\r\n  CREATE_BOOKING_OFFLINE: \"/payment/create-booking-offline\",\r\n};\r\n\r\nexport default ApiConstants;\r\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACnB;EACAC,WAAW,EAAE,mBAAmB;EAChCC,cAAc,EAAE,sBAAsB;EACtCC,YAAY,EAAE,oBAAoB;EAClCC,mBAAmB,EAAE,2BAA2B;EAChDC,eAAe,EAAE,+BAA+B;EAChDC,cAAc,EAAE,8BAA8B;EAC9CC,aAAa,EAAE,qBAAqB;EACpCC,eAAe,EAAE,uBAAuB;EACxCC,cAAc,EAAE,sBAAsB;EACtCC,sBAAsB,EAAE,8BAA8B;EACtD;EACAC,cAAc,EAAE,uCAAuC;EACvDC,oBAAoB,EAAE,uCAAuC;EAC7D;EACAC,eAAe,EAAE,6CAA6C;EAC9DC,uBAAuB,EAAE,2BAA2B;EACpDC,wBAAwB,EAAE,iDAAiD;EAC3E;EACAC,YAAY,EAAE,qBAAqB;EACnCC,iBAAiB,EAAE,qBAAqB;EACxCC,YAAY,EAAE,qBAAqB;EACnCC,oBAAoB,EAAE,oBAAoB;EAC1CC,oBAAoB,EAAE,+BAA+B;EACrDC,2BAA2B,EAAE,4CAA4C;EACzEC,mBAAmB,EAAE,2BAA2B;EAChD;EACAC,YAAY,EAAE,uBAAuB;EACrC;EACAC,gBAAgB,EAAE,sBAAsB;EACxC;EACAC,iBAAiB,EAAE,0BAA0B;EAE7C;EACAC,kBAAkB,EAAE,gCAAgC;EACpDC,oBAAoB,EAAE,kBAAkB;EAExC;EACAC,kBAAkB,EAAE,sBAAsB;EAC1CC,kBAAkB,EAAE,sBAAsB;EAE1C;EACAC,UAAU,EAAE,kCAAkC;EAC9CC,iBAAiB,EAAGC,OAAO,IAAK,mBAAmBA,OAAO,EAAE;EAC5DC,sBAAsB,EAAE;AAC1B,CAAC;AAED,eAAejC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}